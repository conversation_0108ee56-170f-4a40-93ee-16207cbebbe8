apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${APPLICATION_NAME}
  namespace: default
  labels: &labels
    app: ${APPLICATION_NAME}

spec:
  replicas: $REPLICAS
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels: *labels
  template:
    metadata:
      labels: *labels
    spec:
      serviceAccountName: ${APPLICATION_NAME}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: ${APPLICATION_NAME}
      volumes:
        - name: skywalking-agent
          emptyDir: { }
      initContainers:
        - name: init-skywalking-agent
          image: apache/skywalking-java-agent:9.2.0-java21
          command: [ 'sh', '-c', 'cp -r /skywalking/agent/* /skywalking-agent' ]
          volumeMounts:
            - name: skywalking-agent
              mountPath: /skywalking-agent
      containers:
        - name: ${APPLICATION_NAME}
          image: ${DOCKER_REPO_URL}/mercaso/${APPLICATION_NAME}:${IMAGE_VERSION}
          imagePullPolicy: Always
          volumeMounts:
            - name: skywalking-agent
              mountPath: /skywalking-agent
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: $SPRING_PROFILES_ACTIVE
            - name: JAVA_TOOL_OPTIONS
              value: >-
                -javaagent:/skywalking-agent/skywalking-agent.jar
                -Dskywalking.agent.service_name=$APPLICATION_NAME
                -Dskywalking.collector.backend_service=$SKYWALKING_OAP_HOST
                $SKYWALKING_EXTRA_CONFIG
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh", "-c", "sleep 15" ]
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: monitoring
              containerPort: 8081
              protocol: TCP
            - name: jmx
              containerPort: 1099
              protocol: TCP
          resources:
            requests:
              memory: $REQUESTS_MEMORY
              cpu: $REQUESTS_CPU
            limits:
              memory: $LIMITS_MEMORY
              cpu: $LIMITS_CPU
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: monitoring
            timeoutSeconds: 5
            initialDelaySeconds: 120

          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: monitoring
            timeoutSeconds: 5
            successThreshold: 2
            failureThreshold: 2
            periodSeconds: 5
            initialDelaySeconds: 0

          startupProbe:
            httpGet:
              path: /actuator/health/readiness
              port: monitoring
            timeoutSeconds: 5
            successThreshold: 1
            periodSeconds: 5
            failureThreshold: 60
            initialDelaySeconds: 0

      terminationGracePeriodSeconds: 180

---
apiVersion: v1
kind: Service
metadata:
  name: ${APPLICATION_NAME}
  namespace: default
  labels:
    app: ${APPLICATION_NAME}

spec:
  type: ClusterIP
  selector:
    app: ${APPLICATION_NAME}
  ports:
    - name: http
      port: 80
      targetPort: 8080
      protocol: TCP
    - name: monitoring
      port: 8081
      targetPort: 8081
      protocol: TCP