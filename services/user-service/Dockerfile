FROM eclipse-temurin:21-jdk-alpine

# Build arguments
ARG APPLICATION_NAME
ARG APP_PORT

# Application environment
ENV APPLICATION=${APPLICATION_NAME}

# JVM Options - grouped by functionality
ENV JAVA_OPTS="\
    # Memory management
    -XX:+UnlockExperimentalVMOptions \
    -XX:MaxRAMPercentage=50 \
    # System properties
    -Djava.security.egd=file:/dev/./urandom \
    # JMX configuration
    -Dcom.sun.management.jmxremote \
    -Dcom.sun.management.jmxremote.authenticate=false \
    -Dcom.sun.management.jmxremote.ssl=false \
    -Dcom.sun.management.jmxremote.local.only=false \
    -Dcom.sun.management.jmxremote.port=1099 \
    -Dcom.sun.management.jmxremote.rmi.port=1099 \
    -Djava.rmi.server.hostname=127.0.0.1 \
    # Logging
    -DEnableLogstash"

# System setup and directory structure
RUN apk update \
    && apk upgrade \
    && apk add --no-cache bash \
    && rm -rf /var/lib/apt/lists/* \
    # Create and set permissions for application directories
    && mkdir -p \
        /var/run/mercaso \
        /opt/mercaso/${APPLICATION_NAME} \
        /var/log/mercaso/${APPLICATION_NAME} \
        /usr/bin \
    && chmod -R 755 \
        /var/run/mercaso \
        /opt/mercaso/${APPLICATION_NAME} \
        /var/log/mercaso/${APPLICATION_NAME} \
        /usr/bin \
    # Create log symlink
    && ln -s /var/log/mercaso/${APPLICATION_NAME} /opt/mercaso/${APPLICATION_NAME}/log \
    # Security configuration
    && echo "ALL : ALL " >> /etc/hosts.allow \
    # Create and configure run script
    && printf "#!/bin/sh\nexec java ${JAVA_OPTS} -jar template.jar \"$@\"" > /opt/mercaso/${APPLICATION_NAME}/run.sh \
    && chmod 755 /opt/mercaso/${APPLICATION_NAME}/run.sh

# Set working directory
WORKDIR /opt/mercaso/${APPLICATION_NAME}

# Expose application port
EXPOSE ${APP_PORT}

# Copy application jar
COPY build/libs/*-1.0.0.jar ./template.jar

# Set entrypoint
ENTRYPOINT ["./run.sh"]
