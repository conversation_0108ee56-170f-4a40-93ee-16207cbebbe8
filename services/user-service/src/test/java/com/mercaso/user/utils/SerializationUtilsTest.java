package com.mercaso.user.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.type.TypeReference;
import java.util.Map;
import org.junit.jupiter.api.Test;

class SerializationUtilsTest {

    @Test
    void convertValue_ShouldConvertMapToObject() {
        Map<String, Object> map = Map.of("name", "John Doe", "age", 30);
        TypeReference<Map<String, Object>> typeRef = new TypeReference<>() {
        };
        Map<String, Object> result = SerializationUtils.convertValue(map, typeRef);
        assertEquals(map, result);
    }

}