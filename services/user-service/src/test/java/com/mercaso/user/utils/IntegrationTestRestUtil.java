package com.mercaso.user.utils;

import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


public abstract class IntegrationTestRestUtil {

    private final Environment environment;

    private final LocalHostUriTemplateHandler uriTemplateHandler;

    private final RestTemplate restTemplate = new RestTemplate();

    private final TestRestTemplate testRestTemplate = new TestRestTemplate();

    public final ObjectMapper objectMapper = new ObjectMapper()
        .registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    private final String userProfileJson = "";

    public IntegrationTestRestUtil(Environment environment) {
        this.environment = environment;
        this.uriTemplateHandler = new LocalHostUriTemplateHandler(environment);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
    }

    private HttpHeaders createHeaders() {
        return createHeaders(MediaType.APPLICATION_JSON);
    }

    private HttpHeaders createHeaders(MediaType type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        if (userProfileJson != null) {
            headers.set("UserProfile", userProfileJson);
        }
        return headers;
    }

    public ResponseEntity<String> performRequest(String path,
        Map<String, String> params,
        Object body,
        HttpMethod method,
        HttpHeaders headers) throws Exception {
        String url = uriTemplateHandler.getRootUri() + path;

        if (params != null) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            builder.build(false);
            params.forEach((k, v) -> builder.queryParam(k, v));
            url = builder.toUriString();
        }

        HttpEntity<String> entity = new HttpEntity<>(objectMapper.writeValueAsString(body),
            headers);

        return restTemplate.exchange(url, method, entity, String.class);
    }

    public <DtoType> DtoType createEntity(String path, Object payload, Class<DtoType> dtoClass) {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForObject(url, httpentity, dtoClass);
    }

    public <DtoType> ResponseEntity<DtoType> postEntity(String path, Object payload, Class<DtoType> dtoClass) {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForEntity(url, httpentity, dtoClass);
    }

    public void putEntity(String path, Object payload) {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        restTemplate.put(url, httpentity);
    }

    public <DtoType> ResponseEntity<DtoType> postEntity(String path, Object payload, Class<DtoType> responseType,
        MediaType mediaType) {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders(mediaType));
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForEntity(url, httpentity, responseType);
    }

    public <DtoType> ResponseEntity<DtoType> getEntity(String path, Class<DtoType> dtoClass) {
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.getForEntity(url, dtoClass);
    }

    public <DtoType> DtoType getForObject(String path, Class<DtoType> dtoClass) {
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.getForObject(url, dtoClass);
    }

    public <DtoType> ResponseEntity<DtoType> getEntityByMap(String path, ParameterizedTypeReference<DtoType> responseType,
        Map<String, ?> params) {
        String baseUrl = uriTemplateHandler.getRootUri() + path;

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUrl);

        params.forEach((key, value) -> {
            if (value instanceof Collection<?>) {
                ((Collection<?>) value).forEach(v -> builder.queryParam(key, v));
            } else {
                builder.queryParam(key, value);
            }
        });

        String url = builder.build().encode().toUriString();

        return restTemplate.exchange(url, HttpMethod.GET, null, responseType);
    }

    protected <DtoType> List<DtoType> getEntityList(String url, Class<DtoType> dtoClass) throws Exception {
        return getEntityList(url, null, dtoClass);
    }

    public <DtoType> List<DtoType> getEntityList(String url, Map<String, String> params, Class<DtoType> dtoClass)
        throws Exception {
        HttpEntity<String> entity = new HttpEntity<>(null, createHeaders());
        ResponseEntity<String> response = performRequest(url, params, entity, HttpMethod.GET);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(response.getBody(), listType);
    }

    public ResponseEntity<String> performRequest(String path,
        Map<String, String> params,
        Object body,
        HttpMethod method) throws Exception {
        String url = uriTemplateHandler.getRootUri() + path;

        if (params != null) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            builder.build(false);
            params.forEach((k, v) -> builder.queryParam(k, v));
            url = builder.toUriString();
        }

        HttpEntity<String> entity = new HttpEntity<>(objectMapper.writeValueAsString(body),
            createHeaders());

        return restTemplate.exchange(url, method, entity, String.class);
    }

    public ResponseEntity<String> performRequest(String path,
        Map<String, String> params,
        HttpEntity<String> entity,
        HttpMethod method) {
        String url = uriTemplateHandler.getRootUri() + path;

        if (params != null) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            builder.build(false);
            params.forEach(builder::queryParam);
            url = builder.toUriString();
        }

        if (entity == null) {
            entity = new HttpEntity<>(null, createHeaders());
        }

        return restTemplate.exchange(url, method, entity, String.class);
    }

}
