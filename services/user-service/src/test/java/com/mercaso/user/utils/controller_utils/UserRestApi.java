package com.mercaso.user.utils.controller_utils;

import com.mercaso.user.dto.SearchUserFilter;
import com.mercaso.user.dto.user.AssignRolesToUserRequest;
import com.mercaso.user.dto.user.CreateUserRequest;
import com.mercaso.user.dto.user.UpdateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class UserRestApi {

    @Autowired(required = false)
    private RestTemplate restTemplate;

    public ResponseEntity<UserDto> getUserDetails(UUID userId) {
        return restTemplate.getForEntity(
            "/v1/users/{id}",
            UserDto.class,
            userId
        );
    }

    public ResponseEntity<List<UserDto>> listOrSearchUsers(SearchUserFilter filter) {
        return restTemplate.exchange(
            "/v1/users/list",
            HttpMethod.POST,
            new HttpEntity<>(filter),
            new ParameterizedTypeReference<>() {
            }
        );
    }

    public ResponseEntity<UserDto> assignRolesToUser(UUID userId, List<String> roleIds) {
        return restTemplate.exchange(
            "/v1/users/{id}/roles",
            HttpMethod.POST,
            new HttpEntity<>(new AssignRolesToUserRequest(roleIds)),
            UserDto.class,
            userId
        );
    }

    public ResponseEntity<UserDto> updateUser(UUID userId, UpdateUserRequest request) {
        return restTemplate.exchange(
            "/v1/users/{id}",
            HttpMethod.PATCH,
            new HttpEntity<>(request),
            UserDto.class,
            userId
        );
    }

    public ResponseEntity<UserDto> createUser(CreateUserRequest request) {
        return restTemplate.exchange(
            "/v1/users",
            HttpMethod.POST,
            new HttpEntity<>(request),
            UserDto.class
        );
    }
}