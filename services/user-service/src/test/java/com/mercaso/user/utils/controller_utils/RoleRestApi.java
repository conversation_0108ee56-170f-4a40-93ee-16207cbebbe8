package com.mercaso.user.utils.controller_utils;

import com.mercaso.user.dto.user.RoleDto;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class RoleRestApi {

    @Autowired(required = false)
    private RestTemplate restTemplate;

    public ResponseEntity<List<RoleDto>> listRoles() {
        return restTemplate.exchange(
            "/v1/roles",
            HttpMethod.GET,
            null,
            new ParameterizedTypeReference<>() {
            }
        );
    }
} 