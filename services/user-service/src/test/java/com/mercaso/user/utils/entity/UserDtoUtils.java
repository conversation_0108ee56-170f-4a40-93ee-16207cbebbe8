package com.mercaso.user.utils.entity;

import com.auth0.json.mgmt.users.Identity;
import com.mercaso.user.dto.user.IdentityDto;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.dto.user.UserDto;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserDtoUtils {

    @SneakyThrows
    public static UserDto buildUserDto(UUID userId) {

        return UserDto.builder()
            .id(userId)
            .name("Test User")
            .email("<EMAIL>")
            .blocked(false)
            .roles(buildDefaultRoles())
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .identities(List.of(buildIdentity("auth0|676a7f3916e8d188deceea9a")))
            .auth0UserId("auth0|676a7f3916e8d188deceea9a")
            .build();
    }

    private static List<RoleDto> buildDefaultRoles() {
        return List.of(
            RoleDto.builder()
                .id(UUID.randomUUID().toString())
                .name("admin_user")
                .description("User management role")
                .build(),
            RoleDto.builder()
                .id(UUID.randomUUID().toString())
                .name("admin_data")
                .description("Data service admin role")
                .build()
        );
    }

    private static IdentityDto buildIdentity(String userId) {
        return IdentityDto.builder()
            .userId(userId)
            .provider("auth0")
            .connection("Mobile-connection")
            .build();
    }
} 