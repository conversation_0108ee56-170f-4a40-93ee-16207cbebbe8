package com.mercaso.user;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.auth0.client.mgmt.ClientsEntity;
import com.auth0.client.mgmt.ManagementAPI;
import com.auth0.client.mgmt.RolesEntity;
import com.auth0.client.mgmt.UsersEntity;
import com.auth0.client.mgmt.filter.ClientFilter;
import com.auth0.json.mgmt.client.Client;
import com.auth0.json.mgmt.client.ClientsPage;
import com.auth0.net.Request;
import com.auth0.net.Response;
import com.mercaso.user.config.cache.Auth0TokenCache;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(initializers = AbstractIT.Auth0MockInitializer.class, classes = {AbstractIT.TestConfig.class})
@SpringBootTest(
    classes = Application.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = {
        "spring.cloud.vault.enabled=false",
        "spring.main.allow-bean-definition-overriding=true"
    }
)
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public abstract class AbstractIT {

    @MockBean
    protected Auth0TokenCache auth0TokenCache;

    protected static ManagementAPI userManagementApi;
    protected static ManagementAPI m2mManagementApi;

    static class Auth0MockInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(ConfigurableApplicationContext applicationContext) {
            log.info("Initializing Auth0 mocks in context...");
            registerManagementAPIs(applicationContext.getBeanFactory());
            log.info("Auth0 mocks initialized in context");
        }

        public void registerManagementAPIs(ConfigurableListableBeanFactory beanFactory) {
            userManagementApi = mock(ManagementAPI.class);
            m2mManagementApi = mock(ManagementAPI.class);
            setupUserManagementAPIBehavior(userManagementApi);
            setupM2MManagementAPIBehavior(m2mManagementApi);

            BeanDefinitionRegistry registry = (BeanDefinitionRegistry) beanFactory;

            GenericBeanDefinition userApiDef = new GenericBeanDefinition();
            userApiDef.setBeanClass(ManagementAPI.class);
            userApiDef.setInstanceSupplier(() -> userManagementApi);
            userApiDef.setPrimary(true);
            registry.registerBeanDefinition("userManagementApi", userApiDef);

            GenericBeanDefinition m2mApiDef = new GenericBeanDefinition();
            m2mApiDef.setBeanClass(ManagementAPI.class);
            m2mApiDef.setInstanceSupplier(() -> m2mManagementApi);
            m2mApiDef.setPrimary(true);
            registry.registerBeanDefinition("m2mManagementApi", m2mApiDef);
        }

        public void setupUserManagementAPIBehavior(ManagementAPI api) {
            UsersEntity usersEntity = mock(UsersEntity.class);
            RolesEntity rolesEntity = mock(RolesEntity.class);
            when(api.users()).thenReturn(usersEntity);
            when(api.roles()).thenReturn(rolesEntity);
        }

        public void setupM2MManagementAPIBehavior(ManagementAPI api) {
            try {
                ClientsEntity clientsEntity = mock(ClientsEntity.class);
                Request<ClientsPage> clientsRequest = mock(Request.class);
                Response<ClientsPage> clientsResponse = mock(Response.class);
                ClientsPage clientsPage = mock(ClientsPage.class);

                when(api.clients()).thenReturn(clientsEntity);
                when(clientsEntity.list(any(ClientFilter.class))).thenReturn(clientsRequest);
                when(clientsRequest.execute()).thenReturn(clientsResponse);
                when(clientsResponse.getStatusCode()).thenReturn(200);
                when(clientsResponse.getBody()).thenReturn(clientsPage);

                Client mockClient = mock(Client.class);

                when(mockClient.getClientSecret()).thenReturn("test-client-secret");
                when(mockClient.getName()).thenReturn("Test Client");
                when(mockClient.getDescription()).thenReturn("Test Description");
                when(mockClient.getClientId()).thenReturn("rW7FTLJJYAE4dfVmqGIbnQJ47JXZB5Y0");

                when(clientsPage.getItems()).thenReturn(Collections.singletonList(mockClient));
                when(clientsPage.getTotal()).thenReturn(1);
            } catch (Exception e) {
                log.error("Failed to setup M2M Management API behavior", e);
                throw new RuntimeException("Failed to setup M2M Management API mocks", e);
            }
        }
    }

    @BeforeAll
    static void setUp() {
        log.info("Starting integration tests...");
    }

    @AfterAll
    static void tearDown() {
        log.info("Finished integration tests.");
    }

    @TestConfiguration
    static class TestConfig {

        @Bean
        public RestTemplate restTemplate(TestRestTemplate testRestTemplate) {
            return testRestTemplate.getRestTemplate();
        }

        @Bean
        public RestTemplateBuilder restTemplateBuilder() {
            return new RestTemplateBuilder();
        }
    }
}
