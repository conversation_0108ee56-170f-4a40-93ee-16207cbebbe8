package com.mercaso.user.utils.controller_utils;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.user.dto.AuthorizationResponseDto;
import com.mercaso.user.utils.IntegrationTestRestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class AuthorizeRestApi extends IntegrationTestRestUtil {

  private static final String BASE_PATH = "/v1/oauth";

  public AuthorizeRestApi(Environment environment) {
    super(environment);
  }

  public AuthorizationResponseDto getM2MToken(String clientKey) throws Exception {

    HttpHeaders headers = new HttpHeaders();
    headers.set("X-API-Key", clientKey);

    ResponseEntity<String> responseEntity = performRequest(BASE_PATH + "/token", null,
        null, HttpMethod.GET, headers);

    assertTrue(responseEntity.getStatusCode().is2xxSuccessful(),
        "Response status code is not 2xx: " + responseEntity.getStatusCode());

    return objectMapper.readValue(responseEntity.getBody(), AuthorizationResponseDto.class);
  }
}
