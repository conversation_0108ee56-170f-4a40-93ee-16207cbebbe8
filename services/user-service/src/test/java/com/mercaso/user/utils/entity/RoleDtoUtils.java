package com.mercaso.user.utils.entity;

import com.mercaso.user.dto.user.RoleDto;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RoleDtoUtils {

    public static RoleDto buildRoleDto() {
        return RoleDto.builder()
            .id(UUID.randomUUID().toString())
            .name("admin_user")
            .description("User management role")
            .build();
    }

}
