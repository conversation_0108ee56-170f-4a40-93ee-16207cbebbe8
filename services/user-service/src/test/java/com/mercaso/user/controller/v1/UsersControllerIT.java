package com.mercaso.user.controller.v1;

import static com.mercaso.user.utils.entity.RoleDtoUtils.buildRoleDto;
import static com.mercaso.user.utils.entity.UserDtoUtils.buildUserDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.user.AbstractIT;
import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.dto.SearchUserFilter;
import com.mercaso.user.dto.user.CreateUserRequest;
import com.mercaso.user.dto.user.UpdateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import com.mercaso.user.utils.controller_utils.UserRestApi;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class UsersControllerIT extends AbstractIT {

    @Autowired
    private UserRestApi userRestApi;

    @MockBean
    private Auth0ManagementApiAdaptor auth0ManagementApiAdaptor;

    @Test
    @DisplayName("Should return user details when user exists - Happy Path")
    void getUserDetails_ShouldReturnUserDetails_WhenUserExists() {
        UUID userId = UUID.randomUUID();

        when(auth0ManagementApiAdaptor.getUser(any())).thenReturn(buildUserDto(userId));
        when(auth0ManagementApiAdaptor.listUserRolesWithPrefixIsUserGroup(any())).thenReturn(Collections.singletonList(
            buildRoleDto()));

        ResponseEntity<UserDto> result = userRestApi.getUserDetails(userId);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        UserDto actualUser = result.getBody();
        assertNotNull(actualUser);
    }

    @Test
    @DisplayName("Should return all users with roles - Happy Path")
    void searchUsers_ShouldReturnAllUsersWithRoles() {

        when(auth0ManagementApiAdaptor.listUsers(any())).thenReturn(Collections.singletonList(buildUserDto(UUID.randomUUID())));
        when(auth0ManagementApiAdaptor.listUserRolesWithPrefixIsUserGroup(any())).thenReturn(Collections.singletonList(
            buildRoleDto()));
        // Act
        SearchUserFilter filter = new SearchUserFilter();
        ResponseEntity<List<UserDto>> result = userRestApi.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        List<UserDto> users = result.getBody();
        assertNotNull(users);
        assertEquals(1, users.size());
    }

    @Test
    @DisplayName("Should assign roles to user - Happy Path")
    void assignRolesToUser_ShouldAssignRoles() {

        UUID userId = UUID.randomUUID();
        when(auth0ManagementApiAdaptor.getUser(any())).thenReturn(buildUserDto(userId));
        when(auth0ManagementApiAdaptor.listUserRolesWithPrefixIsUserGroup(any())).thenReturn(Collections.singletonList(
            buildRoleDto()));

        List<String> roleIds = List.of("rol_Db7YgptWv5j1cfBQ", "rol_Db7YminWv5j1cfBp");
        ResponseEntity<UserDto> result = userRestApi.assignRolesToUser(userId, roleIds);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        UserDto actualUser = result.getBody();
        assertNotNull(actualUser);
    }

    @Test
    @DisplayName("Should update user details - Happy Path")
    void updateUser_ShouldUpdateUserDetails() {
        UUID userId = UUID.randomUUID();

        when(auth0ManagementApiAdaptor.getUser(any())).thenReturn(buildUserDto(userId));
        when(auth0ManagementApiAdaptor.updateUser(any(), any())).thenReturn(buildUserDto(userId));
        when(auth0ManagementApiAdaptor.listUserRolesWithPrefixIsUserGroup(any())).thenReturn(Collections.singletonList(
            buildRoleDto()));

        UpdateUserRequest request = new UpdateUserRequest();
        request.setName("Updated User");

        ResponseEntity<UserDto> result = userRestApi.updateUser(userId, request);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        UserDto actualUser = result.getBody();
        assertNotNull(actualUser);
    }

    @Test
    @DisplayName("Should create user without roles - Happy Path")
    void createUser_ShouldCreateUserWithoutRoles() {
        // Arrange
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("Password123!");
        request.setName("Test User");

        when(auth0ManagementApiAdaptor.createUser(any(), any(), any(), any())).thenReturn(buildUserDto(UUID.randomUUID()));
        when(auth0ManagementApiAdaptor.listUserRolesWithPrefixIsUserGroup(any())).thenReturn(Collections.singletonList(
            buildRoleDto()));

        // Act
        ResponseEntity<UserDto> result = userRestApi.createUser(request);

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        UserDto createdUser = result.getBody();
        assertNotNull(createdUser);
    }
}
