package com.mercaso.user.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.config.auth0.Auth0M2MProperties;
import com.mercaso.user.config.cache.Auth0TokenCache;
import com.mercaso.user.dto.Auth0Client;
import com.mercaso.user.exception.AuthorizeException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

class Auth0ClientServiceImplTest {

    private final Auth0M2MProperties auth0M2MProperties = mock(Auth0M2MProperties.class);
    private final Auth0TokenCache tokenCache = mock(Auth0TokenCache.class);
    private final Auth0ClientServiceImpl auth0ClientService = new Auth0ClientServiceImpl(auth0M2MProperties, tokenCache);

    @Test
    void testGetClientByClientKey_ValidKey() {
        // Arrange
        String clientKey = "valid-client-key";
        String clientId = "client-id";
        Map<String, String> clients = new HashMap<>();
        clients.put(clientId, clientKey);

        Auth0Client expectedClient = createTestClient(clientId);

        when(auth0M2MProperties.getClients()).thenReturn(clients);
        when(tokenCache.getClientWithValidToken(clientId)).thenReturn(expectedClient);

        // Act
        Auth0Client result = auth0ClientService.getClientByClientKey(clientKey);

        // Assert
        assertNotNull(result);
        assertEquals(clientId, result.getClientId());
        verify(tokenCache).getClientWithValidToken(clientId);
    }

    @Test
    void testGetClientByClientKey_InvalidKey() {
        // Arrange
        String invalidKey = "invalid-key";
        Map<String, String> clients = new HashMap<>();
        clients.put("client-id", "valid-client-key");

        when(auth0M2MProperties.getClients()).thenReturn(clients);

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.getClientByClientKey(invalidKey));
        assertEquals("Client not found", exception.getMessage());
    }

    @Test
    void testGetClientByClientKey_CacheError() {
        // Arrange
        String clientKey = "valid-client-key";
        String clientId = "client-id";
        Map<String, String> clients = new HashMap<>();
        clients.put(clientId, clientKey);

        when(auth0M2MProperties.getClients()).thenReturn(clients);
        when(tokenCache.getClientWithValidToken(clientId))
            .thenThrow(new AuthorizeException("Cache error"));

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.getClientByClientKey(clientKey));
        assertEquals("Cache error", exception.getMessage());
        verify(tokenCache).getClientWithValidToken(clientId);
    }

    @Test
    void testGetClientByClientKey_EmptyClients() {
        // Arrange
        String clientKey = "valid-client-key";
        Map<String, String> clients = new HashMap<>();

        when(auth0M2MProperties.getClients()).thenReturn(clients);

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.getClientByClientKey(clientKey));
        assertEquals("Client not found", exception.getMessage());
    }

    @Test
    void testGetClientByClientKey_NullClients() {
        // Arrange
        String clientKey = "valid-client-key";
        when(auth0M2MProperties.getClients()).thenReturn(Map.of());

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.getClientByClientKey(clientKey));
        assertEquals("Client not found", exception.getMessage());
    }

    @Test
    void when_refreshM2MToken_with_validClientKey_then_refreshClient() {
        // Arrange
        String clientKey = "valid-client-key";
        String clientId = "client-id";
        Map<String, String> clients = new HashMap<>();
        clients.put(clientId, clientKey);

        when(auth0M2MProperties.getClients()).thenReturn(clients);

        // Act
        auth0ClientService.refreshM2MToken(clientKey);

        // Assert
        verify(tokenCache).refreshClient(clientId);
    }

    @Test
    void when_refreshM2MToken_with_invalidClientKey_then_throwAuthorizationException() {
        // Arrange
        String invalidKey = "invalid-key";
        Map<String, String> clients = new HashMap<>();
        clients.put("client-id", "valid-client-key");

        when(auth0M2MProperties.getClients()).thenReturn(clients);

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.refreshM2MToken(invalidKey));
        assertEquals("Client not found", exception.getMessage());
        verify(tokenCache, never()).refreshClient("client-id");
    }

    @Test
    void when_refreshM2MToken_with_cacheError_then_propagateException() {
        // Arrange
        String clientKey = "valid-client-key";
        String clientId = "client-id";
        Map<String, String> clients = new HashMap<>();
        clients.put(clientId, clientKey);

        when(auth0M2MProperties.getClients()).thenReturn(clients);
        doThrow(new AuthorizeException("Failed to refresh token")).when(tokenCache).refreshClient(clientId);

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.refreshM2MToken(clientKey));
        assertEquals("Failed to refresh token", exception.getMessage());
        verify(tokenCache).refreshClient(clientId);
    }

    @Test
    void when_refreshM2MToken_with_emptyClients_then_throwAuthorizationException() {
        // Arrange
        String clientKey = "valid-client-key";
        Map<String, String> clients = new HashMap<>();

        when(auth0M2MProperties.getClients()).thenReturn(clients);

        // Act & Assert
        AuthorizeException exception = assertThrows(AuthorizeException.class,
            () -> auth0ClientService.refreshM2MToken(clientKey));
        assertEquals("Client not found", exception.getMessage());
        verify(tokenCache, never()).refreshClient("client-id");
    }

    private Auth0Client createTestClient(String clientId) {
        return Auth0Client.builder()
            .clientId(clientId)
            .clientSecret("test-secret")
            .domain("test.auth0.com")
            .name("Test Client")
            .description("Test Client Description")
            .audience("test-audience")
            .token("test-token")
            .expiresAt(new Date())
            .build();
    }
}
