package com.mercaso.user.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.adaptor.Auth0ManagementApiAdaptor;
import com.mercaso.user.dto.SearchUserFilter;
import com.mercaso.user.dto.user.CreateUserRequest;
import com.mercaso.user.dto.user.UserDto;
import com.mercaso.user.service.UserService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class UserServiceImplTest {

    private final Auth0ManagementApiAdaptor auth0ManagementApiAdaptor = mock(Auth0ManagementApiAdaptor.class);

    private final UserService userService = new UserServiceImpl(auth0ManagementApiAdaptor);

    @Test
    public void testGetUser() {
        // Arrange
        UUID userId = UUID.randomUUID();

        when(auth0ManagementApiAdaptor.getUser(userId)).thenReturn(new UserDto());

        // Act
        UserDto result = userService.getUser(userId);
        // Assert
        assert result != null;
    }

    @Test
    public void testListOrSearchUsers_WithNullFilter() {
        // Arrange
        when(auth0ManagementApiAdaptor.listUsers(null)).thenReturn(List.of(new UserDto()));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(null);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).listUsers(null);
    }

    @Test
    public void testListOrSearchUsers_WithEmptyFilter() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        when(auth0ManagementApiAdaptor.listUsers(null)).thenReturn(List.of(new UserDto()));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).listUsers(null);
    }

    @Test
    public void testListOrSearchUsers_WithNameFilter() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        filter.setName("John");
        String expectedQuery = "name:John";
        when(auth0ManagementApiAdaptor.listUsers(expectedQuery)).thenReturn(List.of(new UserDto()));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).listUsers(expectedQuery);
    }

    @Test
    public void testListOrSearchUsers_WithEmailFilter() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        filter.setEmail("<EMAIL>");
        String expectedQuery = "email:<EMAIL>";
        when(auth0ManagementApiAdaptor.listUsers(expectedQuery)).thenReturn(List.of(new UserDto()));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).listUsers(expectedQuery);
    }

    @Test
    public void testListOrSearchUsers_WithRoleFilter() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        filter.setRole("admin");
        String roleId = "role123";
        List<String> auth0UserIds = List.of("auth0|123");
        String expectedQuery = "user_id:(auth0|123)";
        UserDto expectedUser = new UserDto();

        when(auth0ManagementApiAdaptor.getAuth0UserIdsByRole(filter.getRole())).thenReturn(auth0UserIds);
        when(auth0ManagementApiAdaptor.listUsers(expectedQuery)).thenReturn(List.of(expectedUser));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).getAuth0UserIdsByRole(filter.getRole());
        verify(auth0ManagementApiAdaptor).listUsers(expectedQuery);
    }

    @Test
    public void testListOrSearchUsers_WithAllFilters() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        filter.setName("John");
        filter.setEmail("<EMAIL>");
        filter.setRole("admin");

        List<String> auth0UserIds = List.of("auth0|123");
        String expectedQuery = "user_id:(auth0|123) AND name:John AND email:<EMAIL>";

        UserDto expectedUser = new UserDto();
        expectedUser.setId(UUID.randomUUID());
        expectedUser.setEmail("<EMAIL>");
        expectedUser.setName("John");

        when(auth0ManagementApiAdaptor.getAuth0UserIdsByRole(filter.getRole()))
            .thenReturn(auth0UserIds);
        when(auth0ManagementApiAdaptor.listUsers(expectedQuery))
            .thenReturn(List.of(expectedUser));

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auth0ManagementApiAdaptor).getAuth0UserIdsByRole(filter.getRole());
        verify(auth0ManagementApiAdaptor).listUsers(expectedQuery);
    }

    @Test
    public void testListOrSearchUsers_WithRoleFilter_NoUsers() {
        // Arrange
        SearchUserFilter filter = new SearchUserFilter();
        filter.setRole("admin");

        when(auth0ManagementApiAdaptor.getAuth0UserIdsByRole(filter.getRole())).thenReturn(List.of());

        // Act
        List<UserDto> result = userService.listOrSearchUsers(filter);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(auth0ManagementApiAdaptor).getAuth0UserIdsByRole(filter.getRole());
    }

    @Test
    public void testCreateUser_Success() {
        // Arrange
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("Password123!");
        request.setName("Test User");
        request.setRoles(List.of("role1", "role2"));

        UserDto expectedUser = new UserDto();
        expectedUser.setId(UUID.randomUUID());
        expectedUser.setAuth0UserId("auth0|123");
        expectedUser.setEmail(request.getEmail());
        expectedUser.setName(request.getName());

        when(auth0ManagementApiAdaptor.createUser(
            eq(request.getEmail()),
            eq(request.getPassword()),
            eq(request.getName()),
            eq("Mobile-connection")
        )).thenReturn(expectedUser);

        when(auth0ManagementApiAdaptor.getUser(expectedUser.getId())).thenReturn(expectedUser);

        // Act
        UserDto result = userService.createUser(request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());
        assertEquals(expectedUser.getEmail(), result.getEmail());
        assertEquals(expectedUser.getName(), result.getName());
        verify(auth0ManagementApiAdaptor).assignRolesToUser(expectedUser.getAuth0UserId(), request.getRoles());
    }

    @Test
    public void testCreateUser_WithoutRoles() {
        // Arrange
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("Password123!");
        request.setName("Test User");

        UserDto expectedUser = new UserDto();
        expectedUser.setId(UUID.randomUUID());
        expectedUser.setEmail(request.getEmail());
        expectedUser.setName(request.getName());

        when(auth0ManagementApiAdaptor.createUser(
            anyString(),
            anyString(),
            anyString(),
            anyString()
        )).thenReturn(expectedUser);

        // Act
        UserDto result = userService.createUser(request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedUser.getId(), result.getId());
        assertEquals(expectedUser.getEmail(), result.getEmail());
        assertEquals(expectedUser.getName(), result.getName());
        verify(auth0ManagementApiAdaptor, never()).assignRolesToUser(anyString(), anyList());
    }
}
