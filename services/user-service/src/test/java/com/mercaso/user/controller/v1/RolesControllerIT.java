package com.mercaso.user.controller.v1;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.auth0.client.mgmt.ManagementAPI;
import com.auth0.client.mgmt.RolesEntity;
import com.auth0.exception.Auth0Exception;
import com.auth0.json.mgmt.roles.Role;
import com.auth0.json.mgmt.roles.RolesPage;
import com.auth0.net.Request;
import com.auth0.net.Response;
import com.mercaso.user.AbstractIT;
import com.mercaso.user.config.auth0.RoleMappingProperties;
import com.mercaso.user.dto.user.RoleDto;
import com.mercaso.user.utils.controller_utils.RoleRestApi;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class RolesControllerIT extends AbstractIT {

    @Autowired
    private RoleRestApi roleRestApi;

    @MockBean(name = "userManagementApi")
    private ManagementAPI userManagementApi;

    @MockBean
    private RolesEntity rolesEntity;

    @MockBean
    private RoleMappingProperties roleMappingProperties;


    @Test
    @DisplayName("Should return only user group roles - Happy Path")
    void listRoles_ShouldReturnOnlyUserGroupRoles() throws Auth0Exception {
        // Setup test data
        Role userGroupRole = createMockRole("user_group_admin_id", "user_group_admin", "Admin user group");
        Role normalRole = createMockRole("normal_role_id", "normal_role", "Normal role");

        RolesPage rolesPage = new RolesPage(0, 2, 2, 2, Arrays.asList(userGroupRole, normalRole));

        // Mock role mapping properties
        Map<String, List<String>> userGroupToRole = Map.of(
            "user_group_admin_id", List.of("admin"),
            "user_group_user_id", List.of("user")
        );
        when(roleMappingProperties.getUserGroupToRole()).thenReturn(userGroupToRole);

        // Mock Auth0 API chain
        mockAuth0ApiChain(rolesPage);

        // Execute test
        ResponseEntity<List<RoleDto>> result = roleRestApi.listRoles();

        // Assert results
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());

        List<RoleDto> roles = result.getBody();
        assertNotNull(roles);
        assertEquals(1, roles.size());

        RoleDto returnedRole = roles.get(0);
        assertEquals("user_group_admin_id", returnedRole.getId());
        assertEquals("user_group_admin", returnedRole.getName());
        assertTrue(returnedRole.getName().startsWith("user_group_"));
    }

    /**
     * Creates a mock Role object with the specified properties.
     */
    private Role createMockRole(String id, String name, String description) {
        Role role = mock(Role.class);
        when(role.getId()).thenReturn(id);
        when(role.getName()).thenReturn(name);
        when(role.getDescription()).thenReturn(description);
        return role;
    }

    /**
     * Mocks the Auth0 API call chain for role listing.
     */
    private void mockAuth0ApiChain(RolesPage rolesPage) throws Auth0Exception {
        when(userManagementApi.roles()).thenReturn(rolesEntity);

        Request<RolesPage> request = mock(Request.class);
        when(rolesEntity.list(any())).thenReturn(request);

        Response<RolesPage> response = mock(Response.class);
        when(request.execute()).thenReturn(response);
        when(response.getStatusCode()).thenReturn(HttpStatus.OK.value());
        when(response.getBody()).thenReturn(rolesPage);
    }
} 