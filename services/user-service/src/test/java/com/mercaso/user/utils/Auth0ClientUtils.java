package com.mercaso.user.utils;

import com.mercaso.user.dto.Auth0Client;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Auth0ClientUtils {

    public static Auth0Client buildAuth0Client(String clientId) {
        return Auth0Client.builder()
            .clientId(clientId)
            .clientSecret("test-secret")
            .domain("test.auth0.com")
            .name("Test Client")
            .description("Test Description")
            .audience("test-audience")
            .token("test-token")
            .build();
    }
}
