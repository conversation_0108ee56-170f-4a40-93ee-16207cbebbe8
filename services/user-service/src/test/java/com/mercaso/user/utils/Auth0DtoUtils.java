package com.mercaso.user.utils;

import com.auth0.json.mgmt.roles.Role;
import com.auth0.json.mgmt.users.User;
import java.util.Collections;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Maps;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Auth0DtoUtils {

    public static User buildUser(UUID userId, String name) {
        User user = new User();
        user.setId("auth0|" + userId);
        user.setEmail(name + "-<EMAIL>");
        user.setName(name);
        user.setFamilyName("Zheng");
        user.setGivenName("Ryan");
        user.setAppMetadata(Collections.singletonMap("userInfo", Maps.newHashMap("userId", userId)));
        return user;
    }

    public static Role buildRole(String name) {
        Role role = new Role();
        role.setName(name);
        role.setDescription("Role for " + name);
        return role;
    }

}
