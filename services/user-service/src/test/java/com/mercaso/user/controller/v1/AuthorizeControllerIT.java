package com.mercaso.user.controller.v1;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.user.AbstractIT;
import com.mercaso.user.dto.Auth0Client;
import com.mercaso.user.dto.AuthorizationResponseDto;
import com.mercaso.user.utils.controller_utils.AuthorizeRestApi;
import java.time.Instant;
import java.util.Date;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class AuthorizeControllerIT extends AbstractIT {

  @Autowired
  private AuthorizeRestApi authorizeRestApi;

  @Test
  @DisplayName("Should return M2M token when client key is valid - Happy Path")
  void getM2MToken_ShouldReturnToken_WhenClientKeyIsValid() throws Exception {
    // Arrange
    String clientKey = "it-key-06e29b2e-3e2b-439a-9320-7264d22d2452";
    String expectedToken = "test-token";

    when(auth0TokenCache.getClientWithValidToken(any())).thenReturn(
        Auth0Client.builder().token(expectedToken)
            .expiresAt(Date.from(Instant.now())).build());

    // Act
    AuthorizationResponseDto result = authorizeRestApi.getM2MToken(clientKey);

    // Assert
    assertNotNull(result);
    assertEquals(expectedToken, result.getAccessToken());
  }
}