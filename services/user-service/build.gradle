plugins {
    id 'org.sonarqube'
    id 'jacoco'
}

def excludedPaths = [
        "**/command/**",
        "**/mapper/**",
        "**/dto/**",
        "**/enums/**",
        "**/adaptor/**",
        "**/config/**",
        "**/exception/**",
        "**/entity/**"
]

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths",
                "build/reports/jacoco/test/jacocoTestReport.xml"
        property "sonar.branch.name",
                'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.coverage.exclusions", excludedPaths
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
        html.required = true
    }
    executionData = files(
            layout.buildDirectory.file("jacoco/test.exec"),
            layout.buildDirectory.file("jacoco/integrationTest.exec")
    )
}

test {
    finalizedBy jacocoTestReport
}

dependencies {
    implementation 'org.aspectj:aspectjrt:1.9.7'
    implementation 'org.apache.skywalking:apm-toolkit-meter:9.2.0'
    implementation 'com.auth0:auth0:2.17.0'
    implementation 'com.google.guava:guava:32.0.1-jre'
}

springBoot {
    mainClass = 'com.mercaso.user.Application'
}

integrationTest {
    systemProperty 'spring.profiles.active', 'integration'
}
