server:
  port: 8080
  tomcat:
    connection-timeout: 120s

spring:
  async:
    core_pool_size: 5
    max_pool_size: 10
    queue_capacity: 200
    alive_time: 60
  profiles:
    active: local
  application:
    name: item-management-service
  datasource:
    url: jdbc:postgresql://${host.db_server}/item_management_service
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-init-sql: "SET ROLE item_management_user; SET statement_timeout TO '300000'"
      connection-timeout: 15000
      minimum-idle: 1
      maximum-pool-size: 50
      registerMbeans: true
      leak-detection-threshold: 200000
      allow-pool-suspension: true
  messages:
    basename: i18n/messages
    encoding: UTF-8
    always-use-message-format: true
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate.dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    locations: classpath:/db/migration
    baseline-on-migrate: true
    init-sqls: SET ROLE item_management_user
  cloud:
    vault:
      enabled: true
      scheme: http
      port: 8200
      host: vault.vault.svc.cluster.local
      authentication: KUBERNETES
      kubernetes:
        role: item-management-service
        kubernetes-path: kubernetes
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
      # Application will fail if it cannot connect to vault, remember to disable vault for envs that don't need it
      fail-fast: true

      # Need to disable generic engine so that spring cloud knows to only pull secrets from KV engine
      generic:
        enabled: false
      kv:
        enabled: true
        backend: secret
        profile-separator: '/'
        application-name: item-management-service
      database:
        enabled: true
        role: item-management-service
        backend: database
        username-property: spring.datasource.username
        password-property: spring.datasource.password
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  ai:
    openai:
      api-key: ${openai.api_key}
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.4
otel:
  traces:
    exporter: otlp
    sampler: always_on
  metrics:
    exporter: otlp
  exporter:
    otlp:
      endpoint: ${OTEL_EXPORTER_OTLP_ENDPOINT}
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState
    shutdown:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: true
    vault:
      enabled: false
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  server:
    port: 8081
mercaso:
  data-url: http://data-service.default.svc.cluster.local
  document:
    operations:
      enabled: true
      storage:
        bucket-name: ${aws-bucket-name}
  featureflags:
    sdk: ${mercaso.featureflags.sdk}
    enabled: true
springdoc:
  swagger-ui:
    enabled: false
finale:
  create_vendor_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/partygroup/
  update_vendor_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/partygroup/
  graphql_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/graphql
  query_vendors_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/partygroup/
  product_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/product/
  get_purchase_order_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/order/
  completed_purchase_orders_report_url: https://app.finaleinventory.com/${finale.accountPathComponent}/doc/report/pivotTable/${finale.reportsId}/Reports.json
  accountPathComponent: mercaso
  reportsId: *************
  token: ${finale.token}


shopify:
  host: https://shop-mercaso.myshopify.com
  access_token: ${shopify.access_token}
  query_product_url: /admin/api/2024-07/graphql.json
  create_product_url: /admin/api/2024-07/products.json
  modify_product_url: /admin/api/2024-07/products/{productId}.json
  delete_product_url: /admin/api/2024-07/products/{productId}.json
  modify_product_image_url: /admin/api/2024-07/products/{productId}/images/{imageId}.json
  remove_product_image_url: /admin/api/2024-07/products/{productId}/images/{imageId}.json
  create_product_image_attach_to_product_variants_url: /admin/api/2024-07/products/{productId}/images.json
  create_product_meta_field_url: /admin/api/2024-07/products/{productId}/metafields.json
  delete_product_meta_field_url: /admin/api/2025-04/graphql.json
  query_product_meta_field_url: /admin/api/2024-07/products/{productId}/metafields.json
  modify_product_meta_field_url: /admin/api/2024-07/products/{productId}/metafields/{metafieldsId}.json
  modify_product_inventory_item_url: /admin/api/2024-07/inventory_items/{inventoryItemId}.json
  set_product_channel_url: /admin/api/2024-10/graphql.json

external:
  vernon:
    userno: ${external.vernon.userno}
    passno: ${external.vernon.passno}
    from_email: <EMAIL>
  costco:
    item_list_sheet_id: 1t_sLnkrS6B50ji2Tf8D7OMxiQL7HfesQurJDaPXxyYc
  exotic_blvd:
    from_email: <EMAIL>
  seven_star:
    from_email: <EMAIL>




ims:
  document_url: https://svcs.us-west-2.{env}.aws.mercaso.store/item-management/v1/document/public-paths/
  saas_url: https://saas.mercaso.store
  alert:
    alert_maximum_price: 500
    alert_minimum_price: 1
    alert_maximum_margin: 0.5
    alert_minimum_margin: 0



googleDriver:
  shared_drive_id: ${googleDrive.shared_drive_id}
  credentials: ${googleDrive.credentials}
gmail:
  app_password: ${gmail.app_password}
  user_email: ${gmail.user_email}

resilience4j.ratelimiter:
  instances:
    syncShopify:
      limitForPeriod: 10
      limitRefreshPeriod: 1s
      timeoutDuration: 60s
    invokeShopifyApi:
      limitForPeriod: 30
      limitRefreshPeriod: 1s
      timeoutDuration: 60s
    # Single entity GET requests - 120/minute
    finaleSingleEntityGet:
      limitForPeriod: 110
      limitRefreshPeriod: 60s
      timeoutDuration: 30s

    # POST/PUT update operations - 120/minute
    finaleUpdateOperations:
      limitForPeriod: 110
      limitRefreshPeriod: 60s
      timeoutDuration: 30s

    # Collection/Report requests - 300/hour
    finaleCollectionRequests:
      limitForPeriod: 280
      limitRefreshPeriod: 3600s
      timeoutDuration: 30s

shopify_sync:
  core_pool_size: 20
  max_pool_size: 30
  queue_capacity: 800
  alive_time: 60

downey:
  search_url: https://downeywholesaleinc.com/?s=

dify:
  base_url: https://api.dify.ai/v1
  api_key: ${dify.api_key}
  knowledge_base_api_key: ${dify.knowledge_base_api_key}
  dataset_id: 2025995e-81b7-46e8-a6b9-bf484f371e7e
  document_id: 22545992-6864-4a4b-8d0e-c4b7908e92ff