spring:
  config:
    import: vault://
    activate:
      on-profile: prod
security:
  public-paths:
    - /v1/document/public-paths/**
    - /v1/document/**
    - /error

resilience4j.ratelimiter:
  instances:
    syncShopify:
      limitForPeriod: 10
      limitRefreshPeriod: 1s
      timeoutDuration: 30s
ims:
  alert:
    alert_maximum_price: 500
    alert_minimum_price: 1
    alert_maximum_margin: 0.5
    alert_minimum_margin: 0
    alert_slack_hook: https://hooks.slack.com/triggers/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


dify:
  dataset_id: 31c080e7-8406-4052-a83f-c08f709739a5
  document_id: 832c617c-506a-43af-baf1-4bbc4e9159a6