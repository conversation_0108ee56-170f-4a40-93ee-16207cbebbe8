package com.mercaso.ims.application.converter;

import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.domain.address.enums.AddressPurpose;
import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.domain.email.enums.EmailType;
import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.domain.phone.enums.PhoneType;
import com.mercaso.ims.domain.supplieradditionalinfo.SupplierAdditionalInfo;
import com.mercaso.ims.domain.supplieradditionalinfo.enums.FreightType;
import com.mercaso.ims.domain.supplieradditionalinfo.enums.OrderMethod;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Converter to transform Finale supplier data to Vendor domain objects
 */
@Component
@Slf4j
public class FinaleToVendorConverter {

    // Finale settlement terms mapping to display names
    private static final Map<String, String> SETTLEMENT_TERMS_MAPPING = Map.of(
        "NET_15_3_15", "Terms",
        "COD", "Check", 
        "NET_10_2_M", "Cash / Check",
        "CREDIT_CARD", "Credit card",
        "ACH", "ACH",
        "NET_15", "Net 14 days",
        "NET_10", "Net 10 days",
        "PREPAID", "Prepaid",
        "##user_00025", "Consignment",
        "PAYPAL", "Bill.com"
    );

    // ContactMechTypeId mappings
    private static final String POSTAL_ADDRESS = "POSTAL_ADDRESS";
    private static final String EMAIL_ADDRESS = "EMAIL_ADDRESS";
    private static final String TELECOM_NUMBER = "TELECOM_NUMBER";

    /**
     * Convert FinaleVendorInfoDto to Vendor domain object
     * 
     * @param finaleVendor The Finale vendor information
     * @return Converted Vendor domain object
     */
    public Vendor convertToVendor(FinaleVendorInfoDto finaleVendor) {
        if (finaleVendor == null) {
            return null;
        }

        log.debug("Converting Finale vendor with partyId: {}", finaleVendor.getPartyId());

        // Extract contact information
        List<Address> addresses = extractAddresses(finaleVendor);
        List<Email> emails = extractEmails(finaleVendor);
        List<PhoneNumber> phoneNumbers = extractPhoneNumbers(finaleVendor);

        // Extract supplier additional info
        SupplierAdditionalInfo supplierAdditionalInfo = extractSupplierAdditionalInfo(finaleVendor);

        // Build vendor
        Vendor vendor = Vendor.builder()
            .id(UUID.randomUUID())
            .vendorName(extractVendorName(finaleVendor))
            .vendorContactName(finaleVendor.getContactName())
            .vendorStatus(mapVendorStatus(finaleVendor.getStatusId()))
            .finaleId(finaleVendor.getPartyId())
            .defaultTerms(mapSettlementTerms(finaleVendor.getSettlementTermId()))
            .defaultLeadDays(finaleVendor.getLeadTime())
            .notes(finaleVendor.getDescription())
            .addresses(addresses)
            .emails(emails)
            .phoneNumbers(phoneNumbers)
            .supplierAdditionalInfo(supplierAdditionalInfo)
            .build();

        log.debug("Successfully converted vendor: {}", vendor.getVendorName());
        return vendor;
    }

    /**
     * Extract vendor name from Finale data
     */
    private String extractVendorName(FinaleVendorInfoDto finaleVendor) {
        // Try to get from groupName first, fallback to partyId
        if (StringUtils.isNotBlank(finaleVendor.getGroupName())) {
            return finaleVendor.getGroupName();
        }
        return "Vendor_" + finaleVendor.getPartyId();
    }

    /**
     * Map Finale status to VendorStatus enum
     */
    private VendorStatus mapVendorStatus(String statusId) {
        if ("PARTY_ENABLED".equals(statusId)) {
            return VendorStatus.ACTIVE;
        }
        return VendorStatus.INACTIVE;
    }

    /**
     * Map Finale settlement terms to display names
     */
    private String mapSettlementTerms(String settlementTermId) {
        if (StringUtils.isBlank(settlementTermId) || "##null".equals(settlementTermId)) {
            return null;
        }
        return SETTLEMENT_TERMS_MAPPING.getOrDefault(settlementTermId, settlementTermId);
    }

    /**
     * Extract addresses from contact mechanisms
     */
    private List<Address> extractAddresses(FinaleVendorInfoDto finaleVendor) {
        if (finaleVendor.getContactMechList() == null) {
            return new ArrayList<>();
        }

        return finaleVendor.getContactMechList().stream()
            .filter(contact -> POSTAL_ADDRESS.equals(contact.getContactMechTypeId()))
            .filter(contact -> StringUtils.isNotBlank(contact.getAddress1()))
            .map(this::convertToAddress)
            .collect(Collectors.toList());
    }

    /**
     * Convert ContactMechDto to Address
     */
    private Address convertToAddress(FinaleVendorInfoDto.ContactMechDto contactMech) {
        return Address.builder()
            .id(UUID.randomUUID())
            .entityType("VENDOR")
            .streetAddress(contactMech.getAddress1())
            .city(contactMech.getCity())
            .state(contactMech.getStateProvinceGeoId())
            .postalCode(contactMech.getPostalCode())
            .country(contactMech.getCountryGeoId())
            .purpose(AddressPurpose.BUSINESS)
            .build();
    }

    /**
     * Extract emails from contact mechanisms
     */
    private List<Email> extractEmails(FinaleVendorInfoDto finaleVendor) {
        if (finaleVendor.getContactMechList() == null) {
            return new ArrayList<>();
        }

        return finaleVendor.getContactMechList().stream()
            .filter(contact -> EMAIL_ADDRESS.equals(contact.getContactMechTypeId()))
            .filter(contact -> StringUtils.isNotBlank(contact.getInfoString()))
            .map(this::convertToEmail)
            .collect(Collectors.toList());
    }

    /**
     * Convert ContactMechDto to Email
     */
    private Email convertToEmail(FinaleVendorInfoDto.ContactMechDto contactMech) {
        return Email.builder()
            .id(UUID.randomUUID())
            .entityType("VENDOR")
            .emailType(EmailType.BUSINESS)
            .email(contactMech.getInfoString())
            .build();
    }

    /**
     * Extract phone numbers from contact mechanisms
     */
    private List<PhoneNumber> extractPhoneNumbers(FinaleVendorInfoDto finaleVendor) {
        if (finaleVendor.getContactMechList() == null) {
            return new ArrayList<>();
        }

        return finaleVendor.getContactMechList().stream()
            .filter(contact -> TELECOM_NUMBER.equals(contact.getContactMechTypeId()))
            .filter(contact -> StringUtils.isNotBlank(contact.getInfoString()))
            .map(this::convertToPhoneNumber)
            .collect(Collectors.toList());
    }

    /**
     * Convert ContactMechDto to PhoneNumber
     */
    private PhoneNumber convertToPhoneNumber(FinaleVendorInfoDto.ContactMechDto contactMech) {
        return PhoneNumber.builder()
            .id(UUID.randomUUID())
            .entityType("VENDOR")
            .phoneType(PhoneType.WORK)
            .phoneNumber(contactMech.getInfoString())
            .extension(contactMech.getExtension())
            .build();
    }

    /**
     * Extract supplier additional info from userFieldDataList
     */
    private SupplierAdditionalInfo extractSupplierAdditionalInfo(FinaleVendorInfoDto finaleVendor) {
        if (finaleVendor.getUserFieldDataList() == null || finaleVendor.getUserFieldDataList().isEmpty()) {
            return null;
        }

        // Convert userFieldDataList to a map for easier lookup
        Map<String, String> userFieldMap = finaleVendor.getUserFieldDataList().stream()
            .filter(field -> StringUtils.isNotBlank(field.getAttrName()))
            .collect(Collectors.toMap(
                FinaleVendorInfoDto.UserFieldDataDto::getAttrName,
                field -> StringUtils.defaultString(field.getAttrValue()),
                (existing, replacement) -> existing // Keep first value in case of duplicates
            ));

        if (userFieldMap.isEmpty()) {
            return null;
        }

        return SupplierAdditionalInfo.builder()
            .id(UUID.randomUUID())
            .destinationDc(userFieldMap.get("user_10009"))
            .purchasingGroup(userFieldMap.get("user_10000"))
            .accountNum(userFieldMap.get("user_10001"))
            .orderMethod(parseOrderMethod(userFieldMap.get("user_10002")))
            .freight(parseFreightType(userFieldMap.get("user_10003")))
            .deliveryMon(parseBoolean(userFieldMap.get("user_10004")))
            .deliveryTue(parseBoolean(userFieldMap.get("user_10005")))
            .deliveryWed(parseBoolean(userFieldMap.get("user_10006")))
            .deliveryThu(parseBoolean(userFieldMap.get("user_10007")))
            .deliveryFri(parseBoolean(userFieldMap.get("user_10008")))
            .build();
    }

    /**
     * Parse order method from string value
     */
    private OrderMethod parseOrderMethod(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return OrderMethod.fromDisplayName(value);
        } catch (IllegalArgumentException e) {
            log.warn("Unknown order method: {}", value);
            return null;
        }
    }

    /**
     * Parse freight type from string value
     */
    private FreightType parseFreightType(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return FreightType.fromDisplayName(value);
        } catch (IllegalArgumentException e) {
            log.warn("Unknown freight type: {}", value);
            return null;
        }
    }

    /**
     * Parse boolean value from string
     */
    private Boolean parseBoolean(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return Boolean.parseBoolean(value) || "1".equals(value) || "yes".equalsIgnoreCase(value) || "true".equalsIgnoreCase(value);
    }
}
