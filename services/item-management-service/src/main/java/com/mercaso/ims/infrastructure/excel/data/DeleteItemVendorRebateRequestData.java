package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeleteItemVendorRebateRequestData extends ItemAdjustmentRequestData {

    @ExcelProperty("SKU")
    private String sku;
    @ExcelProperty("Supplier")
    private String vendor;

    public String getVendor() {
        return vendor != null ? vendor.trim() : vendor;
    }
}