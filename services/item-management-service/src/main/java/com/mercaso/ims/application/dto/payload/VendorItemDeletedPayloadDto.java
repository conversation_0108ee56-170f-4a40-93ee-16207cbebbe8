package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class VendorItemDeletedPayloadDto extends BusinessEventPayloadDto<VendorItemDto> {

    private UUID vendorItemId;


    @Builder
    public VendorItemDeletedPayloadDto(VendorItemDto data, UUID vendorItemId) {
        super(data);
        this.vendorItemId = vendorItemId;
    }
}