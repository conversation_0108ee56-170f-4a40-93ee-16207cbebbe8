package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.service.VendorApplicationService;
import com.mercaso.ims.infrastructure.schedule.VendorShutdownWindowScheduler;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/vendor", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class VendorRestApi {

    private final VendorApplicationService vendorApplicationService;
    private final VendorShutdownWindowScheduler vendorShutdownWindowScheduler;

    @PostMapping
    @PreAuthorize("hasAuthority('ims:write:vendors')")
    public VendorDto createVendor(@RequestBody CreateVendorCommand command) {
        log.info("[createVendor] param command: {}.", command);
        return vendorApplicationService.create(command);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:vendors')")
    public VendorDto updateVendor(@PathVariable("id") UUID id, @RequestBody UpdateVendorCommand command) {
        log.info("[updateVendor] id:{} param command: {}.", id, command);
        Assert.isTrue(command.getVendorId().equals(id), "Vendor id not match");
        return vendorApplicationService.update(command);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:vendors')")
    public void deletedVendor(@PathVariable("id") UUID id) {
        log.info("[deletedVendor] id:{}.", id);
        vendorApplicationService.delete(id);
    }

    @PostMapping("/migrate-finale")
    @PreAuthorize("hasAuthority('ims:write:vendors')")
    public void migrateFinaleVendor() {
        log.info("[migrateFinaleVendor] started");
        vendorApplicationService.migrateFinaleVendor();
    }

    @GetMapping("/shutdown-window/enabled")
    @Operation(summary = "Get all vendors with shutdown window enabled",
        description = "Retrieve all vendors that have shutdown window functionality enabled")
    public List<VendorDto> getVendorsWithShutdownWindowEnabled() {
        log.info("Getting vendors with shutdown window enabled");
        return vendorApplicationService.findByShutdownWindowEnabled();
    }

}
