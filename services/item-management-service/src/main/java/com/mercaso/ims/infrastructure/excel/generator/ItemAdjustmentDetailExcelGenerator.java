package com.mercaso.ims.infrastructure.excel.generator;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import com.mercaso.ims.infrastructure.excel.data.CreateVendorDetailData;
import com.mercaso.ims.infrastructure.excel.data.ItemAdjustmentDetailData;
import com.mercaso.ims.infrastructure.excel.data.ItemVendorRebateDetailData;
import com.mercaso.ims.infrastructure.excel.mapper.CreateVendorDetailDataMapper;
import com.mercaso.ims.infrastructure.excel.mapper.ItemAdjustmentDetailDataMapper;
import com.mercaso.ims.infrastructure.excel.mapper.ItemVendorRebateDetailDataMapper;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.io.ByteArrayOutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.SUPPLIER_REBATES;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemAdjustmentDetailExcelGenerator {

    private final ItemAdjustmentRequestDetailService itemAdjustmentRequestDetailService;
    private final FeatureFlagsManager featureFlagsManager;


    public byte[] generate(UUID requestId) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        List<ItemAdjustmentRequestDetail> itemAdjustmentRequestDetails = itemAdjustmentRequestDetailService.findByItemAdjustmentRequestId(
            requestId);

        List<ItemAdjustmentDetailData> newItemDatas = itemAdjustmentRequestDetails.stream()
            .filter(domain -> domain.getType() == ItemAdjustmentType.CREATE)
            .map(ItemAdjustmentDetailDataMapper.INSTANCE::toExcelData)
            .sorted(Comparator.comparing(ItemAdjustmentDetailData::getStatus).reversed())
            .toList();
        List<ItemAdjustmentDetailData> itemAdjustmentData = itemAdjustmentRequestDetails.stream()
            .filter(domain -> domain.getType() == ItemAdjustmentType.UPDATE || domain.getType() == ItemAdjustmentType.CLEAN_UPC)
            .map(ItemAdjustmentDetailDataMapper.INSTANCE::toExcelData)
            .sorted(Comparator.comparing(ItemAdjustmentDetailData::getStatus).reversed())
            .toList();

        List<ItemAdjustmentDetailData> itemDeletedData = itemAdjustmentRequestDetails.stream()
            .filter(domain -> domain.getType() == ItemAdjustmentType.DELETE)
            .map(ItemAdjustmentDetailDataMapper.INSTANCE::toExcelData)
            .sorted(Comparator.comparing(ItemAdjustmentDetailData::getStatus).reversed())
            .toList();

        List<CreateVendorDetailData> createVendorData = itemAdjustmentRequestDetails.stream()
            .filter(domain -> domain.getType() == ItemAdjustmentType.CREATE_VENDOR)
            .map(CreateVendorDetailDataMapper.INSTANCE::toExcelData)
            .sorted(Comparator.comparing(CreateVendorDetailData::getStatus).reversed())
            .toList();

        List<ItemVendorRebateDetailData> newItemRebateDatas = itemAdjustmentRequestDetails.stream()
                .filter(domain -> domain.getType() == ItemAdjustmentType.CREATE_REBATE)
                .map(ItemVendorRebateDetailDataMapper.INSTANCE::toExcelData)
                .sorted(Comparator.comparing(ItemVendorRebateDetailData::getStatus).reversed())
                .toList();
        List<ItemVendorRebateDetailData> updateItemRebateDatas = itemAdjustmentRequestDetails.stream()
                .filter(domain -> domain.getType() == ItemAdjustmentType.UPDATE_REBATE)
                .map(ItemVendorRebateDetailDataMapper.INSTANCE::toExcelData)
                .sorted(Comparator.comparing(ItemVendorRebateDetailData::getStatus).reversed())
                .toList();

        List<ItemVendorRebateDetailData> deleteItemRebateDatas = itemAdjustmentRequestDetails.stream()
                .filter(domain -> domain.getType() == ItemAdjustmentType.DELETE_REBATE)
                .map(ItemVendorRebateDetailDataMapper.INSTANCE::toExcelData)
                .sorted(Comparator.comparing(ItemVendorRebateDetailData::getStatus).reversed())
                .toList();

        log.info("newItemData size :{} for requestId :{}", newItemDatas.size(), requestId);
        log.info("itemAdjustmentData size :{}  for requestId :{}", itemAdjustmentData.size(), requestId);
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build()) {
            WriteSheet newItemSheet = EasyExcelFactory.writerSheet(0, "New Item").head(ItemAdjustmentDetailData.class).build();
            excelWriter.write(newItemDatas, newItemSheet);
            WriteSheet itemAdjustmentSheet = EasyExcelFactory.writerSheet(1, "Item Adjustment")
                .head(ItemAdjustmentDetailData.class)
                .build();
            excelWriter.write(itemAdjustmentData, itemAdjustmentSheet);
            WriteSheet deleteItemSheet = EasyExcelFactory.writerSheet(2, "Delete Items")
                .head(ItemAdjustmentDetailData.class)
                .build();
            excelWriter.write(itemDeletedData, deleteItemSheet);
            WriteSheet createVendorSheet = EasyExcelFactory.writerSheet(3, "Create Vendor")
                .head(CreateVendorDetailData.class)
                .build();
            excelWriter.write(createVendorData, createVendorSheet);

            if (featureFlagsManager.isFeatureOn(SUPPLIER_REBATES)) {
                WriteSheet newItemRebateSheet = EasyExcelFactory.writerSheet(4, "Create Item Vendor Rebates")
                        .head(ItemVendorRebateDetailData.class)
                        .build();
                excelWriter.write(newItemRebateDatas, newItemRebateSheet);

                WriteSheet updateItemRebateSheet = EasyExcelFactory.writerSheet(5, "Update Item Vendor Rebates")
                        .head(ItemVendorRebateDetailData.class)
                        .build();
                excelWriter.write(updateItemRebateDatas, updateItemRebateSheet);

                WriteSheet deleteItemRebateSheet = EasyExcelFactory.writerSheet(6, "Delete Item Vendor Rebates")
                        .head(ItemVendorRebateDetailData.class)
                        .build();
                excelWriter.write(deleteItemRebateDatas, deleteItemRebateSheet);
            }

        } catch (Exception e) {
            log.error("Error while generating excel for requestId :{}", requestId);
            throw new ImsBusinessException("Error while generating excel for requestId :{}", requestId, e);
        }
        return outputStream.toByteArray();
    }

}
