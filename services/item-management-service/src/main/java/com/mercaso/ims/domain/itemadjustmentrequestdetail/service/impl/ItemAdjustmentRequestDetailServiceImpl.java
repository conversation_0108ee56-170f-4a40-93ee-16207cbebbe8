package com.mercaso.ims.domain.itemadjustmentrequestdetail.service.impl;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ItemAdjustmentRequestDetailServiceImpl implements ItemAdjustmentRequestDetailService {

    private final ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;

    @Override
    public List<ItemAdjustmentRequestDetail> findByItemAdjustmentRequestId(UUID itemAdjustmentRequestId) {
        return itemAdjustmentRequestDetailRepository.findByItemAdjustmentRequestId(itemAdjustmentRequestId);
    }

    @Override
    public List<ItemAdjustmentRequestDetail> findAllBySku(String skuNumber) {
        return itemAdjustmentRequestDetailRepository.findAllBySku(skuNumber);
    }
    
}
