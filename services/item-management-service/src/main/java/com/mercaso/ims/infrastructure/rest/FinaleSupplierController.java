package com.mercaso.ims.infrastructure.rest;

import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for Finale Supplier operations
 * Provides endpoints to retrieve supplier information from Finale API
 */
@RestController
@RequestMapping("/v1/finale/suppliers")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Finale Suppliers", description = "Operations related to Finale supplier management")
public class FinaleSupplierController {

    private final FinaleApplicationService finaleApplicationService;

    /**
     * Get all suppliers from Finale API
     * 
     * This endpoint calls the Finale API: https://app.finaleinventory.com/mercaso/api/partygroup
     * and returns the supplier information as a List<FinaleVendorInfoDto>
     * 
     * @return List of all suppliers from Finale
     */
    @GetMapping
    @Operation(
        summary = "Get all suppliers from Finale",
        description = "Retrieves all supplier information from Finale API and converts the array-based response to a list of supplier objects"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved suppliers"),
        @ApiResponse(responseCode = "500", description = "Failed to retrieve suppliers from Finale API")
    })
    @PreAuthorize("hasAnyAuthority('finale:read:suppliers')")
    public ResponseEntity<List<FinaleVendorInfoDto>> getAllSuppliers() {
        try {
            log.info("Received request to get all suppliers from Finale");
            
            List<FinaleVendorInfoDto> suppliers = finaleApplicationService.getAllSuppliers();
            
            log.info("Successfully retrieved {} suppliers from Finale", suppliers.size());
            return ResponseEntity.ok(suppliers);
            
        } catch (Exception e) {
            log.error("Failed to retrieve suppliers from Finale API", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get suppliers filtered by status
     * 
     * @param status The status to filter by (e.g., "PARTY_ENABLED")
     * @return List of suppliers with the specified status
     */
    @GetMapping("/by-status")
    @Operation(
        summary = "Get suppliers by status",
        description = "Retrieves suppliers filtered by their status (e.g., PARTY_ENABLED, PARTY_DISABLED)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved suppliers by status"),
        @ApiResponse(responseCode = "400", description = "Invalid status parameter"),
        @ApiResponse(responseCode = "500", description = "Failed to retrieve suppliers from Finale API")
    })
    @PreAuthorize("hasAnyAuthority('finale:read:suppliers')")
    public ResponseEntity<List<FinaleVendorInfoDto>> getSuppliersByStatus(
        @Parameter(description = "Status to filter suppliers by", example = "PARTY_ENABLED")
        @RequestParam String status) {
        
        try {
            log.info("Received request to get suppliers with status: {}", status);
            
            List<FinaleVendorInfoDto> allSuppliers = finaleApplicationService.getAllSuppliers();
            List<FinaleVendorInfoDto> filteredSuppliers = allSuppliers.stream()
                .filter(supplier -> status.equals(supplier.getStatusId()))
                .toList();
            
            log.info("Found {} suppliers with status: {}", filteredSuppliers.size(), status);
            return ResponseEntity.ok(filteredSuppliers);
            
        } catch (Exception e) {
            log.error("Failed to retrieve suppliers by status from Finale API", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get a specific supplier by party ID
     * 
     * @param partyId The party ID of the supplier to retrieve
     * @return The supplier with the specified party ID
     */
    @GetMapping("/{partyId}")
    @Operation(
        summary = "Get supplier by party ID",
        description = "Retrieves a specific supplier by their party ID"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved supplier"),
        @ApiResponse(responseCode = "404", description = "Supplier not found"),
        @ApiResponse(responseCode = "500", description = "Failed to retrieve supplier from Finale API")
    })
    @PreAuthorize("hasAnyAuthority('finale:read:suppliers')")
    public ResponseEntity<FinaleVendorInfoDto> getSupplierByPartyId(
        @Parameter(description = "Party ID of the supplier", example = "100001")
        @PathVariable String partyId) {
        
        try {
            log.info("Received request to get supplier with party ID: {}", partyId);
            
            List<FinaleVendorInfoDto> allSuppliers = finaleApplicationService.getAllSuppliers();
            FinaleVendorInfoDto supplier = allSuppliers.stream()
                .filter(s -> partyId.equals(s.getPartyId()))
                .findFirst()
                .orElse(null);
            
            if (supplier != null) {
                log.info("Found supplier with party ID: {}", partyId);
                return ResponseEntity.ok(supplier);
            } else {
                log.warn("Supplier not found with party ID: {}", partyId);
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Failed to retrieve supplier by party ID from Finale API", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get count of suppliers by status
     * 
     * @return Count of enabled suppliers
     */
    @GetMapping("/count/enabled")
    @Operation(
        summary = "Get count of enabled suppliers",
        description = "Returns the count of suppliers with PARTY_ENABLED status"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved supplier count"),
        @ApiResponse(responseCode = "500", description = "Failed to retrieve suppliers from Finale API")
    })
    @PreAuthorize("hasAnyAuthority('finale:read:suppliers')")
    public ResponseEntity<Long> getEnabledSuppliersCount() {
        try {
            log.info("Received request to get count of enabled suppliers");
            
            List<FinaleVendorInfoDto> allSuppliers = finaleApplicationService.getAllSuppliers();
            long enabledCount = allSuppliers.stream()
                .filter(supplier -> "PARTY_ENABLED".equals(supplier.getStatusId()))
                .count();
            
            log.info("Found {} enabled suppliers", enabledCount);
            return ResponseEntity.ok(enabledCount);
            
        } catch (Exception e) {
            log.error("Failed to get enabled suppliers count from Finale API", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
