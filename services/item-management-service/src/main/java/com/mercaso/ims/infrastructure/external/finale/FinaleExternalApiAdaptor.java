package com.mercaso.ims.infrastructure.external.finale;

import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleCompletedPurchaseOrderReportDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleProductDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinalePurchaseOrderDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleSupplierItemDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PurchaseOrderPageInfo;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinaleVendorsResultDto;
import com.mercaso.ims.infrastructure.external.finale.dto.UpdateSupplierItemRequestDto;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FinaleExternalApiAdaptor {

    private final FinaleExternalApiClient finaleExternalApiClient;
    @Value("${finale.accountPathComponent}")
    private String accountPathComponent;

    public FinaleExternalApiAdaptor(FinaleExternalApiClient finaleExternalApiClient) {
        this.finaleExternalApiClient = finaleExternalApiClient;
    }

    public FinaleVendorDto createVendor(String vendorName) {
        QueryFinaleVendorsResultDto finaleVendorsResultDto = finaleExternalApiClient.queryFinaleVendors();
        FinaleVendorDto finaleVendorDto = finaleVendorsResultDto.getFinaleVendor(vendorName);
        if (finaleVendorDto == null) {
            return finaleExternalApiClient.createVendor(vendorName);
        }
        return finaleVendorDto;
    }

    public FinaleVendorDto getVendor(String vendorName) {
        QueryFinaleVendorsResultDto finaleVendorsResultDto = finaleExternalApiClient.queryFinaleVendors();
        return finaleVendorsResultDto.getFinaleVendor(vendorName);
    }

    public FinaleVendorInfoDto getVendorById(String id) {
        return finaleExternalApiClient.queryFinaleVendorById(id);
    }

    public void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto) {
        finaleExternalApiClient.updateVendor(finaleVendorInfoDto);
    }

    public void updateVendorItem(String skuNumber,
        StatusId statusId,
        String vendorFinaleId,
        BigDecimal cost,
        Long inventory,
        String vendorSkuNumber,
        FinaleProductDto finaleProductDto) {
        if (StringUtils.isBlank(vendorFinaleId)) {
            log.warn("[updateVendorItem] vendorFinaleId is blank, skip update vendor item");
        } else {
            UpdateSupplierItemRequestDto updateSupplierItemRequestDto = buildUpdateSupplierItemRequestDto(vendorFinaleId,
                cost,
                inventory,
                vendorSkuNumber, finaleProductDto);
            // Use the original method with @RateLimitedTask annotation for normal business flow
            finaleExternalApiClient.updateVendorItem(skuNumber, statusId.getAction(), updateSupplierItemRequestDto);
        }
    }

    public void updateItemStatus(String skuNumber, StatusId statusId) {
        // Use the original method with @RateLimitedTask annotation for normal business flow
        finaleExternalApiClient.updateItemStatus(skuNumber, statusId.getAction());
    }

    public FinaleProductDto getFinaleProduct(String skuNumber, StatusId statusId) {
        FinaleProductDto finaleProductDto = finaleExternalApiClient.getFinaleProduct(skuNumber, statusId.getAction());
        if (finaleProductDto == null) {
            log.warn("[getFinaleProduct] finaleProductDto is null");
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED);
        }
        return finaleProductDto;
    }


    public List<PurchaseOrderPageInfo> queryPurchaseOrders(int length) {
        QueryFinalePurchaseOrderResultDto queryFinalePurchaseOrderResultDto = finaleExternalApiClient.queryPurchaseOrders(length);
        if (queryFinalePurchaseOrderResultDto != null) {
            return queryFinalePurchaseOrderResultDto.getPurchaseOrderInfoList();
        }
        return List.of();
    }

    public FinalePurchaseOrderDto getPurchaseOrder(String orderId) {
        return finaleExternalApiClient.getPurchaseOrder(orderId);
    }

    public List<FinaleCompletedPurchaseOrderReportDto> getCompletedPurchaseOrdersReport() {
        return finaleExternalApiClient.getCompletedPurchaseOrdersReport();
    }

    private UpdateSupplierItemRequestDto buildUpdateSupplierItemRequestDto(String vendorFinaleId,
        BigDecimal cost,
        Long inventory,
        String vendorSkuNumber,
        FinaleProductDto finaleProductDto) {

        FinaleSupplierItemDto firstFinaleSupplierItemDto = new FinaleSupplierItemDto(accountPathComponent,
            vendorFinaleId,
            cost,
            inventory,
            vendorSkuNumber);
        firstFinaleSupplierItemDto.setSupplierPrefOrderId("10_MAIN_SUPPL"); // Default supplier preference order ID

        List<FinaleSupplierItemDto> supplierList = finaleProductDto.getSupplierList();

        if (!supplierList.isEmpty() && Objects.equals(supplierList.get(0).getVendorFinaleId(), vendorFinaleId)) {
            firstFinaleSupplierItemDto.setLeadTime(supplierList.getFirst().getLeadTime());
            firstFinaleSupplierItemDto.setGeneralComments(supplierList.getFirst().getGeneralComments());
        }

        List<FinaleSupplierItemDto> newSupplierList = new ArrayList<>();
        newSupplierList.add(firstFinaleSupplierItemDto);

        supplierList.stream()
            .filter(supplier -> !Objects.equals(supplier.getVendorFinaleId(), vendorFinaleId))
            .forEach(supplier -> {
                int supplierIndex = newSupplierList.size() - 1;
                if (supplierIndex < 9) {
                    String supplierPrefOrderId = 90 + supplierIndex + "_ALT_SUPPL";
                    if (supplierIndex > 0) {
                        supplierPrefOrderId = supplierPrefOrderId + newSupplierList.size();
                    }
                    supplier.setSupplierPrefOrderId(supplierPrefOrderId);
                    supplier.setQuantityAvailable(0L);
                    newSupplierList.add(supplier);
                }

            });
        return new UpdateSupplierItemRequestDto(finaleProductDto.getProductUrl(),
            finaleProductDto.getProductId(),
            newSupplierList
        );
    }


}

