package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.VendorItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/vendor-item", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class VendorItemRestApi {

    private final VendorItemApplicationService vendorItemApplicationService;


    @PostMapping
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public VendorItemDto createVendorItem(@RequestBody CreateVendorItemCommand command) {
        log.info("[createVendorItem] param command: {}.", command);
        return vendorItemApplicationService.create(command);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public VendorItemDto updateVendorItem(@PathVariable("id") UUID id, @RequestBody UpdateVendorItemCommand command) {
        log.info("[updateVendorItem] id:{} param command: {}.", id, command);
        Assert.isTrue(command.getVendorItemId().equals(id), "Item id not match");
        return vendorItemApplicationService.update(command);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public void deleted(@PathVariable("id") UUID id) {
        vendorItemApplicationService.delete(id);
    }


    @GetMapping("/{id}/audit-history")
    @PreAuthorize("hasAuthority('ims:read:items')")
    public List<VendorItemAuditHistoryInfoDto> getVendorItemAuditHistories(@PathVariable("id") UUID id) {
        log.info("[getVendorItemAuditHistories] param id: {}.", id);
        return vendorItemApplicationService.getVendorItemAuditHistories(id);
    }
    
}
