package com.mercaso.ims.application.service;

import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import java.util.UUID;


public interface FinaleApplicationService {


    void syncItemToFinaleById(UUID itemId);

    FinaleVendorDto createVendor(String vendorName);

    FinaleVendorDto getVendor(String vendorName);

    FinaleVendorInfoDto getVendorById(String finaleId);

    void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto);
}
