package com.mercaso.ims.application.service;

import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import java.util.List;
import java.util.UUID;


public interface FinaleApplicationService {


    void syncItemToFinaleById(UUID itemId);

    FinaleVendorDto createVendor(String vendorName);

    FinaleVendorDto getVendor(String vendorName);

    FinaleVendorInfoDto getVendorById(String finaleId);

    void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto);

    /**
     * Get all suppliers from Finale API
     *
     * @return List of FinaleVendorInfoDto containing all supplier information
     */
    List<FinaleVendorInfoDto> getAllSuppliers();
}
