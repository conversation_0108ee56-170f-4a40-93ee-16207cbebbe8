package com.mercaso.ims.infrastructure.external.shopify;

import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShopifyAccessTokenBalancer {

    @Value("${shopify.access_token}")
    private String accessToken;

    private final AtomicInteger currentIndex;

    public ShopifyAccessTokenBalancer() {
        this.currentIndex = new AtomicInteger(0);
    }

    public String getNextAccessToken() {
        String[] accessTokens = accessToken.split(",");
        int index = currentIndex.getAndUpdate(i -> (i + 1) % accessTokens.length);
        return accessTokens[index];
    }
}

