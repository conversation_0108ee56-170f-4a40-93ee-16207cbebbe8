package com.mercaso.ims.infrastructure.excel.mapper;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.infrastructure.excel.data.CreateVendorDetailData;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CreateVendorDetailDataMapper {

    CreateVendorDetailDataMapper INSTANCE = Mappers.getMapper(CreateVendorDetailDataMapper.class);

    CreateVendorDetailData toExcelData(ItemAdjustmentRequestDetail domain);

}
