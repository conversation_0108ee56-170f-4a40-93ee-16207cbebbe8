package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_ADJUSTMENT_REQUEST_NOT_FOUND;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentFailureProcessedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestProcessedPayloadDto;
import com.mercaso.ims.application.mapper.itemadjustmentrequest.ItemAdjustmentRequestDtoApplicationMapper;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequestRepository;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.excel.generator.ItemAdjustmentDetailExcelGenerator;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.FileUtil;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemAdjustmentRequestApplicationServiceImpl implements ItemAdjustmentRequestApplicationService {

    private final ItemAdjustmentDetailExcelGenerator itemAdjustmentDetailExcelGenerator;
    private final ItemAdjustmentRequestDtoApplicationMapper itemAdjustmentRequestDtoApplicationMapper;
    private final ItemAdjustmentRequestRepository itemAdjustmentRequestRepository;
    private final BusinessEventService businessEventService;
    private final ItemAdjustmentRequestDetailService itemAdjustmentRequestDetailService;
    private final DocumentApplicationService documentApplicationService;

    @Override
    public DocumentResponse downloadItemAdjustmentDetail(UUID itemAdjustmentRequestId) {

        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(itemAdjustmentRequestId);
        if (itemAdjustmentRequest == null) {
            throw new ImsBusinessException(ITEM_ADJUSTMENT_REQUEST_NOT_FOUND);
        }
        String itemAdjustmentRequestFileName = FileUtil.getFileNameWithoutExtension(itemAdjustmentRequest.getRequestFile());

        byte[] content = itemAdjustmentDetailExcelGenerator.generate(itemAdjustmentRequestId);

        String sb = itemAdjustmentRequestFileName
            + "-details"
            + ".xlsx";
        String fileName = URLEncoder.encode(sb, StandardCharsets.UTF_8).replace("\\+", "-");
        log.info("uploadItemAdjustmentDetail for docName  :{}", fileName);
        return documentApplicationService.uploadExcel(content, fileName);
    }

    @Override
    @ReportMetric(metricsType = MetricsTypeEnum.UPLOAD_ITEM_ADJUSTMENT_REQUEST)
    public ItemAdjustmentRequestDto uploadItemAdjustmentRequest(MultipartFile file, ItemAdjustmentRequestType type) {
        try {
            String docName = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14).concat("-")
                .concat(Objects.requireNonNull(file.getOriginalFilename()));

            log.info("uploadItemAdjustmentRequest for docName  :{}", docName);
            DocumentResponse documentResponse = documentApplicationService.uploadExcel(file.getBytes(), docName);
            log.info("DocumentResponse for uploadItemAdjustmentRequest,documentName :{}  response :{}, ",
                documentResponse.getName(),
                documentResponse);

            ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequest.builder()
                .requestFile(documentResponse.getName())
                .type(type)
                .status(ItemAdjustmentRequestStatus.UPLOADED)
                .build();

            ItemAdjustmentRequestDto dto = itemAdjustmentRequestDtoApplicationMapper.domainToDto(itemAdjustmentRequestRepository.save(
                itemAdjustmentRequest));

            businessEventService.dispatch(ItemAdjustmentRequestCreatedPayloadDto.builder()
                .itemAdjustmentRequestId(dto.getId())
                .data(dto)
                .build());
            return dto;
        } catch (Exception e) {
            log.error("uploadItemAdjustmentRequest Error", e);
            throw new ImsBusinessException("Upload file error.", e);
        }
    }

    @Override
    public DocumentResponse getItemAdjustmentRequestFile(UUID id) {
        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(id);
        if (itemAdjustmentRequest == null) {
            throw new ImsBusinessException(ITEM_ADJUSTMENT_REQUEST_NOT_FOUND);
        }
        DocumentResponse itemAdjustmentRequestFileDto = new DocumentResponse();
        itemAdjustmentRequestFileDto.setName(itemAdjustmentRequest.getRequestFile());
        String url = documentApplicationService.getSignedUrl(itemAdjustmentRequest.getRequestFile());
        itemAdjustmentRequestFileDto.setSignedUrl(url);
        return itemAdjustmentRequestFileDto;
    }

    @Override
    public ItemAdjustmentRequestDto addCountOfParsed(UUID id,
        Integer createdRowCount,
        Integer modifiedRowCount,
        Integer deletedRowCount) {
        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(id);
        itemAdjustmentRequest.addCountOfParsed(createdRowCount, modifiedRowCount, deletedRowCount);
        return itemAdjustmentRequestDtoApplicationMapper.domainToDto(itemAdjustmentRequestRepository.save(
            itemAdjustmentRequest));
    }

    @Override
    public void checkAndComplete(UUID id) {
        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(id);
        if (itemAdjustmentRequest == null) {
            throw new ImsBusinessException(ITEM_ADJUSTMENT_REQUEST_NOT_FOUND);
        }
        if (itemAdjustmentRequest.getStatus() != ItemAdjustmentRequestStatus.FILE_PROCESSED) {
            return;
        }
        List<ItemAdjustmentRequestDetail> details = itemAdjustmentRequestDetailService.findByItemAdjustmentRequestId(id);
        boolean isAllCompleted = details.stream().allMatch(ItemAdjustmentRequestDetail::isCompleted);

        if (isAllCompleted) {
            Map<ItemAdjustmentType, Integer> successCounts = new EnumMap<>(ItemAdjustmentType.class);
            Map<ItemAdjustmentType, Integer> failedCounts = new EnumMap<>(ItemAdjustmentType.class);
            for (ItemAdjustmentType type : ItemAdjustmentType.values()) {
                successCounts.put(type, 0);
                failedCounts.put(type, 0);
            }

            details.forEach(detail -> {
                Map<ItemAdjustmentType, Integer> counts = detail.isFailure() ? failedCounts : successCounts;
                counts.merge(detail.getType(), 1, Integer::sum);
            });

            Integer createSuccessRowCount = successCounts.getOrDefault(ItemAdjustmentType.CREATE, 0) +
                    successCounts.getOrDefault(ItemAdjustmentType.CREATE_REBATE, 0);

            Integer createFailedRowCount = failedCounts.getOrDefault(ItemAdjustmentType.CREATE, 0) +
                    failedCounts.getOrDefault(ItemAdjustmentType.CREATE_REBATE, 0);

            Integer modifySuccessRowCount = successCounts.getOrDefault(ItemAdjustmentType.UPDATE, 0) + successCounts.getOrDefault(
                ItemAdjustmentType.CLEAN_UPC,
                0) + successCounts.getOrDefault(ItemAdjustmentType.UPDATE_REBATE, 0);

            Integer modifyFailedRowCount = failedCounts.getOrDefault(ItemAdjustmentType.UPDATE, 0) + failedCounts.getOrDefault(
                ItemAdjustmentType.CLEAN_UPC,
                0) + failedCounts.getOrDefault(ItemAdjustmentType.UPDATE_REBATE, 0);

            Integer deleteSuccessRowCount = successCounts.getOrDefault(ItemAdjustmentType.DELETE, 0) +
                    successCounts.getOrDefault(ItemAdjustmentType.DELETE_REBATE, 0);

            Integer deleteFailedRowCount = failedCounts.getOrDefault(ItemAdjustmentType.DELETE, 0) +
                    failedCounts.getOrDefault(ItemAdjustmentType.DELETE_REBATE, 0);

            itemAdjustmentRequest.complete(
                createSuccessRowCount,
                modifySuccessRowCount,
                deleteSuccessRowCount,
                createFailedRowCount,
                modifyFailedRowCount,
                deleteFailedRowCount
            );

            ItemAdjustmentRequestDto dto = itemAdjustmentRequestDtoApplicationMapper.domainToDto(itemAdjustmentRequestRepository.save(
                itemAdjustmentRequest));
            businessEventService.dispatch(ItemAdjustmentRequestCompletedPayloadDto.builder()
                .itemAdjustmentRequestId(dto.getId())
                .data(dto)
                .build());
        }
    }

    @Override
    public void finishProcessed(UUID id) {
        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(id);
        if (itemAdjustmentRequest == null) {
            throw new ImsBusinessException(ITEM_ADJUSTMENT_REQUEST_NOT_FOUND);
        }
        itemAdjustmentRequest.processed();
        ItemAdjustmentRequestDto dto = itemAdjustmentRequestDtoApplicationMapper.domainToDto(itemAdjustmentRequestRepository.save(
            itemAdjustmentRequest));
        businessEventService.dispatch(ItemAdjustmentRequestProcessedPayloadDto.builder()
            .itemAdjustmentRequestId(dto.getId())
            .data(dto)
            .build());

    }

    @Override
    public void processedFailure(UUID id, String failureReason) {
        log.warn("processedFailure for id :{} , reason :{}", id, failureReason);
        ItemAdjustmentRequest itemAdjustmentRequest = itemAdjustmentRequestRepository.findById(id);
        itemAdjustmentRequest.processedFailure(failureReason);
        ItemAdjustmentRequestDto dto = itemAdjustmentRequestDtoApplicationMapper.domainToDto(itemAdjustmentRequestRepository.save(
            itemAdjustmentRequest));
        businessEventService.dispatch(ItemAdjustmentFailureProcessedPayloadDto.builder()
            .itemAdjustmentRequestId(dto.getId())
            .data(dto)
            .build());
    }


}
