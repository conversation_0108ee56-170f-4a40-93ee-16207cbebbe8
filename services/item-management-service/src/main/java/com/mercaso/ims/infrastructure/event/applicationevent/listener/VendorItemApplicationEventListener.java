package com.mercaso.ims.infrastructure.event.applicationevent.listener;


import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_EXCEPTION_RECORD;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.VendorItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.VendorItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.VendorItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemDeletedPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class VendorItemApplicationEventListener {

    private final ItemApplicationService itemApplicationService;
    private final ItemService itemService;
    private final FeatureFlagsManager featureFlagsManager;
    private final VendorItemSpecification vendorItemSpecification;
    private final ExceptionRecordApplicationService exceptionRecordApplicationService;
    private final FinaleApplicationService finaleApplicationService;
    private final ItemQueryApplicationService itemQueryApplicationService;
    private final ItemPriceApplicationService itemPriceApplicationService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleVendorItemCreatedApplicationEvent(VendorItemCreatedApplicationEvent vendorItemCreatedApplicationEvent) {
        VendorItemCreatedPayloadDto vendorItemCreatedPayloadDto = vendorItemCreatedApplicationEvent.getPayload();
        log.info("handleVendorItemAmendApplicationEvent for vendorItemCreatedPayloadDto={}", vendorItemCreatedPayloadDto);

        itemApplicationService.refreshPrimaryBackupVendor(vendorItemCreatedPayloadDto.getData().getItemId());

        updateExoticBlvdItemPrice(vendorItemCreatedPayloadDto.getData());
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleVendorItemAmendApplicationEvent(VendorItemAmendApplicationEvent vendorItemAmendApplicationEvent) {
        VendorItemAmendPayloadDto vendorItemAmendPayloadDto = vendorItemAmendApplicationEvent.getPayload();
        if (vendorItemAmendPayloadDto.isCostChanged()) {
            checkCostException(vendorItemAmendPayloadDto, vendorItemAmendApplicationEvent.getBusinessEventId());
        }

        // First refresh primary/backup vendor to ensure latest vendor configuration
        itemApplicationService.refreshPrimaryBackupVendor(vendorItemAmendPayloadDto.getCurrent().getItemId());

        // Then sync to Finale - this ensures VendorItem changes are always synced
        // even if refreshPrimaryBackupVendor doesn't trigger ItemAmendEvent
        if (!vendorItemAmendPayloadDto.isVendorItemTypeChanged()) {
            // Use synchronous call to ensure proper execution order
            syncVendorItemToFinaleSync(vendorItemAmendPayloadDto.getCurrent());
        }

        updateExoticBlvdItemPrice(vendorItemAmendPayloadDto.getCurrent());
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleVendorItemDeletedApplicationEvent(VendorItemDeletedApplicationEvent vendorItemDeletedApplicationEvent) {
        VendorItemDeletedPayloadDto vendorItemDeletedPayloadDto = vendorItemDeletedApplicationEvent.getPayload();
        log.info("handleVendorItemDeletedApplicationEvent for vendorItemDeletedPayloadDto={}", vendorItemDeletedPayloadDto);

        VendorItemDto vendorItemDto = vendorItemDeletedPayloadDto.getData();

        itemApplicationService.refreshPrimaryBackupVendor(vendorItemDto.getItemId());

        updateExoticBlvdItemPrice(vendorItemDto);
    }


    private void checkCostException(VendorItemAmendPayloadDto vendorItemAmendPayloadDto, UUID eventId) {
        if (!featureFlagsManager.isFeatureOn(IMS_EXCEPTION_RECORD)) {
            return;
        }

        // Create VendorItem from current DTO data for margin calculation
        VendorItem currentVendorItem = createVendorItemFromDto(vendorItemAmendPayloadDto.getCurrent());

        String exception = vendorItemSpecification.getExceptionMessage(currentVendorItem,
            vendorItemAmendPayloadDto.getData().getPrevious().getCost(),
            vendorItemAmendPayloadDto.getData().getCurrent().getCost());
        String backupException = vendorItemSpecification.getExceptionMessage(currentVendorItem,
            vendorItemAmendPayloadDto.getData().getPrevious().getBackupCost(),
            vendorItemAmendPayloadDto.getData().getCurrent().getBackupCost());
        if (StringUtils.isNotBlank(exception)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(vendorItemAmendPayloadDto.getCurrent().getVendorItemId())
                .entityType(EntityType.VENDOR_ITEM)
                .exceptionType(ExceptionRecordType.PO_COST_EXCEPTION)
                .description(exception)
                .build());
        }

        if (StringUtils.isNotBlank(backupException)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(vendorItemAmendPayloadDto.getCurrent().getVendorItemId())
                .entityType(EntityType.VENDOR_ITEM)
                .exceptionType(ExceptionRecordType.JIT_COST_EXCEPTION)
                .description(backupException)
                .build());
        }

    }

    private void updateExoticBlvdItemPrice(VendorItemDto vendorItemDto) {
        Item item = fetchItemOrThrow(vendorItemDto.getItemId());
        ItemDto itemDto = itemQueryApplicationService.findById(item.getId());
        if (itemDto.getVendorItemDtos() != null && itemDto.getVendorItemDtos().size() == 1
            && itemDto.getVendorItemDtos().getFirst().getVendorName().equals(VendorConstant.EXOTIC_BLVD)) {
            BigDecimal price = itemDto.getVendorItemDtos().getFirst()
                .getBackupCost()
                .divide(BigDecimal.valueOf(0.875), 2, RoundingMode.HALF_UP).subtract(BigDecimal.valueOf(0.01));
            log.debug("Update ExoticBlvd item price for itemId: {}, price: {}", vendorItemDto.getItemId(),
                price);
            UpdateItemRegPriceCommand command = new UpdateItemRegPriceCommand(vendorItemDto.getItemId(),
                price);
            itemPriceApplicationService.updateRegPrice(command);
        }
    }

    private void syncVendorItemToFinaleSync(VendorItemDto vendorItemDto) {
        if (isPrimaryVendorItem(vendorItemDto)) {
            finaleApplicationService.syncItemToFinaleById(vendorItemDto.getItemId());
        }
    }

    private boolean isPrimaryVendorItem(VendorItemDto vendorItemDto) {
        Item item = fetchItemOrThrow(vendorItemDto.getItemId());
        return vendorItemDto.getVendorId().equals(item.getPrimaryVendorId()) ||
            vendorItemDto.getVendorId().equals(item.getBackupVendorId());

    }

    private Item fetchItemOrThrow(UUID id) {
        return Optional.ofNullable(itemService.findById(id))
            .orElseThrow(() -> new ImsBusinessException(ITEM_NOT_FOUND));
    }

    /**
     * Create a minimal VendorItem object from VendorItemDto for margin calculations This is needed because the
     * VendorItemSpecification now requires VendorItem objects to ensure correct vendor-specific rebate calculations
     */
    private VendorItem createVendorItemFromDto(VendorItemDto dto) {
        if (dto == null) {
            return null;
        }

        return VendorItem.builder()
            .id(dto.getVendorItemId())
            .vendorId(dto.getVendorId())
            .itemId(dto.getItemId())
            .packPlusCrvCost(dto.getCost())
            .backupPackPlusCrvCost(dto.getBackupCost())
            .vendorItemType(dto.getVendorItemType())
            .build();
    }

}
