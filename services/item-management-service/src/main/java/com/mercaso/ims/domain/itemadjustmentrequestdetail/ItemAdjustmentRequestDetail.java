package com.mercaso.ims.domain.itemadjustmentrequestdetail;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentTransitionEvents;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.infrastructure.statemachine.BaseStateMachine;
import java.math.BigDecimal;
import java.util.EnumSet;
import java.util.Set;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestDetail extends
    BaseStateMachine<ItemAdjustmentRequestDetail, ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> {


    protected static final Set<ItemAdjustmentStatus> FAILURE_STATUS = EnumSet.of(ItemAdjustmentStatus.VALIDATION_FAILURE,
        ItemAdjustmentStatus.IMS_UPDATED_FAILURE,
        ItemAdjustmentStatus.PLYTIX_SYNCHRONIZED_FAILURE,
        ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED_FAILURE
    );
    private final UUID id;
    private UUID requestId;
    private ItemAdjustmentType type;
    private String sku;
    private AvailabilityStatus itemStatus;
    private String primaryVendorItemAisle;
    private String primaryPoVendor;
    private String primaryJitVendor;
    private String title;
    private Integer packageSize;
    private String itemSize;
    private String department;
    private String category;
    private String subCategory;
    private String classType;
    private String brand;
    private BigDecimal regPricePackNoCrv;
    private BigDecimal primaryPoVendorItemCost;
    private BigDecimal primaryJitVendorItemCost;
    private BigDecimal poVendorItemCost;
    private BigDecimal jitVendorItemCost;
    private Long inventory;
    private String upc;
    private String caseUpc;
    private String eachUpc;
    private String missingEachUPCReason;
    private String missingCaseUPCReason;
    private String vendorItemNumber;
    private String disposition;
    private String failureReason;

    private String vendor;
    private String vendorAisle;
    private String attributeName;
    private String attributeValue;

    private String companyId;
    private String locationId;
    private String newDescription;
    private String itemUnitMeasure;
    private Boolean promoFlag;
    private BigDecimal promoPricePackNoCrv;
    private Boolean crvFlag;
    private String imageUrl;
    private String tags;

    private Double length;
    private Double height;
    private Double width;
    private Double caseWeight;
    private String caseWeightUnit;

    private Double eachWeight;
    private String eachWeightUnit;

    private Boolean vendorItemAvailability;

    private String vendorItemType;
    private Boolean cooler;
    private Boolean highValue;
    private String archivedReason;

    private String rebateStartDate;

    private String rebateEndDate;

    private String rebatePerSellingUnit;


    public ItemAdjustmentRequestDetail updateIms() {
        processEvent(ItemAdjustmentTransitionEvents.IMS_UPDATED);
        return this;
    }

    public ItemAdjustmentRequestDetail updateImsFailure(String failureReason) {
        this.failureReason = failureReason;
        processEvent(ItemAdjustmentTransitionEvents.IMS_UPDATED_FAILURE);
        return this;
    }

    public ItemAdjustmentRequestDetail syncToShopify() {
        if (getStatus().equals(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED)) {
            return this;
        }
        if (getStatus().equals(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED_FAILURE)) {
            processEvent(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED_RECOVER);
            return this;
        }
        if (getStatus().equals(ItemAdjustmentStatus.PENDING)) {
            processEvent(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED_DIRECTLY);
            return this;
        }
        processEvent(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED);
        return this;
    }

    public ItemAdjustmentRequestDetail syncToShopifyFailure(String failureReason) {
        processEvent(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED_FAILURE);
        this.failureReason = failureReason;
        return this;
    }


    public boolean isCompleted() {
        ItemAdjustmentStatus status = getStatus();
        return ItemAdjustmentStatus.VALIDATION_FAILURE.equals(status) ||
            ItemAdjustmentStatus.IMS_UPDATED_FAILURE.equals(status) ||
            ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED.equals(status) ||
            ItemAdjustmentStatus.IMS_UPDATED.equals(status)
            ;
    }

    public boolean isFailure() {
        return FAILURE_STATUS.contains(getStatus());
    }

}

