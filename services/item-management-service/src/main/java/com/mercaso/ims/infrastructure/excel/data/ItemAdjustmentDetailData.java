package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@EqualsAndHashCode
public class ItemAdjustmentDetailData {

    @ColumnWidth(20)
    @ExcelProperty("requestStatus")
    private String status;
    @ColumnWidth(20)
    @ExcelProperty("failureReason")
    private String failureReason;
    @ExcelProperty("type")
    private String type;
    @ExcelProperty("status")
    private String itemStatus;
    @ExcelProperty("Company ID")
    private String companyId;
    @ExcelProperty("Location ID")
    private String locationId;
    @ExcelProperty("Primary Direct Vendor")
    @ColumnWidth(20)
    private String primaryPoVendor;
    @ExcelProperty("Primary JIT Vendor")
    @ColumnWidth(10)
    private String primaryJitVendor;
    @ExcelProperty("sku")
    @ColumnWidth(15)
    private String sku;
    @ExcelProperty("Supplier Item Number")
    @ColumnWidth(10)
    private String vendorItemNumber;
    @ExcelProperty("Item Description")
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    @ColumnWidth(50)
    private String itemDescription;
    @ExcelProperty("New Description")
    @ColumnWidth(50)
    private String newDescription;
    @ExcelProperty("Pack Size")
    private Integer packSize;
    @ExcelProperty("Item Size")
    private String itemSize;
    @ExcelProperty("Item Unit Measure")
    private String itemUnitMeasure;
    @ExcelProperty("CRV flag")
    private Boolean crvFlag;
    @ExcelProperty("Department")
    @ColumnWidth(15)
    private String department;
    @ExcelProperty("Category")
    @ColumnWidth(15)
    private String category;
    @ExcelProperty("Sub-Category")
    @ColumnWidth(15)
    private String subCategory;
    @ExcelProperty("Class")
    @ColumnWidth(15)
    private String classType;
    @ExcelProperty("Brand")
    @ColumnWidth(10)
    private String brand;
    @ExcelProperty("Reg. Price (Pack No CRV)")
    private BigDecimal regPricePackNoCrv;
    @ColumnWidth(40)
    @ExcelProperty("upc")
    private String upc;
    @ExcelProperty("Primary Direct Vendor Item Cost")
    private BigDecimal primaryPoVendorItemCost;
    @ExcelProperty("Primary JIT Vendor Item Cost")
    private BigDecimal primaryJitVendorItemCost;
    @ExcelProperty("aisle")
    private String primaryVendorItemAisle;
    @ExcelProperty("Promo (Pack)")
    private BigDecimal promoPricePackNoCrv;
    @ExcelProperty("Promo flag")
    private Boolean promoFlag;
    @ExcelProperty("Image url")
    private String imageUrl;
    @ExcelProperty("tags")
    @ColumnWidth(20)
    private String tags;
    @ExcelProperty("Supplier")
    @ColumnWidth(10)
    private String vendor;
    @ExcelProperty("Direct Vendor Item Cost")
    private BigDecimal poVendorItemCost;
    @ExcelProperty("JIT Vendor Item Cost")
    private BigDecimal jitVendorItemCost;


    @ExcelProperty("disposition")
    @ColumnWidth(30)
    private String disposition;
    @ExcelProperty("inventory")
    private Long inventory;


    public String getIdTag() {
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(locationId)) {
            return null;
        }
        return companyId + "_" + locationId;
    }


}
