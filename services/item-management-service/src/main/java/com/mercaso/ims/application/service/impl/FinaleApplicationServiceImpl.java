package com.mercaso.ims.application.service.impl;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleProductDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleApplicationServiceImpl implements FinaleApplicationService {

    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final Environment environment;
    private final ItemQueryApplicationService itemQueryApplicationService;
    private final PgAdvisoryLock pgAdvisoryLock;


    @Override
    @Transactional(propagation = REQUIRES_NEW)
    public void syncItemToFinaleById(UUID itemId) {
        if (environment.getActiveProfiles().length > 0 && environment.getActiveProfiles()[0].equals("sat")) {
            return;
        }

        // Create item-specific lock key to prevent concurrent sync operations for the same item
        Integer lockKey = ("FinaleSync-" + itemId).hashCode();

        try {
            // Use transactional-level advisory lock to ensure proper transaction handling
            pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(lockKey, "Finale Sync for Item " + itemId);

            // Re-query the latest ItemDto from database to ensure data consistency
            // This query will see the latest committed data due to REQUIRES_NEW transaction
            ItemDto item = itemQueryApplicationService.findById(itemId);

            if (item == null) {
                log.error("Item not found for id: {}", itemId);
                return;
            }

            StatusId statusId = StatusId.fromAvailabilityStatus(item.getAvailabilityStatus());

            FinaleProductDto finaleProductDto = finaleExternalApiAdaptor.getFinaleProduct(item.getSkuNumber(), statusId);

            syncStatus(item.getSkuNumber(), finaleProductDto, statusId);
            syncVendorItem(item, finaleProductDto, statusId);

        } catch (Exception e) {
            log.error("Error syncing item {} to finale: {}", itemId, e.getMessage(), e);
        }
        // Transactional-level advisory lock will be automatically released when transaction ends
    }

    @Override
    public FinaleVendorDto createVendor(String vendorName) {
        if (environment.getActiveProfiles().length > 0 && environment.getActiveProfiles()[0].equals("sat")) {
            return FinaleVendorDto.builder().partyId("1").build();
        }
        return finaleExternalApiAdaptor.createVendor(vendorName);
    }

    @Override
    public FinaleVendorDto getVendor(String vendorName) {
        return finaleExternalApiAdaptor.getVendor(vendorName);
    }

    @Override
    public FinaleVendorInfoDto getVendorById(String finaleId) {
        return finaleExternalApiAdaptor.getVendorById(finaleId);
    }

    @Override
    public void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto) {
        finaleExternalApiAdaptor.updateVendor(finaleVendorInfoDto);
    }

    private void syncStatus(String skuNumber, FinaleProductDto finaleProductDto, StatusId statusId) {
        if (finaleProductDto == null) {
            return;
        }

        if (finaleProductDto.getStatusId().equals(statusId)) {
            return;
        }
        if (!StatusId.UNKNOWN.equals(statusId)) {
            finaleExternalApiAdaptor.updateItemStatus(skuNumber, statusId);
        }
    }

    private void syncVendorItem(ItemDto item, FinaleProductDto finaleProductDto, StatusId statusId) {
        if (StatusId.PRODUCT_INACTIVE.equals(statusId)) {
            return;
        }
        long inventory;
        if (item.getBackupVendorItem() != null
            && item.getBackupVendorItem().getAvailability() != null
            && Boolean.TRUE.equals(item.getBackupVendorItem().getAvailability())) {
            inventory = 9999L;
        } else {
            inventory = 0L;
        }

        Optional.ofNullable(item.getPrimaryVendorItem())
            .or(() -> Optional.ofNullable(item.getBackupVendorItem()))
            .ifPresentOrElse(vendorItemDto -> {
                    BigDecimal cost = vendorItemDto.isDirectVendorItem() ? vendorItemDto.getCost()
                        : vendorItemDto.getBackupCost();
                    if (null == cost) {
                        log.warn("Cost is null, skip sync vendor item to finale, item:{}", item);
                        return;
                    }

                    cost = cost.add(item.getCrvAmount());
                    finaleExternalApiAdaptor.updateVendorItem(item.getSkuNumber(), statusId, vendorItemDto.getVendorFinaleId(),
                        cost, inventory, vendorItemDto.getVendorSkuNumber(), finaleProductDto);
                },
                () -> log.warn("Skip sync vendor item to finale, both primary and backup vendor items are null for item:{}",
                    item));
    }
}
