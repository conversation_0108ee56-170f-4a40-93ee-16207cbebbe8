package com.mercaso.ims.application.service;

import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemBackupVendorCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPrimaryVendorCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.domain.item.enums.ArchivedReason;
import java.util.List;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;

public interface ItemApplicationService {

    ItemDto create(CreateItemCommand command);

    ItemDto update(UpdateItemCommand updateItemCommand);

    ItemDto deleteItemById(DeleteItemCommand deleteItemCommand);

    ItemDto bindingPhoto(UUID id, MultipartFile file);

    void cleanItemUPCs(CleanItemUpcCommand command);

    ItemDto active(UUID id);

    ItemDto draft(UUID id);

    ItemDto archive(UUID id, ArchivedReason archivedReason);

    ItemDto updatePrimaryVendor(UpdateItemPrimaryVendorCommand command);

    ItemDto updateBackupVendor(UpdateItemBackupVendorCommand command);

    BatchUpdateItemStatusResultDto batchUpdateItemStatus(BatchUpdateItemStatusCommand command);

    ValidateBarcodeResultDto validateBarcode(ValidateBarcodeCommand command);

    List<ValidateBarcodeResultDto> batchValidateBarcode(List<ValidateBarcodeCommand> commands);

    BatchUpdateItemPhotoResultDto batchUpdateItemPhoto(List<BatchUpdateItemPhotoCommand> commands);

    ItemDto updatePromoPrice(UpdateItemPromoPriceCommand command);

    ItemDto updateItemUpc(UpdateItemUpcCommand command);

    ItemDto refreshPrimaryBackupVendor(UUID id);

    BatchUpdateItemPromoPriceResultDto batchUpdatePromoPrice(List<UpdateItemPromoPriceCommand> commands);

    ItemDto bindingSkuPhoto(MultipartFile file);

}
