package com.mercaso.ims.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinaleVendorInfoDto {

    private String partyId;
    private String partyUrl;
    private String description;
    private String statusId;
    private String guiOptions;
    private String lastUpdatedDate;
    private String createdDate;
    private String settlementTermId;
    private List<String> roleTypeIdList;
    private List<ContactMechDto> contactMechList;
    private List<Object> glAccountList;
    private List<Object> contentList;
    private List<UserFieldDataDto> userFieldDataList;
    private List<Object> connectionRelationUrlList;
    private List<Object> productStoreUrlList;
    private String groupName;
    private String contactName;
    private Integer leadTime;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ContactMechDto {
        private String contactMechId;
        private String contactMechTypeId;
        private String contactMechPurposeTypeId;
        private String address1;
        private String city;
        private String stateProvinceGeoId;
        private String postalCode;
        private String countryGeoId;
        private String infoString;
        private String statusId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserFieldDataDto {
        private String attrName;
        private String attrValue;
    }
}