package com.mercaso.ims.infrastructure.apitaskprocess;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.domain.taskqueue.service.ApiTaskQueueService;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskExecutionContextPayload;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * Router for task execution decisions Determines whether to execute synchronously or asynchronously
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskRouter {

    private final SynchronousTaskProcessor synchronousTaskProcessor;
    private final ApiTaskQueueService apiTaskQueueService;
    private final ObjectMapper objectMapper;

    /**
     * Route task execution based on annotation configuration
     *
     * @param joinPoint The AOP join point
     * @param annotation The @RateLimitedTask annotation
     * @return Result for synchronous execution or task ID for asynchronous
     * @throws Throwable If execution fails
     */
    public Object routeTask(ProceedingJoinPoint joinPoint, RateLimitedTask annotation) throws Throwable {
        TaskExecutionContextPayload context = createExecutionContext(joinPoint, annotation);

        // Check if bypass is enabled (for testing or emergency scenarios)
        if (context.isBypass()) {
            log.info("Bypassing task queue for method: {}", context.getFullMethodName());
            return context.executeOriginalMethod();
        }

        if (context.isSynchronous()) {
            // Synchronous execution - apply rate limiting directly without queuing
            return synchronousTaskProcessor.executeTask(context);
        } else {
            // Asynchronous execution - create task and return task ID
            return createAsynchronousTask(context);
        }
    }

    /**
     * Create execution context from join point and annotation
     */
    private TaskExecutionContextPayload createExecutionContext(ProceedingJoinPoint joinPoint, RateLimitedTask annotation) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String className = method.getDeclaringClass().getName();
        String methodName = method.getName();
        String fullMethodName = method.getDeclaringClass().getSimpleName() + "." + methodName;

        // Create parameter map
        String[] parameterNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        Map<String, Object> parameters = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            parameters.put(parameterNames[i], args[i]);
        }

        // Get task type and rate limiter name
        TaskType taskType = annotation.taskType();
        String rateLimiterName = getRateLimiterNameForTaskType(taskType);

        // Determine endpoint
        String endpoint = annotation.endpoint().isEmpty() ? fullMethodName : annotation.endpoint();

        return TaskExecutionContextPayload.builder()
            .joinPoint(joinPoint)
            .annotation(annotation)
            .method(method)
            .signature(signature)
            .parameters(parameters)
            .fullMethodName(fullMethodName)
            .methodName(methodName)
            .className(className)
            .taskType(taskType.getType())
            .rateLimiterName(rateLimiterName)
            .endpoint(endpoint)
            .synchronous(annotation.synchronous())
            .bypass(annotation.bypass())
            .build();
    }

    /**
     * Create asynchronous task and return task ID
     */
    private UUID createAsynchronousTask(TaskExecutionContextPayload context) {
        log.info("Creating asynchronous rate-limited task for method: {}", context.getFullMethodName());

        try {
            // Create task request payload
            TaskRequestPayload requestPayload = createRequestPayload(context);
            String requestJson = objectMapper.writeValueAsString(requestPayload);

            // Create task and return task ID
            ApiTaskQueue task = apiTaskQueueService.createTask(
                context.getTaskType(),
                context.getEndpoint(),
                context.getAnnotation().httpMethod(),
                requestJson,
                context.getAnnotation().priority(),
                context.getAnnotation().maxRetryCount()
            );

            log.info("Created async task {} for method {}", task.getId(), context.getFullMethodName());
            return task.getId();

        } catch (Exception e) {
            log.error("Error creating async task for method {}: {}", context.getFullMethodName(), e.getMessage(), e);
            throw new ImsBusinessException("Failed to create async task for method: " + context.getFullMethodName(), e);
        }
    }

    /**
     * Create request payload from execution context
     */
    private TaskRequestPayload createRequestPayload(TaskExecutionContextPayload context) {
        return TaskRequestPayload.builder()
            .methodName(context.getMethodName())
            .className(context.getClassName())
            .parameters(context.getParameters())
            .taskType(context.getTaskType())
            .build();
    }

    /**
     * Get rate limiter name for task type
     */
    private String getRateLimiterNameForTaskType(TaskType taskType) {
        try {
            return taskType.getRateLimiterName();
        } catch (IllegalArgumentException e) {
            log.warn("Unknown task type {}, using default rate limiter", taskType);
            return "finaleGetProduct"; // Default fallback
        }
    }
}
