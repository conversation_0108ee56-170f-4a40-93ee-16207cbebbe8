package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus.PENDING;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailImsUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailImsUpdatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemAdjustmentRequestDetailApplicationEventListener {

    private final ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    private final ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;

    @EventListener
    public void handleItemAdjustmentRequestDetailCreatedApplicationEvent(ItemAdjustmentRequestDetailCreatedApplicationEvent requestDetailCreatedApplicationEvent) {
        ItemAdjustmentRequestDetailCreatedPayloadDto requestDetailCreatedApplicationEventPayload = requestDetailCreatedApplicationEvent.getPayload();
        ItemAdjustmentRequestDetailDto requestDetailDto = requestDetailCreatedApplicationEventPayload.getData();
        log.info("handleItemAdjustmentRequestDetailCreatedApplicationEvent for request={}",
            requestDetailCreatedApplicationEventPayload);
        try {
            if (requestDetailDto.getStatus() == PENDING) {
                itemAdjustmentRequestDetailApplicationService.updateImsAsItemAdjustmentRequest(requestDetailDto);
            }
        } catch (Exception e) {
            log.error("handleItemAdjustmentRequestDetailCreatedApplicationEvent error for request={}",
                requestDetailCreatedApplicationEventPayload,
                e);
            itemAdjustmentRequestApplicationService.processedFailure(requestDetailDto.getRequestId(),
                "Failed to process request, please contact the engineer.");
        }
    }

    @EventListener
    public void handleItemAdjustmentRequestDetailImsUpdatedApplicationEvent(ItemAdjustmentRequestDetailImsUpdatedApplicationEvent requestDetailImsUpdatedApplicationEvent) {
        ItemAdjustmentRequestDetailImsUpdatedPayloadDto requestDetailImsUpdatedPayloadDto = requestDetailImsUpdatedApplicationEvent.getPayload();
        ItemAdjustmentRequestDetailDto requestDetailDto = requestDetailImsUpdatedPayloadDto.getData();

        itemAdjustmentRequestApplicationService.checkAndComplete(requestDetailDto.getRequestId());
        log.info("handleItemAdjustmentRequestDetailImsUpdatedApplicationEvent for request={}", requestDetailImsUpdatedPayloadDto);
    }

    @EventListener
    public void handleItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent(
        ItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent event) {
        ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto payload = event.getPayload();
        ItemAdjustmentRequestDetailDto requestDetailDto = payload.getData();

        itemAdjustmentRequestApplicationService.checkAndComplete(requestDetailDto.getRequestId());
        log.info("handleItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent for request={}", payload);
    }


}
