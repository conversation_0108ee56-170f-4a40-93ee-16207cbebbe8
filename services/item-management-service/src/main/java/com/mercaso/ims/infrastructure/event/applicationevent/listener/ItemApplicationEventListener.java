package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_EXCEPTION_RECORD;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemBoundToPriceGroupApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemBoundToPriceGroupPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.service.*;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceSpecification;
import java.util.List;
import java.util.UUID;

import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class ItemApplicationEventListener {

    private final ShopifyApplicationService shopifyApplicationService;
    private final ItemPriceApplicationService itemPriceApplicationService;
    private final ItemRegPriceSpecification itemRegPriceSpecification;
    private final ExceptionRecordApplicationService exceptionRecordApplicationService;
    private final FeatureFlagsManager featureFlagsManager;
    private final AsyncItemVersionService asyncItemVersionService;
    private final FinaleApplicationService finaleApplicationService;
    private final ItemPromoPriceService itemPromoPriceService;
    private final VendorItemService vendorItemService;


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleItemCreatedApplicationEvent(ItemCreatedApplicationEvent itemCreatedApplicationEvent) {

        ItemCreatedPayloadDto itemCreatedPayloadDto = itemCreatedApplicationEvent.getPayload();

        createItemVersion(itemCreatedPayloadDto.getData());

        shopifyApplicationService.syncItemCreatedEvent(itemCreatedApplicationEvent);

        checkPriceException(itemCreatedPayloadDto.getData().getItemRegPrice(), itemCreatedApplicationEvent.getBusinessEventId());

        finaleApplicationService.syncItemToFinaleById(itemCreatedPayloadDto.getData().getId());

        log.info("handleItemCreatedApplicationEvent for request ={}", itemCreatedPayloadDto);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleItemAmendApplicationEvent(ItemAmendApplicationEvent itemAmendApplicationEvent) {
        ItemAmendPayloadDto itemAmendPayloadDto = itemAmendApplicationEvent.getPayload();

        createItemVersion(itemAmendPayloadDto.getData().getCurrent());

        shopifyApplicationService.syncItemAmendEvent(itemAmendApplicationEvent);

        if (itemAmendPayloadDto.isPriceChanged()) {
            checkPriceException(itemAmendPayloadDto.getData().getCurrent().getItemRegPrice(),
                itemAmendApplicationEvent.getBusinessEventId());
        }
        if (itemAmendPayloadDto.isPrimaryVendorChanged() || itemAmendPayloadDto.isStatusChanged()) {
            finaleApplicationService.syncItemToFinaleById(itemAmendPayloadDto.getCurrent().getId());
        }

        log.info("handleItemAmendApplicationEvent for request ={}", itemAmendPayloadDto);
    }

    @EventListener
    public void handleItemDeletedApplicationEvent(ItemDeletedApplicationEvent itemDeletedApplicationEvent) {
        ItemDeletedPayloadDto itemDeletedPayloadDto = itemDeletedApplicationEvent.getPayload();
        ItemDto itemDto = itemDeletedPayloadDto.getData();
        if (itemDto == null) {
            log.warn("ItemDto is null in handleItemDeletedApplicationEvent. Payload: {}", itemDeletedPayloadDto);
            return;
        }

        cleanupItemRelatedData(itemDto);

        shopifyApplicationService.syncItemDeleteEvent(itemDeletedApplicationEvent);

        log.info("handleItemDeletedApplicationEvent for item id ={}", itemDto.getId());
    }


    @EventListener
    public void handleItemBoundToPriceGroupApplicationEvent(ItemBoundToPriceGroupApplicationEvent itemBoundToPriceGroupApplicationEvent) {
        ItemBoundToPriceGroupPayloadDto payload = itemBoundToPriceGroupApplicationEvent.getPayload();
        UpdateItemRegPriceCommand command = new UpdateItemRegPriceCommand(payload.getItemId(), payload.getPrice());

        List<UpdateItemRegPriceCommand> commands = List.of(command);
        itemPriceApplicationService.batchUpdateRegPrice(commands);

        log.info("handleItemBoundToPriceGroupApplicationEvent for payload ={}", payload);
    }

    private void cleanupItemRelatedData(ItemDto itemDto) {
        cleanupItemPrices(itemDto);
        cleanupVendorItems(itemDto);
        cleanupPromoPrices(itemDto);
    }

    private void cleanupItemPrices(ItemDto itemDto) {
        ItemRegPriceDto itemRegPrice = itemDto.getItemRegPrice();
        if (itemRegPrice == null) {
            return;
        }

        if (itemRegPrice.getItemPriceGroupId() != null) {
            log.info("[handleItemDeletedApplicationEvent] item reg price group id {} found for item {}, unbinding it.", itemRegPrice.getItemPriceGroupId(), itemDto.getId());
            try {
                itemPriceApplicationService.unbindingItemPriceGroup(itemDto.getId(), itemRegPrice.getItemPriceGroupId());
            } catch (Exception e) {
                log.error("Failed to unbind item price group for item {}", itemDto.getId(), e);
            }
        }
        try {
            log.info("[handleItemDeletedApplicationEvent] deleting item reg price for item {}", itemDto.getId());
            itemPriceApplicationService.deleteByItemId(itemDto.getId());
        } catch (Exception e) {
            log.error("Failed to delete item reg price for item {}", itemDto.getId(), e);
        }
    }

    private void cleanupVendorItems(ItemDto itemDto) {
        if (CollectionUtils.isEmpty(itemDto.getVendorItemDtos())) {
            return;
        }
        itemDto.getVendorItemDtos().forEach(vendorItemDto -> {
            try {
                log.info("[handleItemDeletedApplicationEvent] deleting vendor item {} for item {}", vendorItemDto.getVendorItemId(), itemDto.getId());
                vendorItemService.delete(vendorItemDto.getVendorItemId());
            } catch (Exception e) {
                log.error("Failed to delete vendor item {} for item {}", vendorItemDto.getVendorItemId(), itemDto.getId(), e);
            }
        });
    }

    private void cleanupPromoPrices(ItemDto itemDto) {
        if (CollectionUtils.isEmpty(itemDto.getItemPromoPrices())) {
            return;
        }
        itemDto.getItemPromoPrices().forEach(itemPromoPriceDto -> {
            try {
                log.info("[handleItemDeletedApplicationEvent] deleting promo price {} for item {}", itemPromoPriceDto.getItemPromoPriceId(), itemDto.getId());
                itemPromoPriceService.delete(itemPromoPriceDto.getItemPromoPriceId());
            } catch (Exception e) {
                log.error("Failed to delete promo price {} for item {}", itemPromoPriceDto.getItemPromoPriceId(), itemDto.getId(), e);
            }
        });
    }


    private void checkPriceException(ItemRegPriceDto itemRegPrice, UUID eventId) {
        if (!featureFlagsManager.isFeatureOn(IMS_EXCEPTION_RECORD)) {
            return;
        }
        String exception = itemRegPriceSpecification.getExceptionMessage(itemRegPrice);

        if (StringUtils.isNotBlank(exception)) {
            exceptionRecordApplicationService.create(CreateExceptionRecordCommand.builder()
                .businessEventId(eventId)
                .entityId(itemRegPrice.getItemId())
                .entityType(EntityType.ITEM)
                .exceptionType(ExceptionRecordType.PRICE_EXCEPTION)
                .description(exception)
                .build());
        }
    }

    private void createItemVersion(ItemDto itemDto) {
        log.info("begin createItemVersion for id={}", itemDto.getId());
        asyncItemVersionService.asyncSaveItemVersion(itemDto.getId());
    }

}
