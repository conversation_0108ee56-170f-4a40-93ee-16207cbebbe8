package com.mercaso.ims.application.service.impl;

import static com.mercaso.document.operations.constants.CommonSymbols.SLASH;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.UPLOAD_FILE_ERROR;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.FileUtil;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentApplicationServiceImpl implements DocumentApplicationService {

    private static final String DOCUMENTS_PATH = "documents";
    public static final String DATE_FORMAT_17 = "yyyyMMddHHmmssSSS";
    private final DocumentOperations documentOperations;
    private final Environment environment;
    @Value("${ims.document_url}")
    private String imsDocumentUrl;

    @Override
    public String getImsUrl(String documentName) {
        String url = imsDocumentUrl.replace("{env}", getActiveProfile());
        url += documentName;
        return url;
    }

    @Override
    @ReportMetric(metricsType = MetricsTypeEnum.GET_ITEM_VALID_URL)
    public void downloadDoc(HttpServletResponse httpResponse, String documentName) {
        try {
            byte[] docData = getDocData(documentName);
            if (docData == null) {
                log.error("[getValidURL] Document not found.");
                return;
            }
            Tika tika = new Tika();
            String docType = tika.detect(docData);
            httpResponse.setContentType(docType);
            httpResponse.setCharacterEncoding("utf-8");
            httpResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + documentName);
            httpResponse.setContentLength(docData.length);
            OutputStream os = httpResponse.getOutputStream();
            os.write(docData);
            os.flush();
        } catch (IOException ioe) {
            log.warn("[downloadDoc] documentName:{} error {}.", documentName, ioe.getMessage(), ioe);
        } catch (Exception e) {
            log.error("[downloadDoc] error. :{}", documentName, e);
            throw new ImsBusinessException("Download file error.", e);
        }

    }

    @Override
    public DocumentResponse uploadImage(byte[] content, String imageName) {
        UploadDocumentRequest document = UploadDocumentRequest.builder()
            .content(content)
            .documentName(imageName)
            .build();

        return documentOperations.uploadDocument(document);
    }

    @Override
    public DocumentResponse uploadExcel(byte[] content, String excelName) {
        UploadDocumentRequest document = UploadDocumentRequest.builder()
            .content(content)
            .documentName(DOCUMENTS_PATH + SLASH + excelName)
            .build();

        return documentOperations.uploadDocument(document);
    }

    @Override
    public String getSignedUrl(String documentName) {
        return documentOperations.getSignedUrl(documentName);
    }

    @Override
    public DocumentResponse uploadFile(MultipartFile file, String fileName, boolean isImage) {
        try {
            return uploadFileContent(file.getBytes(), String.valueOf(fileName.hashCode()), isImage);
        } catch (Exception e) {
            log.error("[uploadFile] error: {}", e.getMessage(), e);
            throw new ImsBusinessException(UPLOAD_FILE_ERROR);
        }
    }

    @Override
    public DocumentResponse uploadFileContent(byte[] content, String fileName, boolean isImage) {

        String fileExtension = FileUtil.getFileExtension(content);
        if (StringUtils.isBlank(fileExtension)) {
            log.error("[uploadFile] Unable to get format fileName: {}", fileName);
            return null;
        }

        return uploadFileContent(content, fileName, fileExtension, isImage);
    }

    @Override
    public DocumentResponse uploadFileContent(byte[] content, String fileName, String fileExtension, boolean isImage) {
        try {
            String filename = fileName
                .replaceAll(fileExtension, "")
                .concat("-")
                .concat(DateUtils.format(new Date(), DATE_FORMAT_17))
                .concat(fileExtension);

            UploadDocumentRequest document = UploadDocumentRequest.builder()
                .content(content)
                .documentName(isImage ? filename : DOCUMENTS_PATH + SLASH + filename)
                .build();

            return documentOperations.uploadDocument(document);
        } catch (Exception e) {
            log.error("[uploadFile] error: {}", e.getMessage(), e);
            throw new ImsBusinessException(UPLOAD_FILE_ERROR);
        }
    }

    @Override
    public byte[] downloadDocument(String documentName) {
        return documentOperations.downloadDocument(documentName);
    }

    @Override
    public String getTimelinessUrl(String documentName) {
        return documentOperations.getSignedUrl(documentName);
    }

    public String getActiveProfile() {
        return environment.getProperty("spring.profiles.active");
    }


    private byte[] getDocData(String documentName) {
        try {
            return documentOperations.downloadDocument(documentName);
        } catch (Exception e) {
            log.warn("getDocument: Error getting document: {}", documentName);
            return documentOperations.downloadDocument("coming_soon.jpg");

        }
    }
}
