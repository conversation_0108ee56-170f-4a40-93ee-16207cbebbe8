package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import lombok.*;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentRequestDetailCreatedPayloadDto extends BusinessEventPayloadDto<ItemAdjustmentRequestDetailDto> {

    private UUID itemAdjustmentRequestDetailId;

    @Builder
    public ItemAdjustmentRequestDetailCreatedPayloadDto(ItemAdjustmentRequestDetailDto data, UUID itemAdjustmentRequestDetailId) {
        super(data);
        this.itemAdjustmentRequestDetailId = itemAdjustmentRequestDetailId;
    }
}