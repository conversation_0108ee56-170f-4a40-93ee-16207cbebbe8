package com.mercaso.ims.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinaleAllSuppliersResponseDto {

    private List<String> partyId;
    private List<String> partyUrl;
    private List<String> statusId;
    private List<String> lastUpdatedDate;
    private List<String> createdDate;
    private List<List<String>> roleTypeIdList;
    private List<List<Object>> glAccountList;
    private List<List<Object>> contentList;
    private List<List<Object>> userFieldDataList;
    private List<List<Object>> connectionRelationUrlList;
    private List<List<Object>> productStoreUrlList;

    /**
     * Converts the array-based response to a List of FinaleVendorInfoDto objects
     * @return List of FinaleVendorInfoDto with populated basic information
     */
    public List<FinaleVendorInfoDto> toFinaleVendorInfoDtoList() {
        if (partyId == null || partyId.isEmpty()) {
            return new ArrayList<>();
        }

        List<FinaleVendorInfoDto> result = new ArrayList<>();
        int size = partyId.size();

        for (int i = 0; i < size; i++) {
            FinaleVendorInfoDto vendorInfo = FinaleVendorInfoDto.builder()
                .partyId(getValueAtIndex(partyId, i))
                .partyUrl(getValueAtIndex(partyUrl, i))
                .statusId(getValueAtIndex(statusId, i))
                .lastUpdatedDate(getValueAtIndex(lastUpdatedDate, i))
                .createdDate(getValueAtIndex(createdDate, i))
                .roleTypeIdList(getListValueAtIndex(roleTypeIdList, i))
                .glAccountList(getListValueAtIndex(glAccountList, i))
                .contentList(getListValueAtIndex(contentList, i))
                .userFieldDataList(convertUserFieldDataList(getListValueAtIndex(userFieldDataList, i)))
                .connectionRelationUrlList(getListValueAtIndex(connectionRelationUrlList, i))
                .productStoreUrlList(getListValueAtIndex(productStoreUrlList, i))
                .build();

            result.add(vendorInfo);
        }

        return result;
    }

    /**
     * Safely gets a value from a list at the specified index
     */
    private String getValueAtIndex(List<String> list, int index) {
        if (list == null || index >= list.size()) {
            return null;
        }
        return list.get(index);
    }

    /**
     * Safely gets a list value from a list of lists at the specified index
     */
    private <T> List<T> getListValueAtIndex(List<List<T>> listOfLists, int index) {
        if (listOfLists == null || index >= listOfLists.size()) {
            return new ArrayList<>();
        }
        List<T> value = listOfLists.get(index);
        return value != null ? value : new ArrayList<>();
    }

    /**
     * Converts a list of objects to UserFieldDataDto list
     * This is a placeholder implementation - you may need to adjust based on actual data structure
     */
    private List<FinaleVendorInfoDto.UserFieldDataDto> convertUserFieldDataList(List<Object> userFieldData) {
        if (userFieldData == null || userFieldData.isEmpty()) {
            return new ArrayList<>();
        }
        
        // This is a basic implementation - you may need to adjust based on the actual structure
        // of userFieldData objects returned by the API
        List<FinaleVendorInfoDto.UserFieldDataDto> result = new ArrayList<>();
        for (Object obj : userFieldData) {
            if (obj instanceof FinaleVendorInfoDto.UserFieldDataDto) {
                result.add((FinaleVendorInfoDto.UserFieldDataDto) obj);
            }
            // Add more conversion logic here if needed based on actual data structure
        }
        
        return result;
    }
}
