package com.mercaso.ims.infrastructure.statemachine;


import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.ims.infrastructure.util.SpringContextUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public abstract class BaseStateMachine<T extends StatefulContext<S>, S extends StateType, E extends StateTransitionType> extends
        BaseDomain implements StatefulContext<S> {

    private S status;

    @Override
    public S getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(S status) {
        this.status = status;
    }

    @SuppressWarnings("unchecked")
    public void processEvent(E event) {
        StateMachineProcessor<T, S, E> stateMachineProcessor = SpringContextUtil.getBean(StateMachineProcessor.class);
        stateMachineProcessor.processEvent((T) this, event);
    }


}
