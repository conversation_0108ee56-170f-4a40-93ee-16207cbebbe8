package com.mercaso.ims.domain.itemadjustmentrequestdetail.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum ItemAdjustmentType {
    CREATE,
    UPDATE,
    DELETE,
    CLEAN_UPC,
    CREATE_VENDOR,
    CREATE_REBATE,
    UPDATE_REBATE,
    DELETE_REBATE,
    UNKNOWN,
    ;

    @JsonCreator
    public static ItemAdjustmentType fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }
}
