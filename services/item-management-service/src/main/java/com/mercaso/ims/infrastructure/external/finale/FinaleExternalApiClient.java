package com.mercaso.ims.infrastructure.external.finale;

import static org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION;
import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.CreateFinaleItemRequestDto;
import com.mercaso.ims.infrastructure.external.finale.dto.CreateVendorRequestDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleAllSuppliersResponseDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleCompletedPurchaseOrderReportDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleProductDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinalePurchaseOrderDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinaleVendorsResultDto;
import com.mercaso.ims.infrastructure.external.finale.dto.UpdateSupplierItemRequestDto;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.PathItem.HttpMethod;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class FinaleExternalApiClient {

    private static final String CREATE_VENDOR_STATUS_ID = "PARTY_ENABLED";
    private static final String APPLICATION_JSON = "application/json";
    private final HttpClient client;
    @Value("${finale.create_vendor_url}")
    private String createVendorUrl;
    @Value("${finale.graphql_url}")
    private String graphqlUrl;
    @Value("${finale.query_vendors_url}")
    private String queryVendorUrl;
    @Value("${finale.get_all_suppliers_url}")
    private String getAllSuppliersUrl;
    @Value("${finale.product_url}")
    private String productUrl;
    @Value("${finale.get_purchase_order_url}")
    private String getPurchaseOrderUrl;
    @Value("${finale.update_vendor_url}")
    private String updateVendorUrl;
    @Value("${finale.completed_purchase_orders_report_url}")
    private String completedPurchaseOrdersReportUrl;


    @Value("${finale.token}")
    private String token;

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_VENDORS,
        endpoint = "queryFinaleVendorById",
        synchronous = true
    )
    public FinaleVendorInfoDto queryFinaleVendorById(String id) {
        try {
            Request request = new Request.Builder()
                .url(queryVendorUrl + id)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            if (!response.isSuccessful()) {
                log.error("[querySupplierById] Failed to query supplier by id: {}", id);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, FinaleVendorInfoDto.class);
        } catch (IOException e) {
            log.error("[querySupplierById] Failed to query supplier by id: {}", id, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
        }
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_VENDORS,
        endpoint = "queryFinaleVendors",
        synchronous = true
    )
    public QueryFinaleVendorsResultDto queryFinaleVendors() {
        try {
            Request request = new Request.Builder()
                .url(queryVendorUrl)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[queryFinaleVendors] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[queryFinaleVendors] Failed to query Vendors from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, QueryFinaleVendorsResultDto.class);
        } catch (IOException e) {
            log.error("[queryFinaleVendors] Failed to query Vendors from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
        }
    }

    /**
     * Get all suppliers from Finale API
     * Calls the partygroup API endpoint to retrieve all supplier information
     *
     * @return List of FinaleVendorInfoDto containing all supplier information
     */
    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_VENDORS,
        endpoint = "getAllSuppliers",
        synchronous = true
    )
    public List<FinaleVendorInfoDto> getAllSuppliers() {
        try {
            Request request = new Request.Builder()
                .url(getAllSuppliersUrl)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            Response response = client.execute(request);
            if (!response.isSuccessful()) {
                log.error("[getAllSuppliers] Failed to get all suppliers, response code: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            log.debug("[getAllSuppliers] Response body: {}", responseBody);

            FinaleAllSuppliersResponseDto responseDto = SerializationUtils.deserialize(responseBody, FinaleAllSuppliersResponseDto.class);
            return responseDto.toFinaleVendorInfoDtoList();

        } catch (IOException e) {
            log.error("[getAllSuppliers] Failed to get all suppliers from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
        }
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_CREATE_VENDOR,
        endpoint = "createVendor",
        httpMethod = "POST",
        synchronous = true
    )
    public FinaleVendorDto createVendor(String vendorName) {
        CreateVendorRequestDto createVendorRequestDto = buildCreateVendorRequestDto(vendorName);
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(createVendorRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(createVendorUrl)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("[createVendor] Started, request body: {}", jsonBody);
            response = client.execute(request);
            if (!response.isSuccessful()) {
                log.error("[createVendor] Failed to create vendor for name: {}", vendorName);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[createVendor] end, responseBody: {} ", responseBody);
            return SerializationUtils.deserialize(responseBody, FinaleVendorDto.class);
        } catch (IOException e) {
            log.error("[createVendor]  Failed to create vendor for name: {}", vendorName, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_UPDATE_VENDOR,
        endpoint = "updateVendor",
        httpMethod = "POST"
    )
    public void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto) {
        String partyId = finaleVendorInfoDto.getPartyId();
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(finaleVendorInfoDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(updateVendorUrl + partyId)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("[updateVendor] Started, request body: {}", jsonBody);
            response = client.execute(request);
            if (!response.isSuccessful()) {
                log.error("[updateVendor] Failed to update vendor for partyId: {}", partyId);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[updateVendor] end, responseBody: {} ", responseBody);
        } catch (IOException e) {
            log.error("[updateVendor]  Failed to update vendor for partyId: {}", partyId, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_FAILED);
        }
    }


    @RateLimitedTask(
        taskType = TaskType.FINALE_UPDATE_VENDOR_ITEM,
        endpoint = "updateVendorItem",
        httpMethod = "POST",
        needsResponse = false
    )
    public void updateVendorItem(String skuNumber, String status, UpdateSupplierItemRequestDto updateSupplierItemRequestDto) {
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(updateSupplierItemRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(productUrl + skuNumber)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            log.info("[updateVendorItem] Started, request body: {}", jsonBody);
            response = client.execute(request);
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    log.warn("[updateVendorItem] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "updateVendorItem");
                }

                log.error("[updateVendorItem] Failed to update vendor item for skuNumber: {}", skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED);

            } else {
                log.info("[updateVendorItem] end, responseBody: {} ", response.body().string());
            }
        } catch (IOException e) {
            log.error("[updateVendorItem] Failed to update vendor item for skuNumber: {}", skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_PURCHASE_ORDERS,
        endpoint = "queryPurchaseOrders",
        synchronous = true,
        httpMethod = "POST"
    )
    public QueryFinalePurchaseOrderResultDto queryPurchaseOrders(int length) {
        try {
            String json =
                """
                    {
                        "operationName": "Rows",
                        "variables": {
                            "after": null,
                            "first": 100
                        },
                        "query": "query Rows($after: String, $first: Int) {\\n  orderViewConnection(type: [\\"PURCHASE_ORDER\\"],orderDate: {duration: \\"day\\", offset: -40, length: 41, timezone: \\"America/Los_Angeles\\"}, status: [\\"ORDER_COMPLETED\\"], after: $after, first: $first, sort: [{field: \\"orderDate\\", mode: \\"desc\\"}]) {\\n    summary {\\n      metrics {\\n        count\\n      }\\n    }\\n    edges {\\n      node(timezone: \\"America/Los_Angeles\\") {\\n        orderUrl\\n        fulfillment\\n        orderDateFormatted: orderDate\\n        orderDestination: destination {\\n          name\\n        }\\n        orderId        partySupplierGroupName: supplier {\\n          partyId\\n      name\\n       }\\n        receiveDateFormatted: receiveDate\\n        shipmentsFormatted: shipmentsSummary\\n        statusIdFormatted: status\\n        totalFormatted: total\\n      }\\n    }\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n    strategy\\n  }\\n}\\n"
                    }
                    """;
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            String jsonWithLength = json.replace("41", String.valueOf(length));
            RequestBody body = RequestBody.create(jsonWithLength, mediaType);
            Request request = new Request.Builder()
                .url(graphqlUrl)
                .method(HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[queryPurchaseOrders] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[queryPurchaseOrders] Failed to get Purchase Order from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, QueryFinalePurchaseOrderResultDto.class);
        } catch (IOException e) {
            log.error("[queryPurchaseOrders] Failed to query Purchase Orders from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_PURCHASE_ORDER,
        endpoint = "getPurchaseOrder",
        synchronous = true
    )
    public FinalePurchaseOrderDto getPurchaseOrder(String orderId) {
        try {

            Request request = new Request.Builder()
                .url(getPurchaseOrderUrl + orderId)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[getPurchaseOrder] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[getPurchaseOrder] Failed to get  Purchase Order from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, FinalePurchaseOrderDto.class);
        } catch (IOException e) {
            log.error("[queryPurchaseOrders] Failed to query Purchase Order for :{}", orderId, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }

    @RateLimitedTask(
        taskType = TaskType.FINALE_CREATE_ITEM,
        endpoint = "createFinaleItem",
        httpMethod = "POST",
        needsResponse = false
    )
    public void createFinaleItem(String skuNumber, String statusId) {
        executeCreateFinaleItem(skuNumber, statusId, "createFinaleItem");
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 2, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_CHECK_ITEM_UNIQUE,
        endpoint = "skuIsUnique",
        httpMethod = "POST",
        synchronous = true,
        needsResponse = false
    )
    public boolean skuIsUnique(String skuNumber) {
        try {
            String json = String.format("""
                    {
                        "operationName": "IdIsUnique",
                        "query": "query IdIsUnique($id: [String]) { productViewConnection(productId: $id) { summary { metrics { count } } } }",
                        "variables": {
                            "id": ["%s"]
                        },
                        "sessionSecret": "itvBqZxEcddB9yLseXj8"
                    }
                """, skuNumber);

            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(json, mediaType);
            Request request = new Request.Builder()
                .url(graphqlUrl)
                .method(HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            Response response = client.execute(request);

            if (!response.isSuccessful()) {
                log.error("[skuIsUnique] Failed to query SKU from GraphQL, status: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED);
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[skuIsUnique] Response body: {}", responseBody);

            JsonNode rootNode = new ObjectMapper().readTree(responseBody);
            JsonNode countNode = rootNode.path("data")
                .path("productViewConnection")
                .path("summary")
                .path("metrics")
                .path("count");

            if (!countNode.isArray() || countNode.isEmpty()) {
                log.warn("[skuIsUnique] Invalid or missing count array in response.");
                return true;
            }

            int count = countNode.get(0).asInt(0);
            return count == 0;

        } catch (Exception e) {
            log.error("[skuIsUnique] Exception occurred when querying SKU uniqueness", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED, e);
        }
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_PRODUCT,
        endpoint = "getFinaleProduct",
        synchronous = true
    )
    public FinaleProductDto getFinaleProduct(String skuNumber, String status) {
        try {
            Request request = new Request.Builder()
                .url(productUrl + skuNumber)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            if (!response.isSuccessful()) {

                if (response.code() == 404) {
                    log.warn("[getFinaleProduct] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "getFinaleProduct");
                }

                log.warn("[getFinaleProduct] Failed to get product from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("getFinaleProduct response body : {}", responseBody);
            return SerializationUtils.deserialize(responseBody, FinaleProductDto.class);
        } catch (IOException e) {
            log.error("[getFinaleProduct] Failed to get product from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_UPDATE_ITEM,
        endpoint = "updateItemStatus",
        httpMethod = "POST",
        needsResponse = false
    )
    public void updateItemStatus(String skuNumber, String status) {
        Response response;
        try {
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create("", mediaType);
            Request request = new Request.Builder()
                .url(productUrl + skuNumber + "/" + status)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            log.info("[updateItemStatus] Started, change sku :{} status to: {}", skuNumber, status);
            response = client.execute(request);
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    log.warn("[updateItemStatus] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "updateItemStatus");
                }

                log.error("[updateItemStatus] Failed to update item status for skuNumber: {}", skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED);

            } else {
                log.info("[updateItemStatus] end, responseBody: {} ", response.body().string());
            }

        } catch (IOException e) {
            log.error("[updateItemStatus] Failed to update item status for skuNumber: {}", skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED);
        }
    }

    private CreateVendorRequestDto buildCreateVendorRequestDto(String vendorName) {
        return CreateVendorRequestDto.builder()
            .groupName(vendorName)
            .statusId(CREATE_VENDOR_STATUS_ID)
            .roleTypeIdList(Collections.singletonList("SUPPLIER"))
            .build();
    }

    /**
     * Internal method for handling item creation for status without rate limiting annotation Used by processors to avoid infinite
     * loop
     */
    private void handleItemCreationForStatusInternal(String skuNumber, String status, String methodName) {
        switch (status) {
            case "activate":
                executeCreateFinaleItem(skuNumber, StatusId.PRODUCT_ACTIVE.name(), methodName);
                break;
            case "deactivate":
                executeCreateFinaleItem(skuNumber, StatusId.PRODUCT_INACTIVE.name(), methodName);
                break;
            default:
                log.warn("[updateItemStatus] Unsupported status '{}' for creating item, skipping item creation for skuNumber: {}",
                    status,
                    skuNumber);
                break;
        }
    }

    private void executeCreateFinaleItem(String skuNumber, String statusId, String methodName) {
        try {
            CreateFinaleItemRequestDto createFinaleItemRequestDto = new CreateFinaleItemRequestDto(skuNumber, statusId);
            String jsonBody = SerializationUtils.serialize(createFinaleItemRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(productUrl)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("[{}] Started, request body: {}", methodName, jsonBody);
            Response response = client.execute(request);
            if (!response.isSuccessful()) {
                log.error("[{}] Failed to creat finale item for skuNumber: {}", methodName, skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED);
            }
            assert response.body() != null;
            log.info("[{}] end, responseBody: {} ", methodName, response.body().string());
        } catch (IOException e) {
            log.error("[{}] Failed to creat finale item for skuNumber:  :{}", methodName, skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED);
        }
    }

    /**
     * Get completed purchase orders report from Finale This method calls the pivot table report API to get recently completed
     * purchase orders
     *
     * @return List of completed purchase order items
     */
    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
        taskType = TaskType.FINALE_GET_COMPLETED_PURCHASE_ORDERS_REPORT,
        endpoint = "getCompletedPurchaseOrdersReport",
        synchronous = true
    )
    public List<FinaleCompletedPurchaseOrderReportDto> getCompletedPurchaseOrdersReport() {
        try {
            // Build the URL with all the query parameters from the curl command
            String url = completedPurchaseOrdersReportUrl +
                "?format=jsonObject" +
                "&data=orderItem" +
                "&attrName=%23%23user061" +
                "&rowDimensions=~3AAWmszWwMz-wMDAwMDAwJrNAQXAzP7AwMDAwMDAmszVwMz-wMDAwMDAwJq6b3JkZXJVc2VyUHVyY2hhc2VVc2VyMTAwMDHAzP7AwMDAwMDAmrpvcmRlclVzZXJQdXJjaGFzZVVzZXIxMDAwMsDM_sDAwMDAwMCazN7AzP7AwMDAwMDAmrpvcmRlclJlY29yZExhc3RVcGRhdGVkVXNlcsDM_sDAwMDAwMCav29yZGVyUmVjb3JkTGFzdFVwZGF0ZWRUaW1lc3RhbXDAzP7AwMDAwMDAmqxvcmRlckR1ZURhdGXAzP7AwMDAwMDAmsy5wMz-wMDAwMDAwJq0b3JkZXJTdXBwbGllclBhcnR5SWTAzP7AwMDAwMDAms0BBsDM_sDAwMDAwMCazQH-wMz-wMDAwMDAwJrNAdTAzP7AwMDAwMDAmrpvcmRlckl0ZW1TdXBwbGllclByb2R1Y3RJZMDM_sDAwMDAwMCazMjAzP7AwMDAwMDAmszJwMz-wMDAwMDAwJqxb3JkZXJJdGVtU3VidG90YWzAzP7AwMDAwMDAmrxvcmRlckl0ZW1Qcm9kdWN0VW5pdHNPcmRlcmVkwMz-wMDAwMDAwJq1b3JkZXJTaGlwbWVudHNTdW1tYXJ5wMz-wMDAwMDAwJq7b3JkZXJTaGlwbWVudHNTdGF0dXNTdW1tYXJ5wMz-wMDAwMDAwJrNAQ3AzP7AwMDAwMDA"
                +
                "&filters=W1sib3JkZXJTdGF0dXMiLFsiT1JERVJfQ09NUExFVEVEIl0sbnVsbF0sWyJvcmRlclJlY29yZExhc3RVcGRhdGVkIix7ImR1cmF0aW9uIjoiZGF5Iiwib2Zmc2V0IjowLCJsZW5ndGgiOjEsInRpbWV6b25lIjoiQXNpYS9TaGFuZ2hhaSJ9LG51bGxdLFsib3JkZXJUeXBlIixbIlBVUkNIQVNFX09SREVSIl0sbnVsbF1d"
                +
                "&reportTitle=%5BIMS%5D%20Recently%20completed%20PO";

            Request request = new Request.Builder()
                .url(url)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("[getCompletedPurchaseOrdersReport] Started");
            Response response = client.execute(request);

            if (!response.isSuccessful()) {
                log.error("[getCompletedPurchaseOrdersReport] Failed to get completed purchase orders report from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[getCompletedPurchaseOrdersReport] Response body: {}", responseBody);

            return SerializationUtils.readValue(responseBody,
                new TypeReference<List<FinaleCompletedPurchaseOrderReportDto>>() {
                });

        } catch (IOException e) {
            log.error("[getCompletedPurchaseOrdersReport] Failed to get completed purchase orders report from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }
}

