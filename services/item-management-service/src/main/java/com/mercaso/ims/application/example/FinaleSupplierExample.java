package com.mercaso.ims.application.example;

import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Example class demonstrating how to use the new getAllSuppliers functionality
 * This class shows how to call the Finale API to get all supplier information
 * and convert it to a List<FinaleVendorInfoDto>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FinaleSupplierExample {

    private final FinaleApplicationService finaleApplicationService;

    /**
     * Example method showing how to get all suppliers from Finale API
     * 
     * This method demonstrates:
     * 1. Calling the Finale API endpoint: https://app.finaleinventory.com/mercaso/api/partygroup
     * 2. Converting the array-based response to List<FinaleVendorInfoDto>
     * 3. Processing the supplier information
     * 
     * @return List of all suppliers from Finale
     */
    public List<FinaleVendorInfoDto> getAllSuppliersExample() {
        try {
            log.info("Fetching all suppliers from Finale API...");
            
            // Call the new getAllSuppliers method
            List<FinaleVendorInfoDto> suppliers = finaleApplicationService.getAllSuppliers();
            
            log.info("Successfully retrieved {} suppliers from Finale", suppliers.size());
            
            // Example of processing the supplier data
            for (FinaleVendorInfoDto supplier : suppliers) {
                log.debug("Supplier ID: {}, URL: {}, Status: {}, Created: {}, Updated: {}", 
                    supplier.getPartyId(),
                    supplier.getPartyUrl(),
                    supplier.getStatusId(),
                    supplier.getCreatedDate(),
                    supplier.getLastUpdatedDate()
                );
            }
            
            return suppliers;
            
        } catch (Exception e) {
            log.error("Failed to retrieve suppliers from Finale API", e);
            throw e;
        }
    }

    /**
     * Example method showing how to filter suppliers by status
     * 
     * @param statusFilter The status to filter by (e.g., "PARTY_ENABLED")
     * @return List of suppliers with the specified status
     */
    public List<FinaleVendorInfoDto> getSuppliersByStatus(String statusFilter) {
        List<FinaleVendorInfoDto> allSuppliers = getAllSuppliersExample();
        
        return allSuppliers.stream()
            .filter(supplier -> statusFilter.equals(supplier.getStatusId()))
            .toList();
    }

    /**
     * Example method showing how to get supplier count by status
     * 
     * @return Count of enabled suppliers
     */
    public long getEnabledSuppliersCount() {
        List<FinaleVendorInfoDto> enabledSuppliers = getSuppliersByStatus("PARTY_ENABLED");
        return enabledSuppliers.size();
    }

    /**
     * Example method showing how to find a supplier by party ID
     * 
     * @param partyId The party ID to search for
     * @return The supplier with the specified party ID, or null if not found
     */
    public FinaleVendorInfoDto findSupplierByPartyId(String partyId) {
        List<FinaleVendorInfoDto> allSuppliers = getAllSuppliersExample();
        
        return allSuppliers.stream()
            .filter(supplier -> partyId.equals(supplier.getPartyId()))
            .findFirst()
            .orElse(null);
    }
}
