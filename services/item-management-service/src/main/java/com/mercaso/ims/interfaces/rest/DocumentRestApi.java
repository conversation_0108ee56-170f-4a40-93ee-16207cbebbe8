package com.mercaso.ims.interfaces.rest;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping(value = "/v1/document", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class DocumentRestApi {

    private final DocumentApplicationService documentApplicationService;


    @GetMapping(value = "/public-paths/{documentName}", produces = MediaType.ALL_VALUE)
    public void getValidURL(HttpServletResponse httpResponse, @PathVariable String documentName) {
        documentApplicationService.downloadDoc(httpResponse, documentName);
    }

    @GetMapping(value = {"/public-paths/", "/public-paths"}, produces = MediaType.ALL_VALUE)
    public void getDefaultValidURL(HttpServletResponse httpResponse) {
        log.info("[getDefaultValidURL] by user :{}", SecurityContextUtil.getUsername());
        documentApplicationService.downloadDoc(httpResponse, "coming_soon.jpg");
    }

    @GetMapping("/{documentName}")
    @Deprecated
    public void getDefaultPhotoURL(HttpServletResponse httpResponse, @PathVariable String documentName) {
        log.info("[getDefaultPhotoURL] param documentName: {}.", documentName);
        documentApplicationService.downloadDoc(httpResponse, documentName);
    }

    @GetMapping({"/", ""})
    @Deprecated
    public void getDefaultPhotoComingSoonURL(HttpServletResponse httpResponse) {
        log.info("[getDefaultPhotoComingSoonURL] by user :{}", SecurityContextUtil.getUsername());
        documentApplicationService.downloadDoc(httpResponse, "coming_soon.jpg");
    }

    @PostMapping("/upload/{fileName}")
    @PreAuthorize("hasAuthority('ims:write:documents')")
    public DocumentResponse uploadFile(@PathVariable("fileName") String fileName, @RequestParam("requestFile") MultipartFile file,
        @RequestParam(value = "isImage", required = false, defaultValue = "true") boolean isImage) {
        DocumentResponse documentResponse = documentApplicationService.uploadFile(file, fileName, isImage);
        log.info("[uploadFile] response documentResponse: {}.", documentResponse);
        return documentResponse;
    }


}
