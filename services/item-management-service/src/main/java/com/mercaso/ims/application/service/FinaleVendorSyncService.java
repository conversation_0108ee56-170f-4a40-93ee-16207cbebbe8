package com.mercaso.ims.application.service;

import com.mercaso.ims.application.converter.FinaleToVendorConverter;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.domain.address.service.AddressService;
import com.mercaso.ims.domain.email.service.EmailService;
import com.mercaso.ims.domain.phone.service.PhoneNumberService;
import com.mercaso.ims.domain.supplieradditionalinfo.service.SupplierAdditionalInfoService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Service for synchronizing Finale supplier data to local Vendor entities
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleVendorSyncService {

    private final FinaleApplicationService finaleApplicationService;
    private final FinaleToVendorConverter finaleToVendorConverter;
    private final VendorService vendorService;
    private final AddressService addressService;
    private final EmailService emailService;
    private final PhoneNumberService phoneNumberService;
    private final SupplierAdditionalInfoService supplierAdditionalInfoService;

    /**
     * Synchronize all suppliers from Finale to local Vendor table
     * 
     * @return SyncResult containing statistics about the sync operation
     */
    @Transactional
    public SyncResult syncAllSuppliersFromFinale() {
        log.info("Starting sync of all suppliers from Finale");
        
        SyncResult result = new SyncResult();
        
        try {
            // Get all suppliers from Finale
            List<FinaleVendorInfoDto> finaleSuppliers = finaleApplicationService.getAllSuppliers();
            log.info("Retrieved {} suppliers from Finale", finaleSuppliers.size());
            
            result.setTotalFinaleSuppliers(finaleSuppliers.size());
            
            // Process each supplier
            for (FinaleVendorInfoDto finaleSupplier : finaleSuppliers) {
                try {
                    SyncSupplierResult supplierResult = syncSingleSupplier(finaleSupplier);
                    updateSyncResult(result, supplierResult);
                } catch (Exception e) {
                    log.error("Failed to sync supplier with partyId: {}", finaleSupplier.getPartyId(), e);
                    result.incrementFailed();
                }
            }
            
            log.info("Completed sync of suppliers from Finale. Results: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("Failed to sync suppliers from Finale", e);
            result.setError(e.getMessage());
            throw e;
        }
    }

    /**
     * Synchronize a single supplier from Finale
     * 
     * @param finaleSupplier The Finale supplier data
     * @return SyncSupplierResult indicating what action was taken
     */
    @Transactional
    public SyncSupplierResult syncSingleSupplier(FinaleVendorInfoDto finaleSupplier) {
        if (finaleSupplier == null || StringUtils.isBlank(finaleSupplier.getPartyId())) {
            log.warn("Invalid supplier data provided");
            return SyncSupplierResult.SKIPPED;
        }

        log.debug("Syncing supplier with partyId: {}", finaleSupplier.getPartyId());

        try {
            // Check if vendor already exists by finaleId
            Vendor existingVendor = vendorService.findByFinaleId(finaleSupplier.getPartyId());
            
            if (existingVendor != null) {
                return updateExistingVendor(existingVendor, finaleSupplier);
            } else {
                return createNewVendor(finaleSupplier);
            }
            
        } catch (Exception e) {
            log.error("Failed to sync supplier with partyId: {}", finaleSupplier.getPartyId(), e);
            throw e;
        }
    }

    /**
     * Create a new vendor from Finale supplier data
     */
    private SyncSupplierResult createNewVendor(FinaleVendorInfoDto finaleSupplier) {
        log.debug("Creating new vendor for partyId: {}", finaleSupplier.getPartyId());
        
        // Convert Finale data to Vendor domain object
        Vendor newVendor = finaleToVendorConverter.convertToVendor(finaleSupplier);
        
        if (newVendor == null) {
            log.warn("Failed to convert Finale supplier to Vendor: {}", finaleSupplier.getPartyId());
            return SyncSupplierResult.SKIPPED;
        }

        // Check if vendor name already exists
        Vendor existingByName = vendorService.findByVendorName(newVendor.getVendorName());
        if (existingByName != null) {
            log.warn("Vendor with name '{}' already exists, skipping creation for partyId: {}", 
                newVendor.getVendorName(), finaleSupplier.getPartyId());
            return SyncSupplierResult.SKIPPED;
        }

        // Save the vendor
        Vendor savedVendor = vendorService.save(newVendor);
        
        // Save related entities
        saveRelatedEntities(savedVendor);
        
        log.info("Created new vendor: {} (partyId: {})", savedVendor.getVendorName(), finaleSupplier.getPartyId());
        return SyncSupplierResult.CREATED;
    }

    /**
     * Update existing vendor with Finale supplier data
     */
    private SyncSupplierResult updateExistingVendor(Vendor existingVendor, FinaleVendorInfoDto finaleSupplier) {
        log.debug("Updating existing vendor: {} (partyId: {})", existingVendor.getVendorName(), finaleSupplier.getPartyId());
        
        boolean hasChanges = false;
        
        // Update basic vendor information
        if (updateVendorBasicInfo(existingVendor, finaleSupplier)) {
            hasChanges = true;
        }
        
        if (hasChanges) {
            vendorService.save(existingVendor);
            
            // Update related entities
            updateRelatedEntities(existingVendor, finaleSupplier);
            
            log.info("Updated vendor: {} (partyId: {})", existingVendor.getVendorName(), finaleSupplier.getPartyId());
            return SyncSupplierResult.UPDATED;
        } else {
            log.debug("No changes needed for vendor: {} (partyId: {})", existingVendor.getVendorName(), finaleSupplier.getPartyId());
            return SyncSupplierResult.NO_CHANGE;
        }
    }

    /**
     * Update basic vendor information
     */
    private boolean updateVendorBasicInfo(Vendor vendor, FinaleVendorInfoDto finaleSupplier) {
        boolean hasChanges = false;
        
        // Update default terms
        String newTerms = finaleToVendorConverter.mapSettlementTerms(finaleSupplier.getSettlementTermId());
        if (!StringUtils.equals(vendor.getDefaultTerms(), newTerms)) {
            vendor.updateDefaultTerms(newTerms);
            hasChanges = true;
        }
        
        // Update default lead days
        if (!Objects.equals(vendor.getDefaultLeadDays(), finaleSupplier.getLeadTime())) {
            vendor.updateDefaultLeadDays(finaleSupplier.getLeadTime());
            hasChanges = true;
        }
        
        // Update notes
        if (!StringUtils.equals(vendor.getNotes(), finaleSupplier.getDescription())) {
            vendor.updateNotes(finaleSupplier.getDescription());
            hasChanges = true;
        }
        
        // Update contact name
        if (!StringUtils.equals(vendor.getVendorContactName(), finaleSupplier.getContactName())) {
            vendor.setVendorContactName(finaleSupplier.getContactName());
            hasChanges = true;
        }
        
        return hasChanges;
    }

    /**
     * Save related entities for a new vendor
     */
    private void saveRelatedEntities(Vendor vendor) {
        // Save addresses
        if (vendor.getAddresses() != null && !vendor.getAddresses().isEmpty()) {
            vendor.getAddresses().forEach(address -> {
                address.setEntityId(vendor.getId());
                addressService.save(address);
            });
        }
        
        // Save emails
        if (vendor.getEmails() != null && !vendor.getEmails().isEmpty()) {
            vendor.getEmails().forEach(email -> {
                email.setEntityId(vendor.getId());
                emailService.save(email);
            });
        }
        
        // Save phone numbers
        if (vendor.getPhoneNumbers() != null && !vendor.getPhoneNumbers().isEmpty()) {
            vendor.getPhoneNumbers().forEach(phone -> {
                phone.setEntityId(vendor.getId());
                phoneNumberService.save(phone);
            });
        }
        
        // Save supplier additional info
        if (vendor.getSupplierAdditionalInfo() != null) {
            vendor.getSupplierAdditionalInfo().updateVendorId(vendor.getId());
            supplierAdditionalInfoService.save(vendor.getSupplierAdditionalInfo());
        }
    }

    /**
     * Update related entities for an existing vendor
     */
    private void updateRelatedEntities(Vendor vendor, FinaleVendorInfoDto finaleSupplier) {
        // For simplicity, we'll recreate related entities
        // In a production system, you might want to implement more sophisticated merging logic
        
        // Delete existing related entities
        deleteExistingRelatedEntities(vendor.getId());
        
        // Convert and save new related entities
        Vendor convertedVendor = finaleToVendorConverter.convertToVendor(finaleSupplier);
        if (convertedVendor != null) {
            vendor.setAddresses(convertedVendor.getAddresses());
            vendor.setEmails(convertedVendor.getEmails());
            vendor.setPhoneNumbers(convertedVendor.getPhoneNumbers());
            vendor.setSupplierAdditionalInfo(convertedVendor.getSupplierAdditionalInfo());
            
            saveRelatedEntities(vendor);
        }
    }

    /**
     * Delete existing related entities
     */
    private void deleteExistingRelatedEntities(UUID vendorId) {
        // Delete addresses
        addressService.deleteByEntityId(vendorId);
        
        // Delete emails
        emailService.deleteByEntityId(vendorId);
        
        // Delete phone numbers
        phoneNumberService.deleteByEntityId(vendorId);
        
        // Delete supplier additional info
        supplierAdditionalInfoService.deleteByVendorId(vendorId);
    }

    private void updateSyncResult(SyncResult result, SyncSupplierResult supplierResult) {
        switch (supplierResult) {
            case CREATED -> result.incrementCreated();
            case UPDATED -> result.incrementUpdated();
            case NO_CHANGE -> result.incrementNoChange();
            case SKIPPED -> result.incrementSkipped();
        }
    }

    /**
     * Result of syncing a single supplier
     */
    public enum SyncSupplierResult {
        CREATED,
        UPDATED,
        NO_CHANGE,
        SKIPPED
    }

    /**
     * Overall sync result statistics
     */
    public static class SyncResult {
        private int totalFinaleSuppliers = 0;
        private final AtomicInteger created = new AtomicInteger(0);
        private final AtomicInteger updated = new AtomicInteger(0);
        private final AtomicInteger noChange = new AtomicInteger(0);
        private final AtomicInteger skipped = new AtomicInteger(0);
        private final AtomicInteger failed = new AtomicInteger(0);
        private String error;

        // Getters and setters
        public int getTotalFinaleSuppliers() { return totalFinaleSuppliers; }
        public void setTotalFinaleSuppliers(int total) { this.totalFinaleSuppliers = total; }
        
        public int getCreated() { return created.get(); }
        public void incrementCreated() { created.incrementAndGet(); }
        
        public int getUpdated() { return updated.get(); }
        public void incrementUpdated() { updated.incrementAndGet(); }
        
        public int getNoChange() { return noChange.get(); }
        public void incrementNoChange() { noChange.incrementAndGet(); }
        
        public int getSkipped() { return skipped.get(); }
        public void incrementSkipped() { skipped.incrementAndGet(); }
        
        public int getFailed() { return failed.get(); }
        public void incrementFailed() { failed.incrementAndGet(); }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }

        @Override
        public String toString() {
            return String.format("SyncResult{total=%d, created=%d, updated=%d, noChange=%d, skipped=%d, failed=%d}", 
                totalFinaleSuppliers, created.get(), updated.get(), noChange.get(), skipped.get(), failed.get());
        }
    }
}
