package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SupplierBulkExportStrategyTest {

    @Mock
    private VendorService vendorService;

    @Mock
    private BulkExportExcelGenerator excelGenerator;

    @Mock
    private ItemQueryApplicationService itemQueryApplicationService;

    @Mock
    private ItemSearchApplicationService itemSearchApplicationService;

    @Mock
    private Executor taskExecutor;

    @Mock
    private ItemVendorRebateSearchApplicationService itemVendorRebateSearchApplicationService;

    private SupplierBulkExportStrategy supplierBulkExportStrategy;

    private static final String CUSTOM_FILTER = "{\"vendor\":\"Test Vendor\"}";
    private List<ItemSerachDto> mockItems;
    private List<Vendor> mockVendors;
    private Map<UUID, Vendor> mockVendorMap;

    @BeforeEach
    void setUp() {
        supplierBulkExportStrategy = new SupplierBulkExportStrategy(
                itemQueryApplicationService,
                taskExecutor,
                itemSearchApplicationService,
                vendorService,
                excelGenerator,
                itemVendorRebateSearchApplicationService
        );

        mockItems = createMockItems();
        mockVendors = createMockVendors();
        mockVendorMap = mockVendors.stream()
                .collect(Collectors.toMap(Vendor::getId, v -> v));
    }

    @Test
    void shouldReturnCorrectExportType() {
        // When & Then
        assertEquals(ExportType.SUPPLIER, supplierBulkExportStrategy.getExportType());
    }

    @Test
    void shouldExecuteSuccessfully() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        SupplierBulkExportStrategy spy = spy(supplierBulkExportStrategy);
        doReturn(mockItems).when(spy).fetchItemsWithSimpleData(CUSTOM_FILTER);

        when(vendorService.findAll()).thenReturn(mockVendors);
        when(excelGenerator.generateBulkExportReport(
                mockItems,
                Collections.emptyList(),
                mockVendorMap,
                Collections.emptyList(),
                ExportType.SUPPLIER
        )).thenReturn(expectedBytes);

        // When
        byte[] result = spy.execute(CUSTOM_FILTER);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);

        verify(vendorService).findAll();
        verify(excelGenerator).generateBulkExportReport(
                mockItems, Collections.emptyList(), mockVendorMap, Collections.emptyList(), ExportType.SUPPLIER
        );
    }

    @Test
    void shouldUseSimpleDataFetching() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        SupplierBulkExportStrategy spy = spy(supplierBulkExportStrategy);
        doReturn(mockItems).when(spy).fetchItemsWithSimpleData(CUSTOM_FILTER);

        when(vendorService.findAll()).thenReturn(mockVendors);
        when(excelGenerator.generateBulkExportReport(
                any(), any(), any(), any(), eq(ExportType.SUPPLIER)
        )).thenReturn(expectedBytes);

        // When
        spy.execute(CUSTOM_FILTER);

        // Then
        verify(spy, never()).fetchFullItemData(anyString());
    }

    @Test
    void shouldPassEmptyFinaleDataAndCategoryMap() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        SupplierBulkExportStrategy spy = spy(supplierBulkExportStrategy);
        doReturn(mockItems).when(spy).fetchItemsWithSimpleData(CUSTOM_FILTER);

        when(vendorService.findAll()).thenReturn(mockVendors);
        when(excelGenerator.generateBulkExportReport(
                any(), eq(Collections.emptyList()), any(), any(), eq(ExportType.SUPPLIER)
        )).thenReturn(expectedBytes);

        // When
        spy.execute(CUSTOM_FILTER);

        // Then
        verify(excelGenerator).generateBulkExportReport(
                mockItems, Collections.emptyList(), mockVendorMap, Collections.emptyList(), ExportType.SUPPLIER
        );
    }

    @Test
    void shouldHandleEmptyVendorList() {
        // Given
        List<Vendor> emptyVendors = Collections.emptyList();
        Map<UUID, Vendor> emptyVendorMap = Collections.emptyMap();
        byte[] expectedBytes = "Excel content".getBytes();

        SupplierBulkExportStrategy spy = spy(supplierBulkExportStrategy);
        doReturn(mockItems).when(spy).fetchItemsWithSimpleData(CUSTOM_FILTER);

        when(vendorService.findAll()).thenReturn(emptyVendors);
        when(excelGenerator.generateBulkExportReport(
                mockItems,
                Collections.emptyList(),
                emptyVendorMap,
                Collections.emptyList(),
                ExportType.SUPPLIER
        )).thenReturn(expectedBytes);

        // When
        byte[] result = spy.execute(CUSTOM_FILTER);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);
    }

    @Test
    void shouldHandleVendorServiceException() {
        // Given
        SupplierBulkExportStrategy spy = spy(supplierBulkExportStrategy);
        doReturn(mockItems).when(spy).fetchItemsWithSimpleData(CUSTOM_FILTER);

        when(vendorService.findAll()).thenThrow(new RuntimeException("Vendor service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            spy.execute(CUSTOM_FILTER);
        });
    }

    private List<ItemSerachDto> createMockItems() {
        List<ItemSerachDto> items = new ArrayList<>();
        ItemSerachDto item1 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU001")
                .title("Test Item 1")
                .availabilityStatus("ACTIVE")
                .primaryVendorId(UUID.randomUUID())
                .build();

        ItemSerachDto item2 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU002")
                .title("Test Item 2")
                .availabilityStatus("ACTIVE")
                .primaryVendorId(UUID.randomUUID())
                .build();

        items.add(item1);
        items.add(item2);
        return items;
    }

    private List<Vendor> createMockVendors() {
        List<Vendor> vendors = new ArrayList<>();

        Vendor vendor1 = Vendor.builder()
                .id(UUID.randomUUID())
                .vendorName("Test Vendor 1")
                .vendorStatus(VendorStatus.ACTIVE)
                .externalPicking(false)
                .build();

        Vendor vendor2 = Vendor.builder()
                .id(UUID.randomUUID())
                .vendorName("Test Vendor 2")
                .vendorStatus(VendorStatus.ACTIVE)
                .externalPicking(true)
                .build();

        vendors.add(vendor1);
        vendors.add(vendor2);
        return vendors;
    }
}