package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestListDto;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.ItemAdjustmentRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.dataobject.ItemAdjustmentRequestDo;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.mapper.ItemAdjustmentRequestDoApplicationMapper;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAdjustmentRequestQueryApplicationServiceImpl.class})
class ItemAdjustmentRequestQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private ItemAdjustmentRequestDoApplicationMapper itemAdjustmentRequestDoApplicationMapper;

    @MockBean
    private ItemAdjustmentRequestJpaDao itemAdjustmentRequestJpaDao;

    @Autowired
    private ItemAdjustmentRequestQueryApplicationServiceImpl itemAdjustmentRequestQueryApplicationServiceImpl;

    @Test
    void testFindByItemAdjustmentRequestStatusIn_givenFileProcessed() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAllByStatusInOrderByCreatedAtDesc(
            Mockito.<List<ItemAdjustmentRequestStatus>>any(), Mockito.<Pageable>any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));

        ArrayList<ItemAdjustmentRequestStatus> statuses = new ArrayList<>();
        statuses.add(ItemAdjustmentRequestStatus.FILE_PROCESSED);
        statuses.add(ItemAdjustmentRequestStatus.UPLOADED);

        // Act
        ItemAdjustmentRequestListDto actualFindByItemAdjustmentRequestStatusInResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .findByItemAdjustmentRequestStatusIn(statuses, 1, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAllByStatusInOrderByCreatedAtDesc(isA(List.class), isA(Pageable.class));
        assertEquals(0L, actualFindByItemAdjustmentRequestStatusInResult.getTotalCount().longValue());
        assertTrue(actualFindByItemAdjustmentRequestStatusInResult.getData().isEmpty());
    }


    @Test
    void testFindByItemAdjustmentRequestStatusIn_givenUploaded_whenArrayListAddUploaded() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAllByStatusInOrderByCreatedAtDesc(
            Mockito.<List<ItemAdjustmentRequestStatus>>any(), Mockito.<Pageable>any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));

        ArrayList<ItemAdjustmentRequestStatus> statuses = new ArrayList<>();
        statuses.add(ItemAdjustmentRequestStatus.UPLOADED);

        // Act
        ItemAdjustmentRequestListDto actualFindByItemAdjustmentRequestStatusInResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .findByItemAdjustmentRequestStatusIn(statuses, 1, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAllByStatusInOrderByCreatedAtDesc(isA(List.class), isA(Pageable.class));
        assertEquals(0L, actualFindByItemAdjustmentRequestStatusInResult.getTotalCount().longValue());
        assertTrue(actualFindByItemAdjustmentRequestStatusInResult.getData().isEmpty());
    }


    @Test
    void testFindByItemAdjustmentRequestStatusIn_thenReturnTotalCountLongValueIsZero() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAllByStatusInOrderByCreatedAtDesc(
            Mockito.<List<ItemAdjustmentRequestStatus>>any(), Mockito.<Pageable>any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));

        // Act
        ItemAdjustmentRequestListDto actualFindByItemAdjustmentRequestStatusInResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .findByItemAdjustmentRequestStatusIn(new ArrayList<>(), 1, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAllByStatusInOrderByCreatedAtDesc(isA(List.class), isA(Pageable.class));
        assertEquals(0L, actualFindByItemAdjustmentRequestStatusInResult.getTotalCount().longValue());
        assertTrue(actualFindByItemAdjustmentRequestStatusInResult.getData().isEmpty());
    }


    @Test
    void testFindByItemAdjustmentRequestStatusIn_whenZero() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAllByStatusInOrderByCreatedAtDesc(
            Mockito.<List<ItemAdjustmentRequestStatus>>any(), Mockito.<Pageable>any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));

        // Act
        ItemAdjustmentRequestListDto actualFindByItemAdjustmentRequestStatusInResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .findByItemAdjustmentRequestStatusIn(new ArrayList<>(), 0, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAllByStatusInOrderByCreatedAtDesc(isA(List.class), isA(Pageable.class));
        assertEquals(0L, actualFindByItemAdjustmentRequestStatusInResult.getTotalCount().longValue());
        assertTrue(actualFindByItemAdjustmentRequestStatusInResult.getData().isEmpty());
    }


    @Test
    void testPagingFindItemAdjustmentRequest_thenReturnTotalCountLongValueIsZero() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAll(Mockito.<Pageable>any())).thenReturn(new PageImpl<>(new ArrayList<>()));

        // Act
        ItemAdjustmentRequestListDto actualPagingFindItemAdjustmentRequestResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .pagingFindItemAdjustmentRequest(1, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAll(isA(Pageable.class));
        assertEquals(0L, actualPagingFindItemAdjustmentRequestResult.getTotalCount().longValue());
        assertTrue(actualPagingFindItemAdjustmentRequestResult.getData().isEmpty());
    }


    @Test
    void testPagingFindItemAdjustmentRequest_whenZero_thenReturnTotalCountLongValueIsZero() {
        // Arrange
        when(itemAdjustmentRequestDoApplicationMapper.doListToDtoList(Mockito.<List<ItemAdjustmentRequestDo>>any()))
            .thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestJpaDao.findAll(Mockito.<Pageable>any())).thenReturn(new PageImpl<>(new ArrayList<>()));

        // Act
        ItemAdjustmentRequestListDto actualPagingFindItemAdjustmentRequestResult = itemAdjustmentRequestQueryApplicationServiceImpl
            .pagingFindItemAdjustmentRequest(0, 3);

        // Assert
        verify(itemAdjustmentRequestDoApplicationMapper).doListToDtoList(isA(List.class));
        verify(itemAdjustmentRequestJpaDao).findAll(isA(Pageable.class));
        assertEquals(0L, actualPagingFindItemAdjustmentRequestResult.getTotalCount().longValue());
        assertTrue(actualPagingFindItemAdjustmentRequestResult.getData().isEmpty());
    }
}
