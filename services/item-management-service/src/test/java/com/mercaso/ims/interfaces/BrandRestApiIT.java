package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.command.UpdateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.item.Item;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.HttpClientErrorException;

import java.util.UUID;

class BrandRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenCreateBrandRequest() throws Exception {
        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";

        CreateBrandCommand command = CreateBrandCommand.builder().brandName(brandName).build();

        BrandDto brandDto = brandRestApiUtil.createBrand(command);

        assertEquals(brandName, brandDto.getBrandName());
        assertNotNull(brandDto.getBrandId());
    }

    @Test
    void shouldSuccessWhenUpdateBrandRequest() throws Exception {
        // Given - Create a brand first
        String originalBrandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        CreateBrandCommand createCommand = CreateBrandCommand.builder()
            .brandName(originalBrandName)
            .logo("original-logo.png")
            .description("Original Description")
            .build();
        BrandDto createdBrand = brandRestApiUtil.createBrand(createCommand);

        // When - Update the brand
        String updatedBrandName = RandomStringUtils.randomAlphabetic(5) + "updated";
        UpdateBrandCommand updateCommand = UpdateBrandCommand.builder()
            .brandName(updatedBrandName)
            .logo("updated-logo.png")
            .description("Updated Description")
            .build();

        BrandDto updatedBrand = brandRestApiUtil.updateBrand(createdBrand.getBrandId(), updateCommand);

        // Then
        assertEquals(createdBrand.getBrandId(), updatedBrand.getBrandId());
        assertEquals(updatedBrandName, updatedBrand.getBrandName());
    }

    @Test
    void shouldFailWhenUpdateBrandWithNonExistentId() throws Exception {
        // Given
        UUID nonExistentBrandId = UUID.randomUUID();
        UpdateBrandCommand updateCommand = UpdateBrandCommand.builder()
            .brandName("Updated Brand")
            .build();

        // When & Then
        assertThrows(HttpClientErrorException.class, () -> {
            brandRestApiUtil.updateBrand(nonExistentBrandId, updateCommand);
        });
    }

    @Test
    void shouldFailWhenUpdateBrandWithDuplicateName() throws Exception {
        // Given - Create two brands
        String brandName1 = RandomStringUtils.randomAlphabetic(5) + "brand1";
        String brandName2 = RandomStringUtils.randomAlphabetic(5) + "brand2";

        CreateBrandCommand createCommand1 = CreateBrandCommand.builder().brandName(brandName1).build();
        CreateBrandCommand createCommand2 = CreateBrandCommand.builder().brandName(brandName2).build();

        BrandDto brand1 = brandRestApiUtil.createBrand(createCommand1);
        BrandDto brand2 = brandRestApiUtil.createBrand(createCommand2);

        // When & Then - Try to update brand1 to have the same name as brand2
        UpdateBrandCommand updateCommand = UpdateBrandCommand.builder()
            .brandName(brandName2)
            .build();

        assertThrows(HttpClientErrorException.class, () -> {
            brandRestApiUtil.updateBrand(brand1.getBrandId(), updateCommand);
        });
    }

    @Test
    void shouldSuccessWhenDeleteBrandRequest() throws Exception {
        // Given - Create a brand first
        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        CreateBrandCommand createCommand = CreateBrandCommand.builder().brandName(brandName).build();
        BrandDto createdBrand = brandRestApiUtil.createBrand(createCommand);

        // When & Then - Delete the brand (should not throw exception)
        brandRestApiUtil.deleteBrand(createdBrand.getBrandId());
    }

    @Test
    void shouldFailWhenDeleteBrandWithNonExistentId() throws Exception {
        // Given
        UUID nonExistentBrandId = UUID.randomUUID();

        // When & Then
        assertThrows(HttpClientErrorException.class, () -> {
            brandRestApiUtil.deleteBrand(nonExistentBrandId);
        });
    }

    @Test
    void shouldFailWhenDeleteBrandWithAssociatedItems() throws Exception {
        // Given - Create a brand first
        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        CreateBrandCommand createCommand = CreateBrandCommand.builder().brandName(brandName).build();
        BrandDto createdBrand = brandRestApiUtil.createBrand(createCommand);

        // Create an item associated with this brand
        Brand brand = brandService.findById(createdBrand.getBrandId());
        Item item = Item.builder()
            .brandId(brand.getId())
            .name("Test Item")
            .title("Test Item Title")
            .skuNumber("TEST-SKU-" + UUID.randomUUID().toString().substring(0, 8))
            .build();
        itemService.save(item);

        // When & Then - Try to delete the brand (should fail)
        assertThrows(HttpClientErrorException.class, () -> {
            brandRestApiUtil.deleteBrand(createdBrand.getBrandId());
        });
    }


}
