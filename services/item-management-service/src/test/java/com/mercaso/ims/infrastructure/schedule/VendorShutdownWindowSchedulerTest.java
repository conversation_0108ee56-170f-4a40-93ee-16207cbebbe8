package com.mercaso.ims.infrastructure.schedule;

import static com.mercaso.ims.domain.vendoritem.enums.SnapshotType.RESTORE;
import static com.mercaso.ims.domain.vendoritem.enums.SnapshotType.SHUTDOWN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.service.VendorItemAvailabilitySnapshotService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for VendorShutdownWindowScheduler.
 *
 * Testing Strategy: - Manual operations (manualShutdown/manualRestore) are tested with a vendor that has
 * shutdownWindowEnabled=false to avoid time-based validation issues - Scheduled operations
 * (shutdownVendorsOnFriday/restoreVendorsOnSaturday) are tested with a vendor that has shutdownWindowEnabled=true - Time-based
 * validation is tested separately to ensure it works correctly
 */
@ExtendWith(MockitoExtension.class)
class VendorShutdownWindowSchedulerTest {

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private EntityManagerFactory managerFactory;

    @Mock
    private EntityManager entityManager;

    @Mock
    private VendorService vendorService;

    @Mock
    private VendorItemService vendorItemService;

    @Mock
    private VendorItemApplicationService vendorItemApplicationService;

    @Mock
    private VendorItemAvailabilitySnapshotService snapshotService;

    @InjectMocks
    private VendorShutdownWindowScheduler scheduler;

    private Vendor testVendor;
    private VendorItem testVendorItem1;
    private VendorItem testVendorItem2;
    private Vendor spyVendor;


    @BeforeEach
    void setUp() {
        testVendor = Vendor.builder()
            .id(UUID.randomUUID())
            .vendorName("Test Vendor")
            .shutdownWindowEnabled(true)
            .shutdownWindowStart(LocalTime.of(23, 30))
            .shutdownWindowEnd(LocalTime.of(6, 0))
            .shutdownWindowDays("THURSDAY,FRIDAY,SATURDAY,SUNDAY,MONDAY")
            .build();

        spyVendor = spy(testVendor);

        testVendorItem1 = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(testVendor.getId())
            .itemId(UUID.randomUUID())
            .vendorSkuNumber("SKU001")
            .vendorItemName("Test Item 1")
            .availability(true)
            .packPlusCrvCost(BigDecimal.valueOf(10.00))
            .backupPackPlusCrvCost(BigDecimal.valueOf(12.00))
            .note("Test note")
            .aisle("A1")
            .build();

        testVendorItem2 = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(testVendor.getId())
            .itemId(UUID.randomUUID())
            .vendorSkuNumber("SKU002")
            .vendorItemName("Test Item 2")
            .availability(true)
            .packPlusCrvCost(BigDecimal.valueOf(15.00))
            .backupPackPlusCrvCost(BigDecimal.valueOf(18.00))
            .note("Test note 2")
            .aisle("A2")
            .build();

        // Setup Mock behavior with lenient stubbing
        lenient().when(managerFactory.createEntityManager()).thenReturn(entityManager);
        lenient().when(pgAdvisoryLock.tryLockWithSessionLevel(any(), anyInt(), anyString())).thenReturn(true);
        lenient().when(vendorService.findById(testVendor.getId())).thenReturn(spyVendor);
        lenient().when(vendorItemService.findByVendorID(testVendor.getId()))
            .thenReturn(Arrays.asList(testVendorItem1, testVendorItem2));
    }

    @Test
    void testCheckVendorShutdownWindows_shouldShutdownVendor_whenEnteringWindow() {
        // Given: Vendor is currently outside the shutdown window and will move inside
        Optional<VendorItemAvailabilitySnapshot> optionalSnapshot = Optional.of(VendorItemAvailabilitySnapshot.builder()
            .snapshotType(RESTORE)
            .snapshotTime(Instant.now())
            .build());
        when(snapshotService.findLatestByVendorIdAndSnapshotType(any(), any())).thenReturn(optionalSnapshot);
        when(spyVendor.isWithinShutdownWindow()).thenReturn(true);
        when(vendorService.findByShutdownWindowEnabled()).thenReturn(Collections.singletonList(spyVendor));

        // When
        scheduler.checkVendorShutdownWindows();

        // Then: Verify shutdown logic is called
        verify(snapshotService).createShutdownSnapshot(eq(spyVendor.getId()), anyList());
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());
        commandCaptor.getAllValues().forEach(command -> assertFalse(command.getAvailability()));
    }

    @Test
    void testCheckVendorShutdownWindows_shouldRestoreVendor_whenExitingWindow() {
        // Given: Vendor is currently inside the shutdown window and will move outside
        Optional<VendorItemAvailabilitySnapshot> optionalSnapshot = Optional.of(VendorItemAvailabilitySnapshot.builder()
            .snapshotType(SHUTDOWN)
            .snapshotTime(Instant.now())
            .build());
        when(snapshotService.findLatestByVendorIdAndSnapshotType(any(), any())).thenReturn(optionalSnapshot);
        when(spyVendor.isWithinShutdownWindow()).thenReturn(false);
        when(vendorService.findByShutdownWindowEnabled()).thenReturn(Collections.singletonList(spyVendor));
        when(snapshotService.getLatestShutdownSnapshotDetails(spyVendor.getId()))
            .thenReturn(Collections.singletonList(VendorItemAvailabilitySnapshotDetail.builder()
                .vendorItemId(testVendorItem1.getId())
                .previousAvailability(true)
                .build()));

        // When
        scheduler.checkVendorShutdownWindows();

        // Then: Verify restore logic is called
        verify(snapshotService).createRestoreSnapshot(eq(spyVendor.getId()), anyList());
        verify(vendorItemApplicationService, times(2)).update(any(UpdateVendorItemCommand.class));
    }

    @Test
    void testCheckVendorShutdownWindows_shouldDoNothing_whenStateIsUnchanged() {
        Optional<VendorItemAvailabilitySnapshot> optionalSnapshot = Optional.of(VendorItemAvailabilitySnapshot.builder()
            .snapshotType(SHUTDOWN)
            .snapshotTime(Instant.now())
            .build());
        when(snapshotService.findLatestByVendorIdAndSnapshotType(any(), any())).thenReturn(optionalSnapshot);

        when(spyVendor.isWithinShutdownWindow()).thenReturn(true);
        when(vendorService.findByShutdownWindowEnabled()).thenReturn(Collections.singletonList(spyVendor));

        // When
        scheduler.checkVendorShutdownWindows();

        // Then: No shutdown or restore logic should be called
        verify(snapshotService, never()).createShutdownSnapshot(any(), anyList());
        verify(snapshotService, never()).createRestoreSnapshot(any(), anyList());
        verify(vendorItemApplicationService, never()).update(any());
    }

    @Test
    void testManualShutdown() {
        when(spyVendor.isWithinShutdownWindow()).thenReturn(true);

        // Execute manual shutdown
        scheduler.manualShutdown(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());

        // Verify snapshot service was called
        verify(snapshotService).createShutdownSnapshot(eq(testVendor.getId()), anyList());

        // Verify vendor items availability was updated
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());
        assertFalse(capturedCommands.get(0).getAvailability());
        assertFalse(capturedCommands.get(1).getAvailability());
    }

    @Test
    void testManualRestore() {
        when(spyVendor.isWithinShutdownWindow()).thenReturn(true);
        // Setup snapshot details for restore
        VendorItemAvailabilitySnapshotDetail snapshotDetail1 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem1.getId())
            .previousAvailability(true)
            .build();
        VendorItemAvailabilitySnapshotDetail snapshotDetail2 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem2.getId())
            .previousAvailability(false)
            .build();

        when(snapshotService.getLatestShutdownSnapshotDetails(testVendor.getId()))
            .thenReturn(Arrays.asList(snapshotDetail1, snapshotDetail2));

        // Execute manual restore
        scheduler.manualRestore(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(snapshotService).getLatestShutdownSnapshotDetails(testVendor.getId());
        verify(snapshotService).createRestoreSnapshot(eq(testVendor.getId()), anyList());

        // Verify vendor items availability was updated to specific values
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify first vendor item restored to true
        assertTrue(capturedCommands.stream()
            .anyMatch(c -> c.getVendorItemId().equals(testVendorItem1.getId()) && c.getAvailability()));
        // Verify second vendor item restored to false
        assertTrue(capturedCommands.stream()
            .anyMatch(c -> c.getVendorItemId().equals(testVendorItem2.getId()) && !c.getAvailability()));
    }

}