package com.mercaso.ims.infrastructure.repository.itempricegroup;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.utils.itempricegroup.ItemPriceGroupUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemPriceGroupRepositoryImplIT extends AbstractIT {


    @Test
    void testSave() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        ItemPriceGroup result = itemPriceGroupRepository.save(itemPriceGroup);
        Assertions.assertEquals(itemPriceGroup.getGroupName(), result.getGroupName());
    }

    @Test
    void testFindById() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        ItemPriceGroup savedItemPriceGroup = itemPriceGroupRepository.save(itemPriceGroup);
        Assertions.assertEquals(itemPriceGroup.getGroupName(), savedItemPriceGroup.getGroupName());
        ItemPriceGroup result = itemPriceGroupRepository.findById(savedItemPriceGroup.getId());
        Assertions.assertEquals(itemPriceGroup.getGroupName(), result.getGroupName());
    }

    @Test
    void testUpdate() {
        String groupName = RandomStringUtils.randomNumeric(5);
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        ItemPriceGroup savedItemPriceGroup = itemPriceGroupRepository.save(itemPriceGroup);
        Assertions.assertEquals(itemPriceGroup.getGroupName(), savedItemPriceGroup.getGroupName());
        ItemPriceGroup previous = itemPriceGroupRepository.findById(savedItemPriceGroup.getId());

        previous.setGroupName(groupName);
        ItemPriceGroup result = itemPriceGroupRepository.update(previous);
        Assertions.assertEquals(groupName, result.getGroupName());
    }

    @Test
    void testDeleteById() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        ItemPriceGroup savedItemPriceGroup = itemPriceGroupRepository.save(itemPriceGroup);
        Assertions.assertEquals(itemPriceGroup.getGroupName(), savedItemPriceGroup.getGroupName());
        itemPriceGroupRepository.deleteById(savedItemPriceGroup.getId());
        ItemPriceGroup result = itemPriceGroupRepository.findById(savedItemPriceGroup.getId());
        Assertions.assertNull( result);
    }
}