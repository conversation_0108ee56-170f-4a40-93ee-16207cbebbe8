package com.mercaso.ims.infrastructure.external.google;

import com.mercaso.ims.application.dto.AttachmentDto;
import com.mercaso.ims.application.dto.GmailMessageDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedConstruction;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMessage.RecipientType;
import javax.mail.internet.MimeMultipart;
import javax.mail.search.SearchTerm;
import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GmailServiceTest {

    @InjectMocks
    private GmailService gmailService;

    @Mock
    private Session mockSession;

    @Mock
    private Store mockStore;

    @Mock
    private Folder mockFolder;

    @Mock
    private Message mockMessage;

    @Mock
    private SearchTerm mockSearchTerm;

    @Mock
    private Transport mockTransport;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_PASSWORD = "testPassword";
    private static final String TEST_SUBJECT = "Test Subject";
    private static final String TEST_BODY = "Test Body";
    private static final String TEST_FROM = "<EMAIL>";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(gmailService, "userEmail", TEST_EMAIL);
        ReflectionTestUtils.setField(gmailService, "appPassword", TEST_PASSWORD);
    }

    @Test
    void queryEmails_WithNullSearchTerm_ShouldReturnEmptyList() {
        // When
        List<GmailMessageDto> result = gmailService.queryEmails(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void queryEmails_WithValidSearchTerm_ShouldReturnEmailList() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class)) {
            sessionMock.when(() -> Session.getDefaultInstance(any())).thenReturn(mockSession);
            when(mockSession.getStore("imaps")).thenReturn(mockStore);
            when(mockStore.getFolder("INBOX")).thenReturn(mockFolder);

            Message[] messages = {mockMessage};
            when(mockFolder.search(mockSearchTerm)).thenReturn(messages);

            when(mockMessage.getSubject()).thenReturn(TEST_SUBJECT);
            when(mockMessage.getFrom()).thenReturn(new Address[]{new InternetAddress(TEST_FROM)});
            when(mockMessage.getReceivedDate()).thenReturn(new Date());
            when(mockMessage.isMimeType("multipart/*")).thenReturn(false);

            // When
            List<GmailMessageDto> result = gmailService.queryEmails(mockSearchTerm);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_SUBJECT, result.get(0).getSubject());
            assertNotNull(result.get(0).getReceivedDate());

            verify(mockStore).connect("imap.gmail.com", TEST_EMAIL, TEST_PASSWORD);
            verify(mockFolder).open(Folder.READ_ONLY);
            verify(mockFolder).close();
            verify(mockStore).close();
        }
    }

    @Test
    void queryEmails_WithMessagingException_ShouldReturnEmptyList() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class)) {
            sessionMock.when(() -> Session.getDefaultInstance(any())).thenReturn(mockSession);
            when(mockSession.getStore("imaps")).thenReturn(mockStore);
            doThrow(new MessagingException("Connection failed")).when(mockStore).connect(anyString(), anyString(), anyString());

            // When
            List<GmailMessageDto> result = gmailService.queryEmails(mockSearchTerm);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void queryEmails_WithMultipartMessage_ShouldExtractAttachments() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class)) {
            sessionMock.when(() -> Session.getDefaultInstance(any())).thenReturn(mockSession);
            when(mockSession.getStore("imaps")).thenReturn(mockStore);
            when(mockStore.getFolder("INBOX")).thenReturn(mockFolder);

            Message[] messages = {mockMessage};
            when(mockFolder.search(mockSearchTerm)).thenReturn(messages);

            when(mockMessage.getSubject()).thenReturn(TEST_SUBJECT);
            when(mockMessage.getFrom()).thenReturn(new Address[]{new InternetAddress(TEST_FROM)});
            when(mockMessage.getReceivedDate()).thenReturn(new Date());
            when(mockMessage.isMimeType("multipart/*")).thenReturn(true);

            MimeMultipart mockMultipart = mock(MimeMultipart.class);
            when(mockMessage.getContent()).thenReturn(mockMultipart);
            when(mockMultipart.getCount()).thenReturn(1);

            MimeBodyPart mockBodyPart = mock(MimeBodyPart.class);
            when(mockMultipart.getBodyPart(0)).thenReturn(mockBodyPart);
            when(mockBodyPart.getDisposition()).thenReturn(Part.ATTACHMENT);
            when(mockBodyPart.getFileName()).thenReturn("test.txt");
            when(mockBodyPart.getInputStream()).thenReturn(new ByteArrayInputStream("test content".getBytes()));

            // When
            List<GmailMessageDto> result = gmailService.queryEmails(mockSearchTerm);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertNotNull(result.get(0).getAttachments());
            assertEquals(1, result.get(0).getAttachments().size());
            assertEquals("test.txt", result.get(0).getAttachments().get(0).getFilename());
        }
    }

    @Test
    void sendEmail_WithNullRecipient_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail(null, TEST_SUBJECT, TEST_BODY, Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithEmptyRecipient_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail("", TEST_SUBJECT, TEST_BODY, Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithNullSubject_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail(TEST_EMAIL, null, TEST_BODY, Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithEmptySubject_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail(TEST_EMAIL, "", TEST_BODY, Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithNullBody_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, null, Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithEmptyBody_ShouldReturnFalse() {
        // When
        boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, "", Collections.emptyList());

        // Then
        assertFalse(result);
    }

    @Test
    void sendEmail_WithValidParameters_ShouldReturnTrue() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class);
             MockedStatic<Transport> transportMock = mockStatic(Transport.class);
             MockedConstruction<MimeMessage> mimeMessageMock = mockConstruction(MimeMessage.class)) {

            sessionMock.when(() -> Session.getInstance(any(), any(Authenticator.class))).thenReturn(mockSession);

            // When
            boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, TEST_BODY, Collections.emptyList());

            // Then
            assertTrue(result);
            transportMock.verify(() -> Transport.send(any(Message.class)));

            // Verify MimeMessage was created and configured
            assertEquals(1, mimeMessageMock.constructed().size());
            MimeMessage createdMessage = mimeMessageMock.constructed().get(0);
            verify(createdMessage).setFrom(any(InternetAddress.class));
            verify(createdMessage).setRecipients(eq(RecipientType.TO), any(Address[].class));
            verify(createdMessage).setSubject(TEST_SUBJECT);
            verify(createdMessage).setContent(any());
        }
    }

    @Test
    void sendEmail_WithAttachments_ShouldIncludeAttachments() throws Exception {
        // Given
        AttachmentDto attachment = AttachmentDto.builder()
            .filename("test.txt")
            .fileData("test content".getBytes())
            .build();

        try (MockedStatic<Session> sessionMock = mockStatic(Session.class);
             MockedStatic<Transport> transportMock = mockStatic(Transport.class);
             MockedConstruction<MimeMessage> mimeMessageMock = mockConstruction(MimeMessage.class)) {

            sessionMock.when(() -> Session.getInstance(any(), any(Authenticator.class))).thenReturn(mockSession);

            // When
            boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, TEST_BODY, Arrays.asList(attachment));

            // Then
            assertTrue(result);
            transportMock.verify(() -> Transport.send(any(Message.class)));

            // Verify MimeMessage was created
            assertEquals(1, mimeMessageMock.constructed().size());
        }
    }

    @Test
    void sendEmail_WithMessagingException_ShouldReturnFalse() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class);
             MockedStatic<Transport> transportMock = mockStatic(Transport.class);
             MockedConstruction<MimeMessage> mimeMessageMock = mockConstruction(MimeMessage.class)) {

            sessionMock.when(() -> Session.getInstance(any(), any(Authenticator.class))).thenReturn(mockSession);
            transportMock.when(() -> Transport.send(any(Message.class))).thenThrow(new MessagingException("Send failed"));

            // When
            boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, TEST_BODY, Collections.emptyList());

            // Then
            assertFalse(result);
        }
    }

    @Test
    void sendEmail_WithNullAttachmentList_ShouldSucceed() throws Exception {
        // Given
        try (MockedStatic<Session> sessionMock = mockStatic(Session.class);
             MockedStatic<Transport> transportMock = mockStatic(Transport.class);
             MockedConstruction<MimeMessage> mimeMessageMock = mockConstruction(MimeMessage.class)) {

            sessionMock.when(() -> Session.getInstance(any(), any(Authenticator.class))).thenReturn(mockSession);

            // When
            boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, TEST_BODY, null);

            // Then
            assertTrue(result);
            transportMock.verify(() -> Transport.send(any(Message.class)));
        }
    }

    @Test
    void sendEmail_WithInvalidAttachment_ShouldSkipAttachment() throws Exception {
        // Given
        AttachmentDto invalidAttachment = AttachmentDto.builder()
            .filename(null)
            .fileData("test content".getBytes())
            .build();

        try (MockedStatic<Session> sessionMock = mockStatic(Session.class);
             MockedStatic<Transport> transportMock = mockStatic(Transport.class);
             MockedConstruction<MimeMessage> mimeMessageMock = mockConstruction(MimeMessage.class)) {

            sessionMock.when(() -> Session.getInstance(any(), any(Authenticator.class))).thenReturn(mockSession);

            // When
            boolean result = gmailService.sendEmail(TEST_EMAIL, TEST_SUBJECT, TEST_BODY, Arrays.asList(invalidAttachment));

            // Then
            assertTrue(result);
            transportMock.verify(() -> Transport.send(any(Message.class)));
        }
    }

    @Test
    void getAttachments_WithNonMultipartMessage_ShouldReturnEmptyList() throws Exception {
        // Given
        Message message = mock(Message.class);
        when(message.isMimeType("multipart/*")).thenReturn(false);

        // When
        List<AttachmentDto> attachments = ReflectionTestUtils.invokeMethod(gmailService, "getAttachments", message);

        // Then
        assertNotNull(attachments);
        assertTrue(attachments.isEmpty());
    }

    @Test
    void getAttachments_WithAttachmentWithoutFilename_ShouldUseDefaultName() throws Exception {
        // Given
        Message message = mock(Message.class);
        MimeMultipart multipart = mock(MimeMultipart.class);
        MimeBodyPart bodyPart = mock(MimeBodyPart.class);

        when(message.isMimeType("multipart/*")).thenReturn(true);
        when(message.getContent()).thenReturn(multipart);
        when(multipart.getCount()).thenReturn(1);
        when(multipart.getBodyPart(0)).thenReturn(bodyPart);
        when(bodyPart.getDisposition()).thenReturn(Part.ATTACHMENT);
        when(bodyPart.getFileName()).thenReturn(null);
        when(bodyPart.getInputStream()).thenReturn(new ByteArrayInputStream("test content".getBytes()));

        // When
        List<AttachmentDto> attachments = ReflectionTestUtils.invokeMethod(gmailService, "getAttachments", message);

        // Then
        assertNotNull(attachments);
        assertEquals(1, attachments.size());
        assertEquals("unknown_attachment", attachments.get(0).getFilename());
    }
}