package com.mercaso.ims.infrastructure.repository.address;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.domain.address.enums.AddressPurpose;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.address.AddressTestUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

/**
 * Integration tests for AddressRepositoryImpl
 */
class AddressRepositoryImplIT extends AbstractIT {

    @Test
    void testSave_shouldSaveAndReturnAddress() {
        // Arrange
        Address address = AddressTestUtil.buildAddress();

        // Act
        Address result = addressRepository.save(address);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(address.getEntityType(), result.getEntityType());
        assertEquals(address.getEntityId(), result.getEntityId());
        assertEquals(address.getStreetAddress(), result.getStreetAddress());
        assertEquals(address.getCity(), result.getCity());
        assertEquals(address.getState(), result.getState());
        assertEquals(address.getPostalCode(), result.getPostalCode());
        assertEquals(address.getCountry(), result.getCountry());
        assertEquals(address.getDirections(), result.getDirections());
        assertEquals(address.getPurpose(), result.getPurpose());
        assertEquals(address.getAdditionalLines(), result.getAdditionalLines());
    }

    @Test
    void testFindById_whenEntityExists_shouldReturnEntity() {
        // Arrange
        Address address = AddressTestUtil.buildAddress();
        Address saved = addressRepository.save(address);

        // Act
        Address result = addressRepository.findById(saved.getId());

        // Assert
        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());
        assertEquals(saved.getEntityType(), result.getEntityType());
        assertEquals(saved.getStreetAddress(), result.getStreetAddress());
    }

    @Test
    void testFindById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        Address result = addressRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testUpdate_shouldUpdateAndReturnAddress() {
        // Arrange
        Address address = AddressTestUtil.buildAddress();
        Address saved = addressRepository.save(address);

        String newStreetAddress = "456 Updated St";
        saved.setStreetAddress(newStreetAddress);

        // Act
        Address result = addressRepository.update(saved);

        // Assert
        assertNotNull(result);
        assertEquals(newStreetAddress, result.getStreetAddress());
        assertEquals(saved.getId(), result.getId());
    }

    @Test
    void testUpdate_whenEntityDoesNotExist_shouldThrowException() {
        // Arrange
        Address address = AddressTestUtil.buildAddress();
        address.setId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ImsBusinessException.class, () -> addressRepository.update(address));
    }

    @Test
    void testDeleteById_shouldMarkAsDeleted() {
        // Arrange
        Address address = AddressTestUtil.buildAddress();
        Address saved = addressRepository.save(address);

        // Act
        Address result = addressRepository.deleteById(saved.getId());

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDeletedAt());
        assertNotNull(result.getDeletedBy());
    }

    @Test
    void testDeleteById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        Address result = addressRepository.deleteById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindByEntityTypeAndEntityId_shouldReturnMatchingAddresses() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();

        Address address1 = AddressTestUtil.buildAddress(entityType, entityId, AddressPurpose.BUSINESS);
        Address address2 = AddressTestUtil.buildAddress(entityType, entityId, AddressPurpose.SHIPPING);
        Address address3 = AddressTestUtil.buildAddress("Customer", entityId, AddressPurpose.HOME);

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> entityType.equals(addr.getEntityType()) && entityId.equals(addr.getEntityId())));
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndPurpose_shouldReturnMatchingAddresses() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        AddressPurpose purpose = AddressPurpose.BUSINESS;

        Address address1 = AddressTestUtil.buildAddress(entityType, entityId, purpose);
        Address address2 = AddressTestUtil.buildAddress(entityType, entityId, AddressPurpose.SHIPPING);

        addressRepository.save(address1);
        addressRepository.save(address2);

        // Act
        List<Address> result = addressRepository.findByEntityTypeAndEntityIdAndPurpose(entityType, entityId, purpose);

        // Assert
        assertEquals(1, result.size());
        assertEquals(purpose, result.get(0).getPurpose());
    }

    @Test
    void testFindByPostalCode_shouldReturnMatchingAddresses() {
        // Arrange
        String postalCode = "10001";
        Address address1 = AddressTestUtil.buildAddress("123 Main St");
        address1.setPostalCode(postalCode);
        Address address2 = AddressTestUtil.buildAddress("456 Oak Ave");
        address2.setPostalCode(postalCode);
        Address address3 = AddressTestUtil.buildAddress("789 Pine St");
        address3.setPostalCode("20001");

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByPostalCode(postalCode);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> postalCode.equals(addr.getPostalCode())));
    }

    @Test
    void testFindByCity_shouldReturnMatchingAddresses() {
        // Arrange
        String city = "Los Angeles";
        Address address1 = AddressTestUtil.buildAddress("123 Main St");
        address1.setCity(city);
        Address address2 = AddressTestUtil.buildAddress("456 Oak Ave");
        address2.setCity(city);
        Address address3 = AddressTestUtil.buildAddress("789 Pine St");
        address3.setCity("San Francisco");

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByCity(city);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> city.equals(addr.getCity())));
    }

    @Test
    void testFindByState_shouldReturnMatchingAddresses() {
        // Arrange
        String state = "CA";
        Address address1 = AddressTestUtil.buildAddress("123 Main St");
        address1.setState(state);
        Address address2 = AddressTestUtil.buildAddress("456 Oak Ave");
        address2.setState(state);
        Address address3 = AddressTestUtil.buildAddress("789 Pine St");
        address3.setState("NY");

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByState(state);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> state.equals(addr.getState())));
    }

    @Test
    void testFindByCountry_shouldReturnMatchingAddresses() {
        // Arrange
        String country = "Canada";
        Address address1 = AddressTestUtil.buildAddress("123 Main St");
        address1.setCountry(country);
        Address address2 = AddressTestUtil.buildAddress("456 Oak Ave");
        address2.setCountry(country);
        Address address3 = AddressTestUtil.buildAddress("789 Pine St");
        address3.setCountry("USA");

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByCountry(country);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> country.equals(addr.getCountry())));
    }

    @Test
    void testFindByEntityType_shouldReturnMatchingAddresses() {
        // Arrange
        String entityType = "Driver";
        Address address1 = AddressTestUtil.buildAddress(entityType, AddressPurpose.BUSINESS);
        Address address2 = AddressTestUtil.buildAddress(entityType, AddressPurpose.HOME);
        Address address3 = AddressTestUtil.buildAddress("Customer", AddressPurpose.SHIPPING);

        addressRepository.save(address1);
        addressRepository.save(address2);
        addressRepository.save(address3);

        // Act
        List<Address> result = addressRepository.findByEntityType(entityType);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(addr -> entityType.equals(addr.getEntityType())));
    }

    @Test
    void testFindByEntityTypeAndEntityId_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";
        UUID entityId = UUID.randomUUID();

        // Act
        List<Address> result = addressRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndPurpose_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        AddressPurpose purpose = AddressPurpose.OTHER;

        // Act
        List<Address> result = addressRepository.findByEntityTypeAndEntityIdAndPurpose(entityType, entityId, purpose);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByPostalCode_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String postalCode = "99999";

        // Act
        List<Address> result = addressRepository.findByPostalCode(postalCode);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByCity_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String city = "NonExistentCity";

        // Act
        List<Address> result = addressRepository.findByCity(city);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByState_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String state = "XX";

        // Act
        List<Address> result = addressRepository.findByState(state);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByCountry_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String country = "NonExistentCountry";

        // Act
        List<Address> result = addressRepository.findByCountry(country);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityType_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";

        // Act
        List<Address> result = addressRepository.findByEntityType(entityType);

        // Assert
        assertTrue(result.isEmpty());
    }
}
