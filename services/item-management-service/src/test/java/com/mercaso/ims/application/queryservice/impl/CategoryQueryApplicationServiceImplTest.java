package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CategoryQueryApplicationServiceImplTest extends AbstractTest {

    @Mock
    private CategoryService categoryService;

    @Mock
    private CategoryHierarchyService categoryHierarchyService;

    private CategoryQueryApplicationServiceImpl categoryQueryApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        categoryQueryApplicationService = new CategoryQueryApplicationServiceImpl(categoryService, categoryHierarchyService);
    }

    @Test
    void findLeafCategory_whenCategoryIdIsNull_thenReturnEmptyList() {
        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findLeafCategory(null);

        // Assert
        assertTrue(result.isEmpty());
        verifyNoInteractions(categoryService, categoryHierarchyService);
    }

    @Test
    void findLeafCategory_whenNoHierarchiesFound_thenReturnCategoryAsLeaf() {
        // Arrange
        UUID categoryId = UUID.randomUUID();
        Category category = Category.builder()
            .id(categoryId)
            .name("Test Category")
            .build();

        when(categoryHierarchyService.findByAncestorCategoryId(categoryId)).thenReturn(Collections.emptyList());
        when(categoryService.findById(categoryId)).thenReturn(category);

        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findLeafCategory(categoryId);

        // Assert
        assertEquals(1, result.size());
        assertEquals(categoryId, result.getFirst().getCategoryId());
        assertEquals("Test Category", result.getFirst().getCategoryName());

        verify(categoryHierarchyService).findByAncestorCategoryId(categoryId);
        verify(categoryService).findById(categoryId);
    }

    @Test
    void findLeafCategory_withHierarchies_thenReturnLeafCategories() {
        // Arrange
        UUID parentId = UUID.randomUUID();
        UUID leaf1Id = UUID.randomUUID();
        UUID leaf2Id = UUID.randomUUID();
        UUID midLevelId = UUID.randomUUID();

        CategoryHierarchy hierarchy1 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(midLevelId)
            .ancestorCategoryId(parentId)
            .depth(1)
            .build();

        CategoryHierarchy hierarchy2 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leaf1Id)
            .ancestorCategoryId(parentId)
            .depth(2)
            .build();

        CategoryHierarchy hierarchy3 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leaf2Id)
            .ancestorCategoryId(parentId)
            .depth(2)
            .build();

        List<CategoryHierarchy> hierarchies = Arrays.asList(hierarchy1, hierarchy2, hierarchy3);

        Category leaf1 = Category.builder()
            .id(leaf1Id)
            .name("Leaf Category 1")
            .build();

        Category leaf2 = Category.builder()
            .id(leaf2Id)
            .name("Leaf Category 2")
            .build();

        when(categoryHierarchyService.findByAncestorCategoryId(parentId)).thenReturn(hierarchies);
        when(categoryService.findAllByIdIn(Arrays.asList(leaf1Id, leaf2Id)))
            .thenReturn(Arrays.asList(leaf1, leaf2));
        when(categoryService.findById(parentId)).thenReturn(leaf1, leaf2);

        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findLeafCategory(parentId);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(dto -> dto.getCategoryId().equals(leaf1Id) && dto.getCategoryName().equals("Leaf Category 1")));
        assertTrue(result.stream().anyMatch(dto -> dto.getCategoryId().equals(leaf2Id) && dto.getCategoryName().equals("Leaf Category 2")));

        verify(categoryHierarchyService).findByAncestorCategoryId(parentId);
        verify(categoryService).findAllByIdIn(Arrays.asList(leaf1Id, leaf2Id));
    }


    @Test
    void findAncestorNameByLeafCategory_whenCategoryHasAncestors_thenReturnAncestorCategories() {
        // Arrange
        UUID leafCategoryId = UUID.randomUUID();
        UUID ancestor1Id = UUID.randomUUID();
        UUID ancestor2Id = UUID.randomUUID();
        UUID ancestor3Id = UUID.randomUUID();

        CategoryHierarchy hierarchy1 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId)
            .ancestorCategoryId(ancestor1Id)
            .depth(1)
            .build();

        CategoryHierarchy hierarchy2 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId)
            .ancestorCategoryId(ancestor2Id)
            .depth(2)
            .build();

        CategoryHierarchy hierarchy3 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId)
            .ancestorCategoryId(ancestor3Id)
            .depth(3)
            .build();

        List<CategoryHierarchy> hierarchies = Arrays.asList(hierarchy1, hierarchy2, hierarchy3);

        Category ancestor1 = Category.builder()
            .id(ancestor1Id)
            .name("Ancestor 1")
            .build();

        Category ancestor2 = Category.builder()
            .id(ancestor2Id)
            .name("Ancestor 2")
            .build();

        Category ancestor3 = Category.builder()
            .id(ancestor3Id)
            .name("Ancestor 3")
            .build();

        when(categoryHierarchyService.findByCategoryId(leafCategoryId)).thenReturn(hierarchies);
        when(categoryService.findAllByIdIn(Arrays.asList(ancestor1Id, ancestor2Id, ancestor3Id)))
            .thenReturn(Arrays.asList(ancestor1, ancestor2, ancestor3));

        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findAncestorNameByLeafCategory(leafCategoryId);

        // Assert
        assertEquals(3, result.size());

        assertTrue(result.stream()
            .anyMatch(dto -> dto.getCategoryId().equals(ancestor1Id)
                && dto.getCategoryName().equals("Ancestor 1")
                && dto.getDepth() == 1));

        assertTrue(result.stream()
            .anyMatch(dto -> dto.getCategoryId().equals(ancestor2Id)
                && dto.getCategoryName().equals("Ancestor 2")
                && dto.getDepth() == 2));

        assertTrue(result.stream()
            .anyMatch(dto -> dto.getCategoryId().equals(ancestor3Id)
                && dto.getCategoryName().equals("Ancestor 3")
                && dto.getDepth() == 3));

        verify(categoryHierarchyService).findByCategoryId(leafCategoryId);
        verify(categoryService).findAllByIdIn(Arrays.asList(ancestor1Id, ancestor2Id, ancestor3Id));
    }

    @Test
    void findAncestorNameByLeafCategory_whenNoAncestorsFound_thenReturnEmptyList() {
        // Arrange
        UUID leafCategoryId = UUID.randomUUID();
        when(categoryHierarchyService.findByCategoryId(leafCategoryId)).thenReturn(Collections.emptyList());

        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findAncestorNameByLeafCategory(leafCategoryId);

        // Assert
        assertTrue(result.isEmpty());
        verify(categoryHierarchyService).findByCategoryId(leafCategoryId);
    }

    @Test
    void findAncestorNameByLeafCategory_whenCategoryNotFound_thenReturnEmptyCategoryName() {
        // Arrange
        UUID leafCategoryId = UUID.randomUUID();
        UUID ancestorId = UUID.randomUUID();

        CategoryHierarchy hierarchy = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId)
            .ancestorCategoryId(ancestorId)
            .depth(1)
            .build();

        when(categoryHierarchyService.findByCategoryId(leafCategoryId)).thenReturn(Collections.singletonList(hierarchy));
        when(categoryService.findAllByIdIn(Collections.singletonList(ancestorId))).thenReturn(Collections.emptyList());

        // Act
        List<CategoryDto> result = categoryQueryApplicationService.findAncestorNameByLeafCategory(leafCategoryId);

        // Assert
        assertEquals(1, result.size());
        assertEquals(ancestorId, result.getFirst().getCategoryId());
        assertEquals("", result.getFirst().getCategoryName());
        assertEquals(1, result.getFirst().getDepth());

        verify(categoryHierarchyService).findByCategoryId(leafCategoryId);
        verify(categoryService).findAllByIdIn(Collections.singletonList(ancestorId));
    }
}