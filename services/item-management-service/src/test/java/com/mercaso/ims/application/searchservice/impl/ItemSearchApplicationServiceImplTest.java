package com.mercaso.ims.application.searchservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemListCustomFilterKeyDto;
import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.service.impl.DocumentApplicationServiceImpl;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDaoImpl;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.web.reactive.context.StandardReactiveWebEnvironment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {DocumentApplicationServiceImpl.class})
class ItemSearchApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private DocumentOperations documentOperations;


    @Test
    void testSearchItemListV2() {
        // Arrange
        ArrayList<UUID> uuidList = new ArrayList<>();
        uuidList.add(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.getItemDtoList(Mockito.<ItemQuery>any()))
            .thenThrow(new ImsBusinessException("[searchItemListV2] param itemQuery: {}."));
        when(customizedItemJpaDao.getItemIdListV2(Mockito.<ItemQuery>any())).thenReturn(uuidList);
        when(customizedItemJpaDao.countQueryV2(Mockito.<ItemQuery>any())).thenReturn(3L);
        ItemSearchApplicationServiceImpl itemSearchApplicationServiceImpl = new ItemSearchApplicationServiceImpl(
            customizedItemJpaDao,
            new DocumentApplicationServiceImpl(documentOperations, new StandardReactiveWebEnvironment()));
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemSearchApplicationServiceImpl.searchItemListV2(null));
        verify(customizedItemJpaDao).countQueryV2(isNull());
        verify(customizedItemJpaDao).getItemDtoList(isNull());
    }

    @Test
    @DisplayName("Test searchItemListV2(ItemQuery); then return Data size is one")
    void testSearchItemListV2_thenReturnDataSizeIsOne() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ArrayList<UUID> uuidList = new ArrayList<>();
        uuidList.add(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        ArrayList<ItemSerachDto> itemSerachDtoList = new ArrayList<>();
        ItemSerachDto.ItemSerachDtoBuilder clazzResult = ItemSerachDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .backupVendorName("Backup Vendor Name")
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .clazz("Clazz");
        ItemSerachDto.ItemSerachDtoBuilder createdUserNameResult = clazzResult
            .createdAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .createdBy("Jan 1, 2020 8:00am GMT+0100")
            .createdUserName("janedoe");
        ItemSerachDto.ItemSerachDtoBuilder primaryVendorSkuNumberResult = createdUserNameResult.crv(new BigDecimal("2.3"))
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemAttribute("Item Attribute")
            .itemTags("Item Tags")
            .itemType("Item Type")
            .itemUPCs("Item UPCs")
            .length(10.0d)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .primaryVendorName("Primary Vendor Name")
            .primaryVendorSkuNumber("42");
        ItemSerachDto.ItemSerachDtoBuilder regIndividualPriceResult = primaryVendorSkuNumberResult
            .regIndividualPrice(new BigDecimal("2.3"));
        ItemSerachDto.ItemSerachDtoBuilder regPlusCrvPriceResult = regIndividualPriceResult
            .regPlusCrvPrice(new BigDecimal("2.3"));
        ItemSerachDto.ItemSerachDtoBuilder titleResult = regPlusCrvPriceResult.regPrice(new BigDecimal("2.3"))
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr");
        ItemSerachDto buildResult = titleResult
            .updatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .updatedBy("2020-03-01")
            .updatedUserName("janedoe")
            .vendorItem("Vendor Item")
            .width(10.0d)
            .build();
        itemSerachDtoList.add(buildResult);
        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.getItemDtoList(Mockito.<ItemQuery>any()))
            .thenReturn(itemSerachDtoList);
        when(customizedItemJpaDao.getItemIdListV2(Mockito.<ItemQuery>any())).thenReturn(uuidList);
        when(customizedItemJpaDao.countQueryV2(Mockito.<ItemQuery>any())).thenReturn(3L);

        // Act
        ItemListDto actualSearchItemListV2Result = (new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
            new DocumentApplicationServiceImpl(documentOperations, new StandardReactiveWebEnvironment())))
            .searchItemListV2(null);

        // Assert
        verify(customizedItemJpaDao).countQueryV2(isNull());
        verify(customizedItemJpaDao).getItemDtoList(isNull());
        List<ItemSerachDto> data = actualSearchItemListV2Result.getData();
        assertEquals(1, data.size());
        assertSame(itemSerachDtoList, data);
    }


    @Test
    void testSearchItemsListCustomFilter() {

        CustomizedItemJpaDaoImpl customizedItemJpaDao = new CustomizedItemJpaDaoImpl(mock(JdbcTemplate.class));
        DocumentApplicationServiceImpl documentService = new DocumentApplicationServiceImpl(documentOperations,
            new StandardReactiveWebEnvironment());
        ItemSearchApplicationServiceImpl itemSearchService = new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
            documentService);

        // Arrange and Act
        List<ItemListCustomFilterKeyDto> actualSearchItemsListCustomFilterResult = itemSearchService
            .searchItemsListCustomFilter();

        // Assert
        assertEquals(17, actualSearchItemsListCustomFilterResult.size());
        ItemListCustomFilterKeyDto getResult = actualSearchItemsListCustomFilterResult.get(6);
        assertEquals("EQUALS", getResult.getCustomFilterKeyType());
        ItemListCustomFilterKeyDto getResult1 = actualSearchItemsListCustomFilterResult.get(0);
        assertEquals("LIKE", getResult1.getCustomFilterKeyType());
        ItemListCustomFilterKeyDto getResult2 = actualSearchItemsListCustomFilterResult.get(3);
        assertEquals("EQUALS", getResult2.getCustomFilterKeyType());
        ItemListCustomFilterKeyDto getResult3 = actualSearchItemsListCustomFilterResult.get(7);
        assertEquals("IN", getResult3.getCustomFilterKeyType());
        assertEquals("title", getResult1.getCustomFilterKeyName());
        assertEquals("backupVendorId", getResult.getCustomFilterKeyName());
        assertEquals("brandId", getResult3.getCustomFilterKeyName());
    }

    @Test
    void testSearchItemListIds_ItemsFound() {
        // Arrange
        ArrayList<UUID> expectedUuidList = new ArrayList<>();
        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();
        expectedUuidList.add(itemId1);
        expectedUuidList.add(itemId2);

        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.getItemIdListV2(Mockito.<ItemQuery>any())).thenReturn(expectedUuidList);

        DocumentApplicationServiceImpl documentService = new DocumentApplicationServiceImpl(documentOperations,
            new StandardReactiveWebEnvironment());
        ItemSearchApplicationServiceImpl itemSearchService = new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
            documentService);

        ItemQuery itemQuery = ItemQuery.builder().build();

        // Act
        List<UUID> result = itemSearchService.searchItemListIds(itemQuery);

        // Assert
        verify(customizedItemJpaDao).getItemIdListV2(itemQuery);
        assertEquals(expectedUuidList, result);
        assertEquals(2, result.size());
    }

    @Test
    void testSearchItemListIds_NoItemsFound() {
        // Arrange
        ArrayList<UUID> emptyUuidList = new ArrayList<>();

        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.getItemIdListV2(Mockito.<ItemQuery>any())).thenReturn(emptyUuidList);

        DocumentApplicationServiceImpl documentService = new DocumentApplicationServiceImpl(documentOperations,
            new StandardReactiveWebEnvironment());
        ItemSearchApplicationServiceImpl itemSearchService = new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
            documentService);

        ItemQuery itemQuery = ItemQuery.builder().build();

        // Act
        List<UUID> result = itemSearchService.searchItemListIds(itemQuery);

        // Assert
        verify(customizedItemJpaDao).getItemIdListV2(itemQuery);
        assertEquals(List.of(), result);
        assertEquals(0, result.size());
    }

    @Test
    void testSearchItemListIds_NullItemQuery() {
        // Arrange
        ArrayList<UUID> uuidList = new ArrayList<>();
        UUID itemId = UUID.randomUUID();
        uuidList.add(itemId);

        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.getItemIdListV2(null)).thenReturn(uuidList);

        DocumentApplicationServiceImpl documentService = new DocumentApplicationServiceImpl(documentOperations,
            new StandardReactiveWebEnvironment());
        ItemSearchApplicationServiceImpl itemSearchService = new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
            documentService);

        // Act
        List<UUID> result = itemSearchService.searchItemListIds(null);

        // Assert
        verify(customizedItemJpaDao).getItemIdListV2(null);
        assertEquals(uuidList, result);
        assertEquals(1, result.size());
    }

    @Test
    void testSearchItemCount_NullItemQuery() {
        // Arrange
        Long expectedCount = 5L;

        CustomizedItemJpaDao customizedItemJpaDao = mock(CustomizedItemJpaDao.class);
        when(customizedItemJpaDao.countQueryV2(null)).thenReturn(expectedCount);

        DocumentApplicationServiceImpl documentService = new DocumentApplicationServiceImpl(documentOperations,
                new StandardReactiveWebEnvironment());
        ItemSearchApplicationServiceImpl itemSearchService = new ItemSearchApplicationServiceImpl(customizedItemJpaDao,
                documentService);

        // Act
        Long result = itemSearchService.searchItemCount(null);

        // Assert
        verify(customizedItemJpaDao).countQueryV2(null);
        assertEquals(expectedCount, result);
    }
}
