package com.mercaso.ims.infrastructure.repository.vendoritem;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemJpaDao;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.mapper.VendorItemDoMapper;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class VendorItemRepositoryImplTest {

    @Mock
    VendorItemDoMapper vendorItemDoMapper;
    @Mock
    VendorItemJpaDao vendorItemJpaDao;
    @InjectMocks
    VendorItemRepositoryImpl vendorItemRepositoryImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSave() {

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);

        VendorItem result = vendorItemRepositoryImpl.save(null);
        Assertions.assertEquals(null, result);
    }

    @Test
    void testFindById() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);
        VendorItem result = vendorItemRepositoryImpl.findById(new UUID(0L, 0L));
        Assertions.assertEquals(vendorItem.getItemId(), result.getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.getId());
    }

    @Test
    void testUpdate() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);

        VendorItem result = vendorItemRepositoryImpl.update(vendorItem);
        Assertions.assertEquals(vendorItem.getItemId(), result.getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.getId());
    }

    @Test
    void testDeleteById() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);

        VendorItem result = vendorItemRepositoryImpl.deleteById(new UUID(0L, 0L));
        Assertions.assertEquals(vendorItem.getItemId(), result.getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.getId());
    }

    @Test
    void testFindByItemID() {

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);
        when(vendorItemJpaDao.findByItemId(any(UUID.class))).thenReturn(List.of(vendorItemDo));

        List<VendorItem> result = vendorItemRepositoryImpl.findByItemID(new UUID(0L, 0L));
        Assertions.assertEquals(1, result.size());

        Assertions.assertEquals(vendorItem.getItemId(), result.get(0).getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.get(0).getId());
    }

    @Test
    void testFindByVendorID() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);
        when(vendorItemJpaDao.findByVendorId(any(UUID.class))).thenReturn(List.of(vendorItemDo));

        List<VendorItem> result = vendorItemRepositoryImpl.findByVendorID(new UUID(0L, 0L));
        Assertions.assertEquals(1, result.size());

        Assertions.assertEquals(vendorItem.getItemId(), result.get(0).getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.get(0).getId());
    }

    @Test
    void testFindByVendorIDAndItemIdAndVendorSkuNum() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);
        when(vendorItemJpaDao.findByVendorIdAndItemIdAndVendorSkuNumber(any(UUID.class),
            any(UUID.class),
            anyString())).thenReturn(vendorItemDo);

        VendorItem result = vendorItemRepositoryImpl.findByVendorIDAndItemIdAndVendorSkuNum(new UUID(0L, 0L),
            new UUID(0L, 0L),
            "vendorSkuNum");
        Assertions.assertEquals(vendorItem.getItemId(), result.getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.getId());
    }

    @Test
    void testFindByVendorIDAndItemId() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.save(vendorItemDo)).thenReturn(vendorItemDo);
        when(vendorItemJpaDao.findByVendorIdAndItemId(any(UUID.class), any(UUID.class))).thenReturn(vendorItemDo);

        VendorItem result = vendorItemRepositoryImpl.findByVendorIDAndItemId(new UUID(0L, 0L), new UUID(0L, 0L));

        Assertions.assertEquals(vendorItem.getItemId(), result.getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.getId());


    }

    @Test
    void testFindByItemIdWithin() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        VendorItemDo vendorItemDo = VendorItemUtil.buildVendorItemDo();

        when(vendorItemJpaDao.findById(any())).thenReturn(Optional.of(vendorItemDo));
        when(vendorItemDoMapper.domainToDo(vendorItem)).thenReturn(vendorItemDo);
        when(vendorItemDoMapper.doToDomain(vendorItemDo)).thenReturn(vendorItem);
        when(vendorItemJpaDao.findByItemIdIn(any())).thenReturn(List.of(vendorItemDo));

        List<VendorItem> result = vendorItemRepositoryImpl.findByItemIdWithin(List.of(new UUID(0L, 0L)));
        Assertions.assertEquals(1, result.size());

        Assertions.assertEquals(vendorItem.getItemId(), result.get(0).getItemId());
        Assertions.assertEquals(vendorItem.getId(), result.get(0).getId());
    }
}
