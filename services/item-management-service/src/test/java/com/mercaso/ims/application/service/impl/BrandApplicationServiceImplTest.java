package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.command.DeleteBrandCommand;
import com.mercaso.ims.application.command.UpdateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BrandApplicationServiceImplTest {

    @Mock
    BrandService brandService;
    @Mock
    BrandDtoApplicationMapper brandDtoApplicationMapper;
    @Mock
    ItemService itemService;
    @InjectMocks
    BrandApplicationServiceImpl brandApplicationServiceImpl;

    @Mock
    BusinessEventService businessEventService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateBrand_Success() {
        // Arrange
        String brandName = RandomStringUtils.randomAlphabetic(5) + "brand";
        UUID id = UUID.randomUUID();
        CreateBrandCommand command = new CreateBrandCommand(brandName, "logo", "description");
        Brand savedBrand = Brand.builder().id(id).name(brandName).build();
        BrandDto expectedDto = new BrandDto(id, brandName);

        when(brandService.findByNameIgnoreCase(brandName)).thenReturn(null);
        when(brandService.save(any(Brand.class))).thenReturn(savedBrand);
        when(brandDtoApplicationMapper.domainToDto(savedBrand)).thenReturn(expectedDto);

        // Act
        BrandDto result = brandApplicationServiceImpl.createBrand(command);

        // Assert
        assertEquals(id, result.getBrandId());
        assertEquals(brandName, result.getBrandName());
        verify(brandService).findByNameIgnoreCase(brandName);
        verify(brandService).save(any(Brand.class));
        verify(brandDtoApplicationMapper).domainToDto(savedBrand);
    }

    @Test
    void testCreateBrand_InvalidBrandName_Empty() {
        // Arrange
        CreateBrandCommand command = new CreateBrandCommand("", "logo", "description");

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.INVALID_BRAND_NAME.getCode(), exception.getCode());
        verify(brandService, never()).findByName(anyString());
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_InvalidBrandName_Blank() {
        // Arrange
        CreateBrandCommand command = new CreateBrandCommand("   ", "logo", "description");

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.INVALID_BRAND_NAME.getCode(), exception.getCode());
        verify(brandService, never()).findByName(anyString());
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_BrandAlreadyExists() {
        // Arrange
        String brandName = "ExistingBrand";
        CreateBrandCommand command = new CreateBrandCommand(brandName, "logo", "description");
        Brand existingBrand = Brand.builder().id(UUID.randomUUID()).name(brandName).build();

        when(brandService.findByNameIgnoreCase(brandName)).thenReturn(Lists.newArrayList(existingBrand));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.createBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_ALREADY_EXIST.getCode(), exception.getCode());
        verify(brandService).findByNameIgnoreCase(brandName);
        verify(brandService, never()).save(any(Brand.class));
        verify(brandDtoApplicationMapper, never()).domainToDto(any(Brand.class));
    }

    @Test
    void testCreateBrand_SuccessWithTrimmedName() {
        // Arrange
        String brandNameWithSpaces = "  TestBrand  ";
        String trimmedBrandName = "TestBrand";
        UUID id = UUID.randomUUID();
        CreateBrandCommand command = new CreateBrandCommand(brandNameWithSpaces, "logo", "description");
        Brand savedBrand = Brand.builder().id(id).name(trimmedBrandName).build();
        BrandDto expectedDto = new BrandDto(id, trimmedBrandName);

        when(brandService.findByNameIgnoreCase(trimmedBrandName)).thenReturn(null);
        when(brandService.save(any(Brand.class))).thenReturn(savedBrand);
        when(brandDtoApplicationMapper.domainToDto(savedBrand)).thenReturn(expectedDto);

        // Act
        BrandDto result = brandApplicationServiceImpl.createBrand(command);

        // Assert
        assertEquals(id, result.getBrandId());
        assertEquals(trimmedBrandName, result.getBrandName());
        verify(brandService).findByNameIgnoreCase(trimmedBrandName);
        verify(brandService).save(any(Brand.class));
        verify(brandDtoApplicationMapper).domainToDto(savedBrand);
    }

    @Test
    void testUpdateBrand_Success() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        String newBrandName = "Updated Brand";
        UpdateBrandCommand command = UpdateBrandCommand.builder()
            .brandId(brandId)
            .brandName(newBrandName)
            .logo("updated-logo.png")
            .description("Updated Description")
            .build();

        Brand existingBrand = Brand.builder()
            .id(brandId)
            .name("Old Brand")
            .build();

        Brand updatedBrand = Brand.builder()
            .id(brandId)
            .name(newBrandName)
            .build();

        BrandDto expectedDto = new BrandDto(brandId, newBrandName);

        when(brandService.findById(brandId)).thenReturn(existingBrand);
        when(brandService.findByNameIgnoreCase(newBrandName)).thenReturn(Collections.emptyList());
        when(brandService.update(any(Brand.class))).thenReturn(updatedBrand);
        when(brandDtoApplicationMapper.domainToDto(updatedBrand)).thenReturn(expectedDto);

        // Act
        BrandDto result = brandApplicationServiceImpl.updateBrand(command);

        // Assert
        assertEquals(brandId, result.getBrandId());
        assertEquals(newBrandName, result.getBrandName());
        verify(brandService).findById(brandId);
        verify(brandService).findByNameIgnoreCase(newBrandName);
        verify(brandService).update(any(Brand.class));
        verify(brandDtoApplicationMapper).domainToDto(updatedBrand);
    }

    @Test
    void testUpdateBrand_BrandNotFound() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        UpdateBrandCommand command = UpdateBrandCommand.builder()
            .brandId(brandId)
            .brandName("Updated Brand")
            .build();

        when(brandService.findById(brandId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.updateBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_NOT_FOUND.getCode(), exception.getCode());
        verify(brandService).findById(brandId);
        verify(brandService, never()).update(any(Brand.class));
    }

    @Test
    void testUpdateBrand_BrandNameAlreadyExists() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        UUID anotherBrandId = UUID.randomUUID();
        String newBrandName = "Existing Brand";

        UpdateBrandCommand command = UpdateBrandCommand.builder()
            .brandId(brandId)
            .brandName(newBrandName)
            .build();

        Brand existingBrand = Brand.builder()
            .id(brandId)
            .name("Old Brand")
            .build();

        Brand anotherBrand = Brand.builder()
            .id(anotherBrandId)
            .name(newBrandName)
            .build();

        when(brandService.findById(brandId)).thenReturn(existingBrand);
        when(brandService.findByNameIgnoreCase(newBrandName)).thenReturn(List.of(anotherBrand));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.updateBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_ALREADY_EXIST.getCode(), exception.getCode());
        verify(brandService).findById(brandId);
        verify(brandService).findByNameIgnoreCase(newBrandName);
        verify(brandService, never()).update(any(Brand.class));
    }

    @Test
    void testDeleteBrand_Success() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        DeleteBrandCommand command = DeleteBrandCommand.builder()
            .brandId(brandId)
            .build();

        Brand existingBrand = Brand.builder()
            .id(brandId)
            .name("Test Brand")
            .build();

        when(brandService.findById(brandId)).thenReturn(existingBrand);
        when(itemService.findByBrandId(brandId)).thenReturn(Collections.emptyList());

        // Act
        brandApplicationServiceImpl.deleteBrand(command);

        // Assert
        verify(brandService).findById(brandId);
        verify(itemService).findByBrandId(brandId);
        verify(brandService).delete(brandId);
    }

    @Test
    void testDeleteBrand_BrandNotFound() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        DeleteBrandCommand command = DeleteBrandCommand.builder()
            .brandId(brandId)
            .build();

        when(brandService.findById(brandId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.deleteBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_NOT_FOUND.getCode(), exception.getCode());
        verify(brandService).findById(brandId);
        verify(brandService, never()).delete(brandId);
    }

    @Test
    void testDeleteBrand_BrandHasItems() {
        // Arrange
        UUID brandId = UUID.randomUUID();
        DeleteBrandCommand command = DeleteBrandCommand.builder()
            .brandId(brandId)
            .build();

        Brand existingBrand = Brand.builder()
            .id(brandId)
            .name("Test Brand")
            .build();

        Item item = Item.builder()
            .id(UUID.randomUUID())
            .brandId(brandId)
            .build();

        when(brandService.findById(brandId)).thenReturn(existingBrand);
        when(itemService.findByBrandId(brandId)).thenReturn(List.of(item));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> brandApplicationServiceImpl.deleteBrand(command));

        assertEquals(ErrorCodeEnums.BRAND_HAS_ITEMS.getCode(), exception.getCode());
        verify(brandService).findById(brandId);
        verify(itemService).findByBrandId(brandId);
        verify(brandService, never()).delete(brandId);
    }
}