package com.mercaso.ims.application.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ItemDto.class})
@ExtendWith(SpringExtension.class)
class ItemDtoTest {

    @Autowired
    private ItemDto itemDto;

    @Test
    void testGetVariantBarcode() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVariantBarcode());
    }


    @Test
    void testGetMasterUpc() {
        // Arrange, Act and Assert
        assertNull(itemDto.getMasterUpc());
    }


    @Test
    void testGetCaseUpc() {
        // Arrange, Act and Assert
        assertNull(itemDto.getCaseUpc());
    }


    @Test
    void testGetEachUpc() {
        // Arrange, Act and Assert
        assertNull(itemDto.getEachUpc());
    }


    @Test
    void testGetItemSize() {
        // Arrange, Act and Assert
        assertNull(itemDto.getItemSize());
    }


    @Test
    void testGetItemSizeUnitMeasure() {
        // Arrange, Act and Assert
        assertNull(itemDto.getItemSizeUnitMeasure());
    }


    @Test
    void testGetItemSizeAttribute() {
        // Arrange, Act and Assert
        assertNull(itemDto.getItemSizeAttribute());
    }


    @Test
    void testGetBottleSizeAttribute() {
        // Arrange, Act and Assert
        assertNull(itemDto.getBottleSizeAttribute());
    }


    @Test
    void testGetBottleSizeStr() {
        // Arrange, Act and Assert
        assertNull(itemDto.getBottleSize());
    }


    @Test
    void testGetVariantWeight() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVariantWeight());
    }


    @Test
    void testGetVariantWeightUnit() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVariantWeightUnit());
    }


    @Test
    void testGetVariantWeightAttribute() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVariantWeightAttribute());
    }


    @Test
    void testGetVolumeValue() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVolumeValue());
    }


    @Test
    void testGetVolumeUom() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVolumeUom());
    }


    @Test
    void testGetVolumeValueAttribute() {
        // Arrange, Act and Assert
        assertNull(itemDto.getVolumeValueAttribute());
    }


    @Test
    void testGetIndividualPrice() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        BigDecimal regPriceIndividual = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(regPriceIndividual);
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualIndividualPrice = buildResult.getIndividualPrice();

        // Assert
        assertEquals(new BigDecimal("2.3"), actualIndividualPrice);
        assertSame(regPriceIndividual, actualIndividualPrice);
        assertSame(actualIndividualPrice, buildResult.getItemRegPrice().getRegPriceIndividual());
    }


    @Test
    void testGetIndividualPrice2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        BigDecimal regPriceIndividual = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(regPriceIndividual);
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualIndividualPrice = buildResult.getIndividualPrice();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        assertEquals(new BigDecimal("2.3"), actualIndividualPrice);
        assertSame(regPriceIndividual, actualIndividualPrice);
        assertSame(actualIndividualPrice, buildResult.getItemRegPrice().getRegPriceIndividual());
    }


    @Test
    void testGetIndividualPrice3() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());

        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder builderResult3 = ItemPromoPriceDto.builder();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder itemPromoPriceIdResult = crvResult.itemPromoPriceId(UUID.randomUUID());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoBeginTimeResult = itemPromoPriceIdResult
            .promoBeginTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoLiveCheckResult = promoBeginTimeResult
            .promoEndTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .promoFlag(true)
            .promoLiveCheck("Promo Live Check");
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceResult = promoLiveCheckResult
            .promoPrice(new BigDecimal("2.3"));
        BigDecimal promoPriceIndividual = new BigDecimal("2.3");
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceIndividualResult = promoPriceResult
            .promoPriceIndividual(promoPriceIndividual);
        ItemPromoPriceDto buildResult = promoPriceIndividualResult.promoPricePlusCrv(new BigDecimal("2.3"))
            .promoPricingValidation("Promo Pricing Validation")
            .build();
        itemPromoPrices.add(buildResult);
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult4 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult2 = builderResult4.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult2.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult2 = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualIndividualPrice = buildResult2.getIndividualPrice();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        List<ItemPromoPriceDto> itemPromoPrices2 = buildResult2.getItemPromoPrices();
        assertEquals(1, itemPromoPrices2.size());
        assertEquals(new BigDecimal("2.3"), actualIndividualPrice);
        assertSame(promoPriceIndividual, actualIndividualPrice);
        assertSame(actualIndividualPrice, buildResult2.getPromoIndividualPrice());
        assertSame(actualIndividualPrice, itemPromoPrices2.getFirst().getPromoPriceIndividual());
    }


    @Test
    void testGetPrice() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        BigDecimal regPrice = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(regPrice);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPrice = buildResult.getPrice();

        // Assert
        assertEquals(new BigDecimal("2.3"), actualPrice);
        assertSame(regPrice, actualPrice);
        assertSame(actualPrice, buildResult.getItemRegPrice().getRegPrice());
    }


    @Test
    void testGetPrice2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        BigDecimal regPrice = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(regPrice);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPrice = buildResult.getPrice();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        assertEquals(new BigDecimal("2.3"), actualPrice);
        assertSame(regPrice, actualPrice);
        assertSame(actualPrice, buildResult.getItemRegPrice().getRegPrice());
    }


    @Test
    void testGetPrice3() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());

        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder builderResult3 = ItemPromoPriceDto.builder();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder itemPromoPriceIdResult = crvResult.itemPromoPriceId(UUID.randomUUID());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoBeginTimeResult = itemPromoPriceIdResult
            .promoBeginTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoLiveCheckResult = promoBeginTimeResult
            .promoEndTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .promoFlag(true)
            .promoLiveCheck("Promo Live Check");
        BigDecimal promoPrice = new BigDecimal("2.3");
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceResult = promoLiveCheckResult.promoPrice(promoPrice);
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceIndividualResult = promoPriceResult
            .promoPriceIndividual(new BigDecimal("2.3"));
        ItemPromoPriceDto buildResult = promoPriceIndividualResult.promoPricePlusCrv(new BigDecimal("2.3"))
            .promoPricingValidation("Promo Pricing Validation")
            .build();
        itemPromoPrices.add(buildResult);
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult4 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult2 = builderResult4.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult2.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult2 = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPrice = buildResult2.getPrice();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        List<ItemPromoPriceDto> itemPromoPrices2 = buildResult2.getItemPromoPrices();
        assertEquals(1, itemPromoPrices2.size());
        assertEquals(new BigDecimal("2.3"), actualPrice);
        assertSame(promoPrice, actualPrice);
        assertSame(actualPrice, buildResult2.getPromoPrice());
        assertSame(actualPrice, itemPromoPrices2.getFirst().getPromoPrice());
    }


    @Test
    void testGetPricePlusCrv() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        BigDecimal regPricePlusCrv = new BigDecimal("2.3");
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(regPricePlusCrv).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPricePlusCrv = buildResult.getPricePlusCrv();

        // Assert
        assertEquals(new BigDecimal("2.3"), actualPricePlusCrv);
        assertSame(regPricePlusCrv, actualPricePlusCrv);
        assertSame(actualPricePlusCrv, buildResult.getItemRegPrice().getRegPricePlusCrv());
    }


    @Test
    void testGetPricePlusCrv2() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        BigDecimal regPricePlusCrv = new BigDecimal("2.3");
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(regPricePlusCrv).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPricePlusCrv = buildResult.getPricePlusCrv();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        assertEquals(new BigDecimal("2.3"), actualPricePlusCrv);
        assertSame(regPricePlusCrv, actualPricePlusCrv);
        assertSame(actualPricePlusCrv, buildResult.getItemRegPrice().getRegPricePlusCrv());
    }


    @Test
    void testGetPricePlusCrv3() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        ItemDto.ItemDtoBuilder itemDtoBuilder = mock(ItemDto.ItemDtoBuilder.class);
        when(itemDtoBuilder.availabilityStatus(Mockito.<String>any())).thenReturn(ItemDto.builder());
        ItemDto.ItemDtoBuilder bodyHtmlResult = itemDtoBuilder.availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder categoryResult = brandResult.brandId(UUID.randomUUID()).category("Category");
        ItemDto.ItemDtoBuilder categoryNameResult = categoryResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());

        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder builderResult3 = ItemPromoPriceDto.builder();
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder itemPromoPriceIdResult = crvResult.itemPromoPriceId(UUID.randomUUID());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoBeginTimeResult = itemPromoPriceIdResult
            .promoBeginTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoLiveCheckResult = promoBeginTimeResult
            .promoEndTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .promoFlag(true)
            .promoLiveCheck("Promo Live Check");
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceResult = promoLiveCheckResult
            .promoPrice(new BigDecimal("2.3"));
        ItemPromoPriceDto.ItemPromoPriceDtoBuilder promoPriceIndividualResult = promoPriceResult
            .promoPriceIndividual(new BigDecimal("2.3"));
        BigDecimal promoPricePlusCrv = new BigDecimal("2.3");
        ItemPromoPriceDto buildResult = promoPriceIndividualResult.promoPricePlusCrv(promoPricePlusCrv)
            .promoPricingValidation("Promo Pricing Validation")
            .build();
        itemPromoPrices.add(buildResult);
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult4 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult2 = builderResult4.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult2.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult2 = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type")
            .build();

        // Act
        BigDecimal actualPricePlusCrv = buildResult2.getPricePlusCrv();

        // Assert
        verify(itemDtoBuilder).availabilityStatus("Availability Status");
        List<ItemPromoPriceDto> itemPromoPrices2 = buildResult2.getItemPromoPrices();
        assertEquals(1, itemPromoPrices2.size());
        assertEquals(new BigDecimal("2.3"), actualPricePlusCrv);
        assertSame(promoPricePlusCrv, actualPricePlusCrv);
        assertSame(actualPricePlusCrv, buildResult2.getPromoPricePlusCrv());
        assertSame(actualPricePlusCrv, itemPromoPrices2.getFirst().getPromoPricePlusCrv());
    }

    @Test
    void testGetPromoFlag() {
        // Arrange, Act and Assert
        assertFalse(itemDto.getPromoFlag());
    }


    @Test
    void testGetPromoIndividualPrice() {
        // Arrange, Act and Assert
        assertNull(itemDto.getPromoIndividualPrice());
    }


    @Test
    void testGetPromoPrice() {
        // Arrange, Act and Assert
        assertNull(itemDto.getPromoPrice());
    }


    @Test
    void testGetPromoPricePlusCrv() {
        // Arrange, Act and Assert
        assertNull(itemDto.getPromoPricePlusCrv());
    }
}
