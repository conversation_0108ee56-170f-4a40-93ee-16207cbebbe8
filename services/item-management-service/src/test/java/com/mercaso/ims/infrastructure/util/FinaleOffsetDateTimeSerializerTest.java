package com.mercaso.ims.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.fasterxml.jackson.core.JsonStreamContext;
import com.fasterxml.jackson.core.filter.FilteringGeneratorDelegate;
import com.fasterxml.jackson.core.filter.TokenFilter;
import com.fasterxml.jackson.core.util.JsonGeneratorDelegate;
import com.fasterxml.jackson.databind.ser.DefaultSerializerProvider;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {FinaleOffsetDateTimeSerializer.class})
@ExtendWith(SpringExtension.class)
class FinaleOffsetDateTimeSerializerTest {

    @Autowired
    private FinaleOffsetDateTimeSerializer finaleOffsetDateTimeSerializer;

    @Test
    void testSerializeWithOffsetDateTimeJsonGeneratorSerializerProvider() throws IOException {
        // Arrange
        OffsetDateTime value = OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC);
        JsonGeneratorDelegate gen = new JsonGeneratorDelegate(
            new FilteringGeneratorDelegate(new JsonGeneratorDelegate(new JsonGeneratorDelegate(null), true), null,
                TokenFilter.Inclusion.ONLY_INCLUDE_ALL, true),
            true);

        // Act
        finaleOffsetDateTimeSerializer.serialize(value, gen, new DefaultSerializerProvider.Impl());

        // Assert that nothing has changed
        JsonStreamContext outputContext = gen.getOutputContext();
        assertEquals(0, outputContext.getEntryCount());
        assertFalse(outputContext.hasCurrentIndex());
    }
}
