package com.mercaso.ims.infrastructure.repository.attributegroup;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attributegroup.AttributeGroup;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.AttributeGroupJpaDao;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDo;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.mapper.AttributeGroupDoMapper;
import com.mercaso.ims.utils.attributegroup.AttributeGroupUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {AttributeGroupRepositoryImpl.class})
class AttributeGroupRepositoryImplTest extends AbstractTest {

    @MockBean
    private AttributeGroupDoMapper attributeGroupDoMapper;

    @MockBean
    private AttributeGroupJpaDao attributeGroupJpaDao;

    @Autowired
    private AttributeGroupRepositoryImpl attributeGroupRepositoryImpl;


    @Test
    void testSaveWithAttributeGroup_thenReturnNull() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        when(attributeGroupJpaDao.save(Mockito.<AttributeGroupDo>any())).thenReturn(attributeGroupDo);

        AttributeGroupDo attributeGroupDo2 = AttributeGroupUtil.buildAttributeGroupDo();
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any())).thenReturn(null);
        when(attributeGroupDoMapper.domainToDo(Mockito.<AttributeGroup>any())).thenReturn(attributeGroupDo2);

        // Act
        AttributeGroup actualSaveResult = attributeGroupRepositoryImpl.save(null);

        // Assert
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupDoMapper).domainToDo(isNull());
        verify(attributeGroupJpaDao).save(isA(AttributeGroupDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSaveWithAttributeGroup_thenThrowImsBusinessException() {
        // Arrange
        when(attributeGroupDoMapper.domainToDo(Mockito.<AttributeGroup>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> attributeGroupRepositoryImpl.save(null));
        verify(attributeGroupDoMapper).domainToDo(isNull());
    }


    @Test
    void testFindByIdWithUuid_thenReturnNull() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        Optional<AttributeGroupDo> ofResult = Optional.of(attributeGroupDo);
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any())).thenReturn(null);

        // Act
        AttributeGroup actualFindByIdResult = attributeGroupRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        Optional<AttributeGroupDo> ofResult = Optional.of(attributeGroupDo);
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeGroupRepositoryImpl.findById(uuid));
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdateWithAttributeGroup_thenReturnNull() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        Optional<AttributeGroupDo> ofResult = Optional.of(attributeGroupDo);

        AttributeGroupDo attributeGroupDo2 = AttributeGroupUtil.buildAttributeGroupDo();
        when(attributeGroupJpaDao.save(Mockito.<AttributeGroupDo>any())).thenReturn(attributeGroupDo2);
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        AttributeGroupDo attributeGroupDo3 = AttributeGroupUtil.buildAttributeGroupDo();
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any())).thenReturn(null);
        when(attributeGroupDoMapper.domainToDo(Mockito.<AttributeGroup>any())).thenReturn(attributeGroupDo3);
        AttributeGroup domain = mock(AttributeGroup.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        AttributeGroup actualUpdateResult = attributeGroupRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupDoMapper).domainToDo(isA(AttributeGroup.class));
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
        verify(attributeGroupJpaDao).save(isA(AttributeGroupDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testUpdateWithAttributeGroup_thenThrowImsBusinessException() {
        // Arrange
        Optional<AttributeGroupDo> emptyResult = Optional.empty();
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);
        AttributeGroup domain = mock(AttributeGroup.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> attributeGroupRepositoryImpl.update(domain));
        verify(domain).getId();
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testDeleteById_givenAttributeGroupDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        Optional<AttributeGroupDo> ofResult = Optional.of(attributeGroupDo);
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any())).thenReturn(null);

        // Act
        AttributeGroup actualDeleteByIdResult = attributeGroupRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_givenAttributeGroupJpaDaoFindByIdReturnEmpty_thenReturnNull() {
        // Arrange
        Optional<AttributeGroupDo> emptyResult = Optional.empty();
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        AttributeGroup actualDeleteByIdResult = attributeGroupRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        AttributeGroupDo attributeGroupDo = AttributeGroupUtil.buildAttributeGroupDo();
        Optional<AttributeGroupDo> ofResult = Optional.of(attributeGroupDo);
        when(attributeGroupJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeGroupDoMapper.doToDomain(Mockito.<AttributeGroupDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeGroupRepositoryImpl.deleteById(uuid));
        verify(attributeGroupDoMapper).doToDomain(isA(AttributeGroupDo.class));
        verify(attributeGroupJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testFindByCategoryId_givenAttributeGroupDoMapper_thenReturnEmpty() {
        // Arrange
        when(attributeGroupJpaDao.findByCategoryId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<AttributeGroup> actualFindByCategoryIdResult = attributeGroupRepositoryImpl
            .findByCategoryId(UUID.randomUUID());

        // Assert
        verify(attributeGroupJpaDao).findByCategoryId(isA(UUID.class));
        assertTrue(actualFindByCategoryIdResult.isEmpty());
    }
}
