package com.mercaso.ims.infrastructure.repository.category.jpa;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.query.CategoryQuery;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.ContextConfiguration;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import java.util.Collections;

@ContextConfiguration(classes = {CustomizedCategoryJpaDaoImpl.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class CustomizedCategoryJpaDaoImplTest {

    @Autowired
    private CustomizedCategoryJpaDaoImpl customizedCategoryJpaDaoImpl;

    @MockBean
    private NamedParameterJdbcTemplate jdbcTemplate;

    private CategoryQuery categoryQuery;

    @BeforeEach
    void setUp() {
        categoryQuery = CategoryQuery.builder().build();
        categoryQuery.setCategoryId(UUID.randomUUID());
        categoryQuery.setAncestorCategoryId(UUID.randomUUID());
        categoryQuery.setDepth(1);
        categoryQuery.setStatus(CategoryStatus.ACTIVE);
    }

    @Test
    void testGetCategories() throws SQLException {
        // Prepare mock result set
        ResultSet resultSet = mock(ResultSet.class);
        when(resultSet.getString("category_id")).thenReturn(UUID.randomUUID().toString());
        when(resultSet.getString("name")).thenReturn("Category Name");
        when(resultSet.getString("ancestor_category_id")).thenReturn(UUID.randomUUID().toString());
        when(resultSet.getInt("depth")).thenReturn(1);
        when(resultSet.getInt("sort_order")).thenReturn(1);

        RowMapper<CategoryDto> rowMapper = mock(RowMapper.class);

        CategoryDto categoryDto = rowMapper.mapRow(resultSet, 1);

        // Mock jdbcTemplate.query() to return a list with the mock categoryDto
        when(jdbcTemplate.query(anyString(), any(MapSqlParameterSource.class), any(RowMapper.class)))
            .thenReturn(Collections.singletonList(categoryDto));

        // Call the method
        List<CategoryDto> result = customizedCategoryJpaDaoImpl.getCategories(categoryQuery);

        // Verify the results
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testGetCategoriesNoResults() {
        // Mock jdbcTemplate.query() to return an empty list
        when(jdbcTemplate.query(anyString(), any(MapSqlParameterSource.class), any(RowMapper.class)))
            .thenReturn(Collections.emptyList());

        // Call the method
        List<CategoryDto> result = customizedCategoryJpaDaoImpl.getCategories(categoryQuery);

        // Verify the results
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
