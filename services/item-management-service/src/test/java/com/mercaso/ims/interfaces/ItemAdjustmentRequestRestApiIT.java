package com.mercaso.ims.interfaces;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.infrastructure.util.FileUtil;
import java.io.File;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class ItemAdjustmentRequestRestApiIT extends AbstractIT {

    @MockBean
    protected DocumentApplicationService documentApplicationService;

    @Test
    void shouldSuccessWhenUploadItemAdjustmentRequestWithNewTemplate() throws Exception {

        String fileName = getFilePath("excel/NewTemplate1.xlsx");
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("NewTemplate1.xlsx");
        documentResponse.setSignedUrl("https://example.org/example");
        when(documentApplicationService.uploadExcel(any(), anyString())).thenReturn(documentResponse);
        when(documentApplicationService.downloadDocument("NewTemplate1.xlsx")).thenReturn(FileUtil.readFileToByteArray(new File(
            fileName)));
        ItemAdjustmentRequestDto result = itemAdjustmentRequestRestApiUtil.uploadItemAdjustmentRequest(new File(fileName),
            ItemAdjustmentRequestType.NEW_TEMPLATE_ADJUSTMENT);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(ItemAdjustmentRequestType.NEW_TEMPLATE_ADJUSTMENT, result.getType());
        Assertions.assertNotNull(result.getId());
    }

    private String getFilePath(String fileName) {
        return getClass().getClassLoader().getResource(fileName).getPath();

    }

}