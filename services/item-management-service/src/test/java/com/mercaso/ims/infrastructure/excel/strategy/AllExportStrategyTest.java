package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AllExportStrategyTest {

    @Mock
    private FinaleAdaptor finaleAdaptor;

    @Mock
    private VendorService vendorService;

    @Mock
    private BulkExportExcelGenerator excelGenerator;

    @Mock
    private ItemQueryApplicationService itemQueryApplicationService;

    @Mock
    private ItemSearchApplicationService itemSearchApplicationService;

    @Mock
    private Executor taskExecutor;

    @Spy
    @InjectMocks
    private AllExportStrategy allExportStrategy;

    private static final String CUSTOM_FILTER = "{\"status\":\"ACTIVE\"}";
    private List<ItemSerachDto> mockItems;
    private List<FinaleAvailableStockDto> mockFinaleData;
    private Map<UUID, Vendor> mockVendorMap;

    @BeforeEach
    void setUp() {
        mockItems = createMockItems();
        mockFinaleData = createMockFinaleData();
        mockVendorMap = createMockVendorMap();
    }

    @Test
    void shouldReturnCorreectExportType() {
        // When & Then
        assertEquals(ExportType.ALL, allExportStrategy.getExportType());
    }

    @Test
    void shouldExecuteSuccessfully() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        doReturn(mockItems).when(allExportStrategy).fetchFullItemData(CUSTOM_FILTER);
        when(finaleAdaptor.getAllProducts()).thenReturn(mockFinaleData);
        when(vendorService.findAll()).thenReturn(new ArrayList<>(mockVendorMap.values()));
        when(excelGenerator.generateBulkExportReport(
                mockItems,
                mockFinaleData,
                mockVendorMap,
                Collections.emptyList(),
                ExportType.ALL
        )).thenReturn(expectedBytes);

        // When
        byte[] result = allExportStrategy.execute(CUSTOM_FILTER);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);

        verify(allExportStrategy).fetchFullItemData(CUSTOM_FILTER);
        verify(finaleAdaptor).getAllProducts();
        verify(vendorService).findAll();
        verify(excelGenerator).generateBulkExportReport(
                mockItems, mockFinaleData, mockVendorMap, Collections.emptyList(), ExportType.ALL
        );
    }

    @Test
    void shouldHandleEmptyItemsList() {
        // Given
        List<ItemSerachDto> emptyItems = Collections.emptyList();
        byte[] expectedBytes = "Empty Excel content".getBytes();

        doReturn(emptyItems).when(allExportStrategy).fetchFullItemData(CUSTOM_FILTER);
        when(finaleAdaptor.getAllProducts()).thenReturn(mockFinaleData);
        when(vendorService.findAll()).thenReturn(new ArrayList<>(mockVendorMap.values()));
        when(excelGenerator.generateBulkExportReport(
                emptyItems,
                mockFinaleData,
                mockVendorMap,
                Collections.emptyList(),
                ExportType.ALL
        )).thenReturn(expectedBytes);

        // When
        byte[] result = allExportStrategy.execute(CUSTOM_FILTER);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);

        //
        verify(allExportStrategy).fetchFullItemData(CUSTOM_FILTER);
        verify(excelGenerator).generateBulkExportReport(
                emptyItems, mockFinaleData, mockVendorMap, Collections.emptyList(), ExportType.ALL
        );
    }

    @Test
    void shouldHandleNullCustomFilter() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        doReturn(mockItems).when(allExportStrategy).fetchFullItemData(null);
        when(finaleAdaptor.getAllProducts()).thenReturn(mockFinaleData);
        when(vendorService.findAll()).thenReturn(new ArrayList<>(mockVendorMap.values()));
        when(excelGenerator.generateBulkExportReport(any(), any(), any(), any(), eq(ExportType.ALL))).thenReturn(expectedBytes);

        // When
        byte[] result = allExportStrategy.execute(null);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);

        verify(allExportStrategy).fetchFullItemData(null);
    }

    private List<ItemSerachDto> createMockItems() {
        List<ItemSerachDto> items = new ArrayList<>();
        ItemSerachDto item1 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU001")
                .title("Test Item 1")
                .availabilityStatus("ACTIVE")
                .categoryId(UUID.randomUUID())
                .brandId(UUID.randomUUID())
                .primaryVendorId(UUID.randomUUID())
                .regPrice(new BigDecimal("10.00"))
                .build();
        items.add(item1);
        return items;
    }

    private List<FinaleAvailableStockDto> createMockFinaleData() {
        List<FinaleAvailableStockDto> finaleData = new ArrayList<>();
        FinaleAvailableStockDto stockDto = new FinaleAvailableStockDto();
        stockDto.setSku("SKU001");
        stockDto.setShopifyQoh(100);
        stockDto.setMfcQoh(50);
        stockDto.setReservationsQoh(10);
        finaleData.add(stockDto);
        return finaleData;
    }

    private Map<UUID, Vendor> createMockVendorMap() {
        Map<UUID, Vendor> vendorMap = new HashMap<>();
        UUID vendorId = UUID.randomUUID();
        Vendor vendor = Vendor.builder()
                .id(vendorId)
                .vendorName("Test Vendor")
                .vendorStatus(VendorStatus.ACTIVE)
                .externalPicking(false)
                .build();
        vendorMap.put(vendorId, vendor);
        return vendorMap;
    }
}