package com.mercaso.ims.application.searchservice.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.dto.ItemCostCollectionListDto;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.CustomizedItemCostCollectionJpaDao;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionQueryUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionSearchUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ItemCostCollectionSearchApplicationServiceImplTest {

    @Mock
    CustomizedItemCostCollectionJpaDao customizedItemCostCollectionJpaDao;
    @Mock
    DocumentApplicationService documentApplicationService;
    @InjectMocks
    ItemCostCollectionSearchApplicationServiceImpl itemCostCollectionSearchApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSearchItemCostCollectionList() {
        UUID id = UUID.randomUUID();
        ItemCostCollectionDetailDto collectionSearchDto = ItemCostCollectionSearchUtil.buildItemCostCollectionDto(id);
        when(customizedItemCostCollectionJpaDao.fetchItemCostCollectionDtoList(any(ItemCostCollectionQuery.class))).thenReturn(
            List.of(collectionSearchDto));
        when(customizedItemCostCollectionJpaDao.countQuery(any(ItemCostCollectionQuery.class))).thenReturn(1L);
        when(documentApplicationService.getImsUrl(anyString())).thenReturn("getImsUrlResponse");

        ItemCostCollectionListDto result = itemCostCollectionSearchApplicationServiceImpl.searchItemCostCollectionList(
            ItemCostCollectionQueryUtil.buildItemCostCollectionQuery());
        Assertions.assertEquals(1l, result.getTotalCount());
        Assertions.assertEquals(id, result.getData().get(0).getId());
    }
}