package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingOutputDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingResultDto;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.domain.difyworkflowsrecord.enums.WorkflowStatus;
import com.mercaso.ims.domain.difyworkflowsrecord.service.DifyWorkflowRecordService;
import com.mercaso.ims.infrastructure.external.dify.DifyApiClient;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DifyworkflowApplicationServiceImplTest {

    @Mock
    private DifyApiClient difyApiClient;

    @Mock
    private DifyWorkflowRecordService difyWorkflowRecordService;

    @InjectMocks
    private DifyworkflowApplicationServiceImpl difyworkflowApplicationService;

    private static final String VALID_DESCRIPTION = "Test product description";
    private static final String VALID_WORKFLOW_ID = "550e8400-e29b-41d4-a716-************";
    private static final String VALID_CATEGORY_ID = "550e8400-e29b-41d4-a716-************";
  private static final String VALID_JSON_RESULT =
      """
        {
        "output":{
            "finalResult": {
                "matchedClazzId": "550e8400-e29b-41d4-a716-************",
                "matchedCategory": "Test Category"
            },
            "analysis": "Analysis result"
            }
        }
        """;

    private DifyWorkflowResult validWorkflowResult;

    @BeforeEach
    void setUp() {
        validWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId(VALID_WORKFLOW_ID)
                .totalTokens(100L)
                .totalSteps(5)
                .elapsedTime(2.5)
                .result(VALID_JSON_RESULT)
                .status("SUCCEEDED")
                .build();

    }

    @Test
    void getRecommendedCategories_Success() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .matchedCategory("Test Category")
                        .build())
                .analysis("Analysis result")
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());
            assertEquals("Analysis result", result.getAnalysis());

            verify(difyApiClient).callDifyWorkflow("description", VALID_DESCRIPTION);
            verify(difyWorkflowRecordService).save(any(DifyWorkflowRecord.class));
        }
    }

    @Test
    void getRecommendedCategories_BlankDescription_ReturnsEmptyResult() {
        DifyCategoryMatchingFinalResultDto result1 = difyworkflowApplicationService
                .getRecommendedCategories(null);
        assertNotNull(result1);
        assertNull(result1.getFinalResult());

        DifyCategoryMatchingFinalResultDto result2 = difyworkflowApplicationService
                .getRecommendedCategories("");
        assertNotNull(result2);
        assertNull(result2.getFinalResult());

        DifyCategoryMatchingFinalResultDto result3 = difyworkflowApplicationService
                .getRecommendedCategories("   ");
        assertNotNull(result3);
        assertNull(result3.getFinalResult());

    }

    @Test
    void getRecommendedCategories_DifyApiThrowsException_ReturnsEmptyResult() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenThrow(new RuntimeException("API Call failed"));

        assertThrows(Exception.class, () -> difyworkflowApplicationService.getRecommendedCategories(VALID_DESCRIPTION));

        verify(difyApiClient).callDifyWorkflow("description", VALID_DESCRIPTION);
    }

    @Test
    void getRecommendedCategories_SaveRecordFails_ContinuesExecution() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenThrow(new RuntimeException("Save failed"));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());

            verify(difyWorkflowRecordService).save(any(DifyWorkflowRecord.class));
        }
    }

    @Test
    void getRecommendedCategories_BlankWorkflowResult_ReturnsEmptyResult() throws Exception {
        DifyWorkflowResult blankResult = DifyWorkflowResult.builder()
                .workflowRunId(VALID_WORKFLOW_ID)
                .result("")
                .build();

        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(blankResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                .getRecommendedCategories(VALID_DESCRIPTION);

        assertNotNull(result);
        assertNull(result.getFinalResult());

        verify(difyWorkflowRecordService).save(any(DifyWorkflowRecord.class));
    }

    @Test
    void getRecommendedCategories_DeserializationFails_ReturnsEmptyResult() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenThrow(new RuntimeException("Deserialization failed"));

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNull(result.getFinalResult());

        }
    }

    @Test
    void getRecommendedCategories_NullFinalResult_ReturnsEmptyResult() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto dtoWithNullFinalResult = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(null)
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(dtoWithNullFinalResult).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNull(result.getFinalResult());

        }
    }

    @Test
    void getRecommendedCategories_InvalidCategoryId_ReturnsEmptyResult() throws Exception {
        String[] invalidIds = {"", "   ", "N/A", null};

        for (String invalidId : invalidIds) {
            when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                    .thenReturn(validWorkflowResult);
            when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                    .thenReturn(mock(DifyWorkflowRecord.class));

            DifyCategoryMatchingFinalResultDto dtoWithInvalidId = DifyCategoryMatchingFinalResultDto.builder()
                    .finalResult(DifyCategoryMatchingResultDto.builder()
                            .matchedClazzId(invalidId)
                            .build())
                    .build();

            DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(dtoWithInvalidId).build();

            try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
                mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                        .thenReturn(difyCategoryMatchingOutputDto);

                DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                        .getRecommendedCategories(VALID_DESCRIPTION);

                assertNotNull(result);
                assertNotNull(result.getFinalResult());
                assertEquals(invalidId, result.getFinalResult().getMatchedClazzId());

            }
            
            reset(difyApiClient, difyWorkflowRecordService);
        }
    }

    @Test
    void getRecommendedCategories_InvalidUUIDFormat_ReturnsEmptyResult() throws Exception {
        String invalidUUID = "Not valid UUID";
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto dtoWithInvalidUUID = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(invalidUUID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(dtoWithInvalidUUID).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());

        }
    }

    @Test
    void getRecommendedCategories_CategoryNotFound_ReturnsEmptyResult() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());

        }
    }

    @Test
    void getRecommendedCategories_CategoryServiceThrowsException_ReturnsEmptyResult() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());

        }
    }

    @Test
    void getRecommendedCategories_WorkflowRecordWithNullValues() throws Exception {
        DifyWorkflowResult resultWithNulls = DifyWorkflowResult.builder()
                .workflowRunId(null)
                .totalTokens(null)
                .totalSteps(null)
                .elapsedTime(null)
                .result(VALID_JSON_RESULT)
                .status(null)
                .build();

        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(resultWithNulls);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertNotNull(result.getFinalResult());
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());

            verify(difyWorkflowRecordService).save(argThat(difyWorkflowRecord ->
                difyWorkflowRecord.getWorkflowId() == null &&
                difyWorkflowRecord.getStatus() == null &&
                difyWorkflowRecord.getTotalTokens() == null &&
                difyWorkflowRecord.getTotalSteps() == null &&
                difyWorkflowRecord.getElapsedTime() == null
            ));
        }
    }

    @Test
    void getRecommendedCategories_VerifyWorkflowRecordBuilding() throws Exception {
        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(validWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            difyworkflowApplicationService.getRecommendedCategories(VALID_DESCRIPTION);

            verify(difyWorkflowRecordService).save(argThat(difyWorkflowRecord ->
                VALID_DESCRIPTION.equals(difyWorkflowRecord.getInput()) &&
                UUID.fromString(VALID_WORKFLOW_ID).equals(difyWorkflowRecord.getWorkflowId()) &&
                Long.valueOf(100L).equals(difyWorkflowRecord.getTotalTokens()) &&
                Integer.valueOf(5).equals(difyWorkflowRecord.getTotalSteps()) &&
                Double.valueOf(2.5).equals(difyWorkflowRecord.getElapsedTime()) &&
                VALID_JSON_RESULT.equals(difyWorkflowRecord.getResult()) &&
                WorkflowStatus.SUCCEEDED.equals(difyWorkflowRecord.getStatus())
            ));
        }
    }

    @Test
    void getRecommendedCategories_InvalidWorkflowStatus() throws Exception {
        DifyWorkflowResult resultWithInvalidStatus = DifyWorkflowResult.builder()
                .workflowRunId(VALID_WORKFLOW_ID)
                .result(VALID_JSON_RESULT)
                .status("INVALID_STATUS")
                .build();

        when(difyApiClient.callDifyWorkflow("description", VALID_DESCRIPTION))
                .thenReturn(resultWithInvalidStatus);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(mock(DifyWorkflowRecord.class));

        DifyCategoryMatchingFinalResultDto expectedDto = DifyCategoryMatchingFinalResultDto.builder()
                .finalResult(DifyCategoryMatchingResultDto.builder()
                        .matchedClazzId(VALID_CATEGORY_ID)
                        .build())
                .build();

        DifyCategoryMatchingOutputDto difyCategoryMatchingOutputDto = DifyCategoryMatchingOutputDto.builder().output(expectedDto).build();

        try (MockedStatic<SerializationUtils> mockedUtils = mockStatic(SerializationUtils.class)) {
            mockedUtils.when(() -> SerializationUtils.deserialize(VALID_JSON_RESULT, DifyCategoryMatchingOutputDto.class))
                    .thenReturn(difyCategoryMatchingOutputDto);

            DifyCategoryMatchingFinalResultDto result = difyworkflowApplicationService
                    .getRecommendedCategories(VALID_DESCRIPTION);

            assertNotNull(result);
            assertEquals(VALID_CATEGORY_ID, result.getFinalResult().getMatchedClazzId());

            verify(difyWorkflowRecordService).save(argThat(difyWorkflowRecord -> difyWorkflowRecord.getStatus() == null));
        }
    }
} 