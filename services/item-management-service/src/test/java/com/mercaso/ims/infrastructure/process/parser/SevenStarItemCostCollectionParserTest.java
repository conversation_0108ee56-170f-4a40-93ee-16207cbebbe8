package com.mercaso.ims.infrastructure.process.parser;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.data.SevenStarItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.processor.SevenStarItemDailyUpdateSheetProcessor;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class SevenStarItemCostCollectionParserTest {

    @Mock
    ItemCostCollectionService itemCostCollectionService;
    @Mock
    SevenStarItemDailyUpdateSheetProcessor sevenStarItemDailyUpdateSheetProcessor;
    @InjectMocks
    SevenStarItemCostCollectionParser sevenStarItemCostCollectionParser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testParse() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        SevenStarItemDailyUpdatedData sevenStarData = new SevenStarItemDailyUpdatedData(
            "itemCode",
            "itemDescription",
            "upcCode",
            1,
            "pUom",
            "1",
            "sUom",
            "1",
            "1",
            "1",
            "1",
            "1",
            "1",
            "1");
        when(itemCostCollectionService.findById(any(UUID.class))).thenReturn(itemCostCollection);
        when(sevenStarItemDailyUpdateSheetProcessor.process(anyString())).thenReturn(List.of(sevenStarData));

        List<ItemCostCollectionItemParsingResultDto> result = sevenStarItemCostCollectionParser.parse(new UUID(0L, 0L));
        Assertions.assertEquals(sevenStarData.getItemCode(), result.get(0).getVendorSkuNumber());

    }

    @Test
    void testIsSupported() {
        boolean result = sevenStarItemCostCollectionParser.isSupported(VendorConstant.SEVEN_STAR,
            ItemCostCollectionSources.SEVEN_STAR_DAILY_ITEM_LISTS);
        Assertions.assertEquals(true, result);
    }

    @Test
    void testIsUpdateAvailability() {
        boolean result = sevenStarItemCostCollectionParser.isUpdateAvailability();
        Assertions.assertEquals(true, result);
    }
}