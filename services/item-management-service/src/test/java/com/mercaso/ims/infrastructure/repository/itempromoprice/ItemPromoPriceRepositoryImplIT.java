package com.mercaso.ims.infrastructure.repository.itempromoprice;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.utils.itempromoprice.ItemPromoPriceUtil;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemPromoPriceRepositoryImplIT extends AbstractIT {


    @Test
    void testFindByPromoBeginTimeBetween() {
        Instant now = Instant.now();
        Instant start = now.minus(1, ChronoUnit.SECONDS);
        Instant end = now.plus(1, ChronoUnit.SECONDS);
        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        ItemPromoPrice itemPromoPrice = ItemPromoPriceUtil.buildItemPromoPrice(itemDo.getId());
        itemPromoPrice.setPromoBeginTime(now);
        itemPromoPrice.setPromoFlag(false);
        itemPromoPriceRepositoryImpl.save(itemPromoPrice);

        List<ItemPromoPrice> result = itemPromoPriceRepositoryImpl.findByPromoBeginTimeBetween(start, end);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(itemDo.getId(), result.get(0).getItemId());

    }

    @Test
    void testFindByPromoEndTimeBetween() {
        Instant now = Instant.now();
        Instant start = now.minus(1, ChronoUnit.SECONDS);
        Instant end = now.plus(1, ChronoUnit.SECONDS);
        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        ItemPromoPrice itemPromoPrice = ItemPromoPriceUtil.buildItemPromoPrice(itemDo.getId());
        itemPromoPrice.setPromoEndTime(now);
        itemPromoPrice.setPromoFlag(false);
        itemPromoPriceRepositoryImpl.save(itemPromoPrice);

        List<ItemPromoPrice> result = itemPromoPriceRepositoryImpl.findByPromoEndTimeBetween(start, end);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(itemDo.getId(), result.get(0).getItemId());
    }
}