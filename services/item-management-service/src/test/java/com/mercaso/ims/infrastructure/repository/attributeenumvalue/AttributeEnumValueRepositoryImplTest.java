package com.mercaso.ims.infrastructure.repository.attributeenumvalue;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValue;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.AttributeEnumValueJpaDao;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.mapper.AttributeEnumValueDoMapper;
import com.mercaso.ims.utils.attributeenumvalue.AttributeEnumValueUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {AttributeEnumValueRepositoryImpl.class})
class AttributeEnumValueRepositoryImplTest extends AbstractTest {

    @MockBean
    private AttributeEnumValueDoMapper attributeEnumValueDoMapper;

    @MockBean
    private AttributeEnumValueJpaDao attributeEnumValueJpaDao;

    @Autowired
    private AttributeEnumValueRepositoryImpl attributeEnumValueRepositoryImpl;


    @Test
    void testSaveWithAttributeEnumValue_thenReturnNull() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueJpaDao.save(Mockito.<AttributeEnumValueDo>any())).thenReturn(attributeEnumValueDo);

        AttributeEnumValueDo attributeEnumValueDo2 = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any())).thenReturn(null);
        when(attributeEnumValueDoMapper.domainToDo(Mockito.<AttributeEnumValue>any())).thenReturn(attributeEnumValueDo2);

        // Act
        AttributeEnumValue actualSaveResult = attributeEnumValueRepositoryImpl.save(null);

        // Assert
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueDoMapper).domainToDo(isNull());
        verify(attributeEnumValueJpaDao).save(isA(AttributeEnumValueDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSaveWithAttributeEnumValue_thenThrowImsBusinessException() {
        // Arrange
        when(attributeEnumValueDoMapper.domainToDo(Mockito.<AttributeEnumValue>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> attributeEnumValueRepositoryImpl.save(null));
        verify(attributeEnumValueDoMapper).domainToDo(isNull());
    }

    @Test
    void testFindByIdWithUuid_thenReturnNull() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        Optional<AttributeEnumValueDo> ofResult = Optional.of(attributeEnumValueDo);
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any())).thenReturn(null);

        // Act
        AttributeEnumValue actualFindByIdResult = attributeEnumValueRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        Optional<AttributeEnumValueDo> ofResult = Optional.of(attributeEnumValueDo);
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeEnumValueRepositoryImpl.findById(uuid));
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
    }

    @Test
    void testUpdateWithAttributeEnumValue_thenReturnNull() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        Optional<AttributeEnumValueDo> ofResult = Optional.of(attributeEnumValueDo);

        AttributeEnumValueDo attributeEnumValueDo2 = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueJpaDao.save(Mockito.<AttributeEnumValueDo>any())).thenReturn(attributeEnumValueDo2);
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        AttributeEnumValueDo attributeEnumValueDo3 = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any())).thenReturn(null);
        when(attributeEnumValueDoMapper.domainToDo(Mockito.<AttributeEnumValue>any())).thenReturn(attributeEnumValueDo3);
        AttributeEnumValue domain = mock(AttributeEnumValue.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        AttributeEnumValue actualUpdateResult = attributeEnumValueRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueDoMapper).domainToDo(isA(AttributeEnumValue.class));
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
        verify(attributeEnumValueJpaDao).save(isA(AttributeEnumValueDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testUpdateWithAttributeEnumValue_thenThrowImsBusinessException() {
        // Arrange
        Optional<AttributeEnumValueDo> emptyResult = Optional.empty();
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);
        AttributeEnumValue domain = mock(AttributeEnumValue.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> attributeEnumValueRepositoryImpl.update(domain));
        verify(domain).getId();
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testDeleteById_givenAttributeEnumValueDoMapperDoToDomainReturnNull() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        Optional<AttributeEnumValueDo> ofResult = Optional.of(attributeEnumValueDo);

        AttributeEnumValueDo attributeEnumValueDo2 = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueJpaDao.save(Mockito.<AttributeEnumValueDo>any())).thenReturn(attributeEnumValueDo2);
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any())).thenReturn(null);

        // Act
        AttributeEnumValue actualDeleteByIdResult = attributeEnumValueRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
        verify(attributeEnumValueJpaDao).save(isA(AttributeEnumValueDo.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testDeleteById_givenAttributeEnumValueJpaDaoFindByIdReturnEmpty_thenReturnNull() {
        // Arrange
        Optional<AttributeEnumValueDo> emptyResult = Optional.empty();
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        AttributeEnumValue actualDeleteByIdResult = attributeEnumValueRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        AttributeEnumValueDo attributeEnumValueDo = AttributeEnumValueUtil.buildAttributeEnumValue();
        Optional<AttributeEnumValueDo> ofResult = Optional.of(attributeEnumValueDo);

        AttributeEnumValueDo attributeEnumValueDo2 = AttributeEnumValueUtil.buildAttributeEnumValue();
        when(attributeEnumValueJpaDao.save(Mockito.<AttributeEnumValueDo>any())).thenReturn(attributeEnumValueDo2);
        when(attributeEnumValueJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(attributeEnumValueDoMapper.doToDomain(Mockito.<AttributeEnumValueDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeEnumValueRepositoryImpl.deleteById(uuid));
        verify(attributeEnumValueDoMapper).doToDomain(isA(AttributeEnumValueDo.class));
        verify(attributeEnumValueJpaDao).findById(isA(UUID.class));
        verify(attributeEnumValueJpaDao).save(isA(AttributeEnumValueDo.class));
    }


    @Test
    void testFindByAttributeId_givenAttributeEnumValueDoMapper_thenReturnEmpty() {
        // Arrange
        when(attributeEnumValueJpaDao.findByAttributeId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<AttributeEnumValue> actualFindByAttributeIdResult = attributeEnumValueRepositoryImpl
            .findByAttributeId(UUID.randomUUID());

        // Assert
        verify(attributeEnumValueJpaDao).findByAttributeId(isA(UUID.class));
        assertTrue(actualFindByAttributeIdResult.isEmpty());
    }
}
