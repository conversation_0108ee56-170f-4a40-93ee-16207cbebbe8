package com.mercaso.ims.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class BarcodeValidatorTest {

    @Test
    void testIsValidUPC() {

        boolean validUPC = BarcodeValidator.isValidBarcode("01213104");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("121310");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("9555755800036");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("7501058617866");
        assertTrue(validUPC);

        validUPC = BarcodeValidator.isValidBarcode("10018700000258");
        assertTrue(validUPC);
    }

    @Test
    void testIsValidUPCFalse() {
        boolean validUPC = BarcodeValidator.isValidBarcode("");
        assertFalse(validUPC);
    }

    @Test
    void testIsValidUPCLengthFalse() {
        boolean validUPC = BarcodeValidator.isValidBarcode("9555755800036122121");
        assertFalse(validUPC);
    }

}
