package com.mercaso.ims.infrastructure.process.parser;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.api.client.util.Lists;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class VernonItemCostCollectionParserTest {

    @Mock
    ItemCostCollectionService itemCostCollectionService;
    @Mock
    VernonAdaptor vernonAdaptor;
    @InjectMocks
    VernonItemCostCollectionParser vernonItemCostCollectionParser;
    @Mock
    private DocumentApplicationService documentApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testParse() throws IOException {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();

        Path path = Paths.get(getClass().getClassLoader().getResource("file/VernonOrderDetail.html").getPath());
        String htmlContent = Files.readString(path);

        when(itemCostCollectionService.findById(any(UUID.class))).thenReturn(itemCostCollection);
        when(vernonAdaptor.getOrderDetailPage(anyString())).thenReturn(htmlContent);
        when(vernonAdaptor.searchVernonItem(anyString())).thenReturn(Lists.newArrayList());

        List<ItemCostCollectionItemParsingResultDto> result = vernonItemCostCollectionParser.parse(new UUID(0L, 0L));
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void testIsSupported() {
        boolean result = vernonItemCostCollectionParser.isSupported(VendorConstant.VERNON_SALES,
            ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
        Assertions.assertEquals(true, result);
    }

    @Test
    @DisplayName("Test parse(UUID)")
    void testParse_assert() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getVendorCollectionNumber()).thenReturn("table.CsSaStatusDetail");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_ORDER);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(vernonAdaptor.getOrderDetailPage(Mockito.<String>any())).thenReturn("Order Detail Page");

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemCostCollection).getSource();
        verify(itemCostCollection).getVendorCollectionNumber();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        verify(vernonAdaptor).getOrderDetailPage("table.CsSaStatusDetail");
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID)")
    void testParse2() throws UnsupportedEncodingException {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getFileName()).thenReturn("foo.txt");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(documentApplicationService).downloadDocument("foo.txt");
        verify(itemCostCollection).getFileName();
        verify(itemCostCollection).getSource();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID)")
    void testParse3() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getFileName()).thenReturn("foo.txt");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any()))
            .thenThrow(new NumberFormatException("Error reading csv file"));

        // Act and Assert
        assertThrows(NumberFormatException.class,
            () -> vernonItemCostCollectionParser.parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(documentApplicationService).downloadDocument("foo.txt");
        verify(itemCostCollection).getFileName();
        verify(itemCostCollection).getSource();
        verify(itemCostCollectionService).findById(isA(UUID.class));
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Given {@link com.mercaso.ims.application.service.DocumentApplicationService} {@link com.mercaso.ims.application.service.DocumentApplicationService#downloadDocument(String)} return empty array of {@code byte}.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); given DocumentApplicationService downloadDocument(String) return empty array of byte")
    void testParse_givenDocumentApplicationServiceDownloadDocumentReturnEmptyArrayOfByte() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getFileName()).thenReturn("foo.txt");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn(new byte[]{});

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(documentApplicationService).downloadDocument("foo.txt");
        verify(itemCostCollection).getFileName();
        verify(itemCostCollection).getSource();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Given {@link ItemCostCollection} {@link ItemCostCollection#getFileName()} return {@code foo}.</li>
     *   <li>Then calls {@link com.mercaso.ims.application.service.DocumentApplicationService#downloadDocument(String)}.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); given ItemCostCollection getFileName() return 'foo'; then calls downloadDocument(String)")
    void testParse_givenItemCostCollectionGetFileNameReturnFoo_thenCallsDownloadDocument()
        throws UnsupportedEncodingException {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getFileName()).thenReturn("foo");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(documentApplicationService.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(documentApplicationService).downloadDocument("foo");
        verify(itemCostCollection).getFileName();
        verify(itemCostCollection).getSource();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Given {@link ItemCostCollection} {@link ItemCostCollection#getSource()} return {@code MANUAL_UPLOADED}.</li>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); given ItemCostCollection getSource() return 'MANUAL_UPLOADED'; then return Empty")
    void testParse_givenItemCostCollectionGetSourceReturnManualUploaded_thenReturnEmpty() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.MANUAL_UPLOADED);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemCostCollection).getSource();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Given {@link ItemCostCollectionService} {@link ItemCostCollectionService#findById(UUID)} return {@code null}.</li>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); given ItemCostCollectionService findById(UUID) return 'null'; then return Empty")
    void testParse_givenItemCostCollectionServiceFindByIdReturnNull_thenReturnEmpty() {
        // Arrange
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertTrue(actualParseResult.isEmpty());
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Given {@link VernonAdaptor} {@link VernonAdaptor#getOrderDetailPage(String)} throw {@link NumberFormatException#NumberFormatException(String)} with {@code foo}.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); given VernonAdaptor getOrderDetailPage(String) throw NumberFormatException(String) with 'foo'")
    void testParse_givenVernonAdaptorGetOrderDetailPageThrowNumberFormatExceptionWithFoo() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getVendorCollectionNumber()).thenReturn("42");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_ORDER);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(vernonAdaptor.getOrderDetailPage(Mockito.<String>any())).thenThrow(new NumberFormatException("foo"));

        // Act and Assert
        assertThrows(NumberFormatException.class,
            () -> vernonItemCostCollectionParser.parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemCostCollection).getSource();
        verify(itemCostCollection).getVendorCollectionNumber();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        verify(vernonAdaptor).getOrderDetailPage("42");
    }

    /**
     * Test {@link VernonItemCostCollectionParser#parse(UUID)}.
     * <ul>
     *   <li>Then calls {@link ItemCostCollection#getVendorCollectionNumber()}.</li>
     * </ul>
     * <p>
     * Method under test: {@link VernonItemCostCollectionParser#parse(UUID)}
     */
    @Test
    @DisplayName("Test parse(UUID); then calls getVendorCollectionNumber()")
    void testParse_thenCallsGetVendorCollectionNumber() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getVendorCollectionNumber()).thenReturn("42");
        when(itemCostCollection.getSource()).thenReturn(ItemCostCollectionSources.VERNON_ORDER);
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(vernonAdaptor.getOrderDetailPage(Mockito.<String>any())).thenReturn("Order Detail Page");

        // Act
        List<ItemCostCollectionItemParsingResultDto> actualParseResult = vernonItemCostCollectionParser
            .parse(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemCostCollection).getSource();
        verify(itemCostCollection).getVendorCollectionNumber();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        verify(vernonAdaptor).getOrderDetailPage("42");
        assertTrue(actualParseResult.isEmpty());
    }


}