package com.mercaso.ims.infrastructure.repository.phonenumber;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.domain.phone.enums.PhoneType;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.phonenumber.PhoneNumberTestUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

/**
 * Integration tests for PhoneNumberRepositoryImpl
 */
class PhoneNumberRepositoryImplIT extends AbstractIT {

    @Test
    void testSave_shouldSaveAndReturnPhoneNumber() {
        // Arrange
        PhoneNumber phoneNumber = PhoneNumberTestUtil.buildPhoneNumber();

        // Act
        PhoneNumber result = phoneNumberRepository.save(phoneNumber);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(phoneNumber.getEntityType(), result.getEntityType());
        assertEquals(phoneNumber.getEntityId(), result.getEntityId());
        assertEquals(phoneNumber.getPhoneType(), result.getPhoneType());
        assertEquals(phoneNumber.getPhoneNumber(), result.getPhoneNumber());
        assertEquals(phoneNumber.getExtension(), result.getExtension());
    }

    @Test
    void testFindById_whenEntityExists_shouldReturnEntity() {
        // Arrange
        PhoneNumber phoneNumber = PhoneNumberTestUtil.buildPhoneNumber();
        PhoneNumber saved = phoneNumberRepository.save(phoneNumber);

        // Act
        PhoneNumber result = phoneNumberRepository.findById(saved.getId());

        // Assert
        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());
        assertEquals(saved.getEntityType(), result.getEntityType());
        assertEquals(saved.getPhoneNumber(), result.getPhoneNumber());
    }

    @Test
    void testFindById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        PhoneNumber result = phoneNumberRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testUpdate_shouldUpdateAndReturnPhoneNumber() {
        // Arrange
        PhoneNumber phoneNumber = PhoneNumberTestUtil.buildPhoneNumber();
        PhoneNumber saved = phoneNumberRepository.save(phoneNumber);

        String newPhoneNumber = "************";
        saved.setPhoneNumber(newPhoneNumber);

        // Act
        PhoneNumber result = phoneNumberRepository.update(saved);

        // Assert
        assertNotNull(result);
        assertEquals(newPhoneNumber, result.getPhoneNumber());
        assertEquals(saved.getId(), result.getId());
    }

    @Test
    void testUpdate_whenEntityDoesNotExist_shouldThrowException() {
        // Arrange
        PhoneNumber phoneNumber = PhoneNumberTestUtil.buildPhoneNumber();
        phoneNumber.setId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ImsBusinessException.class, () -> phoneNumberRepository.update(phoneNumber));
    }

    @Test
    void testDeleteById_shouldMarkAsDeleted() {
        // Arrange
        PhoneNumber phoneNumber = PhoneNumberTestUtil.buildPhoneNumber();
        PhoneNumber saved = phoneNumberRepository.save(phoneNumber);

        // Act
        PhoneNumber result = phoneNumberRepository.deleteById(saved.getId());

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDeletedAt());
        assertNotNull(result.getDeletedBy());
    }

    @Test
    void testDeleteById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        PhoneNumber result = phoneNumberRepository.deleteById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindByEntityTypeAndEntityId_shouldReturnMatchingPhoneNumbers() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();

        PhoneNumber phoneNumber1 = PhoneNumberTestUtil.buildPhoneNumber(entityType, entityId, PhoneType.WORK);
        PhoneNumber phoneNumber2 = PhoneNumberTestUtil.buildPhoneNumber(entityType, entityId, PhoneType.MOBILE);
        PhoneNumber phoneNumber3 = PhoneNumberTestUtil.buildPhoneNumber("Customer", entityId, PhoneType.HOME);

        phoneNumberRepository.save(phoneNumber1);
        phoneNumberRepository.save(phoneNumber2);
        phoneNumberRepository.save(phoneNumber3);

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(pn -> entityType.equals(pn.getEntityType()) && entityId.equals(pn.getEntityId())));
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndPhoneType_shouldReturnMatchingPhoneNumbers() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        PhoneType phoneType = PhoneType.WORK;

        PhoneNumber phoneNumber1 = PhoneNumberTestUtil.buildPhoneNumber(entityType, entityId, phoneType);
        PhoneNumber phoneNumber2 = PhoneNumberTestUtil.buildPhoneNumber(entityType, entityId, PhoneType.MOBILE);

        phoneNumberRepository.save(phoneNumber1);
        phoneNumberRepository.save(phoneNumber2);

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityTypeAndEntityIdAndPhoneType(entityType, entityId, phoneType);

        // Assert
        assertEquals(1, result.size());
        assertEquals(phoneType, result.get(0).getPhoneType());
    }

    @Test
    void testFindByPhoneNumber_shouldReturnMatchingPhoneNumbers() {
        // Arrange
        String phoneNumber = "************";
        PhoneNumber phoneNumber1 = PhoneNumberTestUtil.buildPhoneNumber(phoneNumber);
        PhoneNumber phoneNumber2 = PhoneNumberTestUtil.buildPhoneNumber(phoneNumber);
        PhoneNumber phoneNumber3 = PhoneNumberTestUtil.buildPhoneNumber("************");

        phoneNumberRepository.save(phoneNumber1);
        phoneNumberRepository.save(phoneNumber2);
        phoneNumberRepository.save(phoneNumber3);

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByPhoneNumber(phoneNumber);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(pn -> phoneNumber.equals(pn.getPhoneNumber())));
    }

    @Test
    void testFindByEntityType_shouldReturnMatchingPhoneNumbers() {
        // Arrange
        String entityType = "Driver";
        PhoneNumber phoneNumber1 = PhoneNumberTestUtil.buildPhoneNumber(entityType, PhoneType.WORK);
        PhoneNumber phoneNumber2 = PhoneNumberTestUtil.buildPhoneNumber(entityType, PhoneType.MOBILE);
        PhoneNumber phoneNumber3 = PhoneNumberTestUtil.buildPhoneNumber("Customer", PhoneType.HOME);

        phoneNumberRepository.save(phoneNumber1);
        phoneNumberRepository.save(phoneNumber2);
        phoneNumberRepository.save(phoneNumber3);

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityType(entityType);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(pn -> entityType.equals(pn.getEntityType())));
    }

    @Test
    void testFindByEntityTypeAndEntityId_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";
        UUID entityId = UUID.randomUUID();

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndPhoneType_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        PhoneType phoneType = PhoneType.FAX;

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityTypeAndEntityIdAndPhoneType(entityType, entityId, phoneType);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByPhoneNumber_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String phoneNumber = "************";

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByPhoneNumber(phoneNumber);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityType_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";

        // Act
        List<PhoneNumber> result = phoneNumberRepository.findByEntityType(entityType);

        // Assert
        assertTrue(result.isEmpty());
    }
}
