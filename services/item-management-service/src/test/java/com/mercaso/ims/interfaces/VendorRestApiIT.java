package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.utils.vendor.VendorCommandUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class VendorRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenDeleteVendor() {
        String vendorName = RandomStringUtils.randomAlphabetic(5);

        Vendor vendor = buildDirectVendorData(vendorName);

        Vendor vendorDb = vendorService.findById(vendor.getId());

        assertNotNull(vendorDb);

        vendorRestApiUtil.deleteVendorRequest(vendorDb.getId());

        vendor = vendorService.findById(vendorDb.getId());

        assertNull(vendor);
    }


    @Test
    void shouldSuccessWhenCreateVendor() throws Exception {
        String vendorName = RandomStringUtils.randomAlphabetic(8);
        when(finaleExternalApiAdaptor.createVendor(anyString())).thenReturn(new FinaleVendorDto("1001", "test"));

        CreateVendorCommand command = VendorCommandUtil.buildCreateVendorCommand(vendorName);

        VendorDto result = vendorRestApiUtil.createVendorRequest(command);
        assertNotNull(result.getId());
        assertEquals(vendorName, result.getVendorName());

    }

    @Test
    void shouldSuccessWhenUpdateItem() throws Exception {
        String vendorName = RandomStringUtils.randomAlphabetic(5);

        Vendor vendor = buildVendorData(vendorName);

        String newVendorName = RandomStringUtils.randomAlphabetic(5);
        UpdateVendorCommand updateVendorCommand = VendorCommandUtil.buildUpdateVendorCommand(vendor.getId(), newVendorName);

        VendorDto vendorDto = vendorRestApiUtil.updateVendorRequest(updateVendorCommand.getVendorId(), updateVendorCommand);

        assertNotNull(vendorDto);
        assertEquals(newVendorName, vendorDto.getVendorName());
    }
}
