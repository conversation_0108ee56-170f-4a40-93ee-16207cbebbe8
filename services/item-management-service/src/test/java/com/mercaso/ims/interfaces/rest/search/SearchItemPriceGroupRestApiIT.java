package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.BatchBindingItemToPriceGroupCommand;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.application.dto.ItemPriceGroupListDto;
import com.mercaso.ims.application.query.ItemPriceGroupQuery;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class SearchItemPriceGroupRestApiIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSearchItemPriceGroup() throws Exception {

        Mockito.when(imsAlertConfig.getMaximumMargin()).thenReturn(BigDecimal.valueOf(0.5));
        Mockito.when(imsAlertConfig.getMinimumMargin()).thenReturn(BigDecimal.valueOf(0));
        Mockito.when(imsAlertConfig.getMaximumPrice()).thenReturn(BigDecimal.valueOf(500));
        Mockito.when(imsAlertConfig.getMinimumPrice()).thenReturn(BigDecimal.valueOf(1));
        String prefix = "test-" + UUID.randomUUID().toString().substring(0, 8) + "-";
        String groupName = prefix + RandomStringUtils.randomAlphabetic(5);

        ItemPriceGroupDto savedItemPriceGroup = createItemPriceGroup(groupName, BigDecimal.TEN);

        String sku = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo1 = buildItemData(sku);

        BatchBindingItemToPriceGroupCommand bindingCommand = BatchBindingItemToPriceGroupCommand.builder()
            .itemPriceGroupId(savedItemPriceGroup.getId())
            .itemIds(Arrays.asList(itemDo1.getId())).build();

        itemPriceGroupRestApiUtil.batchBindingItemPriceGroup(bindingCommand);

        ItemPriceGroupListDto result = searchItemPriceGroupRestApiUtil.searchItemPriceGroup(null, null, groupName, null);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getTotalCount());
        Assertions.assertEquals(1, result.getData().getFirst().getItemCount());
    }

    @Test
    void shouldSuccessWhenSearchItemPriceGroupSortedByGroupNameAsc() throws Exception {
        String prefix = "test-" + UUID.randomUUID().toString().substring(0, 8) + "-";
        createItemPriceGroup(prefix + "Alpha", BigDecimal.TEN);
        createItemPriceGroup(prefix + "Bravo", BigDecimal.TEN);
        createItemPriceGroup(prefix + "Charlie", BigDecimal.TEN);

        ItemPriceGroupListDto result = searchItemPriceGroupRestApiUtil.searchItemPriceGroup(
            null, null, prefix + "%", ItemPriceGroupQuery.SortType.GROUP_NAME_ASC);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(3, result.getTotalCount());
        Assertions.assertTrue(result.getData().get(0).getGroupName().contains("Alpha"));
        Assertions.assertTrue(result.getData().get(1).getGroupName().contains("Bravo"));
        Assertions.assertTrue(result.getData().get(2).getGroupName().contains("Charlie"));
    }

    @Test
    void shouldSuccessWhenSearchItemPriceGroupSortedByPriceDesc() throws Exception {
        String prefix = "test-" + UUID.randomUUID().toString().substring(0, 8) + "-";
        createItemPriceGroup(prefix + RandomStringUtils.randomAlphabetic(5), BigDecimal.valueOf(20));
        createItemPriceGroup(prefix + RandomStringUtils.randomAlphabetic(5), BigDecimal.valueOf(10));
        createItemPriceGroup(prefix + RandomStringUtils.randomAlphabetic(5), BigDecimal.valueOf(30));

        ItemPriceGroupListDto result = searchItemPriceGroupRestApiUtil.searchItemPriceGroup(
            null, null, prefix + "%", ItemPriceGroupQuery.SortType.PRICE_DESC);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(3, result.getTotalCount());
        Assertions.assertEquals(BigDecimal.valueOf(30), result.getData().get(0).getPrice());
        Assertions.assertEquals(BigDecimal.valueOf(20), result.getData().get(1).getPrice());
        Assertions.assertEquals(BigDecimal.valueOf(10), result.getData().get(2).getPrice());
    }

    private ItemPriceGroupDto createItemPriceGroup(String groupName, BigDecimal price) throws Exception {
        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(price)
            .build();
        return itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);
    }
}