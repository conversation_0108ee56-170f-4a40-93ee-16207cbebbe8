package com.mercaso.ims.infrastructure.process.matcher;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class SevenStarVendorItemMatcherTest {

    @Mock
    VendorItemService vendorItemService;
    @Mock
    VendorService vendorService;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @InjectMocks
    SevenStarVendorItemMatcher sevenStarVendorItemMatcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMatchItem() {
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        Vendor vendor = VendorUtil.buildVendor(UUID.randomUUID());

        when(vendorItemService.findByVendorIDAndVendorSkuNum(any(), anyString())).thenReturn(List.of(vendorItem));
        when(vendorService.findByVendorName(anyString())).thenReturn(vendor);
        List<VendorItemMappingDto> result = sevenStarVendorItemMatcher.matchItem(new ItemCostCollectionItemParsingResultDto(
            "vendorSkuNumber",
            "vendorItemName",
            "upc",
            Integer.valueOf(0),
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            Boolean.TRUE, null), UUID.randomUUID());
        Assertions.assertEquals(vendor.getId(), result.get(0).getVendorId());
    }

    @Test
    void testIsSupportedFor() {
        boolean result = sevenStarVendorItemMatcher.isSupported(VendorConstant.SEVEN_STAR,
            ItemCostCollectionSources.SEVEN_STAR_DAILY_ITEM_LISTS);
        Assertions.assertTrue(result);

    }
}