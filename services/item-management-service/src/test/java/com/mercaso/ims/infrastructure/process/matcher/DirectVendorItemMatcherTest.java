package com.mercaso.ims.infrastructure.process.matcher;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DirectVendorItemMatcherTest {

    @Mock
    VendorItemService vendorItemService;
    @Mock
    VendorService vendorService;
    @Mock
    ItemService itemService;
    @InjectMocks
    DirectVendorItemMatcher directVendorItemMatcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMatchItem() {
        Item item = ItemUtil.buildItem();
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        Vendor vendor = VendorUtil.buildVendor(UUID.randomUUID());
        when(vendorItemService.findByVendorIDAndItemId(any(UUID.class), any(UUID.class))).thenReturn(vendorItem);
        when(vendorService.findById(any(UUID.class))).thenReturn(vendor);
        when(itemService.findBySku(anyString())).thenReturn(item);

        List<VendorItemMappingDto> result = directVendorItemMatcher.matchItem(new ItemCostCollectionItemParsingResultDto(
            "vendorSkuNumber",
            "vendorItemName",
            "upc",
            Integer.valueOf(0),
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            Boolean.TRUE, null), UUID.randomUUID());
        Assertions.assertEquals(vendor.getId(), result.get(0).getVendorId());

    }

    @Test
    void testIsSupportedFor() {
        boolean result = directVendorItemMatcher.isSupported(VendorConstant.DIRECT_SUPPLIER,
            ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
        Assertions.assertTrue(result);
    }

    @Test
    void testGetSupportedCostType() {
        String result = directVendorItemMatcher.getSupportedCostType();
        Assertions.assertEquals(CostType.DIRECT_COST.getCostTypeName(), result);
    }
}