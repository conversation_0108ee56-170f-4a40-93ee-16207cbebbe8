package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestListDto;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import java.time.Instant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SearchItemAdjustmentRequestRestApiIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSearchItemAdjustmentRequest() throws Exception {
        String requestFile = "request_file_name";
        int createSuccessRowCount = 10;

        ItemAdjustmentRequest request = ItemAdjustmentRequest.builder()
            .requestFile(requestFile)
            .type(ItemAdjustmentRequestType.NEW_TEMPLATE_ADJUSTMENT)
            .status(ItemAdjustmentRequestStatus.COMPLETED)
            .createSuccessRowCount(createSuccessRowCount)
            .modifySuccessRowCount(1)
            .createdAt(Instant.now().minusSeconds(10))
            .build();

        itemAdjustmentRequestRepository.save(request);
        ItemAdjustmentRequestListDto result = searchItemAdjustmentRequestRestApiUtil.searchItemAdjustmentRequest(null,
            null,
            "");

        Assertions.assertNotNull(result);
        Assertions.assertEquals(createSuccessRowCount, result.getData().getFirst().getCreateSuccessRowCount());

    }


}