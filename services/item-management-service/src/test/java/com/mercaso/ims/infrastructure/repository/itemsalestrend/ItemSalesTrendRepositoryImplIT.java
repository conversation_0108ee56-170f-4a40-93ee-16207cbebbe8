package com.mercaso.ims.infrastructure.repository.itemsalestrend;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.itemsalestrend.ItemSalesTrendTestUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;


class ItemSalesTrendRepositoryImplIT extends AbstractIT {

    @Test
    void testSave_shouldSaveAndReturnItemSalesTrend() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        String skuNumber = "TEST-SKU-" + RandomStringUtils.randomAlphabetic(5);
        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrendForIntegrationTest(
            itemId, ItemSalesTrendTimeGrain.DAY, 100, skuNumber);

        // Act
        ItemSalesTrend result = itemSalesTrendRepository.save(itemSalesTrend);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(itemSalesTrend.getItemId(), result.getItemId());
        assertEquals(itemSalesTrend.getSkuNumber(), result.getSkuNumber());
        assertEquals(itemSalesTrend.getSalesQuantity(), result.getSalesQuantity());
        assertEquals(itemSalesTrend.getTimeGrain(), result.getTimeGrain());
    }

    @Test
    void testFindById_whenEntityExists_shouldReturnEntity() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        String skuNumber = "TEST-SKU-" + RandomStringUtils.randomAlphabetic(5);
        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrendForIntegrationTest(
            itemId, ItemSalesTrendTimeGrain.WEEK, 150, skuNumber);
        ItemSalesTrend saved = itemSalesTrendRepository.save(itemSalesTrend);

        // Act
        ItemSalesTrend result = itemSalesTrendRepository.findById(saved.getId());

        // Assert
        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());
        assertEquals(saved.getItemId(), result.getItemId());
        assertEquals(saved.getSkuNumber(), result.getSkuNumber());
        assertEquals(saved.getSalesQuantity(), result.getSalesQuantity());
        assertEquals(saved.getTimeGrain(), result.getTimeGrain());
    }

    @Test
    void testFindById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        ItemSalesTrend result = itemSalesTrendRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testUpdate_whenEntityDoesNotExist_shouldThrowException() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, ItemSalesTrendTimeGrain.DAY, 100);
        itemSalesTrend.setId(UUID.randomUUID()); // Set a non-existent ID

        // Act & Assert
        assertThrows(ImsBusinessException.class, () -> {
            itemSalesTrendRepository.update(itemSalesTrend);
        });
    }

    @Test
    void testDeleteById_whenEntityExists_shouldMarkAsDeleted() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, ItemSalesTrendTimeGrain.DAY, 100);
        itemSalesTrend.setId(null);
        ItemSalesTrend saved = itemSalesTrendRepository.save(itemSalesTrend);

        // Act
        ItemSalesTrend result = itemSalesTrendRepository.deleteById(saved.getId());

        // Assert
        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());

        // Verify entity is no longer findable (soft delete)
        ItemSalesTrend deletedEntity = itemSalesTrendRepository.findById(saved.getId());
        assertNull(deletedEntity);
    }

    @Test
    void testDeleteById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        ItemSalesTrend result = itemSalesTrendRepository.deleteById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindByItemIdAndTimeGrain_whenDataExists_shouldReturnData() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        String itemIdString = itemId.toString();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;

        // Create test data
        ItemSalesTrend trend1 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 100);
        trend1.setId(null);
        ItemSalesTrend trend2 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 150);
        trend2.setId(null);

        itemSalesTrendRepository.save(trend1);
        itemSalesTrendRepository.save(trend2);

        // Act
        List<ItemSalesTrend> result = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemIdString, timeGrain);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // Verify the results are reversed (newest first)
        assertTrue(result.stream().allMatch(trend -> trend.getItemId().equals(itemIdString)));
        assertTrue(result.stream().allMatch(trend -> trend.getTimeGrain().equals(timeGrain)));
    }

    @Test
    void testFindByItemIdAndTimeGrain_whenNoDataExists_shouldReturnEmptyList() {
        // Arrange
        String itemId = UUID.randomUUID().toString();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.WEEK;

        // Act
        List<ItemSalesTrend> result = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withDifferentTimeGrains_shouldFilterCorrectly() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        String itemIdString = itemId.toString();

        // Create data with different time grains
        ItemSalesTrend dayTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, ItemSalesTrendTimeGrain.DAY, 100);
        dayTrend.setId(null);
        ItemSalesTrend weekTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, ItemSalesTrendTimeGrain.WEEK, 200);
        weekTrend.setId(null);
        ItemSalesTrend monthTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, ItemSalesTrendTimeGrain.MONTH, 300);
        monthTrend.setId(null);

        itemSalesTrendRepository.save(dayTrend);
        itemSalesTrendRepository.save(weekTrend);
        itemSalesTrendRepository.save(monthTrend);

        // Act
        List<ItemSalesTrend> dayResults = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemIdString, ItemSalesTrendTimeGrain.DAY);
        List<ItemSalesTrend> weekResults = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemIdString, ItemSalesTrendTimeGrain.WEEK);
        List<ItemSalesTrend> monthResults = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemIdString, ItemSalesTrendTimeGrain.MONTH);

        // Assert
        assertEquals(1, dayResults.size());
        assertEquals(ItemSalesTrendTimeGrain.DAY, dayResults.getFirst().getTimeGrain());
        assertEquals(100, dayResults.getFirst().getSalesQuantity());

        assertEquals(1, weekResults.size());
        assertEquals(ItemSalesTrendTimeGrain.WEEK, weekResults.getFirst().getTimeGrain());
        assertEquals(200, weekResults.getFirst().getSalesQuantity());

        assertEquals(1, monthResults.size());
        assertEquals(ItemSalesTrendTimeGrain.MONTH, monthResults.getFirst().getTimeGrain());
        assertEquals(300, monthResults.getFirst().getSalesQuantity());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withDifferentItemIds_shouldFilterCorrectly() {
        // Arrange
        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;

        // Create data for different items
        ItemSalesTrend trend1 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId1, timeGrain, 100);
        trend1.setId(null);
        ItemSalesTrend trend2 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId2, timeGrain, 200);
        trend2.setId(null);

        itemSalesTrendRepository.save(trend1);
        itemSalesTrendRepository.save(trend2);

        // Act
        List<ItemSalesTrend> results1 = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemId1.toString(), timeGrain);
        List<ItemSalesTrend> results2 = itemSalesTrendRepository.findByItemIdAndTimeGrain(itemId2.toString(), timeGrain);

        // Assert
        assertEquals(1, results1.size());
        assertEquals(itemId1.toString(), results1.getFirst().getItemId());
        assertEquals(100, results1.getFirst().getSalesQuantity());

        assertEquals(1, results2.size());
        assertEquals(itemId2.toString(), results2.getFirst().getItemId());
        assertEquals(200, results2.getFirst().getSalesQuantity());
    }
}
