package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BatchUpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.BatchUpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemCostChangeRequestResultDto;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.mapper.itemcostcollection.ItemCostChangeRequestApplicationMapper;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.itemcostchangerequest.service.ItemCostChangeRequestService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestCommandUtil;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestDtoUtil;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ItemCostChangeRequestApplicationServiceImplTest {

    @Mock
    ItemCostChangeRequestService itemCostChangeRequestService;
    @Mock
    ItemCostChangeRequestApplicationMapper itemCostChangeRequestApplicationMapper;
    @Mock
    BusinessEventService businessEventService;
    @InjectMocks
    ItemCostChangeRequestApplicationServiceImpl itemCostChangeRequestApplicationServiceImpl;

    @Mock
    private ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testBatchApproveItemCostChangeRequest() {

        UUID itemCostCollectionId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID matchedItemId = UUID.randomUUID();
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto(
            itemCostCollectionId,
            vendorId,
            matchedItemId);
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest(itemCostCollectionId,
            vendorId,
            matchedItemId);
        itemCostChangeRequest.setStatus(ItemCostChangeRequestStatus.APPROVED);
        when(itemCostChangeRequestService.update(any(ItemCostChangeRequest.class))).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestService.findById(any())).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestApplicationMapper.domainToDto(any(ItemCostChangeRequest.class))).thenReturn(
            itemCostChangeRequestDto);

        BatchUpdateItemCostChangeRequestCommand command = ItemCostChangeRequestCommandUtil.buildBatchUpdateItemCostChangeRequestCommand(

            ItemCostChangeRequestStatus.APPROVED);

        BatchUpdateItemCostChangeRequestResultDto result = itemCostChangeRequestApplicationServiceImpl.batchApproveItemCostChangeRequest(
            command);

        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(0, result.getFailedIds().size());
    }

    @Test
    void testBatchRejectItemCostChangeRequest() {
        UUID itemCostCollectionId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID matchedItemId = UUID.randomUUID();
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto(
            itemCostCollectionId,
            vendorId,
            matchedItemId);
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest(itemCostCollectionId,
            vendorId,
            matchedItemId);
        itemCostChangeRequest.setStatus(ItemCostChangeRequestStatus.REJECTED);
        when(itemCostChangeRequestService.update(any(ItemCostChangeRequest.class))).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestService.findById(any())).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestApplicationMapper.domainToDto(any(ItemCostChangeRequest.class))).thenReturn(
            itemCostChangeRequestDto);

        BatchUpdateItemCostChangeRequestCommand command = ItemCostChangeRequestCommandUtil.buildBatchUpdateItemCostChangeRequestCommand(

            ItemCostChangeRequestStatus.REJECTED);

        BatchUpdateItemCostChangeRequestResultDto result = itemCostChangeRequestApplicationServiceImpl.batchRejectItemCostChangeRequest(
            command);

        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(0, result.getFailedIds().size());
    }

    @Test
    void testUpdateItemCostChangeRequest() {
        UUID id = UUID.randomUUID();
        UUID itemCostCollectionId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID matchedItemId = UUID.randomUUID();
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto(
            itemCostCollectionId,
            vendorId,
            matchedItemId);
        itemCostChangeRequestDto.setStatus(ItemCostChangeRequestStatus.APPROVED);

        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest(itemCostCollectionId,
            vendorId,
            matchedItemId);
        when(itemCostChangeRequestService.update(any(ItemCostChangeRequest.class))).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestService.findById(any())).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestApplicationMapper.domainToDto(any(ItemCostChangeRequest.class))).thenReturn(
            itemCostChangeRequestDto);

        UpdateItemCostChangeRequestCommand command = ItemCostChangeRequestCommandUtil.buildUpdateItemCostChangeRequestCommand(
            id,
            BigDecimal.valueOf(10),
            ItemCostChangeRequestStatus.APPROVED);

        ItemCostChangeRequestDto result = itemCostChangeRequestApplicationServiceImpl.updateItemCostChangeRequest(command);

        Assertions.assertEquals(ItemCostChangeRequestStatus.APPROVED, result.getStatus());

    }

    @Test
    void testCreateItemCostChangeRequest() {
        UUID itemCostCollectionId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID matchedItemId = UUID.randomUUID();
        ItemCostChangeRequestDto itemCostChangeRequestDto = ItemCostChangeRequestDtoUtil.buildItemCostChangeRequestDto(
            itemCostCollectionId,
            vendorId,
            matchedItemId);
        ItemCostChangeRequest itemCostChangeRequest = ItemCostChangeRequestUtil.buildItemCostChangeRequest(itemCostCollectionId,
            vendorId,
            matchedItemId);
        when(itemCostChangeRequestService.save(any(ItemCostChangeRequest.class))).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestApplicationMapper.domainToDto(any(ItemCostChangeRequest.class))).thenReturn(
            itemCostChangeRequestDto);

        CreateItemCostChangeRequestCommand command = ItemCostChangeRequestCommandUtil.buildCreateItemCostChangeRequestCommand(
            itemCostCollectionId,
            vendorId,
            matchedItemId);

        ItemCostChangeRequestDto result = itemCostChangeRequestApplicationServiceImpl.createItemCostChangeRequest(command);
        Assertions.assertEquals(ItemCostChangeRequestStatus.PENDING, result.getStatus());
    }

    @Test
    void testBatchChangeItemTargetCostChangeRequest() {
        // Arrange
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(null);

        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = idResult
            .previousCost(new BigDecimal("2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult = previousCostResult.targetCost(new BigDecimal("2.3")).build();
        updateItemTargetCostChangeRequestCommands.add(buildResult);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
        List<UUID> failedIds = actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds();
        assertEquals(1, failedIds.size());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", failedIds.get(0).toString());
        assertEquals(0, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
    }


    @Test
    void testBatchChangeItemTargetCostChangeRequest2() {
        // Arrange
        ItemCostChangeRequest itemCostChangeRequest = mock(ItemCostChangeRequest.class);
        doNothing().when(itemCostChangeRequest).setTargetCost(Mockito.<BigDecimal>any());
        when(itemCostChangeRequestService.update(Mockito.<ItemCostChangeRequest>any())).thenReturn(null);
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(itemCostChangeRequest);
        when(itemCostChangeRequestApplicationMapper.domainToDto(Mockito.<ItemCostChangeRequest>any()))
            .thenThrow(new ImsBusinessException("Item target cost change request updated: {}"));

        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = idResult
            .previousCost(new BigDecimal("2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult = previousCostResult.targetCost(new BigDecimal("2.3")).build();
        updateItemTargetCostChangeRequestCommands.add(buildResult);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        verify(itemCostChangeRequestApplicationMapper).domainToDto(isA(ItemCostChangeRequest.class));
        verify(itemCostChangeRequest).setTargetCost(isA(BigDecimal.class));
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
        verify(itemCostChangeRequestService).update(isA(ItemCostChangeRequest.class));
        List<UUID> failedIds = actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds();
        assertEquals(1, failedIds.size());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", failedIds.get(0).toString());
        assertEquals(0, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
    }


    @Test
    void testBatchChangeItemTargetCostChangeRequest3() {
        // Arrange
        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = idResult
            .previousCost(new BigDecimal("-2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult = previousCostResult.targetCost(new BigDecimal("2.3")).build();
        updateItemTargetCostChangeRequestCommands.add(buildResult);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        List<UUID> failedIds = actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds();
        assertEquals(1, failedIds.size());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", failedIds.get(0).toString());
        assertEquals(0, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
    }


    @Test
    void testBatchChangeItemTargetCostChangeRequest4() {
        // Arrange
        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .previousCost(null);
        UpdateItemTargetCostChangeRequestCommand buildResult = previousCostResult.targetCost(new BigDecimal("2.3")).build();
        updateItemTargetCostChangeRequestCommands.add(buildResult);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        List<UUID> failedIds = actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds();
        assertEquals(1, failedIds.size());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", failedIds.get(0).toString());
        assertEquals(0, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
    }


    @Test
    void testBatchChangeItemTargetCostChangeRequest_thenReturnFailedIdsEmpty() {
        // Arrange
        BatchUpdateItemTargetCostChangeRequestCommand.BatchUpdateItemTargetCostChangeRequestCommandBuilder builderResult = BatchUpdateItemTargetCostChangeRequestCommand
            .builder();
        BatchUpdateItemTargetCostChangeRequestCommand command = builderResult
            .updateItemTargetCostChangeRequestCommands(new ArrayList<>())
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        assertEquals(0, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
        assertTrue(actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds().isEmpty());
    }


    @Test
    void testBatchChangeItemTargetCostChangeRequest_thenReturnUpdatedCountIsOne() {
        // Arrange
        ItemCostChangeRequest itemCostChangeRequest = mock(ItemCostChangeRequest.class);
        doNothing().when(itemCostChangeRequest).setTargetCost(Mockito.<BigDecimal>any());
        when(itemCostChangeRequestService.update(Mockito.<ItemCostChangeRequest>any())).thenReturn(null);
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(itemCostChangeRequest);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder builderResult = ItemCostChangeRequestDto.builder();
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder matchTypeResult = builderResult.crv(new BigDecimal("2.3"))
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemCostCollectionId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .matchType(MatchedType.MISS_MATCHED);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder statusResult = matchTypeResult
            .previousCost(new BigDecimal("2.3"))
            .skuNumber("42")
            .status(ItemCostChangeRequestStatus.PENDING);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder targetCostResult = statusResult
            .targetCost(new BigDecimal("2.3"));
        ItemCostChangeRequestDto buildResult = targetCostResult.tax(new BigDecimal("2.3"))
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorSkuNumber("42")
            .build();
        when(itemCostChangeRequestApplicationMapper.domainToDto(Mockito.<ItemCostChangeRequest>any()))
            .thenReturn(buildResult);

        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = idResult
            .previousCost(new BigDecimal("2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult2 = previousCostResult.targetCost(new BigDecimal("2.3"))
            .build();
        updateItemTargetCostChangeRequestCommands.add(buildResult2);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        verify(itemCostChangeRequestApplicationMapper).domainToDto(isA(ItemCostChangeRequest.class));
        verify(itemCostChangeRequest).setTargetCost(isA(BigDecimal.class));
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
        verify(itemCostChangeRequestService).update(isA(ItemCostChangeRequest.class));
        assertEquals(1, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
        assertTrue(actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds().isEmpty());
    }

    @Test
    void testBatchChangeItemTargetCostChangeRequest_thenReturnUpdatedCountIsTwo() {
        // Arrange
        ItemCostChangeRequest itemCostChangeRequest = mock(ItemCostChangeRequest.class);
        doNothing().when(itemCostChangeRequest).setTargetCost(Mockito.<BigDecimal>any());
        when(itemCostChangeRequestService.update(Mockito.<ItemCostChangeRequest>any())).thenReturn(null);
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(itemCostChangeRequest);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder builderResult = ItemCostChangeRequestDto.builder();
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder matchTypeResult = builderResult.crv(new BigDecimal("2.3"))
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemCostCollectionId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .matchType(MatchedType.MISS_MATCHED);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder statusResult = matchTypeResult
            .previousCost(new BigDecimal("2.3"))
            .skuNumber("42")
            .status(ItemCostChangeRequestStatus.PENDING);
        ItemCostChangeRequestDto.ItemCostChangeRequestDtoBuilder targetCostResult = statusResult
            .targetCost(new BigDecimal("2.3"));
        ItemCostChangeRequestDto buildResult = targetCostResult.tax(new BigDecimal("2.3"))
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorSkuNumber("42")
            .build();
        when(itemCostChangeRequestApplicationMapper.domainToDto(Mockito.<ItemCostChangeRequest>any()))
            .thenReturn(buildResult);

        ArrayList<UpdateItemTargetCostChangeRequestCommand> updateItemTargetCostChangeRequestCommands = new ArrayList<>();
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult = idResult
            .previousCost(new BigDecimal("2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult2 = previousCostResult.targetCost(new BigDecimal("2.3"))
            .build();
        updateItemTargetCostChangeRequestCommands.add(buildResult2);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder idResult2 = UpdateItemTargetCostChangeRequestCommand
            .builder()
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        UpdateItemTargetCostChangeRequestCommand.UpdateItemTargetCostChangeRequestCommandBuilder previousCostResult2 = idResult2
            .previousCost(new BigDecimal("2.3"));
        UpdateItemTargetCostChangeRequestCommand buildResult3 = previousCostResult2.targetCost(new BigDecimal("2.3"))
            .build();
        updateItemTargetCostChangeRequestCommands.add(buildResult3);
        BatchUpdateItemTargetCostChangeRequestCommand command = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(updateItemTargetCostChangeRequestCommands)
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto actualBatchChangeItemTargetCostChangeRequestResult = itemCostChangeRequestApplicationServiceImpl
            .batchChangeItemTargetCostChangeRequest(command);

        // Assert
        verify(itemCostChangeRequestApplicationMapper, atLeast(1)).domainToDto(isA(ItemCostChangeRequest.class));
        verify(itemCostChangeRequest, atLeast(1)).setTargetCost(isA(BigDecimal.class));
        verify(itemCostChangeRequestService, atLeast(1)).findById(isA(UUID.class));
        verify(itemCostChangeRequestService, atLeast(1)).update(isA(ItemCostChangeRequest.class));
        assertEquals(2, actualBatchChangeItemTargetCostChangeRequestResult.getUpdatedCount());
        assertTrue(actualBatchChangeItemTargetCostChangeRequestResult.getFailedIds().isEmpty());
    }


    @Test
    @DisplayName("Test getItemCostCollectionFile(UUID)")
    void testGetItemCostCollectionFile() {
        // Arrange
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemCostChangeRequestApplicationServiceImpl
            .getItemCostCollectionFile(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
    }


    @Test
    void testGetItemCostCollectionFile2() {
        // Arrange
        ItemCostChangeRequest itemCostChangeRequest = mock(ItemCostChangeRequest.class);
        when(itemCostChangeRequest.getItemCostCollectionId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(itemCostChangeRequest);
        when(itemCostCollectionApplicationService.getItemCostCollectionFile(Mockito.<UUID>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemCostChangeRequestApplicationServiceImpl
            .getItemCostCollectionFile(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemCostCollectionApplicationService).getItemCostCollectionFile(isA(UUID.class));
        verify(itemCostChangeRequest).getItemCostCollectionId();
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
    }

    /**
     * Test {@link ItemCostChangeRequestApplicationServiceImpl#getItemCostCollectionFile(UUID)}.
     * <ul>
     *   <li>Then return {@code Name}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ItemCostChangeRequestApplicationServiceImpl#getItemCostCollectionFile(UUID)}
     */
    @Test
    @DisplayName("Test getItemCostCollectionFile(UUID); then return 'Name'")
    @Tag("MaintainedByDiffblue")
    void testGetItemCostCollectionFile_thenReturnName() {
        // Arrange
        ItemCostChangeRequest itemCostChangeRequest = mock(ItemCostChangeRequest.class);
        when(itemCostChangeRequest.getItemCostCollectionId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemCostChangeRequestService.findById(Mockito.<UUID>any())).thenReturn(itemCostChangeRequest);
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(itemCostCollectionApplicationService.getItemCostCollectionFile(Mockito.<UUID>any())).thenReturn(buildResult);

        // Act
        DocumentResponse actualItemCostCollectionFile = itemCostChangeRequestApplicationServiceImpl
            .getItemCostCollectionFile(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemCostCollectionApplicationService).getItemCostCollectionFile(isA(UUID.class));
        verify(itemCostChangeRequest).getItemCostCollectionId();
        verify(itemCostChangeRequestService).findById(isA(UUID.class));
        assertEquals("Name", actualItemCostCollectionFile.getName());
        assertEquals("https://example.org/example", actualItemCostCollectionFile.getSignedUrl());
    }
}