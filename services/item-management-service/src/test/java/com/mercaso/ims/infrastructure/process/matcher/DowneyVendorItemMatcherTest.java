package com.mercaso.ims.infrastructure.process.matcher;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.dto.VendorItemMappingDto;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.external.downey.DowneyAdaptor;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {DowneyVendorItemMatcher.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class DowneyVendorItemMatcherTest {

    @MockBean
    private DowneyAdaptor downeyAdaptor;

    @Autowired
    private DowneyVendorItemMatcher downeyVendorItemMatcher;

    @MockBean
    private VendorItemService vendorItemService;

    @MockBean
    private VendorService vendorService;
    @MockBean
    private FeatureFlagsManager featureFlagsManager;


    @Test
    void testMatchItem_givenDowneyAdaptor_thenReturnNull() {
        Vendor vendor = VendorUtil.buildVendor(DOWNEY_WHOLESALE);
        // Arrange
        when(vendorService.findByVendorName(Mockito.<String>any())).thenReturn(vendor);

        // Act
        List<VendorItemMappingDto> actualMatchItemResult = downeyVendorItemMatcher
            .matchItem(new ItemCostCollectionItemParsingResultDto(), UUID.randomUUID());

        // Assert
        verify(vendorService).findByVendorName("Downey Wholesale");
        assertNotNull(actualMatchItemResult);
    }
}
