package com.mercaso.ims.infrastructure.repository.itemvendorrebate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.itemvendorrebate.ItemVendorRebateUtil;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class ItemVendorRebateRepositoryImplIT extends AbstractIT {

    @Test
    void save_success() {
        // Arrange
        String skuNumber = "save_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        Instant startDate = Instant.now();
        Instant endDate = Instant.now().plus(6, ChronoUnit.DAYS);
        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
            startDate, endDate, new BigDecimal("10.50"));

        // Act
        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(itemVendorRebate);

        // Assert
        assertNotNull(savedRebate);
        assertNotNull(savedRebate.getId());
        assertEquals(vendorItem.getId(), savedRebate.getVendorItemId());
        assertEquals(vendorDo.getId(), savedRebate.getVendorId());
        assertEquals(itemDo.getId(), savedRebate.getItemId());
        assertEquals(startDate, savedRebate.getStartDate());
        assertEquals(endDate, savedRebate.getEndDate());
        assertEquals(new BigDecimal("10.50"), savedRebate.getRebatePerUnit());
        assertEquals(ItemVendorRebateStatus.ACTIVE, savedRebate.getItemVendorRebateStatus());
    }

    @Test
    void findById_success() {
        // Arrange
        String skuNumber = "findById_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), Instant.now().plus(3, ChronoUnit.DAYS), new BigDecimal("5.25"));

        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(itemVendorRebate);

        // Act
        ItemVendorRebate foundRebate = itemVendorRebateRepository.findById(savedRebate.getId());

        // Assert
        assertNotNull(foundRebate);
        assertEquals(savedRebate.getId(), foundRebate.getId());
        assertEquals(vendorItem.getId(), foundRebate.getVendorItemId());
        assertEquals(vendorDo.getId(), foundRebate.getVendorId());
        assertEquals(itemDo.getId(), foundRebate.getItemId());
        assertEquals(new BigDecimal("5.2500"), foundRebate.getRebatePerUnit());
    }

    @Test
    void findById_notFound() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        ItemVendorRebate foundRebate = itemVendorRebateRepository.findById(nonExistentId);

        // Assert
        assertNull(foundRebate);
    }

    @Test
    void update_success() {
        // Arrange
        String skuNumber = "update_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
                        .plusMonths(6)
                        .toInstant(ZoneOffset.UTC), new BigDecimal("8.00"));

        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(itemVendorRebate);

        // Update the rebate
        Instant endDate = Instant.now().plus(12, ChronoUnit.DAYS);
        savedRebate.setRebatePerUnit(new BigDecimal("12.75"));
        savedRebate.setEndDate(endDate);
        savedRebate.setItemVendorRebateStatus(ItemVendorRebateStatus.DRAFT);

        // Act
        ItemVendorRebate updatedRebate = itemVendorRebateRepository.update(savedRebate);

        // Assert
        assertNotNull(updatedRebate);
        assertEquals(savedRebate.getId(), updatedRebate.getId());
        assertEquals(new BigDecimal("12.75"), updatedRebate.getRebatePerUnit());
        assertEquals(endDate, updatedRebate.getEndDate());
        assertEquals(ItemVendorRebateStatus.DRAFT, updatedRebate.getItemVendorRebateStatus());
    }

    @Test
    void deleteById_success() {
        // Arrange
        String skuNumber = "deleteById_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
                        .plusMonths(6)
                        .toInstant(ZoneOffset.UTC), new BigDecimal("7.50"));

        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(itemVendorRebate);

        // Act
        ItemVendorRebate deletedRebate = itemVendorRebateRepository.deleteById(savedRebate.getId());

        // Assert
        assertNotNull(deletedRebate);
        assertEquals(savedRebate.getId(), deletedRebate.getId());
        
        // Verify the rebate is soft deleted (cannot be found)
        ItemVendorRebate foundRebate = itemVendorRebateRepository.findById(savedRebate.getId());
        assertNull(foundRebate);
    }

    @Test
    void findByVendorId_success() {
        // Arrange
        String skuNumber = "findByVendorId_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate1 = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), Instant.now().plus(6, ChronoUnit.DAYS), new BigDecimal("5.00"));

        ItemVendorRebate itemVendorRebate2 = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now().plus(7, ChronoUnit.DAYS), Instant.now().plus(12, ChronoUnit.DAYS), new BigDecimal("7.50"));

        itemVendorRebateRepository.save(itemVendorRebate1);
        itemVendorRebateRepository.save(itemVendorRebate2);

        // Act
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorId(vendorDo.getId());

        // Assert
        assertNotNull(rebates);
        assertEquals(2, rebates.size());
        assertTrue(rebates.stream().allMatch(rebate -> rebate.getVendorId().equals(vendorDo.getId())));
    }

    @Test
    void findByItemId_success() {
        // Arrange
        String skuNumber = "findByItemId_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
                        .plusMonths(6)
                        .toInstant(ZoneOffset.UTC), new BigDecimal("6.25"));

        itemVendorRebateRepository.save(itemVendorRebate);

        // Act
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByItemId(itemDo.getId());

        // Assert
        assertNotNull(rebates);
        assertEquals(1, rebates.size());
        assertEquals(itemDo.getId(), rebates.getFirst().getItemId());
        assertEquals(new BigDecimal("6.2500"), rebates.getFirst().getRebatePerUnit());
    }

    @Test
    void findByVendorItemId_success() {
        // Arrange
        String skuNumber = "findByVendorItemId_success_" + RandomStringUtils.randomAlphabetic(5);
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = "vendorItem_" + RandomStringUtils.randomAlphabetic(5);
        
        ItemDo itemDo = buildItemDataWithVendor(skuNumber, vendorName, vendorItemNumber);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        VendorItem vendorItem = buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemVendorRebate itemVendorRebate = ItemVendorRebateUtil.buildContinuousItemVendorRebate(
            vendorItem.getId(), vendorDo.getId(), itemDo.getId(),
                Instant.now(), new BigDecimal("15.0000"));

        itemVendorRebateRepository.save(itemVendorRebate);

        // Act
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorItemId(vendorItem.getId());

        // Assert
        assertNotNull(rebates);
        assertEquals(1, rebates.size());
        assertEquals(vendorItem.getId(), rebates.getFirst().getVendorItemId());
        assertEquals(new BigDecimal("15.0000"), rebates.getFirst().getRebatePerUnit());
        assertNull(rebates.getFirst().getEndDate()); // Continuous rebate
    }
}
