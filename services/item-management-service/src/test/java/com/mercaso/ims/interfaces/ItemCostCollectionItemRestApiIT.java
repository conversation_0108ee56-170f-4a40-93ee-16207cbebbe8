package com.mercaso.ims.interfaces;

import static com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources.AWS_ANALYZE;
import static com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes.PDF_FILE;
import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.BatchUpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.BatchUpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.command.UpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemCostChangeRequestResultDto;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecord;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisSource;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisStatus;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemCostCollectionItemRestApiIT extends AbstractIT {

    private static final String JSON = "{\"expenseDocuments\":[{\"summaryFields\":[{\"type\":{\"text\":\"TAX\",\"confidence\":99.82319},\"labelDetection\":{\"text\":\"Sales Tax:\",\"confidence\":98.76991,\"geometry\":{\"boundingBox\":{\"width\":0.2049167,\"height\":0.*********,\"left\":0.46212143,\"top\":0.8868519},\"polygon\":[{\"x\":0.46212143,\"y\":0.8868519},{\"x\":0.6669961,\"y\":0.887803},{\"x\":0.66703814,\"y\":0.8943132},{\"x\":0.4621629,\"y\":0.8933614}]}},\"valueDetection\":{\"text\":\"$.00\",\"confidence\":99.680244,\"geometry\":{\"boundingBox\":{\"width\":0.09086848,\"height\":0.0061217817,\"left\":0.8339465,\"top\":0.88766927},\"polygon\":[{\"x\":0.8339465,\"y\":0.88766927},{\"x\":0.92477775,\"y\":0.8880909},{\"x\":0.924815,\"y\":0.8937911},{\"x\":0.8339836,\"y\":0.89336914}]}}}],\"lineItemGroups\":[{\"lineItems\":[{\"lineItemExpenseFields\":[{\"type\":{\"text\":\"PRODUCT_CODE\",\"confidence\":92.869064},\"labelDetection\":null,\"valueDetection\":{\"text\":\"8070710263\",\"confidence\":92.81443,\"geometry\":{\"boundingBox\":{\"width\":0.2137853,\"height\":0.*********,\"left\":0.0406683,\"top\":0.41364485},\"polygon\":[{\"x\":0.0406683,\"y\":0.41364485},{\"x\":0.25441787,\"y\":0.41458437},{\"x\":0.2544536,\"y\":0.4202556},{\"x\":0.04070362,\"y\":0.41931543}]}}},{\"type\":{\"text\":\"ITEM\",\"confidence\":98.86692},\"labelDetection\":null,\"valueDetection\":{\"text\":\"TAPATIO RAMEN 6/3.7Z X\\n( 9.49)\",\"confidence\":91.08522,\"geometry\":{\"boundingBox\":{\"width\":0.45716178,\"height\":0.013979406,\"left\":0.32067627,\"top\":0.41269746},\"polygon\":[{\"x\":0.32067627,\"y\":0.41269746},{\"x\":0.7777604,\"y\":0.41470608},{\"x\":0.77783805,\"y\":0.42667687},{\"x\":0.32075197,\"y\":0.4246654}]}}},{\"type\":{\"text\":\"PRICE\",\"confidence\":99.37222},\"labelDetection\":null,\"valueDetection\":{\"text\":\"47.4580\",\"confidence\":99.24423,\"geometry\":{\"boundingBox\":{\"width\":0.14561059,\"height\":0.0063834623,\"left\":0.8154744,\"top\":0.41898668},\"polygon\":[{\"x\":0.8154744,\"y\":0.41898668},{\"x\":0.96104735,\"y\":0.4196267},{\"x\":0.96108496,\"y\":0.42537016},{\"x\":0.8155117,\"y\":0.4247297}]}}},{\"type\":{\"text\":\"UNIT_PRICE\",\"confidence\":95.83069},\"labelDetection\":null,\"valueDetection\":{\"text\":\"9.49)\",\"confidence\":95.741615,\"geometry\":{\"boundingBox\":{\"width\":0.11314152,\"height\":0.0068993536,\"left\":0.5776362,\"top\":0.419015},\"polygon\":[{\"x\":0.5776362,\"y\":0.419015},{\"x\":0.69073635,\"y\":0.4195123},{\"x\":0.6907777,\"y\":0.42591435},{\"x\":0.57767725,\"y\":0.42541665}]}}},{\"type\":{\"text\":\"EXPENSE_ROW\",\"confidence\":84.36839},\"labelDetection\":null,\"valueDetection\":{\"text\":\"TAPATIO RAMEN 6/3.7Z X\\n8070710263\\n47.4580\\n( 9.49)\",\"confidence\":79.318695,\"geometry\":{\"boundingBox\":{\"width\":0.9204484,\"height\":0.016714823,\"left\":0.040650386,\"top\":0.4107685},\"polygon\":[{\"x\":0.040650386,\"y\":0.4107685},{\"x\":0.9610158,\"y\":0.41481262},{\"x\":0.9610988,\"y\":0.42748335},{\"x\":0.040729266,\"y\":0.4234331}]}}}]},{\"lineItemExpenseFields\":[{\"type\":{\"text\":\"PRODUCT_CODE\",\"confidence\":92.90963},\"labelDetection\":null,\"valueDetection\":{\"text\":\"4460032263\",\"confidence\":92.89811,\"geometry\":{\"boundingBox\":{\"width\":0.21275455,\"height\":0.0066597727,\"left\":0.045306504,\"top\":0.6732002},\"polygon\":[{\"x\":0.045306504,\"y\":0.6732002},{\"x\":0.25802514,\"y\":0.6741641},{\"x\":0.25806105,\"y\":0.67985994},{\"x\":0.04534198,\"y\":0.67889535}]}}},{\"type\":{\"text\":\"ITEM\",\"confidence\":99.92429},\"labelDetection\":null,\"valueDetection\":{\"text\":\"CLOROX BLEACH 6/81Z DI\\n37.99)\",\"confidence\":98.99544,\"geometry\":{\"boundingBox\":{\"width\":0.46435943,\"height\":0.0132883815,\"left\":0.31627205,\"top\":0.67202204},\"polygon\":[{\"x\":0.31627205,\"y\":0.67202204},{\"x\":0.7805589,\"y\":0.67412543},{\"x\":0.7806315,\"y\":0.6853104},{\"x\":0.31634277,\"y\":0.6832043}]}}},{\"type\":{\"text\":\"PRICE\",\"confidence\":99.96755},\"labelDetection\":null,\"valueDetection\":{\"text\":\"37.99\",\"confidence\":99.92332,\"geometry\":{\"boundingBox\":{\"width\":0.111889884,\"height\":0.0063576493,\"left\":0.81111604,\"top\":0.67894673},\"polygon\":[{\"x\":0.81111604,\"y\":0.67894673},{\"x\":0.9229677,\"y\":0.67945373},{\"x\":0.92300594,\"y\":0.68530434},{\"x\":0.81115407,\"y\":0.68479705}]}}},{\"type\":{\"text\":\"UNIT_PRICE\",\"confidence\":99.54408},\"labelDetection\":null,\"valueDetection\":{\"text\":\"37.99)\",\"confidence\":99.46985,\"geometry\":{\"boundingBox\":{\"width\":0.119110994,\"height\":0.0064469087,\"left\":0.56418705,\"top\":0.67883086},\"polygon\":[{\"x\":0.56418705,\"y\":0.67883086},{\"x\":0.6832599,\"y\":0.6793707},{\"x\":0.68329805,\"y\":0.6852778},{\"x\":0.5642249,\"y\":0.6847376}]}}},{\"type\":{\"text\":\"EXPENSE_ROW\",\"confidence\":94.91887},\"labelDetection\":null,\"valueDetection\":{\"text\":\"( 1\\nCLOROX BLEACH 6/81Z DI\\n4460032263\\n37.99) 37.99\",\"confidence\":76.47398,\"geometry\":{\"boundingBox\":{\"width\":0.87772983,\"height\":0.016930342,\"left\":0.045283046,\"top\":0.6694349},\"polygon\":[{\"x\":0.045283046,\"y\":0.6694349},{\"x\":0.92292815,\"y\":0.67341036},{\"x\":0.92301285,\"y\":0.68636525},{\"x\":0.045363713,\"y\":0.68238384}]}}}]}]}]}],\"documentMetadata\":{\"pages\":1}}";

    @Test
    void testApprovedVendorPoInvoiceItem() throws Exception {
        when(downeyAdaptor.queryDowneyItemNumber(any())).thenReturn("1406411");

        String sku1 = RandomStringUtils.randomAlphabetic(6);
        String fileName = RandomStringUtils.randomAlphabetic(10);

        ItemDo itemDo = buildItemDataWithVendor(sku1, DOWNEY_WHOLESALE, "1406411");

        CreateItemCostCollectionCommand createItemCostCollectionCommand = CreateItemCostCollectionCommand.builder()
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .vendorName(DOWNEY_WHOLESALE)
            .source(AWS_ANALYZE)
            .fileName(fileName)
            .type(PDF_FILE)
            .vendorCollectionNumber("123121")
            .build();

        vendorPoAnalyzeRecordService.save(
            VendorPoAnalyzeRecord.builder()
                .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
                .vendorName(DOWNEY_WHOLESALE)
                .analysisSource(AnalysisSource.AWS)
                .status(AnalysisStatus.APPROVED)
                .originalFileName(fileName)
                .analysisExpensePayload(JSON)
                .build());

        ItemCostCollectionDto costCollectionDto = itemCostCollectionApplicationService.create(createItemCostCollectionCommand);

        CreateItemCostChangeRequestCommand command = CreateItemCostChangeRequestCommand.builder()
            .vendorSkuNumber("1406411")
            .vendorItemName("test")
            .status(ItemCostChangeRequestStatus.PENDING)
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .targetCost(BigDecimal.TEN)
            .itemCostCollectionId(costCollectionDto.getId())
            .itemId(itemDo.getId())
            .skuNumber(itemDo.getSkuNumber())
            .matchType(MatchedType.COST_SAME)
            .build();

        ItemCostChangeRequestDto costChangeRequestDto = itemCostChangeRequestApplicationService.createItemCostChangeRequest(
            command);

        UpdateItemCostChangeRequestCommand changeRequestCommand = UpdateItemCostChangeRequestCommand.builder()
            .status(ItemCostChangeRequestStatus.APPROVED)
            .previousCost(BigDecimal.TEN)
            .id(costChangeRequestDto.getId())
            .build();
        BatchUpdateItemCostChangeRequestCommand batchUpdateItemCostChangeRequestCommand = BatchUpdateItemCostChangeRequestCommand.builder()
            .updateItemCostChangeRequestCommands(List.of(changeRequestCommand))
            .build();
        // Act
        BatchUpdateItemCostChangeRequestResultDto result = itemCostChangeRequestRestApiUtil.approvedVendorPoInvoiceItem(
            batchUpdateItemCostChangeRequestCommand);
        // Assert
        Assertions.assertEquals(1, result.getUpdatedCount());
    }

    @Test
    void testRejectedVendorPoInvoiceItem() throws Exception {
        when(downeyAdaptor.queryDowneyItemNumber(any())).thenReturn("1406411");

        String sku1 = RandomStringUtils.randomAlphabetic(6);
        String fileName = RandomStringUtils.randomAlphabetic(10);

        ItemDo itemDo = buildItemDataWithVendor(sku1, DOWNEY_WHOLESALE, "1406411");

        vendorPoAnalyzeRecordService.save(
            VendorPoAnalyzeRecord.builder()
                .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
                .vendorName(DOWNEY_WHOLESALE)
                .analysisSource(AnalysisSource.AWS)
                .status(AnalysisStatus.APPROVED)
                .originalFileName(fileName)
                .analysisExpensePayload(JSON)
                .build());
        CreateItemCostCollectionCommand createItemCostCollectionCommand = CreateItemCostCollectionCommand.builder()
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .vendorName(DOWNEY_WHOLESALE)
            .source(AWS_ANALYZE)
            .type(PDF_FILE)
            .fileName(fileName)
            .vendorCollectionNumber("123121")
            .build();
        ItemCostCollectionDto costCollectionDto = itemCostCollectionApplicationService.create(createItemCostCollectionCommand);

        CreateItemCostChangeRequestCommand command = CreateItemCostChangeRequestCommand.builder()
            .vendorSkuNumber("1406411")
            .vendorItemName("test")
            .status(ItemCostChangeRequestStatus.PENDING)
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .targetCost(BigDecimal.TEN)
            .itemCostCollectionId(costCollectionDto.getId())
            .itemId(itemDo.getId())
            .skuNumber(itemDo.getSkuNumber())
            .matchType(MatchedType.COST_SAME)
            .build();

        ItemCostChangeRequestDto costChangeRequestDto = itemCostChangeRequestApplicationService.createItemCostChangeRequest(
            command);

        UpdateItemCostChangeRequestCommand changeRequestCommand = UpdateItemCostChangeRequestCommand.builder()
            .status(ItemCostChangeRequestStatus.REJECTED)
            .id(costChangeRequestDto.getId())
            .build();
        BatchUpdateItemCostChangeRequestCommand batchUpdateItemCostChangeRequestCommand = BatchUpdateItemCostChangeRequestCommand.builder()
            .updateItemCostChangeRequestCommands(List.of(changeRequestCommand))
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto result = itemCostChangeRequestRestApiUtil.rejectedVendorPoInvoiceItem(
            batchUpdateItemCostChangeRequestCommand);

        // Assert
        Assertions.assertEquals(1, result.getUpdatedCount());
    }


    @Test
    void testBatchChangeTargetCostRequest() throws Exception {
        when(downeyAdaptor.queryDowneyItemNumber(any())).thenReturn("1406411000111");

        String sku1 = RandomStringUtils.randomAlphabetic(6);
        String fileName = RandomStringUtils.randomAlphabetic(10);

        ItemDo itemDo = buildItemDataWithVendor(sku1, DOWNEY_WHOLESALE, "1406411000111");

        vendorPoAnalyzeRecordService.save(
            VendorPoAnalyzeRecord.builder()
                .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
                .vendorName(DOWNEY_WHOLESALE)
                .analysisSource(AnalysisSource.AWS)
                .status(AnalysisStatus.APPROVED)
                .originalFileName(fileName)
                .analysisExpensePayload(JSON)
                .build());
        CreateItemCostCollectionCommand createItemCostCollectionCommand = CreateItemCostCollectionCommand.builder()
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .vendorName(DOWNEY_WHOLESALE)
            .source(AWS_ANALYZE)
            .type(PDF_FILE)
            .fileName(fileName)
            .vendorCollectionNumber("123121")
            .build();
        ItemCostCollectionDto costCollectionDto = itemCostCollectionApplicationService.create(createItemCostCollectionCommand);

        CreateItemCostChangeRequestCommand command = CreateItemCostChangeRequestCommand.builder()
            .vendorSkuNumber("1406411")
            .vendorItemName("test")
            .status(ItemCostChangeRequestStatus.PENDING)
            .vendorId(UUID.fromString("c05a31dc-8f1d-4b50-906c-58604d0cd307"))
            .targetCost(BigDecimal.TEN)
            .itemCostCollectionId(costCollectionDto.getId())
            .itemId(itemDo.getId())
            .skuNumber(itemDo.getSkuNumber())
            .matchType(MatchedType.COST_SAME)
            .build();

        ItemCostChangeRequestDto costChangeRequestDto = itemCostChangeRequestApplicationService.createItemCostChangeRequest(
            command);

        UpdateItemTargetCostChangeRequestCommand changeRequestCommand = UpdateItemTargetCostChangeRequestCommand.builder()
            .previousCost(BigDecimal.valueOf(50.0))
            .targetCost(BigDecimal.valueOf(10.0))
            .id(costChangeRequestDto.getId())
            .build();
        BatchUpdateItemTargetCostChangeRequestCommand batchUpdateItemCostChangeRequestCommand = BatchUpdateItemTargetCostChangeRequestCommand.builder()
            .updateItemTargetCostChangeRequestCommands(List.of(changeRequestCommand))
            .build();

        // Act
        BatchUpdateItemCostChangeRequestResultDto result = itemCostChangeRequestRestApiUtil.batchChangeTargetCostRequest(
            batchUpdateItemCostChangeRequestCommand);

        // Assert
        Assertions.assertEquals(1, result.getUpdatedCount());
    }
}