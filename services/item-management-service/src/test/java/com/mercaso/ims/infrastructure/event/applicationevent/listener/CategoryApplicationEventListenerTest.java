package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.event.CategoryCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryDeletedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.payload.CategoryCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryDeletedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryUpdatedPayloadDto;
import com.mercaso.ims.application.queryservice.CategoryQueryApplicationService;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.DifyKnowledgeBaseApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CategoryApplicationEventListenerTest {

    @Mock
    private CategoryQueryApplicationService categoryQueryApplicationService;

    @Mock
    private ItemApplicationService itemApplicationService;

    @Mock
    private ItemQueryApplicationService itemQueryApplicationService;

    @Mock
    private Executor taskExecutor;

    @Mock
    private DifyKnowledgeBaseApplicationService difyKnowledgeBaseApplicationService;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @InjectMocks
    private CategoryApplicationEventListener categoryApplicationEventListener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void should_log_event_when_handling_category_created_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        CategoryCreatedPayloadDto payloadDto = CategoryCreatedPayloadDto.builder()
            .data(new CategoryDto())
            .categoryId(categoryId)
            .build();

        CategoryCreatedApplicationEvent event = new CategoryCreatedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryCreatedApplicationEvent(event);

        // Then
        // This method only logs, so we verify no interactions with other services
        verifyNoInteractions(categoryQueryApplicationService);
        verifyNoInteractions(itemApplicationService);
        verifyNoInteractions(itemQueryApplicationService);
        verifyNoInteractions(taskExecutor);
    }

    @Test
    void should_execute_async_task_when_handling_category_updated_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // Mock the executor to run the task synchronously for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));

        // Mock empty leaf categories to avoid further processing
        when(categoryQueryApplicationService.findLeafCategory(categoryId))
            .thenReturn(Collections.emptyList());

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(taskExecutor).execute(any(Runnable.class));
        verify(categoryQueryApplicationService).findLeafCategory(categoryId);
    }

    @Test
    void should_update_items_when_leaf_categories_exist() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID leafCategoryId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();

        CategoryDto leafCategory = CategoryDto.builder()
            .categoryId(leafCategoryId)
            .categoryName("TestClass")
            .build();

        ItemDto item = ItemDto.builder()
            .id(itemId)
            .skuNumber("TEST-SKU")
            .build();

        CategoryDto departmentAncestor = CategoryDto.builder()
            .categoryName("TestDepartment")
            .depth(3)
            .build();

        CategoryDto categoryAncestor = CategoryDto.builder()
            .categoryName("TestCategory")
            .depth(2)
            .build();

        CategoryDto subCategoryAncestor = CategoryDto.builder()
            .categoryName("TestSubCategory")
            .depth(1)
            .build();

        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // Mock the executor to run the task synchronously for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));

        when(categoryQueryApplicationService.findLeafCategory(categoryId))
            .thenReturn(Arrays.asList(leafCategory));
        when(itemQueryApplicationService.findByCategoryId(leafCategoryId))
            .thenReturn(Arrays.asList(item));
        when(categoryQueryApplicationService.findAncestorNameByLeafCategory(leafCategoryId))
            .thenReturn(Arrays.asList(departmentAncestor, categoryAncestor, subCategoryAncestor));

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(taskExecutor).execute(any(Runnable.class));
        verify(categoryQueryApplicationService).findLeafCategory(categoryId);
        verify(itemQueryApplicationService).findByCategoryId(leafCategoryId);
        verify(categoryQueryApplicationService).findAncestorNameByLeafCategory(leafCategoryId);
        verify(itemApplicationService).update(any(UpdateItemCommand.class));
    }

    @Test
    void should_not_update_items_when_no_items_in_leaf_category() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID leafCategoryId = UUID.randomUUID();

        CategoryDto leafCategory = CategoryDto.builder()
            .categoryId(leafCategoryId)
            .categoryName("TestClass")
            .build();

        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // Mock the executor to run the task synchronously for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));

        when(categoryQueryApplicationService.findLeafCategory(categoryId))
            .thenReturn(Arrays.asList(leafCategory));
        when(itemQueryApplicationService.findByCategoryId(leafCategoryId))
            .thenReturn(Collections.emptyList());

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(taskExecutor).execute(any(Runnable.class));
        verify(categoryQueryApplicationService).findLeafCategory(categoryId);
        verify(itemQueryApplicationService).findByCategoryId(leafCategoryId);
        verify(categoryQueryApplicationService, never()).findAncestorNameByLeafCategory(any());
        verify(itemApplicationService, never()).update(any(UpdateItemCommand.class));
    }

    @Test
    void should_log_event_when_handling_category_deleted_event() {
        // Given
        UUID categoryId = UUID.randomUUID();
        CategoryDeletedPayloadDto payloadDto = CategoryDeletedPayloadDto.builder()
            .categoryId(categoryId)
            .build();

        CategoryDeletedApplicationEvent event = new CategoryDeletedApplicationEvent("Category", payloadDto);

        // When
        categoryApplicationEventListener.handleCategoryDeletedApplicationEvent(event);

        // Then
        // This method only logs, so we verify no interactions with other services
        verifyNoInteractions(categoryQueryApplicationService);
        verifyNoInteractions(itemApplicationService);
        verifyNoInteractions(itemQueryApplicationService);
        verifyNoInteractions(taskExecutor);
    }

    @Test
    void should_extract_category_hierarchy_correctly() {
        // Given
        UUID categoryId = UUID.randomUUID();
        UUID leafCategoryId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();

        CategoryDto leafCategory = CategoryDto.builder()
            .categoryId(leafCategoryId)
            .categoryName("TestClass")
            .build();

        ItemDto item = ItemDto.builder()
            .id(itemId)
            .skuNumber("TEST-SKU")
            .build();

        CategoryDto departmentAncestor = CategoryDto.builder()
            .categoryName("TestDepartment")
            .depth(3)
            .build();

        CategoryDto categoryAncestor = CategoryDto.builder()
            .categoryName("TestCategory")
            .depth(2)
            .build();

        CategoryDto subCategoryAncestor = CategoryDto.builder()
            .categoryName("TestSubCategory")
            .depth(1)
            .build();

        CategoryUpdatedPayloadDto payloadDto = CategoryUpdatedPayloadDto.builder()
            .categoryId(categoryId)
            .build();

        CategoryUpdatedApplicationEvent event = new CategoryUpdatedApplicationEvent("Category", payloadDto);

        // Mock the executor to run the task synchronously for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));

        when(categoryQueryApplicationService.findLeafCategory(categoryId))
            .thenReturn(Arrays.asList(leafCategory));
        when(itemQueryApplicationService.findByCategoryId(leafCategoryId))
            .thenReturn(Arrays.asList(item));
        when(categoryQueryApplicationService.findAncestorNameByLeafCategory(leafCategoryId))
            .thenReturn(Arrays.asList(departmentAncestor, categoryAncestor, subCategoryAncestor));

        // When
        categoryApplicationEventListener.handleCategoryUpdatedApplicationEvent(event);

        // Then
        verify(itemApplicationService).update(any(UpdateItemCommand.class));
    }
}