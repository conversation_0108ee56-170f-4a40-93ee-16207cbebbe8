package com.mercaso.ims.infrastructure.config.metrics;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {MetricsAspect.class})
@ExtendWith(SpringExtension.class)
class MetricsAspectTest {

    @Autowired
    private MetricsAspect metricsAspect;

    @Test
    void testHandleThrowing() {
        // Arrange
        Signature signature = mock(Signature.class);
        when(signature.getName()).thenReturn("Name");
        JoinPoint jp = mock(JoinPoint.class);
        when(jp.getSignature()).thenReturn(signature);
        ReportMetric reportMetric = mock(ReportMetric.class);
        when(reportMetric.metricsType()).thenReturn(MetricsTypeEnum.QUERY_SHOPIFY_PRODUCT);

        // Act
        metricsAspect.handleThrowing(jp, reportMetric, new Throwable());

        // Assert
        verify(reportMetric).metricsType();
        verify(jp).getSignature();
        verify(signature).getName();
    }
}
