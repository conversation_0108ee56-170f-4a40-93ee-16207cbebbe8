package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_HAS_ITEMS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_NAME_ALREADY_EXISTS;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.application.command.UpdateCategoryCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryDto.CategoryDtoBuilder;
import com.mercaso.ims.application.dto.CategoryItemCountsDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.application.dto.ItemCountsDto;
import com.mercaso.ims.application.dto.payload.CategoryDeletedPayloadDto;
import com.mercaso.ims.application.query.CategoryQuery;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.CategoryHierarchyApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.repository.category.jpa.CustomizedCategoryJpaDao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {CategoryApplicationServiceImpl.class})
class CategoryApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private CategoryApplicationService categoryApplicationService;

    @MockBean
    private CustomizedCategoryJpaDao customizedCategoryJpaDao;

    @MockBean
    private BusinessEventService businessEventService;

    @MockBean
    private CategoryService categoryService;

    @MockBean
    private CategoryHierarchyApplicationService categoryHierarchyApplicationService;

    @MockBean
    private ItemRepository itemRepository;

    @MockBean
    private CategoryHierarchyService categoryHierarchyService;

    @MockBean
    private ShopifyAdaptor shopifyAdaptor;

    @MockBean
    private FeatureFlagsManager featureFlagsManager;

    @MockBean
    private ItemSearchApplicationService itemSearchApplicationService;

    @MockBean
    private EntityManager entityManager;


    private UUID categoryId = UUID.randomUUID();
    private UpdateCategoryCommand command;
    private final UUID leafId1 = UUID.randomUUID();
    private final UUID leafId2 = UUID.randomUUID();
    private final UUID ancestorId1 = UUID.randomUUID();
    private final UUID ancestorId2 = UUID.randomUUID();
    private final UUID ancestorId3 = UUID.randomUUID();
    private List<CategoryHierarchy> leafNodes;
    private List<CategoryDto> categorieList;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        command = new UpdateCategoryCommand(UUID.randomUUID(), null, 1, CategoryStatus.ACTIVE, "icon", "description");
        // Setup leaf nodes
        leafNodes = Arrays.asList(
            createCategoryHierarchy(leafId1, ancestorId1, 3),
            createCategoryHierarchy(leafId2, ancestorId2, 3)
        );

        // Setup categories
        categorieList = Arrays.asList(
            // Leaf 1 and its ancestors
            createCategoryDto(leafId1, null, 0, "Leaf 1"),
            createCategoryDto(leafId1, ancestorId1, 1, "Leaf 1", "Parent 1"),
            createCategoryDto(leafId1, ancestorId2, 2, "Leaf 1", "Grandparent 1"),
            createCategoryDto(leafId1, ancestorId3, 3, "Leaf 1", "Great Grandparent 1"),

            // Leaf 2 and its ancestors
            createCategoryDto(leafId2, null, 0, "Leaf 2"),
            createCategoryDto(leafId2, ancestorId1, 1, "Leaf 2", "Parent 2"),
            createCategoryDto(leafId2, ancestorId2, 2, "Leaf 2", "Grandparent 2"),
            createCategoryDto(leafId2, ancestorId3, 3, "Leaf 2", "Great Grandparent 2")
        );
    }

    @Test
    void testSearchCategoryTree_thenCallsAncestorCategoryId() {
        // Arrange
        CategoryDtoBuilder categoryDtoBuilder = mock(CategoryDtoBuilder.class);
        when(categoryDtoBuilder.ancestorCategoryId(Mockito.<UUID>any())).thenReturn(CategoryDto.builder());
        CategoryDto buildResult = categoryDtoBuilder.ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();

        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(buildResult);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(Mockito.<List<UUID>>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(categoryDtoBuilder).ancestorCategoryId(isA(UUID.class));
        verify(itemSearchApplicationService).countItemsByCategoryIdAndStatus(isA(List.class));
        verify(customizedCategoryJpaDao).getCategories(isA(CategoryQuery.class));
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }

    @Test
    void testSearchCategoryTree_givenItemSearchApplicationService_thenReturnEmpty() {
        // Arrange
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(new ArrayList<>());

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(isA(CategoryQuery.class));
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }

    @Test
    void testSearchCategoryTree() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        CategoryDto buildResult2 = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult2);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree2() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        CategoryDto buildResult2 = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(1)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult2);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree3() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(1)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        CategoryDto buildResult2 = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(1)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult2);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree4() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        CategoryDto buildResult2 = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(1)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult2);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree_thenReturnEmpty() {
        // Arrange
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(new ArrayList<>());

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree_thenReturnEmpty2() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree_thenReturnEmpty3() {
        // Arrange
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        CategoryDto buildResult2 = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult2);
        when(customizedCategoryJpaDao.getCategories(CategoryQuery.builder().build())).thenReturn(categoryDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(customizedCategoryJpaDao).getCategories(any());
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }


    @Test
    void testSearchCategoryTree_thenReturnSizeIsOne() {
        // Arrange
        CategoryDtoBuilder categoryDtoBuilder = mock(CategoryDtoBuilder.class);
        when(categoryDtoBuilder.ancestorCategoryId(Mockito.<UUID>any())).thenReturn(CategoryDto.builder());
        CategoryDto buildResult = categoryDtoBuilder.ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(0)
            .sortOrder(1)
            .build();

        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(buildResult);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(Mockito.<List<UUID>>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(categoryDtoBuilder).ancestorCategoryId(isA(UUID.class));
        verify(itemSearchApplicationService).countItemsByCategoryIdAndStatus(isA(List.class));
        verify(customizedCategoryJpaDao).getCategories(isA(CategoryQuery.class));
        assertEquals(1, actualSearchCategoryTreeResult.size());
        CategoryTreeDto getResult = actualSearchCategoryTreeResult.get(0);
        assertEquals("Category Name", getResult.getCategoryName());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", getResult.getCategoryId().toString());
        assertEquals(0, getResult.getDepth().intValue());
        ItemCountsDto itemCountsDto = getResult.getItemCountsDto();
        assertEquals(0L, itemCountsDto.getActiveCount().longValue());
        assertEquals(0L, itemCountsDto.getArchivedCount().longValue());
        assertEquals(0L, itemCountsDto.getDraftCount().longValue());
        assertEquals(1, getResult.getSortOrder().intValue());
        assertTrue(getResult.getChildCategories().isEmpty());
    }

    @Test
    void testCreateCategory_thenCallsSave() {
        // Arrange
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(new ArrayList<>());
        when(categoryService.save(Mockito.<Category>any()))
            .thenThrow(new ImsBusinessException("[createCategory] param command: {}."));
        CreateCategoryCommand createCategoryCommand = new CreateCategoryCommand(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID,
            "Category Name",
            1,
            CategoryStatus.DRAFT,
            "icon",
            "description",
            new ArrayList<>());
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.createCategory(createCategoryCommand));
        verify(categoryService).findById(isA(UUID.class));
    }


    @Test
    void testCreateCategory_thenThrowImsBusinessException() {
        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        categoryDtoList.add(buildResult);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);
        CreateCategoryCommand createCategoryCommand = new CreateCategoryCommand(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID,
            "Category Name",
            1,
            CategoryStatus.DRAFT,
            "icon",
            "description",
            new ArrayList<>());
        assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.createCategory(createCategoryCommand));
    }

    @Test
    void testUpdateCategory_CategoryNotFound() {
        when(categoryService.findById(categoryId)).thenReturn(null);

        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            categoryApplicationService.updateCategory(categoryId, command);
        });
        assertEquals(CATEGORY_NOT_FOUND.getCode(), exception.getMessage());
    }

    @Test
    void testUpdateCategory_AncestorCategoryNotFound() {

        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
                .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
                .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
                .categoryName("Category Name")
                .depth(2)
                .sortOrder(1)
                .build();
        categoryDtoList.add(buildResult);

        Category existingCategory = mock(Category.class);
        when(categoryService.findById(categoryId)).thenReturn(existingCategory);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);
        when(categoryService.findById(command.getAncestorCategoryId())).thenReturn(null);

        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            categoryApplicationService.updateCategory(categoryId, command);
        });
        assertEquals(CATEGORY_NAME_ALREADY_EXISTS.getCode(), exception.getMessage());
    }

    @Test
    void testUpdateCategory_CategoryNameNotUnique() {
        CategoryDto categoryDto = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        Category existingCategory = mock(Category.class);
        when(categoryService.findById(categoryId)).thenReturn(existingCategory);
        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(Collections.singletonList(categoryDto));

        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            categoryApplicationService.updateCategory(categoryId, command);
        });
        assertEquals(CATEGORY_NAME_ALREADY_EXISTS.getCode(), exception.getMessage());
    }

    @Test
    void testUpdateCategory_SuccessfulUpdate() {

        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        CategoryDto buildResult = CategoryDto.builder()
                .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
                .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
                .categoryName("Category Name")
                .depth(2)
                .sortOrder(1)
                .build();
        categoryDtoList.add(buildResult);


        CategoryDto categoryDto = CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(command.getAncestorCategoryId())
            .sortOrder(command.getSortOrder())
            .build();

        Category existingCategory = Category.builder().id(categoryDto.getCategoryId()).build();
        Category updatedCategory = Category.builder().id(categoryDto.getCategoryId()).build();
        when(categoryService.findById(any())).thenReturn(existingCategory);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);
        when(categoryService.update(existingCategory)).thenReturn(updatedCategory);

        categoryApplicationService.updateCategory(categoryId, command);

        verify(categoryService, times(1)).update(existingCategory);
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_NullInput() {
        // Arrange
        String leafCategoryName = null;

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertTrue(result.isEmpty());
        verify(customizedCategoryJpaDao, times(0)).getCategories(any());
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_EmptyInput() {
        // Arrange
        String leafCategoryName = "";

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertTrue(result.isEmpty());
        verify(customizedCategoryJpaDao, times(0)).getCategories(any());
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_NoResults() {
        // Arrange
        String leafCategoryName = "NonExistentCategory";
        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(Collections.emptyList());

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertTrue(result.isEmpty());
        verify(customizedCategoryJpaDao).getCategories(any());
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_WithResults() {
        // Arrange
        String leafCategoryName = "TestCategory";

        // Create test data
        List<CategoryDto> categories = new ArrayList<>();

        // Leaf category (depth 0)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .depth(0)
            .build());

        // Sub-category (depth 1)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .ancestorName("SubCategory")
            .depth(1)
            .build());

        // Main category (depth 2)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .ancestorName("MainCategory")
            .depth(2)
            .build());

        // Department (depth 3)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .ancestorName("Department")
            .depth(3)
            .build());

        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(categories);

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertEquals(4, result.size());
        assertEquals(Collections.singletonList("TestCategory"), result.get(0));
        assertEquals(Collections.singletonList("SubCategory"), result.get(1));
        assertEquals(Collections.singletonList("MainCategory"), result.get(2));
        assertEquals(Collections.singletonList("Department"), result.get(3));
        verify(customizedCategoryJpaDao).getCategories(any());
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_WithMultipleResults() {
        // Arrange
        String leafCategoryName = "TestCategory";

        // Create test data with multiple categories at each level
        List<CategoryDto> categories = new ArrayList<>();

        // Leaf categories (depth 0)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("TestCategory1")
            .depth(0)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("TestCategory2")
            .depth(0)
            .build());

        // Sub-categories (depth 1)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .ancestorName("SubCategory1")
            .categoryName("TestCategory1")
            .depth(1)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory2")
            .ancestorName("SubCategory2")
            .depth(1)
            .build());

        // Main categories (depth 2)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory1")
            .ancestorName("MainCategory1")
            .depth(2)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory2")
            .ancestorName("MainCategory2")
            .depth(2)
            .build());

        // Departments (depth 3)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory1")
            .ancestorName("Department1")
            .depth(3)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory2")
            .ancestorName("Department2")
            .depth(3)
            .build());

        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(categories);

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertEquals(4, result.size());

        // Verify each level has the correct number of unique categories
        assertEquals(2, result.get(0).size());
        assertTrue(result.get(0).contains("TestCategory1"));
        assertTrue(result.get(0).contains("TestCategory2"));

        assertEquals(2, result.get(1).size());
        assertTrue(result.get(1).contains("SubCategory1"));
        assertTrue(result.get(1).contains("SubCategory2"));

        assertEquals(2, result.get(2).size());
        assertTrue(result.get(2).contains("MainCategory1"));
        assertTrue(result.get(2).contains("MainCategory2"));

        assertEquals(2, result.get(3).size());
        assertTrue(result.get(3).contains("Department1"));
        assertTrue(result.get(3).contains("Department2"));

        verify(customizedCategoryJpaDao).getCategories(any());
    }

    @Test
    void testGetCategoryTreeByLeafCategoryName_WithDuplicateNames() {
        // Arrange
        String leafCategoryName = "TestCategory";

        // Create test data with duplicate names at each level
        List<CategoryDto> categories = new ArrayList<>();

        // Leaf categories (depth 0)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .depth(0)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("TestCategory")  // Duplicate name
            .depth(0)
            .build());

        // Sub-categories (depth 1)
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .ancestorName("SubCategory")
            .depth(1)
            .build());
        categories.add(CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .categoryName("TestCategory")
            .ancestorName("SubCategory")  // Duplicate name
            .depth(1)
            .build());

        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(categories);

        // Act
        Map<Integer, List<String>> result = categoryApplicationService.getCategoryTreeByLeafCategoryName(leafCategoryName);

        // Assert
        assertEquals(2, result.size());

        // Verify duplicates are removed
        assertEquals(1, result.get(0).size());
        assertEquals("TestCategory", result.get(0).get(0));

        assertEquals(1, result.get(1).size());
        assertEquals("SubCategory", result.get(1).get(0));

        verify(customizedCategoryJpaDao).getCategories(any());
    }


    @Test
    void testSearchCategoriesByName_NullInput() {
        // Arrange
        String name = null;

        // Act
        List<CategoryDto> result = categoryApplicationService.searchCategoriesByName(name);

        // Assert
        assertTrue(result.isEmpty());
        verify(categoryService, times(0)).findByNameAndStatus(any(), any());
    }

    @Test
    void testSearchCategoriesByName_EmptyInput() {
        // Arrange
        String name = "";

        // Act
        List<CategoryDto> result = categoryApplicationService.searchCategoriesByName(name);

        // Assert
        assertTrue(result.isEmpty());
        verify(categoryService, times(0)).findByNameAndStatus(any(), any());
    }

    @Test
    void testSearchCategoriesByName_NoResults() {
        // Arrange
        String name = "NonExistentCategory";
        when(categoryService.findByNameAndStatus(name, CategoryStatus.ACTIVE)).thenReturn(Collections.emptyList());

        // Act
        List<CategoryDto> result = categoryApplicationService.searchCategoriesByName(name);

        // Assert
        assertTrue(result.isEmpty());
        verify(categoryService).findByNameAndStatus(name, CategoryStatus.ACTIVE);
    }

    @Test
    void testSearchCategoriesByName_WithResults() {
        // Arrange
        String name = "TestCategory";

        // Create test categories
        List<Category> categories = new ArrayList<>();
        categories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .build());
        categories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .build());

        when(categoryService.findByNameAndStatus(name, CategoryStatus.ACTIVE)).thenReturn(categories);

        // Act
        List<CategoryDto> result = categoryApplicationService.searchCategoriesByName(name);

        // Assert
        assertEquals(2, result.size());
        result.forEach(dto -> assertEquals(name, dto.getCategoryName()));
        verify(categoryService).findByNameAndStatus(name, CategoryStatus.ACTIVE);
    }

    @Test
    void testSearchCategoriesByName_CaseInsensitive() {
        // Arrange
        String searchName = "testcategory";
        String actualName = "TestCategory";

        // Create test categories
        List<Category> categories = new ArrayList<>();
        categories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(actualName)
            .build());

        // Mock the service to return results regardless of case
        when(categoryService.findByNameAndStatus(searchName, CategoryStatus.ACTIVE)).thenReturn(categories);

        // Act
        List<CategoryDto> result = categoryApplicationService.searchCategoriesByName(searchName);

        // Assert
        assertEquals(1, result.size());
        assertEquals(actualName, result.get(0).getCategoryName());
        verify(categoryService).findByNameAndStatus(searchName, CategoryStatus.ACTIVE);
    }

    @Test
    void getAllLeafNodesWithAncestors_ShouldReturnCorrectMapping() {
        // Given
        when(categoryHierarchyService.findByDepth(3)).thenReturn(leafNodes);
        when(customizedCategoryJpaDao.getCategories(any(CategoryQuery.class))).thenReturn(categorieList);

        // When
        Map<UUID, Map<Integer, String>> result = categoryApplicationService.getAllLeafNodesWithAncestors();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify leaf 1 mapping
        Map<Integer, String> leaf1Map = result.get(leafId1);
        assertNotNull(leaf1Map);
        assertEquals(4, leaf1Map.size());
        assertEquals("Leaf 1", leaf1Map.get(0));
        assertEquals("Parent 1", leaf1Map.get(1));
        assertEquals("Grandparent 1", leaf1Map.get(2));
        assertEquals("Great Grandparent 1", leaf1Map.get(3));

        // Verify leaf 2 mapping
        Map<Integer, String> leaf2Map = result.get(leafId2);
        assertNotNull(leaf2Map);
        assertEquals(4, leaf2Map.size());
        assertEquals("Leaf 2", leaf2Map.get(0));
        assertEquals("Parent 2", leaf2Map.get(1));
        assertEquals("Grandparent 2", leaf2Map.get(2));
        assertEquals("Great Grandparent 2", leaf2Map.get(3));

        // Verify query parameters
        ArgumentCaptor<CategoryQuery> queryCaptor = ArgumentCaptor.forClass(CategoryQuery.class);
        verify(customizedCategoryJpaDao).getCategories(queryCaptor.capture());
        CategoryQuery capturedQuery = queryCaptor.getValue();
        assertEquals(CategoryStatus.ACTIVE, capturedQuery.getStatus());
        assertTrue(capturedQuery.getCategoryIds().contains(leafId1));
        assertTrue(capturedQuery.getCategoryIds().contains(leafId2));
    }

    @Test
    void getAllLeafNodesWithAncestors_WhenNoLeafNodesFound_ShouldReturnEmptyMap() {
        // Given
        when(categoryHierarchyService.findByDepth(3)).thenReturn(Collections.emptyList());

        // When
        Map<UUID, Map<Integer, String>> result = categoryApplicationService.getAllLeafNodesWithAncestors();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(customizedCategoryJpaDao, never()).getCategories(any());
    }

    @Test
    void getAllLeafNodesWithAncestors_WhenNoCategoriesFound_ShouldReturnEmptyMap() {
        // Given
        when(categoryHierarchyService.findByDepth(3)).thenReturn(leafNodes);
        when(customizedCategoryJpaDao.getCategories(any(CategoryQuery.class))).thenReturn(Collections.emptyList());

        // When
        Map<UUID, Map<Integer, String>> result = categoryApplicationService.getAllLeafNodesWithAncestors();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllLeafNodesWithAncestors_WhenSomeCategoriesMissing_ShouldHandleGracefully() {
        // Given
        when(categoryHierarchyService.findByDepth(3)).thenReturn(leafNodes);

        // Only return categories for leaf1, none for leaf2
        List<CategoryDto> partialCategories = categorieList.subList(0, 4);
        when(customizedCategoryJpaDao.getCategories(any(CategoryQuery.class))).thenReturn(partialCategories);

        // When
        Map<UUID, Map<Integer, String>> result = categoryApplicationService.getAllLeafNodesWithAncestors();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(leafId1));
        assertFalse(result.containsKey(leafId2));
    }

    @Test
    void getAllLeafNodesWithAncestors_WhenIncompleteAncestorData_ShouldIncludeAvailableData() {
        // Given
        when(categoryHierarchyService.findByDepth(3)).thenReturn(leafNodes);

        // Create incomplete data (missing some ancestor levels)
        List<CategoryDto> incompleteCategories = Arrays.asList(
            createCategoryDto(leafId1, null, 0, "Leaf 1"),
            createCategoryDto(leafId1, ancestorId1, 1, "Leaf 1", "Parent 1"),
            // Missing depth 2 for leaf1
            createCategoryDto(leafId1, ancestorId3, 3, "Leaf 1", "Great Grandparent 1")
        );

        when(customizedCategoryJpaDao.getCategories(any(CategoryQuery.class))).thenReturn(incompleteCategories);

        // When
        Map<UUID, Map<Integer, String>> result = categoryApplicationService.getAllLeafNodesWithAncestors();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        Map<Integer, String> leaf1Map = result.get(leafId1);
        assertNotNull(leaf1Map);
        assertEquals(3, leaf1Map.size());
        assertEquals("Leaf 1", leaf1Map.get(0));
        assertEquals("Parent 1", leaf1Map.get(1));
        assertEquals("Great Grandparent 1", leaf1Map.get(3));
        assertFalse(leaf1Map.containsKey(2)); // Depth 2 should be missing
    }


    @Test
    void testSearchCategoryTree_itemCount() {
        // Arrange
        CategoryDtoBuilder categoryDtoBuilder = mock(CategoryDtoBuilder.class);
        when(categoryDtoBuilder.ancestorCategoryId(Mockito.<UUID>any())).thenReturn(CategoryDto.builder());
        CategoryDto buildResult = categoryDtoBuilder.ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();

        ArrayList<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(buildResult);
        when(customizedCategoryJpaDao.getCategories(Mockito.<CategoryQuery>any())).thenReturn(categoryDtoList);

        ArrayList<CategoryItemCountsDto> categoryItemCountsDtoList = new ArrayList<>();
        CategoryItemCountsDto buildResult2 = CategoryItemCountsDto.builder()
            .activeCount(3L)
            .archivedCount(3L)
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .draftCount(3L)
            .build();
        categoryItemCountsDtoList.add(buildResult2);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(Mockito.<List<UUID>>any()))
            .thenReturn(categoryItemCountsDtoList);

        // Act
        List<CategoryTreeDto> actualSearchCategoryTreeResult = categoryApplicationService.searchCategoryTree();

        // Assert
        verify(categoryDtoBuilder).ancestorCategoryId(isA(UUID.class));
        verify(itemSearchApplicationService).countItemsByCategoryIdAndStatus(isA(List.class));
        verify(customizedCategoryJpaDao).getCategories(isA(CategoryQuery.class));
        assertTrue(actualSearchCategoryTreeResult.isEmpty());
    }

    @Test
    void testDeleteCategory_Success() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        List<CategoryHierarchy> categoryHierarchies = new ArrayList<>();
        UUID hierarchyId = UUID.randomUUID();
        CategoryHierarchy hierarchy = CategoryHierarchy.builder().id(hierarchyId).depth(2).build();

        categoryHierarchies.add(hierarchy);

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId)).thenReturn(categoryHierarchies);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(any())).thenReturn(Collections.emptyList());
        when(categoryHierarchyService.findByCategoryIdIn(any())).thenReturn(categoryHierarchies);

        // Act
        categoryApplicationService.deleteCategory(categoryId);

        // Assert
        verify(categoryService).delete(categoryId);

        ArgumentCaptor<CategoryDeletedPayloadDto> payloadCaptor = ArgumentCaptor.forClass(CategoryDeletedPayloadDto.class);
        verify(businessEventService).dispatch(payloadCaptor.capture());

        CategoryDeletedPayloadDto payload = payloadCaptor.getValue();
        assertEquals(categoryId, payload.getCategoryId());
        assertEquals(1, payload.getData().getCategoryHierarchyIds().size());
        assertEquals(hierarchyId, payload.getData().getCategoryHierarchyIds().getFirst());
    }

    @Test
    void testDeleteCategory_CategoryNotFound() {
        // Arrange
        when(categoryService.findById(categoryId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.deleteCategory(categoryId));
        assertEquals(CATEGORY_NOT_FOUND.getCode(), exception.getCode());

        verify(categoryService, never()).delete(any());
        verify(businessEventService, never()).dispatch(any(CategoryDeletedPayloadDto.class));
    }

    @Test
    void testDeleteCategory_NoHierarchy_AddsCategoryIdToLeafIds() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId)).thenReturn(Collections.emptyList());

        // Need to capture the list that's passed to countItemsByCategoryIdAndStatus
        ArgumentCaptor<List<UUID>> leafIdsCaptor = ArgumentCaptor.forClass(List.class);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(leafIdsCaptor.capture()))
            .thenReturn(Collections.emptyList());

        // Act
        categoryApplicationService.deleteCategory(categoryId);

        // Assert
        List<UUID> capturedLeafIds = leafIdsCaptor.getValue();
        assertEquals(1, capturedLeafIds.size());
        assertEquals(categoryId, capturedLeafIds.getFirst());

        verify(categoryService).delete(categoryId);
    }

    @Test
    void testDeleteCategory_WithHierarchy_UsesMaxDepthCategories() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        UUID leafCategoryId1 = UUID.randomUUID();
        UUID leafCategoryId2 = UUID.randomUUID();

        List<CategoryHierarchy> categoryHierarchies = new ArrayList<>();
        // Create hierarchies with different depths
        CategoryHierarchy hierarchy1 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId1)
            .depth(2)
            .build();

        CategoryHierarchy hierarchy2 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(leafCategoryId2)
            .depth(2)
            .build();

        CategoryHierarchy hierarchy3 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(UUID.randomUUID())
            .depth(1)
            .build();

        categoryHierarchies.add(hierarchy1);
        categoryHierarchies.add(hierarchy2);
        categoryHierarchies.add(hierarchy3);

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId)).thenReturn(categoryHierarchies);

        // Need to capture the list that's passed to countItemsByCategoryIdAndStatus
        ArgumentCaptor<List<UUID>> leafIdsCaptor = ArgumentCaptor.forClass(List.class);
        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(leafIdsCaptor.capture()))
            .thenReturn(Collections.emptyList());

        // Act
        categoryApplicationService.deleteCategory(categoryId);

        // Assert
        List<UUID> capturedLeafIds = leafIdsCaptor.getValue();
        assertEquals(2, capturedLeafIds.size());
        assertTrue(capturedLeafIds.contains(leafCategoryId1));
        assertTrue(capturedLeafIds.contains(leafCategoryId2));

        verify(categoryService).delete(categoryId);
    }

    @Test
    void testDeleteCategory_HasItems_ThrowsException() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        UUID leafCategoryId = UUID.randomUUID();
        CategoryHierarchy hierarchy = CategoryHierarchy.builder().categoryId(leafCategoryId).depth(1).build();

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId))
            .thenReturn(Collections.singletonList(hierarchy));

        // Create a dto that indicates there are active items
        CategoryItemCountsDto countsDto = new CategoryItemCountsDto();
        countsDto.setCategoryId(leafCategoryId);
        countsDto.setActiveCount(1L); // Has active items

        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(any()))
            .thenReturn(Collections.singletonList(countsDto));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.deleteCategory(categoryId));
        assertEquals(CATEGORY_HAS_ITEMS.getCode(), exception.getCode());

        verify(categoryService, never()).delete(any());
        verify(businessEventService, never()).dispatch(any(CategoryDeletedPayloadDto.class));
    }

    @Test
    void testDeleteCategory_HasDraftItems_ThrowsException() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId))
            .thenReturn(Collections.emptyList());

        // Create a dto that indicates there are draft items
        CategoryItemCountsDto countsDto = new CategoryItemCountsDto();
        countsDto.setCategoryId(categoryId);
        countsDto.setActiveCount(0L);
        countsDto.setDraftCount(1L); // Has draft items

        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(any()))
            .thenReturn(Collections.singletonList(countsDto));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.deleteCategory(categoryId));
        assertEquals(CATEGORY_HAS_ITEMS.getCode(), exception.getCode());
    }

    @Test
    void testDeleteCategory_HasArchivedItems_ThrowsException() {
        // Arrange
        Category category = Category.builder().id(categoryId).build();

        when(categoryService.findById(categoryId)).thenReturn(category);
        when(categoryHierarchyService.findByAncestorCategoryId(categoryId))
            .thenReturn(Collections.emptyList());

        // Create a dto that indicates there are archived items
        CategoryItemCountsDto countsDto = new CategoryItemCountsDto();
        countsDto.setCategoryId(categoryId);
        countsDto.setActiveCount(0L);
        countsDto.setDraftCount(0L);
        countsDto.setArchivedCount(1L); // Has archived items

        when(itemSearchApplicationService.countItemsByCategoryIdAndStatus(any()))
            .thenReturn(Collections.singletonList(countsDto));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> categoryApplicationService.deleteCategory(categoryId));
        assertEquals(CATEGORY_HAS_ITEMS.getCode(), exception.getCode());
    }

    // Helper methods
    private CategoryHierarchy createCategoryHierarchy(UUID categoryId, UUID ancestorId, int depth) {
        return CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(categoryId)
            .ancestorCategoryId(ancestorId)
            .depth(depth)
            .sortOrder(1)
            .build();
    }

    private CategoryDto createCategoryDto(UUID categoryId, UUID ancestorId, int depth, String categoryName) {
        return CategoryDto.builder()
            .categoryId(categoryId)
            .ancestorCategoryId(ancestorId)
            .depth(depth)
            .categoryName(categoryName)
            .build();
    }

    private CategoryDto createCategoryDto(UUID categoryId, UUID ancestorId, int depth, String categoryName, String ancestorName) {
        return CategoryDto.builder()
            .categoryId(categoryId)
            .ancestorCategoryId(ancestorId)
            .depth(depth)
            .categoryName(categoryName)
            .ancestorName(ancestorName)
            .build();
    }
}