package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.infrastructure.excel.generator.BulkExportExcelGenerator;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ItemBulkExportStrategyTest {

    @Mock
    private FinaleAdaptor finaleAdaptor;

    @Mock
    private BulkExportExcelGenerator excelGenerator;

    @Mock
    private ItemQueryApplicationService itemQueryApplicationService;

    @Mock
    private ItemSearchApplicationService itemSearchApplicationService;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private ItemBulkExportStrategy itemBulkExportStrategy;

    private static final String CUSTOM_FILTER = "{\"status\":\"ACTIVE\"}";
    private List<ItemSerachDto> mockItems;
    private List<FinaleAvailableStockDto> mockFinaleData;

    @BeforeEach
    void setUp() {
        mockItems = createMockItems();
        mockFinaleData = createMockFinaleData();

        ReflectionTestUtils.setField(itemBulkExportStrategy, "itemQueryApplicationService", itemQueryApplicationService);
        ReflectionTestUtils.setField(itemBulkExportStrategy, "taskExecutor", taskExecutor);
        ReflectionTestUtils.setField(itemBulkExportStrategy, "itemSearchApplicationService", itemSearchApplicationService);
    }

    @Test
    void shouldReturnCorrectExportType() {
        assertEquals(ExportType.ITEM, itemBulkExportStrategy.getExportType());
    }

    @Test
    void shouldExecuteSuccessfully() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        ItemBulkExportStrategy spyStrategy = spy(itemBulkExportStrategy);
        doReturn(mockItems).when(spyStrategy).fetchFullItemData(CUSTOM_FILTER);

        when(finaleAdaptor.getAllProducts()).thenReturn(mockFinaleData);
        when(excelGenerator.generateBulkExportReport(
                mockItems,
                mockFinaleData,
                Collections.emptyMap(),
                Collections.emptyList(),
                ExportType.ITEM
        )).thenReturn(expectedBytes);

        // When
        byte[] result = spyStrategy.execute(CUSTOM_FILTER);

        // Then
        assertNotNull(result);
        assertEquals(expectedBytes, result);

        verify(finaleAdaptor).getAllProducts();
        verify(excelGenerator).generateBulkExportReport(
                mockItems, mockFinaleData, Collections.emptyMap(), Collections.emptyList(), ExportType.ITEM
        );
    }

    @Test
    void shouldPassEmptyVendorMapToGenerator() {
        // Given
        byte[] expectedBytes = "Excel content".getBytes();

        ItemBulkExportStrategy spyStrategy = spy(itemBulkExportStrategy);
        doReturn(mockItems).when(spyStrategy).fetchFullItemData(CUSTOM_FILTER);

        when(finaleAdaptor.getAllProducts()).thenReturn(mockFinaleData);
        when(excelGenerator.generateBulkExportReport(
                any(), any(), any(), any(), eq(ExportType.ITEM)
        )).thenReturn(expectedBytes);

        // When
        spyStrategy.execute(CUSTOM_FILTER);

        // Then
        verify(excelGenerator).generateBulkExportReport(
                mockItems, mockFinaleData, Collections.emptyMap(), Collections.emptyList(), ExportType.ITEM
        );
    }

    @Test
    void shouldHandleFinaleAdaptorException() {
        // Given
        ItemBulkExportStrategy spyStrategy = spy(itemBulkExportStrategy);
        doReturn(mockItems).when(spyStrategy).fetchFullItemData(CUSTOM_FILTER);
        when(finaleAdaptor.getAllProducts()).thenThrow(new RuntimeException("Finale service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            spyStrategy.execute(CUSTOM_FILTER);
        });
    }

    private List<ItemSerachDto> createMockItems() {
        List<ItemSerachDto> items = new ArrayList<>();
        ItemSerachDto item1 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU001")
                .title("Test Item 1")
                .availabilityStatus("ACTIVE")
                .categoryId(UUID.randomUUID())
                .brandId(UUID.randomUUID())
                .regPrice(new BigDecimal("10.00"))
                .build();
        items.add(item1);
        return items;
    }

    private List<FinaleAvailableStockDto> createMockFinaleData() {
        List<FinaleAvailableStockDto> finaleData = new ArrayList<>();
        FinaleAvailableStockDto stockDto = new FinaleAvailableStockDto();
        stockDto.setSku("SKU001");
        stockDto.setShopifyQoh(100);
        stockDto.setMfcQoh(50);
        stockDto.setReservationsQoh(10);
        finaleData.add(stockDto);
        return finaleData;
    }
}