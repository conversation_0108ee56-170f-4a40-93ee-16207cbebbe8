package com.mercaso.ims.infrastructure.repository.bulkexportrecords;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecordsRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import java.time.Instant;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;

class BulkExportRecordsRepositoryImplIT extends AbstractIT {

    @Autowired
    private BulkExportRecordsRepository bulkExportRecordsRepository;

    @Test
    void testSave() {
        // Arrange
        BulkExportRecords bulkExportRecords = BulkExportRecords.builder()
            .fileName("test_export.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"key\":\"value\"}")
            .exportBy("System")
            .build();

        // Act
        BulkExportRecords result = bulkExportRecordsRepository.save(bulkExportRecords);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(result.getId());
        Assertions.assertEquals(bulkExportRecords.getFileName(), result.getFileName());
        Assertions.assertEquals(bulkExportRecords.getCustomFilter(), result.getCustomFilter());
        Assertions.assertEquals(bulkExportRecords.getExportBy(), result.getExportBy());
    }

    @Test
    void testFindById() {
        // Arrange
        BulkExportRecords bulkExportRecords = BulkExportRecords.builder()
            .fileName("test_export_find.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"key\": \"value\"}")
            .exportBy("System")
            .build();

        BulkExportRecords savedBulkExportRecords = bulkExportRecordsRepository.save(bulkExportRecords);
        UUID id = savedBulkExportRecords.getId();

        // Act
        BulkExportRecords result = bulkExportRecordsRepository.findById(id);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(id, result.getId());
        Assertions.assertEquals(bulkExportRecords.getFileName(), result.getFileName());
        Assertions.assertEquals(bulkExportRecords.getCustomFilter(), result.getCustomFilter());
        Assertions.assertEquals(bulkExportRecords.getExportBy(), result.getExportBy());
    }

    @Test
    void testFindByIdNotFound() {
        // Arrange
        UUID randomId = UUID.randomUUID();

        // Act
        BulkExportRecords result = bulkExportRecordsRepository.findById(randomId);

        // Assert
        Assertions.assertNull(result);
    }

    @Test
    void testUpdate() {
        // Arrange
        BulkExportRecords bulkExportRecords = BulkExportRecords.builder()
            .fileName("test_export_update.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"key\":\"value\"}")
            .exportBy("System")
            .build();

        BulkExportRecords savedBulkExportRecords = bulkExportRecordsRepository.save(bulkExportRecords);
        UUID id = savedBulkExportRecords.getId();

        // Update fields
        String updatedFileName = "updated_export.csv";
        String updatedCustomFilter = "{\"key\":\"updated_value\"}";
        savedBulkExportRecords.setFileName(updatedFileName);
        savedBulkExportRecords.setCustomFilter(updatedCustomFilter);

        // Act
        BulkExportRecords updatedResult = bulkExportRecordsRepository.update(savedBulkExportRecords);

        // Assert
        Assertions.assertNotNull(updatedResult);
        Assertions.assertEquals(id, updatedResult.getId());
        Assertions.assertEquals(updatedFileName, updatedResult.getFileName());
        Assertions.assertEquals(updatedCustomFilter, updatedResult.getCustomFilter());
    }

    @Test
    void testDeleteById() {
        // Arrange
        BulkExportRecords bulkExportRecords = BulkExportRecords.builder()
            .fileName("test_export_delete.csv")
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"key\":\"value\"}")
            .exportBy("System")
            .build();

        BulkExportRecords savedBulkExportRecords = bulkExportRecordsRepository.save(bulkExportRecords);
        UUID id = savedBulkExportRecords.getId();

        // Act
        BulkExportRecords deletedRecord = bulkExportRecordsRepository.deleteById(id);
        BulkExportRecords result = bulkExportRecordsRepository.findById(id);

        // Assert
        Assertions.assertNotNull(deletedRecord);
        Assertions.assertEquals(id, deletedRecord.getId());
        Assertions.assertNotNull(deletedRecord.getDeletedAt());
        Assertions.assertNotNull(deletedRecord.getDeletedBy());
        Assertions.assertNull(result);
    }
}
