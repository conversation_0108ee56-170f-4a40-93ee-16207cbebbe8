package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.mapper.vendor.VendorDtoApplicationMapper;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import java.util.UUID;
import java.util.Collections;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class VendorApplicationServiceImplTest {

    @Mock
    VendorService vendorService;
    @Mock
    VendorDtoApplicationMapper vendorDtoApplicationMapper;
    @Mock
    FinaleApplicationService finaleApplicationService;
    @Mock
    BusinessEventService businessEventService;
    @Mock
    VendorItemService vendorItemService;
    @InjectMocks
    VendorApplicationServiceImpl vendorApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreate() {
        UUID id = UUID.randomUUID();
        VendorDto vendorDto = VendorDto.builder()
            .id(id)
            .vendorName("vendorName")
            .vendorContactName("vendorContactName")
            .vendorContactTel("vendorContactTel")
            .vendorCompanyName("vendorCompanyName")
            .vendorStatus(VendorStatus.ACTIVE)
            .build();

        when(vendorService.findByVendorName(anyString())).thenReturn(null);
        when(vendorService.save(any(Vendor.class))).thenReturn(Vendor.builder().id(id).build());
        when(vendorDtoApplicationMapper.domainToDto(any(Vendor.class))).thenReturn(vendorDto);
        when(finaleApplicationService.createVendor(any())).thenReturn(new FinaleVendorDto("1001", "test"));
        VendorDto result = vendorApplicationServiceImpl.create(new CreateVendorCommand(id, "vendorName", null, null));
        Assertions.assertEquals(id, result.getId());
    }

    @Test
    void testUpdate() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        VendorDto mockDto = mock(VendorDto.class);
        doNothing().when(vendor).updateVendorName(Mockito.<String>any());
        when(vendorService.update(Mockito.<Vendor>any())).thenReturn(vendor);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        when(vendorService.findByVendorName(Mockito.<String>any())).thenReturn(null);
        when(vendorDtoApplicationMapper.domainToDto(Mockito.<Vendor>any())).thenReturn(mockDto);
        UpdateVendorCommand command = new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null, null);
        
        // Act
        VendorDto result = vendorApplicationServiceImpl.update(command);
        
        // Assert
        assertNotNull(result);
        verify(vendorDtoApplicationMapper, times(2)).domainToDto(vendor); // Called twice in the method
        verify(vendor).updateVendorName("Vendor Name");
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorService).findByVendorName("Vendor Name");
        verify(vendorService).update(isA(Vendor.class));
    }

    @Test
    void testUpdate_givenVendorServiceFindByIdReturnNull_thenThrowImsBusinessException() {
        // Arrange
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);
        UpdateVendorCommand command = new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null, null);
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> vendorApplicationServiceImpl.update(command));
        verify(vendorService).findById(isA(UUID.class));
    }

    @Test
    void testUpdate_thenReturnVendorCompanyName() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        Vendor updatedVendor = mock(Vendor.class);
        doNothing().when(vendor).updateVendorName(Mockito.<String>any());
        doNothing().when(vendor).setShutdownWindow(Mockito.<Boolean>any());
        when(vendorService.update(Mockito.<Vendor>any())).thenReturn(updatedVendor);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        when(vendorService.findByVendorName(Mockito.<String>any())).thenReturn(null);
        
        VendorDto.VendorDtoBuilder builderResult = VendorDto.builder();
        UUID id = UUID.randomUUID();
        VendorDto buildResult = builderResult.id(id)
            .vendorCompanyName("Vendor Company Name")
            .vendorContactName("Vendor Contact Name")
            .vendorContactTel("Vendor Contact Tel")
            .vendorName("Vendor Name")
            .vendorStatus(VendorStatus.ACTIVE)
            .build();
        
        // Mock for the previous vendorDto call (line 68)
        when(vendorDtoApplicationMapper.domainToDto(vendor)).thenReturn(buildResult);
        // Mock for the final vendorDto call (line 81)
        when(vendorDtoApplicationMapper.domainToDto(updatedVendor)).thenReturn(buildResult);

        // Act
        VendorDto actualUpdateResult = vendorApplicationServiceImpl
            .update(new UpdateVendorCommand(UUID.randomUUID(), "Vendor Name", null, null));

        // Assert
        verify(vendorDtoApplicationMapper).domainToDto(vendor); // Called for previous
        verify(vendorDtoApplicationMapper).domainToDto(updatedVendor); // Called for final result
        verify(vendor).updateVendorName("Vendor Name");
        verify(vendor).setShutdownWindow(null);
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorService).findByVendorName("Vendor Name");
        verify(vendorService).update(isA(Vendor.class));
        assertEquals("Vendor Company Name", actualUpdateResult.getVendorCompanyName());
        assertEquals("Vendor Contact Name", actualUpdateResult.getVendorContactName());
        assertEquals("Vendor Contact Tel", actualUpdateResult.getVendorContactTel());
        assertEquals("Vendor Name", actualUpdateResult.getVendorName());
        assertEquals(VendorStatus.ACTIVE, actualUpdateResult.getVendorStatus());
        assertSame(id, actualUpdateResult.getId());
    }

    @Test
    void testDelete_thenReturnVendorCompanyName() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        Vendor deletedVendor = mock(Vendor.class);
        when(vendor.getExternalPicking()).thenReturn(false);
        when(vendor.getVendorName()).thenReturn("Vendor Name");
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        when(vendorItemService.findByVendorID(Mockito.<UUID>any())).thenReturn(Collections.emptyList());
        when(deletedVendor.getFinaleId()).thenReturn("finale123");
        when(vendorService.delete(Mockito.<UUID>any())).thenReturn(deletedVendor);
        when(finaleApplicationService.getVendorById(anyString())).thenReturn(null);

        UUID id = UUID.randomUUID();
        VendorDto buildResult = VendorDto.builder()
                .id(id)
                .vendorCompanyName("Vendor Company Name")
                .vendorContactName("Vendor Contact Name")
                .vendorContactTel("Vendor Contact Tel")
                .vendorName("Vendor Name")
                .vendorStatus(VendorStatus.ACTIVE)
                .build();
        when(vendorDtoApplicationMapper.domainToDto(deletedVendor)).thenReturn(buildResult);

        // Act
        VendorDto actualDeleteResult = vendorApplicationServiceImpl.delete(UUID.randomUUID());

        // Assert
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorItemService).findByVendorID(isA(UUID.class));
        verify(vendorService).delete(isA(UUID.class));
        verify(finaleApplicationService).getVendorById("finale123");
        verify(vendorDtoApplicationMapper).domainToDto(deletedVendor);
        assertEquals("Vendor Company Name", actualDeleteResult.getVendorCompanyName());
        assertEquals("Vendor Contact Name", actualDeleteResult.getVendorContactName());
        assertEquals("Vendor Contact Tel", actualDeleteResult.getVendorContactTel());
        assertEquals("Vendor Name", actualDeleteResult.getVendorName());
        assertEquals(VendorStatus.ACTIVE, actualDeleteResult.getVendorStatus());
        assertSame(id, actualDeleteResult.getId());
    }

    @Test
    void testDelete_whenVendorNotFound_thenThrowImsBusinessException() {
        // Arrange
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);

        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorApplicationServiceImpl.delete(id));
        verify(vendorService).findById(isA(UUID.class));
    }

    @Test
    void testDelete_whenVendorIsExternal_thenThrowImsBusinessException() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        when(vendor.getExternalPicking()).thenReturn(true);
        when(vendor.getVendorName()).thenReturn("External Vendor");
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);

        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorApplicationServiceImpl.delete(id));
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorItemService, times(0)).findByVendorID(isA(UUID.class));
    }

    @Test
    void testDelete_whenVendorHasAssociatedItems_thenThrowImsBusinessException() {
        // Arrange
        Vendor vendor = mock(Vendor.class);
        when(vendor.getExternalPicking()).thenReturn(false);
        when(vendor.getVendorName()).thenReturn("Vendor With Items");
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(vendor);
        when(vendorItemService.findByVendorID(Mockito.<UUID>any())).thenReturn(Collections.singletonList(mock(com.mercaso.ims.domain.vendoritem.VendorItem.class)));

        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorApplicationServiceImpl.delete(id));
        verify(vendorService).findById(isA(UUID.class));
        verify(vendorItemService).findByVendorID(isA(UUID.class));
        verify(vendorService, times(0)).delete(isA(UUID.class));
    }
}