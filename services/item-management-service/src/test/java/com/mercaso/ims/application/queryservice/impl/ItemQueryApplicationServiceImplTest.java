package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemDto.ItemDtoBuilder;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto.ItemRegPriceDtoBuilder;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.VendorItemDto.VendorItemDtoBuilder;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itempackinginfo.ItemPickingInfoDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itempromoprice.ItemPromoPriceDtoApplicationMapper;
import com.mercaso.ims.application.mapper.itemregprice.ItemRegPriceDtoApplicationMapper;
import com.mercaso.ims.application.mapper.vendoritem.VendorItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.queryservice.ItemVendorRebateQueryApplicationService;
import com.mercaso.ims.application.queryservice.VendorItemQueryApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itempricegroup.service.impl.ItemPriceGroupServiceImpl;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.itemversion.service.ItemVersionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import com.mercaso.ims.utils.item.ItemImageUtil;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemQueryApplicationServiceImpl.class})
class ItemQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private AttributeService attributeService;

    @MockBean
    private BrandDtoApplicationMapper brandMapper;

    @MockBean
    private BrandService brandService;

    @MockBean
    private ItemDtoApplicationMapper itemMapper;

    @MockBean
    private ItemPromoPriceDtoApplicationMapper itemPromoPriceMapper;

    @MockBean
    private ItemPromoPriceService itemPromoPriceService;

    @Autowired
    private ItemQueryApplicationService itemQueryApplicationService;

    @MockBean
    private ItemRegPriceDtoApplicationMapper itemRegPriceMapper;

    @MockBean
    private ItemRegPriceService itemRegPriceService;

    @MockBean
    private ItemService itemService;

    @MockBean
    private ItemRepository itemRepository;

    @MockBean
    private VendorItemDtoApplicationMapper vendorItemMapper;

    @MockBean
    private VendorItemQueryApplicationService vendorItemQueryApplicationService;

    @MockBean
    private VendorService vendorService;

    @MockBean
    private DocumentOperations documentOperations;

    @MockBean
    private CategoryService categoryService;

    @MockBean
    private DocumentApplicationService documentApplicationService;

    @MockBean
    private CustomizedItemJpaDao customizedItemJpaDao;

    @MockBean
    private ItemPickingInfoDtoApplicationMapper itemPickingInfoDtoApplicationMapper;
    @MockBean
    private ItemPriceGroupServiceImpl itemPriceGroupServiceImpl;
    @MockBean
    private CategoryApplicationService categoryApplicationService;
    @MockBean
    private FeatureFlagsManager featureFlagsManager;
    @MockBean
    private ItemVersionService itemVersionService;
    @MockBean
    private ItemVendorRebateQueryApplicationService itemVendorRebateQueryApplicationService;

    @Test
    void testFindById() {
        // Arrange
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(null);
        UUID id = UUID.randomUUID();

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService.findById(id));
        verify(itemService).findById(isA(UUID.class));
    }

    @Test
    void testFindById2() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBrandId()).thenReturn(UUID.randomUUID());
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(item.getPrimaryVendorId()).thenReturn(UUID.randomUUID());

        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);
        ItemRegPrice itemRegPrice1 = mock(ItemRegPrice.class);
        when(itemRegPrice1.getRegPrice()).thenReturn(new BigDecimal("2.3"));
        when(itemRegPrice1.getRegPriceIndividual()).thenReturn(new BigDecimal("2.3"));
        when(itemRegPrice1.getCrv()).thenReturn(new BigDecimal("2.3"));
        when(itemRegPrice1.getRegPricePlusCrv()).thenReturn(new BigDecimal("2.3"));
        when(itemRegPrice1.getItemId()).thenReturn(UUID.randomUUID());
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice1);
        ItemPromoPrice itemPromoPrice = mock(ItemPromoPrice.class);
        when(itemPromoPrice.getPromoFlag()).thenReturn(true);
        when(itemPromoPrice.getItemId()).thenReturn(UUID.randomUUID());
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(Collections.singletonList(itemPromoPrice));
        when(item.getPrimaryVendorId()).thenReturn(UUID.randomUUID());
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        ItemDto.ItemDtoBuilder availabilityStatusResult = ItemDto.builder().availabilityStatus("Availability Status");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        UUID brandId = UUID.randomUUID();
        BrandDto brand = builderResult.brandId(brandId).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = availabilityStatusResult.brand(brand);
        UUID categoryId = UUID.randomUUID();
        ItemDto.ItemDtoBuilder handleResult = brandResult.categoryId(categoryId)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        UUID id = UUID.randomUUID();
        ItemDto.ItemDtoBuilder idResult = handleResult.id(id);
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(itemAttributes);
        List<ItemImageDto> itemImages = Arrays.asList(
            ItemImageUtil.buildItemImageDto("main", ImageType.MAIN_IMAGE),
            ItemImageUtil.buildItemImageDto("prmo", ImageType.PROMO_IMAGE)
        );
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(itemImages);
        ItemPromoPriceDto itemPromoPriceDto = ItemPromoPriceDto.builder()
            .promoFlag(true)
            .promoPrice(new BigDecimal("2.3"))
            .promoPriceIndividual(new BigDecimal("2.3"))
            .promoPricePlusCrv(new BigDecimal("2.3"))
            .crv(new BigDecimal("2.3"))
            .build();
        List<ItemPromoPriceDto> itemPromoPrices = Arrays.asList(itemPromoPriceDto);

        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        BigDecimal crv = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult2.crv(crv);
        UUID itemRegPriceId = UUID.randomUUID();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(itemRegPriceId);
        BigDecimal regPrice = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(regPrice);
        BigDecimal regPriceIndividual = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(regPriceIndividual);
        BigDecimal regPricePlusCrv = new BigDecimal("2.3");
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(regPricePlusCrv).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ArrayList<ItemTagDto> itemTags = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(itemTags).itemType("Item Type");
        ArrayList<ItemUPCDto> itemUPCs = new ArrayList<>();
        ItemDto.ItemDtoBuilder packageTypeResult = itemTypeResult.itemUPCs(itemUPCs)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text");
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        BigDecimal cost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(cost);
        BigDecimal highestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(highestCost);
        BigDecimal lowestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(lowestCost).note("Note");

        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        UUID vendorId = UUID.randomUUID();
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(vendorId);
        UUID vendorItemId = UUID.randomUUID();
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(vendorItemId)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = packageTypeResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .build();
        buildResult.setPhotoName("test.jpg");
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        when(itemRegPriceMapper.domainToDto(Mockito.<ItemRegPrice>any())).thenReturn(new ItemRegPriceDto());

        // Act
        ItemDto actualFindByIdResult = itemQueryApplicationService.findById(UUID.randomUUID());

        // Assert
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBrandId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindByIdResult.getSkuNumber());
        VendorItemDto primaryVendorItem2 = actualFindByIdResult.getPrimaryVendorItem();
        assertEquals("42", primaryVendorItem2.getVendorSkuNumber());
        assertEquals("Aisle", primaryVendorItem2.getAisle());
        assertEquals("Availability Status", actualFindByIdResult.getAvailabilityStatus());
        BrandDto brand2 = actualFindByIdResult.getBrand();
        assertEquals("Brand Name", brand2.getBrandName());
        assertEquals("Detail", actualFindByIdResult.getDetail());

    }

    @Test
    void testFindById3() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBrandId()).thenReturn(UUID.randomUUID());
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(item.getPrimaryVendorId()).thenReturn(UUID.randomUUID());
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any()))
            .thenThrow(new ImsBusinessException("[populatePhotoUrl] This item has no photo, Id: {}, SkuNum:{}"));
        ItemDto.ItemDtoBuilder availabilityStatusResult = ItemDto.builder().availabilityStatus("Availability Status");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = availabilityStatusResult.brand(brand);
        ItemDto.ItemDtoBuilder handleResult = brandResult.categoryId(UUID.randomUUID())
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult2.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder packageTypeResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text");
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = packageTypeResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        UUID id = UUID.randomUUID();

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService.findById(id));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBrandId();
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
    }

    @Test
    void testFindById4() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBrandId()).thenReturn(null);
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(item.getPrimaryVendorId()).thenReturn(UUID.randomUUID());
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(vendorService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        ItemDto.ItemDtoBuilder availabilityStatusResult = ItemDto.builder().availabilityStatus("Availability Status");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        UUID brandId = UUID.randomUUID();
        BrandDto brand = builderResult.brandId(brandId).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = availabilityStatusResult.brand(brand);
        UUID categoryId = UUID.randomUUID();
        ItemDto.ItemDtoBuilder handleResult = brandResult.categoryId(categoryId)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        UUID id = UUID.randomUUID();
        ItemDto.ItemDtoBuilder idResult = handleResult.id(id);
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(itemAttributes);
        ArrayList<ItemImageDto> itemImages = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(itemImages);
        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        BigDecimal crv = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult2.crv(crv);
        UUID itemRegPriceId = UUID.randomUUID();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(itemRegPriceId);
        BigDecimal regPrice = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(regPrice);
        BigDecimal regPriceIndividual = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(regPriceIndividual);
        BigDecimal regPricePlusCrv = new BigDecimal("2.3");
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(regPricePlusCrv).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ArrayList<ItemTagDto> itemTags = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(itemTags).itemType("Item Type");
        ArrayList<ItemUPCDto> itemUPCs = new ArrayList<>();
        ItemDto.ItemDtoBuilder packageTypeResult = itemTypeResult.itemUPCs(itemUPCs)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text");
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        BigDecimal cost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(cost);
        BigDecimal highestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(highestCost);
        BigDecimal lowestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(lowestCost).note("Note");

        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        UUID vendorId = UUID.randomUUID();
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(vendorId);
        UUID vendorItemId = UUID.randomUUID();
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(vendorItemId)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = packageTypeResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);

        // Act
        ItemDto actualFindByIdResult = itemQueryApplicationService.findById(UUID.randomUUID());

        // Assert
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(item).getBrandId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindByIdResult.getSkuNumber());
        VendorItemDto primaryVendorItem2 = actualFindByIdResult.getPrimaryVendorItem();
        assertEquals("42", primaryVendorItem2.getVendorSkuNumber());
        assertEquals("Aisle", primaryVendorItem2.getAisle());
        assertEquals("Availability Status", actualFindByIdResult.getAvailabilityStatus());
        BrandDto brand2 = actualFindByIdResult.getBrand();
        assertEquals("Brand Name", brand2.getBrandName());
    }


    @Test
    void testFindById5() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBrandId()).thenReturn(UUID.randomUUID());
        when(item.getId()).thenReturn(UUID.randomUUID());
        when(item.getPrimaryVendorId()).thenReturn(null);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        ItemDto.ItemDtoBuilder availabilityStatusResult = ItemDto.builder().availabilityStatus("Availability Status");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        UUID brandId = UUID.randomUUID();
        BrandDto brand = builderResult.brandId(brandId).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = availabilityStatusResult.brand(brand);
        UUID categoryId = UUID.randomUUID();
        ItemDto.ItemDtoBuilder handleResult = brandResult.categoryId(categoryId)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        UUID id = UUID.randomUUID();
        ItemDto.ItemDtoBuilder idResult = handleResult.id(id);
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(itemAttributes);
        ArrayList<ItemImageDto> itemImages = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(itemImages);
        ArrayList<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(itemPromoPrices);
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        BigDecimal crv = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult2.crv(crv);
        UUID itemRegPriceId = UUID.randomUUID();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(itemRegPriceId);
        BigDecimal regPrice = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(regPrice);
        BigDecimal regPriceIndividual = new BigDecimal("2.3");
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(regPriceIndividual);
        BigDecimal regPricePlusCrv = new BigDecimal("2.3");
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(regPricePlusCrv).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ArrayList<ItemTagDto> itemTags = new ArrayList<>();
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(itemTags).itemType("Item Type");
        ArrayList<ItemUPCDto> itemUPCs = new ArrayList<>();
        ItemDto.ItemDtoBuilder packageTypeResult = itemTypeResult.itemUPCs(itemUPCs)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text");
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        BigDecimal cost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(cost);
        BigDecimal highestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(highestCost);
        BigDecimal lowestCost = new BigDecimal("2.3");
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(lowestCost).note("Note");

        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        UUID vendorId = UUID.randomUUID();
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(vendorId);
        UUID vendorItemId = UUID.randomUUID();
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(vendorItemId)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = packageTypeResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);

        // Act
        ItemDto actualFindByIdResult = itemQueryApplicationService.findById(UUID.randomUUID());

        // Assert
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBrandId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindByIdResult.getSkuNumber());
        VendorItemDto primaryVendorItem2 = actualFindByIdResult.getPrimaryVendorItem();
        assertEquals("42", primaryVendorItem2.getVendorSkuNumber());
        assertEquals("Aisle", primaryVendorItem2.getAisle());
        assertEquals("Availability Status", actualFindByIdResult.getAvailabilityStatus());
        BrandDto brand2 = actualFindByIdResult.getBrand();
        assertEquals("Brand Name", brand2.getBrandName());
    }

    @Test
    void testFindPagedItemsByUpdateAt_givenItemDtoApplicationMapper_thenReturnPageImpl() {
        // Arrange
        when(itemRepository.findByUpdatedAtBetween(Mockito.<Instant>any(), Mockito.<Instant>any(), Mockito.<Pageable>any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));
        Instant startTime = LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant();

        // Act
        Page<ItemDto> actualFindPagedItemsByUpdateAtResult = itemQueryApplicationService.findPagedItemsByUpdateAt(null,
            startTime, LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());

        // Assert
        verify(itemRepository).findByUpdatedAtBetween(isA(Instant.class), isA(Instant.class), isNull());
        assertTrue(actualFindPagedItemsByUpdateAtResult instanceof PageImpl);
        assertTrue(actualFindPagedItemsByUpdateAtResult.toList().isEmpty());
    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBackupVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getBrandId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getPrimaryVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(mock(Brand.class));
        Vendor vendor = mock(Vendor.class);
        when(vendor.getVendorName())
            .thenThrow(new ImsBusinessException("[populatePhotoUrl] This item has no photo, Id: {}, SkuNum:{}"));
        when(vendor.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        ArrayList<Vendor> vendorList = new ArrayList<>();
        vendorList.add(vendor);
        when(vendorService.findAll()).thenReturn(vendorList);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        BrandDto buildResult2 = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        when(brandMapper.domainToDto(Mockito.<Brand>any())).thenReturn(buildResult2);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(Mockito.<UUID>any())).thenReturn(null);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, 10));
        verify(brandMapper).domainToDto(isA(Brand.class));
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService, atLeast(1)).findByItemId(isA(UUID.class));
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(isA(UUID.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBackupVendorId();
        verify(item).getBrandId();
        verify(item, atLeast(1)).getId();
        verify(item).getPrimaryVendorId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));
        verify(vendor).getId();
        verify(vendor).getVendorName();

    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber2() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getBrandId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(null);
        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any()))
            .thenThrow(new ImsBusinessException("[populatePhotoUrl] This item has no photo, Id: {}, SkuNum:{}"));
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, 10));
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService).findByItemId(isA(UUID.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBrandId();
        verify(item).getId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));
    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_givenBrandServiceFindByIdReturnNull() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBackupVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getBrandId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getPrimaryVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(vendorService.findAll()).thenReturn(new ArrayList<>());
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(Mockito.<UUID>any())).thenReturn(null);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemCategoryDto actualFindItemCategoryByIdAndVersionNumberResult = itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(itemId, 10);

        // Assert
        Assertions.assertNotNull(actualFindItemCategoryByIdAndVersionNumberResult);

        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService, atLeast(1)).findByItemId(isA(UUID.class));
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(isA(UUID.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBackupVendorId();
        verify(item).getBrandId();
        verify(item, atLeast(1)).getId();
        verify(item).getPrimaryVendorId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));
    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_givenItemGetBrandIdReturnNull() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBackupVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getBrandId()).thenReturn(null);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getPrimaryVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(vendorService.findAll()).thenReturn(new ArrayList<>());
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(Mockito.<UUID>any())).thenReturn(null);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemCategoryDto actualFindItemCategoryByIdAndVersionNumberResult = itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(itemId, 10);

        // Assert
        Assertions.assertNotNull(actualFindItemCategoryByIdAndVersionNumberResult);

        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService, atLeast(1)).findByItemId(isA(UUID.class));
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(isA(UUID.class));
        verify(item).getBackupVendorId();
        verify(item).getBrandId();
        verify(item, atLeast(1)).getId();
        verify(item).getPrimaryVendorId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));


    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_givenItemService() {
        // Arrange
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any()))
            .thenThrow(new ImsBusinessException("Item not found for id: {}"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, 10));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));
    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_givenItemServiceFindByIdReturnNull() {
        // Arrange
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(null);
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, 10));
        verify(itemService).findById(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));
    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_thenReturnBackupVendorNameIsNull() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBackupVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getBrandId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getPrimaryVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(mock(Brand.class));
        when(vendorService.findAll()).thenReturn(new ArrayList<>());
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        BrandDto buildResult2 = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        when(brandMapper.domainToDto(Mockito.<Brand>any())).thenReturn(buildResult2);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(Mockito.<UUID>any())).thenReturn(null);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemCategoryDto actualFindItemCategoryByIdAndVersionNumberResult = itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(itemId, 10);

        // Assert
        Assertions.assertNotNull(actualFindItemCategoryByIdAndVersionNumberResult);
        verify(brandMapper).domainToDto(isA(Brand.class));
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService, atLeast(1)).findByItemId(isA(UUID.class));
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(isA(UUID.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBackupVendorId();
        verify(item).getBrandId();
        verify(item, atLeast(1)).getId();
        verify(item).getPrimaryVendorId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));

    }

    @Test
    void testFindItemCategoryByIdAndVersionNumber_thenReturnBackupVendorNameIsVendorName() {
        // Arrange
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(item.getBackupVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getBrandId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(item.getPrimaryVendorId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(brandService.findById(Mockito.<UUID>any())).thenReturn(mock(Brand.class));
        Vendor vendor = mock(Vendor.class);
        when(vendor.getVendorName()).thenReturn("Vendor Name");
        when(vendor.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        ArrayList<Vendor> vendorList = new ArrayList<>();
        vendorList.add(vendor);
        when(vendorService.findAll()).thenReturn(vendorList);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(null);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemMapper.domainToDto(Mockito.<Item>any())).thenReturn(buildResult);
        BrandDto buildResult2 = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        when(brandMapper.domainToDto(Mockito.<Brand>any())).thenReturn(buildResult2);
        when(categoryApplicationService.getCategoryTreeByLeafCategoryId(Mockito.<UUID>any())).thenReturn(null);
        when(vendorItemQueryApplicationService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());
        when(itemVersionService.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemCategoryDto actualFindItemCategoryByIdAndVersionNumberResult = itemQueryApplicationService
            .findItemCategoryByIdAndVersionNumber(itemId, 10);

        // Assert
        Assertions.assertNotNull(actualFindItemCategoryByIdAndVersionNumberResult);

        verify(brandMapper).domainToDto(isA(Brand.class));
        verify(itemMapper).domainToDto(isA(Item.class));
        verify(vendorItemQueryApplicationService, atLeast(1)).findByItemId(isA(UUID.class));
        verify(categoryApplicationService).getCategoryTreeByLeafCategoryId(isA(UUID.class));
        verify(brandService).findById(isA(UUID.class));
        verify(item).getBackupVendorId();
        verify(item).getBrandId();
        verify(item, atLeast(1)).getId();
        verify(item).getPrimaryVendorId();
        verify(itemService).findById(isA(UUID.class));
        verify(itemPromoPriceService).findByItemId(isA(UUID.class));
        verify(itemRegPriceService).findByItemId(isA(UUID.class));
        verify(itemVersionService).findByItemIdAndVersion(isA(UUID.class), eq(10));

    }


    @Test
    void findByCategoryId_shouldReturnItemDtos_whenItemsExist() {

        UUID categoryId = UUID.randomUUID();
        Item item1 = Item.builder().id(UUID.randomUUID()).build();
        Item item2 = Item.builder().id(UUID.randomUUID()).build();

        ItemDto itemDto1 = ItemDto.builder().id(item1.getId()).build();

        ItemDto itemDto2 = ItemDto.builder().id(item2.getId()).build();

        // Given
        List<Item> items = List.of(item1, item2);
        when(itemRepository.findByCategoryId(categoryId)).thenReturn(items);
        when(itemMapper.domainToDto(any(Item.class))).thenAnswer(invocation -> {
            Item item = invocation.getArgument(0);
            if (item.getId().equals(item1.getId())) {
                return itemDto1;
            }
            if (item.getId().equals(item2.getId())) {
                return itemDto2;
            }
            return null;
        });

        // When
        List<ItemDto> result = itemQueryApplicationService.findByCategoryId(categoryId);

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(itemDto1));
        assertTrue(result.contains(itemDto2));
        verify(itemRepository).findByCategoryId(categoryId);
    }

    @Test
    void findByCategoryId_shouldReturnEmptyList_whenNoItemsExist() {
        UUID categoryId = UUID.randomUUID();

        // Given
        when(itemRepository.findByCategoryId(categoryId)).thenReturn(Collections.emptyList());

        // When
        List<ItemDto> result = itemQueryApplicationService.findByCategoryId(categoryId);

        // Then
        assertTrue(result.isEmpty());
        verify(itemRepository).findByCategoryId(categoryId);
        verify(itemMapper, never()).domainToDto(any());
    }

    @Test
    void findByCategoryId_shouldReturnDistinctItems_whenDuplicatesExist() {
        UUID categoryId = UUID.randomUUID();
        Item item1 = Item.builder().id(UUID.randomUUID()).build();
        Item item2 = Item.builder().id(UUID.randomUUID()).build();

        ItemDto itemDto1 = ItemDto.builder().id(item1.getId()).build();

        ItemDto itemDto2 = ItemDto.builder().id(item2.getId()).build();

        // Given
        List<Item> items = List.of(item1, item1, item2);
        when(itemRepository.findByCategoryId(categoryId)).thenReturn(items);
        when(itemMapper.domainToDto(any(Item.class))).thenAnswer(invocation -> {
            Item item = invocation.getArgument(0);
            if (item.getId().equals(item1.getId())) {
                return itemDto1;
            }
            if (item.getId().equals(item2.getId())) {
                return itemDto2;
            }
            return null;
        });

        // When
        List<ItemDto> result = itemQueryApplicationService.findByCategoryId(categoryId);

        // Then
        assertEquals(2, result.size());
        verify(itemRepository).findByCategoryId(categoryId);
        verify(itemMapper, times(3)).domainToDto(item1);
    }

    @Test
    void testFindByUpcNumber_whenUpcExists_thenReturnItemDtoList() {
        // Arrange
        String upcNumber = "123456789012";
        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();

        Item item1 = mock(Item.class);
        when(item1.getId()).thenReturn(itemId1);
        when(item1.getSkuNumber()).thenReturn("SKU001");

        Item item2 = mock(Item.class);
        when(item2.getId()).thenReturn(itemId2);
        when(item2.getSkuNumber()).thenReturn("SKU002");

        List<Item> items = Arrays.asList(item1, item2);

        ItemDto itemDto1 = ItemDto.builder()
            .id(itemId1)
            .skuNumber("SKU001")
            .build();
        ItemDto itemDto2 = ItemDto.builder()
            .id(itemId2)
            .skuNumber("SKU002")
            .build();

        when(itemRepository.findByUpcNumber(upcNumber)).thenReturn(items);
        when(itemMapper.domainToDto(item1)).thenReturn(itemDto1);
        when(itemMapper.domainToDto(item2)).thenReturn(itemDto2);

        // Act
        List<ItemDto> result = itemQueryApplicationService.findByUpcNumber(upcNumber);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(itemId1, result.get(0).getId());
        assertEquals(itemId2, result.get(1).getId());
        assertEquals("SKU001", result.get(0).getSkuNumber());
        assertEquals("SKU002", result.get(1).getSkuNumber());

        verify(itemRepository).findByUpcNumber(upcNumber);
        verify(itemMapper).domainToDto(item1);
        verify(itemMapper).domainToDto(item2);
    }

    @Test
    void testFindByUpcNumber_whenUpcNotExists_thenReturnEmptyList() {
        // Arrange
        String upcNumber = "999999999999";
        when(itemRepository.findByUpcNumber(upcNumber)).thenReturn(new ArrayList<>());

        // Act
        List<ItemDto> result = itemQueryApplicationService.findByUpcNumber(upcNumber);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(itemRepository).findByUpcNumber(upcNumber);
        verifyNoInteractions(itemMapper);
    }
}
