package com.mercaso.ims.application.service.impl;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.CreateItemVersionCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemDto.ItemDtoBuilder;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto.ItemRegPriceDtoBuilder;
import com.mercaso.ims.application.dto.ItemVersionDto;
import com.mercaso.ims.application.dto.ItemVersionDto.ItemVersionDtoBuilder;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.VendorItemDto.VendorItemDtoBuilder;
import com.mercaso.ims.application.mapper.itemversion.ItemVersionDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.service.ItemVersionService;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.utils.item.ItemUtil;
import jakarta.persistence.EntityManager;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemVersionApplicationServiceImpl.class})
class ItemVersionApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private ItemVersionApplicationServiceImpl itemVersionApplicationServiceImpl;

    @MockBean
    private ItemVersionService itemVersionService;

    @MockBean
    private ItemVersionDtoApplicationMapper itemVersionDtoApplicationMapper;

    @MockBean
    private ItemQueryApplicationService itemQueryApplicationService;

    @MockBean
    private EntityManager entityManager;

    @Test
    void testFindLastVersion_givenArrayListAddItemVersion_thenReturnSkuNumberIs42() {
        // Arrange
        ArrayList<ItemVersion> itemVersionList = new ArrayList<>();
        itemVersionList.add(mock(ItemVersion.class));
        when(itemVersionService.findByItemId(Mockito.<UUID>any())).thenReturn(itemVersionList);
        ItemVersionDtoBuilder builderResult = ItemVersionDto.builder();
        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult2.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        ItemVersionDto buildResult = builderResult.itemDto(itemDto)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .skuNumber("42")
            .versionNumber(10)
            .build();
        when(itemVersionDtoApplicationMapper.domainToDto(Mockito.<ItemVersion>any())).thenReturn(buildResult);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemVersionDto actualFindLastVersionResult = itemVersionApplicationServiceImpl.findLastVersion(itemId);

        // Assert
        verify(itemVersionDtoApplicationMapper).domainToDto(isA(ItemVersion.class));
        verify(itemVersionService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindLastVersionResult.getSkuNumber());
        assertEquals(10, actualFindLastVersionResult.getVersionNumber());
        assertSame(itemId, actualFindLastVersionResult.getItemId());
    }

    @Test
    void testFindLastVersion_givenItemVersionDtoApplicationMapper_thenReturnNull() {
        // Arrange
        when(itemVersionService.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        ItemVersionDto actualFindLastVersionResult = itemVersionApplicationServiceImpl
            .findLastVersion(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemVersionService).findByItemId(isA(UUID.class));
        assertNull(actualFindLastVersionResult);
    }


    @Test
    void testFindLastVersion_thenCallsGetVersionNumber() {
        // Arrange
        ItemVersion itemVersion = mock(ItemVersion.class);
        when(itemVersion.getVersionNumber()).thenReturn(10);
        ItemVersion itemVersion2 = mock(ItemVersion.class);
        when(itemVersion2.getVersionNumber()).thenReturn(10);
        ItemVersion itemVersion3 = mock(ItemVersion.class);
        when(itemVersion3.getVersionNumber()).thenReturn(10);

        ArrayList<ItemVersion> itemVersionList = new ArrayList<>();
        itemVersionList.add(itemVersion3);
        itemVersionList.add(itemVersion2);
        itemVersionList.add(itemVersion);
        when(itemVersionService.findByItemId(Mockito.<UUID>any())).thenReturn(itemVersionList);
        ItemVersionDtoBuilder builderResult = ItemVersionDto.builder();
        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult2.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        ItemVersionDto buildResult = builderResult.itemDto(itemDto)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .skuNumber("42")
            .versionNumber(10)
            .build();
        when(itemVersionDtoApplicationMapper.domainToDto(Mockito.<ItemVersion>any())).thenReturn(buildResult);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemVersionDto actualFindLastVersionResult = itemVersionApplicationServiceImpl.findLastVersion(itemId);

        // Assert
        verify(itemVersionDtoApplicationMapper).domainToDto(isA(ItemVersion.class));
        verify(itemVersion2).getVersionNumber();
        verify(itemVersion).getVersionNumber();
        verify(itemVersion3, atLeast(1)).getVersionNumber();
        verify(itemVersionService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindLastVersionResult.getSkuNumber());
        assertEquals(10, actualFindLastVersionResult.getVersionNumber());
        assertSame(itemId, actualFindLastVersionResult.getItemId());
    }

    @Test
    void testFindLastVersion_thenCallsGetVersionNumber2() {
        // Arrange
        ItemVersion itemVersion = mock(ItemVersion.class);
        when(itemVersion.getVersionNumber()).thenReturn(10);
        ItemVersion itemVersion2 = mock(ItemVersion.class);
        when(itemVersion2.getVersionNumber()).thenReturn(10);

        ArrayList<ItemVersion> itemVersionList = new ArrayList<>();
        itemVersionList.add(itemVersion2);
        itemVersionList.add(itemVersion);
        when(itemVersionService.findByItemId(Mockito.<UUID>any())).thenReturn(itemVersionList);
        ItemVersionDtoBuilder builderResult = ItemVersionDto.builder();
        ItemDtoBuilder backupVendorIdResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult = availabilityResult.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult = backupCostResult
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult = backupCostFreshnessTimeResult.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult = costResult
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult = costFreshnessTimeResult.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult = itemSkuNumberResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto backupVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder bodyHtmlResult = backupVendorIdResult.backupVendorItem(backupVendorItem)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = CategoryDto.builder()
            .ancestorCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .ancestorName("Ancestor Name")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name")
            .depth(2)
            .sortOrder(1)
            .build();
        ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDtoBuilder builderResult2 = ItemRegPriceDto.builder();
        ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult2.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemPriceGroupName("Item Price Group Name")
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult.regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .lastVersionNumber(10)
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDtoBuilder availabilityResult2 = VendorItemDto.builder().aisle("Aisle").availability(true);
        VendorItemDtoBuilder backupCostResult2 = availabilityResult2.backupCost(new BigDecimal("2.3"));
        VendorItemDtoBuilder backupCostFreshnessTimeResult2 = backupCostResult2
            .backupCostFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder costResult2 = backupCostFreshnessTimeResult2.cost(new BigDecimal("2.3"));
        VendorItemDtoBuilder costFreshnessTimeResult2 = costResult2
            .costFreshnessTime(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        VendorItemDtoBuilder itemSkuNumberResult2 = costFreshnessTimeResult2.highestCost(new BigDecimal("2.3"))
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemSkuNumber("42");
        VendorItemDtoBuilder noteResult2 = itemSkuNumberResult2.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult2
            .statusChangeReason("Just cause")
            .vendorFinaleId("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorItemType("Vendor Item Type")
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto itemDto = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        ItemVersionDto buildResult = builderResult.itemDto(itemDto)
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .skuNumber("42")
            .versionNumber(10)
            .build();
        when(itemVersionDtoApplicationMapper.domainToDto(Mockito.<ItemVersion>any())).thenReturn(buildResult);
        UUID itemId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        ItemVersionDto actualFindLastVersionResult = itemVersionApplicationServiceImpl.findLastVersion(itemId);

        // Assert
        verify(itemVersionDtoApplicationMapper).domainToDto(isA(ItemVersion.class));
        verify(itemVersion2).getVersionNumber();
        verify(itemVersion).getVersionNumber();
        verify(itemVersionService).findByItemId(isA(UUID.class));
        assertEquals("42", actualFindLastVersionResult.getSkuNumber());
        assertEquals(10, actualFindLastVersionResult.getVersionNumber());
        assertSame(itemId, actualFindLastVersionResult.getItemId());
    }

    @Test
    void testSave() throws InterruptedException {
        // Arrange
        Item item = ItemUtil.buildItem();
        UUID itemId = item.getId();
        when(itemQueryApplicationService.findById(any()))
            .thenReturn(ItemDto.builder().id(itemId).lastVersionNumber(1).build());

        when(itemVersionService.save(any()))
            .thenAnswer(invocation -> invocation.getArgument(0)); // return the same ItemVersion

        when(itemVersionDtoApplicationMapper.domainToDto(any()))
            .thenReturn(ItemVersionDto.builder().versionNumber(1).build());

        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    CreateItemVersionCommand cmd = CreateItemVersionCommand.builder().itemId(itemId).build();
                    ItemVersionDto result = itemVersionApplicationServiceImpl.save(cmd);
                    assertNotNull(result);
                } catch (Exception e) {
                    // Handle/log expected constraint exceptions if needed
                    System.out.println("Exception occurred: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();
    }
}
