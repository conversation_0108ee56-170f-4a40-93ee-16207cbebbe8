package com.mercaso.ims.application.searchservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.application.query.CategoryQuery;
import com.mercaso.ims.infrastructure.repository.category.jpa.CustomizedCategoryJpaDao;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {CategorySearchApplicationServiceImpl.class})
class CategorySearchApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private CategorySearchApplicationServiceImpl categorySearchApplicationService;

    @MockBean
    private CustomizedCategoryJpaDao customizedCategoryJpaDao;

    @Test
    void testSearchDepartmentCategoryTrees_EmptyResult() {
        // Arrange
        CategoryQuery query = CategoryQuery.builder().build();
        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(Collections.emptyList());

        // Act
        List<CategoryTreeDto> result = categorySearchApplicationService.searchDepartmentCategoryTrees(query);

        // Assert
        assertTrue(result.isEmpty());
        verify(customizedCategoryJpaDao).getCategories(query);
    }

    @Test
    void testSearchDepartmentCategoryTrees_WithResults() {
        // Arrange
        CategoryQuery query = CategoryQuery.builder().build();
        List<CategoryDto> categories = new ArrayList<>();

        // Create test category
        CategoryDto category = CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category")
            .depth(1)
            .sortOrder(1)
            .build();
        categories.add(category);

        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(categories);

        // Act
        List<CategoryTreeDto> result = categorySearchApplicationService.searchDepartmentCategoryTrees(query);

        // Assert
        assertEquals(1, result.size());
        CategoryTreeDto treeDto = result.getFirst();
        assertEquals(category.getCategoryId(), treeDto.getCategoryId());
        assertEquals(category.getCategoryName(), treeDto.getCategoryName());
        assertEquals(category.getDepth(), treeDto.getDepth());
        assertEquals(category.getSortOrder(), treeDto.getSortOrder());
        assertTrue(treeDto.getChildCategories().isEmpty());
        assertEquals(0L, treeDto.getItemCountsDto().getActiveCount());
        assertEquals(0L, treeDto.getItemCountsDto().getDraftCount());
        assertEquals(0L, treeDto.getItemCountsDto().getArchivedCount());
        verify(customizedCategoryJpaDao).getCategories(query);
    }

    @Test
    void testSearchDepartmentCategoryTrees_MultipleResults() {
        // Arrange
        CategoryQuery query = CategoryQuery.builder().build();
        List<CategoryDto> categories = new ArrayList<>();

        // Create test categories
        CategoryDto category1 = CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category 1")
            .depth(1)
            .sortOrder(1)
            .build();

        CategoryDto category2 = CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("Test Category 2")
            .depth(2)
            .sortOrder(2)
            .build();

        categories.add(category1);
        categories.add(category2);

        when(customizedCategoryJpaDao.getCategories(any())).thenReturn(categories);

        // Act
        List<CategoryTreeDto> result = categorySearchApplicationService.searchDepartmentCategoryTrees(query);

        // Assert
        assertEquals(2, result.size());

        // Verify first category
        CategoryTreeDto treeDto1 = result.getFirst();
        assertEquals(category1.getCategoryId(), treeDto1.getCategoryId());
        assertEquals(category1.getCategoryName(), treeDto1.getCategoryName());
        assertEquals(category1.getDepth(), treeDto1.getDepth());
        assertEquals(category1.getSortOrder(), treeDto1.getSortOrder());

        // Verify second category
        CategoryTreeDto treeDto2 = result.get(1);
        assertEquals(category2.getCategoryId(), treeDto2.getCategoryId());
        assertEquals(category2.getCategoryName(), treeDto2.getCategoryName());
        assertEquals(category2.getDepth(), treeDto2.getDepth());
        assertEquals(category2.getSortOrder(), treeDto2.getSortOrder());

        verify(customizedCategoryJpaDao).getCategories(query);
    }
}