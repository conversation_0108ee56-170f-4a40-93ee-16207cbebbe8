package com.mercaso.ims.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.io.UnsupportedEncodingException;
import java.nio.file.Paths;
import org.apache.groovy.nio.runtime.WritablePath;
import org.junit.jupiter.api.Test;

class FileUtilTest {

    @Test
    void testReadFileToByteArray() {
        // Arrange, Act and Assert
        assertNull(FileUtil.readFileToByteArray(Paths.get(System.getProperty("java.io.tmpdir"), "test.txt").toFile()));
    }

    @Test
    void testReadFileToByteArray2() {
        // Arrange
        System.getProperty("java.io.tmpdir");
        WritablePath writablePath = mock(WritablePath.class);
        when(writablePath.toFile()).thenReturn(Paths.get(System.getProperty("java.io.tmpdir"), "test.txt").toFile());

        // Act
        byte[] actualReadFileToByteArrayResult = FileUtil.readFileToByteArray(writablePath.toFile());

        // Assert
        verify(writablePath).toFile();
        assertNull(actualReadFileToByteArrayResult);
    }

    @Test
    void testGetFileNameWithoutExtension() {
        // Arrange, Act and Assert
        assertEquals("foo", FileUtil.getFileNameWithoutExtension("/directory/foo.txt"));
        assertEquals("File Path", FileUtil.getFileNameWithoutExtension("File Path"));
    }

    @Test
    void testGetFileExtension() throws UnsupportedEncodingException {
        // Arrange, Act and Assert
        assertEquals(".txt", FileUtil.getFileExtension("AXAXAXAX".getBytes("UTF-8")));
        assertEquals(".txt", FileUtil.getFileExtension("XXAXAXAX".getBytes("UTF-8")));
    }

    @Test
    void testIsImageFile() throws UnsupportedEncodingException {
        // Arrange, Act and Assert
        assertFalse(FileUtil.isImageFile("AXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile(new byte[]{-1, 'X', 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertFalse(FileUtil.isImageFile(new byte[]{-119, 'X', 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertFalse(FileUtil.isImageFile("GXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile("BXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile("IXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile("MXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile(null));
        assertFalse(FileUtil.isImageFile(new byte[]{}));
        assertFalse(FileUtil.isImageFile("RXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isImageFile(new byte[]{-1, -40, 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertFalse(FileUtil.isImageFile(new byte[]{-119, 'P', 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertFalse(FileUtil.isImageFile("GIAXAXAX".getBytes("UTF-8")));
    }

    @Test
    void testIsJpegFile() throws UnsupportedEncodingException {
        // Arrange, Act and Assert
        assertFalse(FileUtil.isJpegFile("AXAXAXAX".getBytes("UTF-8")));
        assertFalse(FileUtil.isJpegFile(new byte[]{-1, 'X', 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertFalse(FileUtil.isJpegFile(new byte[]{-1, -40, 'A', 'X', 'A', 'X', 'A', 'X'}));
        assertTrue(FileUtil.isJpegFile(new byte[]{-1, -40, -1, 'X', 'A', 'X', 'A', 'X'}));
    }

    @Test
    void testDownloadFile() {
        assertThrows(ImsBusinessException.class, () -> FileUtil.downloadFile("https://example.org/example"));
    }


}
