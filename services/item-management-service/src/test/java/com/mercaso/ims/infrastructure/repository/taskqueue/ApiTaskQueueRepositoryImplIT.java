package com.mercaso.ims.infrastructure.repository.taskqueue;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.repository.ApiTaskQueueRepository;
import com.mercaso.ims.utils.taskqueue.ApiTaskQueueTestUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for ApiTaskQueueRepositoryImpl
 */
class ApiTaskQueueRepositoryImplIT extends AbstractIT {

    @Autowired
    private ApiTaskQueueRepository apiTaskQueueRepository;

    @Test
    void testSave_shouldSaveAndReturnApiTaskQueue() {
        // Arrange
        ApiTaskQueue apiTaskQueue = ApiTaskQueueTestUtil.buildApiTaskQueue();

        // Act
        ApiTaskQueue result = apiTaskQueueRepository.save(apiTaskQueue);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(apiTaskQueue.getTaskType(), result.getTaskType());
        assertEquals(apiTaskQueue.getApiEndpoint(), result.getApiEndpoint());
        assertEquals(apiTaskQueue.getHttpMethod(), result.getHttpMethod());
        assertEquals(apiTaskQueue.getRequestPayload(), result.getRequestPayload());
        assertEquals(apiTaskQueue.getStatus(), result.getStatus());
        assertEquals(apiTaskQueue.getPriority(), result.getPriority());
        assertEquals(apiTaskQueue.getMaxRetryCount(), result.getMaxRetryCount());
        assertEquals(apiTaskQueue.getCurrentRetryCount(), result.getCurrentRetryCount());
    }

    @Test
    void testFindById_shouldReturnTask() {
        // Arrange
        ApiTaskQueue apiTaskQueue = ApiTaskQueueTestUtil.buildApiTaskQueue();
        ApiTaskQueue saved = apiTaskQueueRepository.save(apiTaskQueue);

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueRepository.findById(saved.getId());

        // Assert
        assertTrue(result.isPresent());
        assertEquals(saved.getId(), result.get().getId());
        assertEquals(saved.getTaskType(), result.get().getTaskType());
        assertEquals(saved.getStatus(), result.get().getStatus());
    }

    @Test
    void testFindById_shouldReturnEmptyWhenNotFound() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueRepository.findById(nonExistentId);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void testFindExecutableTasks_shouldReturnPendingAndRetryTasks() {
        // Arrange
        ApiTaskQueue pendingTask = ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING);
        ApiTaskQueue retryTask = ApiTaskQueueTestUtil.buildRetryApiTaskQueue();
        ApiTaskQueue processingTask = ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PROCESSING);
        ApiTaskQueue completedTask = ApiTaskQueueTestUtil.buildCompletedApiTaskQueue();
        
        apiTaskQueueRepository.save(pendingTask);
        apiTaskQueueRepository.save(retryTask);
        apiTaskQueueRepository.save(processingTask);
        apiTaskQueueRepository.save(completedTask);

        List<TaskStatus> statuses = List.of(TaskStatus.PENDING, TaskStatus.RETRY);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueRepository.findExecutableTasks(statuses, 10);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertTrue(result.stream().anyMatch(task -> task.getStatus() == TaskStatus.PENDING));
        assertTrue(result.stream().anyMatch(task -> task.getStatus() == TaskStatus.RETRY));
        assertFalse(result.stream().anyMatch(task -> task.getStatus() == TaskStatus.PROCESSING));
        assertFalse(result.stream().anyMatch(task -> task.getStatus() == TaskStatus.COMPLETED));
    }

    @Test
    void testFindExecutableTasks_shouldRespectLimit() {
        // Arrange
        for (int i = 0; i < 5; i++) {
            apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING));
        }

        List<TaskStatus> statuses = List.of(TaskStatus.PENDING);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueRepository.findExecutableTasks(statuses, 3);

        // Assert
        assertNotNull(result);
    }

    @Test
    void testFindByStatus_shouldReturnTasksWithSpecificStatus() {
        // Arrange
        ApiTaskQueue pendingTask1 = ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING);
        ApiTaskQueue pendingTask2 = ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING);
        ApiTaskQueue processingTask = ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PROCESSING);
        
        apiTaskQueueRepository.save(pendingTask1);
        apiTaskQueueRepository.save(pendingTask2);
        apiTaskQueueRepository.save(processingTask);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueRepository.findByStatus(TaskStatus.PENDING);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertTrue(result.stream().allMatch(task -> task.getStatus() == TaskStatus.PENDING));
    }

    @Test
    void testFindByTaskTypeAndStatus_shouldReturnMatchingTasks() {
        // Arrange
        String taskType = "FINALE_UPDATE_STOCK";
        ApiTaskQueue matchingTask1 = ApiTaskQueueTestUtil.buildApiTaskQueueWithType(taskType);
        ApiTaskQueue matchingTask2 = ApiTaskQueueTestUtil.buildApiTaskQueueWithType(taskType);
        ApiTaskQueue differentTypeTask = ApiTaskQueueTestUtil.buildApiTaskQueueWithType("DIFFERENT_TYPE");
        
        apiTaskQueueRepository.save(matchingTask1);
        apiTaskQueueRepository.save(matchingTask2);
        apiTaskQueueRepository.save(differentTypeTask);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueRepository.findByTaskTypeAndStatus(taskType, TaskStatus.PENDING);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() >= 2);
        assertTrue(result.stream().allMatch(task -> 
            task.getTaskType().equals(taskType) && task.getStatus() == TaskStatus.PENDING));
    }

    @Test
    void testCountByStatus_shouldReturnCorrectCount() {
        // Arrange
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PENDING));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueue(TaskStatus.PROCESSING));

        // Act
        long pendingCount = apiTaskQueueRepository.countByStatus(TaskStatus.PENDING);
        long processingCount = apiTaskQueueRepository.countByStatus(TaskStatus.PROCESSING);

        // Assert
        assertTrue(pendingCount >= 2);
        assertTrue(processingCount >= 1);
    }

    @Test
    void testCountByTaskTypeAndStatus_shouldReturnCorrectCount() {
        // Arrange
        String taskType = "TEST_TASK_TYPE";
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType(taskType));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType(taskType));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType("OTHER_TYPE"));

        // Act
        long count = apiTaskQueueRepository.countByTaskTypeAndStatus(taskType, TaskStatus.PENDING);

        // Assert
        assertTrue(count >= 2);
    }

    @Test
    void testFindStuckProcessingTasks_shouldReturnTasksStartedBeforeCutoff() {
        // Arrange
        Instant cutoffTime = Instant.now().minusSeconds(3600); // 1 hour ago
        Instant oldStartTime = Instant.now().minusSeconds(7200); // 2 hours ago
        Instant recentStartTime = Instant.now().minusSeconds(1800); // 30 minutes ago

        ApiTaskQueue stuckTask = ApiTaskQueueTestUtil.buildProcessingApiTaskQueue(oldStartTime);
        ApiTaskQueue recentTask = ApiTaskQueueTestUtil.buildProcessingApiTaskQueue(recentStartTime);
        
        apiTaskQueueRepository.save(stuckTask);
        apiTaskQueueRepository.save(recentTask);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueRepository.findStuckProcessingTasks(cutoffTime);

        // Assert
        assertNotNull(result);
        assertTrue(result.stream().anyMatch(task -> 
            task.getStatus() == TaskStatus.PROCESSING && 
            task.getStartedAt().isBefore(cutoffTime)));
    }

    @Test
    void testCleanupCompletedTasks_shouldMarkOldTasksAsDeleted() {
        // Arrange
        Instant cutoffTime = Instant.now().minusSeconds(3600); // 1 hour ago
        
        // Create an old completed task
        ApiTaskQueue oldCompletedTask = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .responsePayload("{\"result\": \"success\"}")
                .status(TaskStatus.COMPLETED)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now().minusSeconds(7200))
                .completedAt(Instant.now().minusSeconds(7200)) // 2 hours ago
                .createdAt(Instant.now().minusSeconds(7200))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now().minusSeconds(7200))
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
        
        // Create a recent completed task
        ApiTaskQueue recentCompletedTask = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .responsePayload("{\"result\": \"success\"}")
                .status(TaskStatus.COMPLETED)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now().minusSeconds(1800))
                .completedAt(Instant.now().minusSeconds(1800)) // 30 minutes ago
                .createdAt(Instant.now().minusSeconds(1800))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now().minusSeconds(1800))
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
        
        apiTaskQueueRepository.save(oldCompletedTask);
        apiTaskQueueRepository.save(recentCompletedTask);

        // Act
        int cleanedCount = apiTaskQueueRepository.cleanupCompletedTasks(cutoffTime);

        // Assert
        assertTrue(cleanedCount >= 1);
    }

    @Test
    void testDelete_shouldRemoveTask() {
        // Arrange
        ApiTaskQueue apiTaskQueue = ApiTaskQueueTestUtil.buildApiTaskQueue();
        ApiTaskQueue saved = apiTaskQueueRepository.save(apiTaskQueue);

        // Act
        apiTaskQueueRepository.delete(saved);

        // Assert
        Optional<ApiTaskQueue> result = apiTaskQueueRepository.findById(saved.getId());
        assertFalse(result.isPresent());
    }

    @Test
    void testDeleteById_shouldRemoveTask() {
        // Arrange
        ApiTaskQueue apiTaskQueue = ApiTaskQueueTestUtil.buildApiTaskQueue();
        ApiTaskQueue saved = apiTaskQueueRepository.save(apiTaskQueue);

        // Act
        apiTaskQueueRepository.deleteById(saved.getId());

        // Assert
        Optional<ApiTaskQueue> result = apiTaskQueueRepository.findById(saved.getId());
        assertFalse(result.isPresent());
    }

    @Test
    void testFindDistinctTaskTypesByStatus_shouldReturnUniqueTaskTypes() {
        // Arrange
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType("TYPE_A"));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType("TYPE_B"));
        apiTaskQueueRepository.save(ApiTaskQueueTestUtil.buildApiTaskQueueWithType("TYPE_A")); // Duplicate
        
        ApiTaskQueue processingTask = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("TYPE_C")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.PROCESSING)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now())
                .createdAt(Instant.now())
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
        apiTaskQueueRepository.save(processingTask);

        List<TaskStatus> statuses = List.of(TaskStatus.PENDING);

        // Act
        List<String> result = apiTaskQueueRepository.findDistinctTaskTypesByStatus(statuses);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("TYPE_A"));
        assertTrue(result.contains("TYPE_B"));
        assertFalse(result.contains("TYPE_C")); // Different status
        // Check uniqueness - TYPE_A should appear only once
        assertEquals(1, result.stream().filter(type -> type.equals("TYPE_A")).count());
    }

    @Test
    void testUpdate_shouldUpdateExistingTask() {
        // Arrange
        ApiTaskQueue saved = ApiTaskQueueTestUtil.buildApiTaskQueue();

        // Modify the task
        saved.setStatus(TaskStatus.PROCESSING);
        saved.setStartedAt(Instant.now());

        // Act
        ApiTaskQueue result = apiTaskQueueRepository.save(saved);

        // Assert
        assertNotNull(result);
        assertEquals(TaskStatus.PROCESSING, result.getStatus());
        assertNotNull(result.getStartedAt());
    }
} 