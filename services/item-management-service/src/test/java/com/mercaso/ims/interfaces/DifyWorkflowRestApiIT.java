package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingResultDto;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.domain.difyworkflowsrecord.service.DifyWorkflowRecordService;
import com.mercaso.ims.infrastructure.external.dify.DifyApiClient;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import java.io.IOException;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.boot.test.mock.mockito.MockBean;

@Execution(ExecutionMode.SAME_THREAD)
class DifyWorkflowRestApiIT extends AbstractIT {

    @MockBean
    private DifyApiClient difyApiClient;

    @MockBean
    private DifyWorkflowRecordService difyWorkflowRecordService;

    @Test
    void shouldSuccessWhenGetRecommendedCategories() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " smartphone product description";
        UUID validCategoryId = UUID.randomUUID();
        UUID validDepartmentId = UUID.randomUUID();
        UUID validCategoryUuid = UUID.randomUUID();
        UUID validSubCategoryId = UUID.randomUUID();

        String mockResultJson = """
            {
                "output": {
                    "finalResult": {
                        "matchedCategoryFullPath": "Electronics > Mobile Phones > Smartphones > Android Phones",
                        "matchedDepartment": "Electronics",
                        "matchedDepartmentId": "%s",
                        "matchedCategory": "Mobile Phones",
                        "matchedCategoryId": "%s",
                        "matchedSubCategory": "Smartphones",
                        "matchedSubCategoryId": "%s",
                        "matchedClazz": "Android Phones",
                        "matchedClazzId": "%s",
                        "matchedCredibility": "0.95",
                        "recommendedCategory": "Electronics > Mobile Phones > Smartphones > Android Phones",
                        "recommendedCredibility": "0.95"
                    },
                    "analysis": "Based on the product description, this item best matches the Android Phones category.",
                    "categoryOptimizationSuggestions": "Consider adding more specific brand information for better categorization."
                }
            }
            """.formatted(validDepartmentId, validCategoryUuid, validSubCategoryId, validCategoryId);

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId(UUID.randomUUID().toString())
                .totalTokens(150L)
                .totalSteps(5)
                .elapsedTime(2.5)
                .result(mockResultJson)
                .status("succeeded")
                .build();

        when(difyApiClient.callDifyWorkflow("description", description))
                .thenReturn(mockWorkflowResult);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(DifyWorkflowRecord.builder().id(UUID.randomUUID()).build());

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFinalResult());
        assertNotNull(result.getAnalysis());
        assertNotNull(result.getCategoryOptimizationSuggestions());

        // Verify final result details
        DifyCategoryMatchingResultDto finalResult = result.getFinalResult();
        assertEquals("Electronics > Mobile Phones > Smartphones > Android Phones", finalResult.getMatchedCategoryFullPath());
        assertEquals("Electronics", finalResult.getMatchedDepartment());
        assertEquals(validDepartmentId.toString(), finalResult.getMatchedDepartmentId());
        assertEquals("Mobile Phones", finalResult.getMatchedCategory());
        assertEquals(validCategoryUuid.toString(), finalResult.getMatchedCategoryId());
        assertEquals("Smartphones", finalResult.getMatchedSubCategory());
        assertEquals(validSubCategoryId.toString(), finalResult.getMatchedSubCategoryId());
        assertEquals("Android Phones", finalResult.getMatchedClazz());
        assertEquals(validCategoryId.toString(), finalResult.getMatchedClazzId());
        assertEquals("0.95", finalResult.getMatchedCredibility());
        assertEquals("Electronics > Mobile Phones > Smartphones > Android Phones", finalResult.getRecommendedCategory());
        assertEquals("0.95", finalResult.getRecommendedCredibility());

        assertEquals("Based on the product description, this item best matches the Android Phones category.", result.getAnalysis());
        assertEquals("Consider adding more specific brand information for better categorization.", result.getCategoryOptimizationSuggestions());
    }

    @Test
    void shouldReturnEmptyResultWhenDescriptionIsBlank() throws Exception {
        // Given
        String description = "";

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult());
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }

    @Test
    void shouldReturnEmptyResultWhenDifyApiClientThrowsException() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " test product";

        when(difyApiClient.callDifyWorkflow("description", description))
                .thenThrow(new IOException("Dify API connection failed"));
        // Then
        assertThrows(Exception.class, () -> difyWorkflowRestApiUtil.getRecommendedCategories(description));
    }

    @Test
    void shouldSuccessWhenGetRecommendedCategoriesWithMinimalResult() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(5) + " simple product";
        UUID validCategoryId = UUID.randomUUID();
        UUID validDepartmentId = UUID.randomUUID();

        String mockResultJson = """
            {
                "output": {
                    "finalResult": {
                        "matchedCategoryFullPath": "General > Miscellaneous",
                        "matchedDepartment": "General",
                        "matchedDepartmentId": "%s",
                        "matchedClazzId": "%s",
                        "matchedCredibility": "0.60"
                    },
                    "analysis": "Limited information available for categorization."
                }
            }
            """.formatted(validDepartmentId, validCategoryId);

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-456")
                .totalTokens(50L)
                .totalSteps(3)
                .elapsedTime(1.2)
                .result(mockResultJson)
                .status("succeeded")
                .build();

        when(difyApiClient.callDifyWorkflow("description", description))
                .thenReturn(mockWorkflowResult);

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFinalResult());
        assertNotNull(result.getAnalysis());

        DifyCategoryMatchingResultDto finalResult = result.getFinalResult();
        assertEquals("General > Miscellaneous", finalResult.getMatchedCategoryFullPath());
        assertEquals("General", finalResult.getMatchedDepartment());
        assertEquals(validDepartmentId.toString(), finalResult.getMatchedDepartmentId());
        assertEquals(validCategoryId.toString(), finalResult.getMatchedClazzId());
        assertEquals("0.60", finalResult.getMatchedCredibility());

        assertEquals("Limited information available for categorization.", result.getAnalysis());
    }

    @Test
    void shouldReturnEmptyResultWhenWorkflowResultIsInvalidJson() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " test product";

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-789")
                .totalTokens(100L)
                .totalSteps(4)
                .elapsedTime(2.0)
                .result("invalid json content")
                .status("succeeded")
                .build();

        when(difyApiClient.callDifyWorkflow("description", description))
                .thenReturn(mockWorkflowResult);

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult());
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }

    @Test
    void shouldReturnEmptyResultWhenWorkflowResultIsNull() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " test product";

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-null")
                .totalTokens(0L)
                .totalSteps(0)
                .elapsedTime(0.0)
                .result(null)
                .status("failed")
                .build();

        when(difyApiClient.callDifyWorkflow("description", description))
                .thenReturn(mockWorkflowResult);

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult());
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }
}