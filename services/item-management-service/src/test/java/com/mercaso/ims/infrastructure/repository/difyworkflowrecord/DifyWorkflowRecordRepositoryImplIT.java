package com.mercaso.ims.infrastructure.repository.difyworkflowrecord;

import static org.junit.jupiter.api.Assertions.*;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.utils.difyworkflowrecord.DifyWorkflowRecordTestUtil;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class DifyWorkflowRecordRepositoryImplIT extends AbstractIT {

    @Test
    void testSave() {
        String skuNumber = RandomStringUtils.randomAlphanumeric(10);
        DifyWorkflowRecord difyWorkflowRecord = DifyWorkflowRecordTestUtil.buildDifyWorkflowRecord(skuNumber);
        
        DifyWorkflowRecord saved = difyWorkflowRecordRepository.save(difyWorkflowRecord);
        
        assertNotNull(saved);
        assertNotNull(saved.getId());
        assertEquals(difyWorkflowRecord.getTotalTokens(), saved.getTotalTokens());
        assertEquals(difyWorkflowRecord.getTotalSteps(), saved.getTotalSteps());
        assertEquals(difyWorkflowRecord.getElapsedTime(), saved.getElapsedTime());
        assertEquals(difyWorkflowRecord.getResult(), saved.getResult());
    }

    @Test
    void testFindById() {
        String skuNumber = RandomStringUtils.randomAlphanumeric(10);
        DifyWorkflowRecord difyWorkflowRecord = DifyWorkflowRecordTestUtil.buildDifyWorkflowRecord(skuNumber);
        DifyWorkflowRecord saved = difyWorkflowRecordRepository.save(difyWorkflowRecord);
        
        DifyWorkflowRecord found = difyWorkflowRecordRepository.findById(saved.getId());
        
        assertNotNull(found);
        assertEquals(saved.getId(), found.getId());
        assertEquals(saved.getTotalTokens(), found.getTotalTokens());
    }

    @Test
    void testFindByIdNotFound() {
        UUID nonExistentId = UUID.randomUUID();
        
        DifyWorkflowRecord found = difyWorkflowRecordRepository.findById(nonExistentId);
        
        assertNull(found);
    }

    @Test
    void testUpdate() {
        String skuNumber = RandomStringUtils.randomAlphanumeric(10);
        DifyWorkflowRecord difyWorkflowRecord = DifyWorkflowRecordTestUtil.buildDifyWorkflowRecord(skuNumber);
        DifyWorkflowRecord saved = difyWorkflowRecordRepository.save(difyWorkflowRecord);

        DifyWorkflowRecord difyWorkflowRecordRepositoryById = difyWorkflowRecordRepository.findById(saved.getId());


        difyWorkflowRecordRepositoryById.setTotalTokens(2000L);
        difyWorkflowRecordRepositoryById.setTotalSteps(10);
        difyWorkflowRecordRepositoryById.setElapsedTime(3.0);
        difyWorkflowRecordRepositoryById.setResult("Updated results");
        
        DifyWorkflowRecord updated = difyWorkflowRecordRepository.update(difyWorkflowRecordRepositoryById);
        
        assertNotNull(updated);
        assertEquals(saved.getId(), updated.getId());
        assertEquals(2000L, updated.getTotalTokens());
        assertEquals(10, updated.getTotalSteps());
        assertEquals(3.0, updated.getElapsedTime());
        assertEquals("Updated results", updated.getResult());
        assertEquals(saved.getCreatedBy(), updated.getCreatedBy());
    }

    @Test
    void testDeleteById() {
        String skuNumber = RandomStringUtils.randomAlphanumeric(10);
        DifyWorkflowRecord difyWorkflowRecord = DifyWorkflowRecordTestUtil.buildDifyWorkflowRecord(skuNumber);
        DifyWorkflowRecord saved = difyWorkflowRecordRepository.save(difyWorkflowRecord);
        
        DifyWorkflowRecord deleted = difyWorkflowRecordRepository.deleteById(saved.getId());
        
        assertNotNull(deleted);
        assertEquals(saved.getId(), deleted.getId());
        assertNotNull(deleted.getDeletedAt());
        assertNotNull(deleted.getDeletedBy());
    }

    @Test
    void testDeleteByIdNotFound() {
        UUID nonExistentId = UUID.randomUUID();
        
        DifyWorkflowRecord deleted = difyWorkflowRecordRepository.deleteById(nonExistentId);
        
        assertNull(deleted);
    }
} 