package com.mercaso.ims.infrastructure.aspect;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.apitaskprocess.TaskExecutionContext;
import com.mercaso.ims.infrastructure.apitaskprocess.TaskRouter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_ADD_RATE_LIMITS_FOR_FINALE_API;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Integration test for RateLimitedTaskAspect with TaskExecutionContext
 */
class RateLimitedTaskAspectIntegrationTest {

    private TaskRouter taskRouter;
    private FeatureFlagsManager featureFlagsManager;
    private ProceedingJoinPoint joinPoint;
    private RateLimitedTask rateLimitedTask;
    private Signature signature;
    private RateLimitedTaskAspect aspect;

    @BeforeEach
    void setUp() {
        // Manually create mocks to avoid initialization issues
        taskRouter = mock(TaskRouter.class);
        featureFlagsManager = mock(FeatureFlagsManager.class);
        joinPoint = mock(ProceedingJoinPoint.class);
        rateLimitedTask = mock(RateLimitedTask.class);
        signature = mock(Signature.class);

        aspect = new RateLimitedTaskAspect(taskRouter, featureFlagsManager);
        TaskExecutionContext.clearTaskExecution();
    }

    @AfterEach
    void tearDown() {
        TaskExecutionContext.clearTaskExecution();
    }

    private void setupJoinPointMocks() {
        when(joinPoint.getSignature()).thenReturn(signature);
        when(signature.getName()).thenReturn("testMethod");
    }

    @Test
    @DisplayName("Should route to task router when feature is enabled and not in task execution context")
    void shouldRouteToTaskRouter_WhenFeatureEnabledAndNotInTaskContext() throws Throwable {
        // Arrange
        setupJoinPointMocks();
        when(featureFlagsManager.isFeatureOn(IMS_ADD_RATE_LIMITS_FOR_FINALE_API)).thenReturn(true);
        when(taskRouter.routeTask(joinPoint, rateLimitedTask)).thenReturn("task-result");

        // Act
        Object result = aspect.handleRateLimitedTask(joinPoint, rateLimitedTask);

        // Assert
        assertEquals("task-result", result);
        verify(taskRouter).routeTask(joinPoint, rateLimitedTask);
        verify(joinPoint, never()).proceed();
    }

    @Test
    @DisplayName("Should bypass task router when in task execution context")
    void shouldBypassTaskRouter_WhenInTaskExecutionContext() throws Throwable {
        // Arrange
        setupJoinPointMocks();
        when(featureFlagsManager.isFeatureOn(IMS_ADD_RATE_LIMITS_FOR_FINALE_API)).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("direct-result");

        // Mark as in task execution context
        TaskExecutionContext.markAsTaskExecution();

        // Act
        Object result = aspect.handleRateLimitedTask(joinPoint, rateLimitedTask);

        // Assert
        assertEquals("direct-result", result);
        verify(joinPoint).proceed();
        verify(taskRouter, never()).routeTask(any(), any());
    }

    @Test
    @DisplayName("Should proceed directly when feature is disabled")
    void shouldProceedDirectly_WhenFeatureDisabled() throws Throwable {
        // Arrange
        setupJoinPointMocks();
        when(featureFlagsManager.isFeatureOn(IMS_ADD_RATE_LIMITS_FOR_FINALE_API)).thenReturn(false);
        when(joinPoint.proceed()).thenReturn("direct-result");

        // Act
        Object result = aspect.handleRateLimitedTask(joinPoint, rateLimitedTask);

        // Assert
        assertEquals("direct-result", result);
        verify(joinPoint).proceed();
        verify(taskRouter, never()).routeTask(any(), any());
    }

    @Test
    @DisplayName("Should work correctly with executeInTaskContext")
    void shouldWorkCorrectly_WithExecuteInTaskContext() throws Throwable {
        // Arrange
        setupJoinPointMocks();
        when(featureFlagsManager.isFeatureOn(IMS_ADD_RATE_LIMITS_FOR_FINALE_API)).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("context-result");

        // Act - Execute within task context
        Object result = TaskExecutionContext.executeInTaskContext(() -> {
            try {
                return aspect.handleRateLimitedTask(joinPoint, rateLimitedTask);
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });

        // Assert
        assertEquals("context-result", result);
        verify(joinPoint).proceed();
        verify(taskRouter, never()).routeTask(any(), any());

        // Context should be cleaned up after execution
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should handle context cleanup after exception")
    void shouldCleanupContext_AfterException() {
        // Arrange
        RuntimeException testException = new RuntimeException("Test exception");

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            TaskExecutionContext.executeInTaskContext(() -> {
                throw testException;
            });
        });

        // Context should be cleaned up after exception
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should detect task execution context correctly")
    void shouldDetectTaskExecutionContext_Correctly() {
        // Initially not in task execution context
        assertFalse(TaskExecutionContext.isInTaskExecution());

        // Mark as in task execution context
        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Clear context
        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }
}
