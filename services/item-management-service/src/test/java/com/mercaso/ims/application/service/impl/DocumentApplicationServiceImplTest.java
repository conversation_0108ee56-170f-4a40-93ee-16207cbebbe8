package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import jakarta.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import org.apache.catalina.connector.Response;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.DelegatingServletOutputStream;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {DocumentApplicationServiceImpl.class})
class DocumentApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private DocumentApplicationServiceImpl documentApplicationServiceImpl;

    @MockBean
    private DocumentOperations documentOperations;

    @MockBean
    private Environment environment;

    @Test
    void testGetImsUrl() {
        // Arrange
        when(environment.getProperty(Mockito.<String>any())).thenReturn("Property");

        // Act
        String actualImsUrl = documentApplicationServiceImpl.getImsUrl("https://example.org/example");

        // Assert
        verify(environment).getProperty("spring.profiles.active");
        assertEquals("${ims.document_url}https://example.org/example", actualImsUrl);
    }

    @Test
    void testGetImsUrl2() {
        // Arrange
        when(environment.getProperty(Mockito.<String>any())).thenThrow(new ImsBusinessException("{env}"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.getImsUrl("https://example.org/example"));
        verify(environment).getProperty("spring.profiles.active");
    }


    @Test
    void testDownloadDocument() throws UnsupportedEncodingException {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));

        // Act
        byte[] actualDownloadDocumentResult = documentApplicationServiceImpl.downloadDocument("Document Name");

        // Assert
        verify(documentOperations).downloadDocument("Document Name");
        assertArrayEquals("AXAXAXAX".getBytes("UTF-8"), actualDownloadDocumentResult);
    }


    @Test
    void testDownloadDocument2() {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> documentApplicationServiceImpl.downloadDocument("Document Name"));
        verify(documentOperations).downloadDocument("Document Name");
    }


    @Test
    void testGetActiveProfile() {
        // Arrange
        when(environment.getProperty(Mockito.<String>any())).thenReturn("Property");

        // Act
        String actualActiveProfile = documentApplicationServiceImpl.getActiveProfile();

        // Assert
        verify(environment).getProperty("spring.profiles.active");
        assertEquals("Property", actualActiveProfile);
    }


    @Test
    void testGetActiveProfile2() {
        // Arrange
        when(environment.getProperty(Mockito.<String>any())).thenThrow(new ImsBusinessException("spring.profiles.active"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> documentApplicationServiceImpl.getActiveProfile());
        verify(environment).getProperty("spring.profiles.active");
    }

    @Test
    void testDownloadDoc() throws UnsupportedEncodingException {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));
        Response response = new Response();
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.downloadDoc(response, "Document Name"));
        verify(documentOperations).downloadDocument("Document Name");
    }

    @Test
    void testDownloadDoc2() {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn(null);

        // Act
        documentApplicationServiceImpl.downloadDoc(new Response(), "Document Name");

        // Assert that nothing has changed
        verify(documentOperations).downloadDocument("Document Name");
    }

    @Test
    void testDownloadDoc3() {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn(new byte[]{});
        Response response = new Response();
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.downloadDoc(response, "Document Name"));
        verify(documentOperations).downloadDocument("Document Name");
    }

    @Test
    void testDownloadDoc4() throws UnsupportedEncodingException {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> documentApplicationServiceImpl.downloadDoc(null, "Document Name"));
        verify(documentOperations).downloadDocument("Document Name");
    }

    @Test
    void testDownloadDoc5() throws IOException {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));
        HttpServletResponseWrapper httpResponse = mock(HttpServletResponseWrapper.class);
        when(httpResponse.getOutputStream()).thenReturn(new DelegatingServletOutputStream(new ByteArrayOutputStream(1)));
        doNothing().when(httpResponse).setCharacterEncoding(Mockito.<String>any());
        doNothing().when(httpResponse).setContentType(Mockito.<String>any());
        doNothing().when(httpResponse).setHeader(Mockito.<String>any(), Mockito.<String>any());

        // Act
        documentApplicationServiceImpl.downloadDoc(httpResponse, "Document Name");

        // Assert
        verify(documentOperations).downloadDocument("Document Name");
        verify(httpResponse).getOutputStream();
        verify(httpResponse).setCharacterEncoding("utf-8");
        verify(httpResponse).setContentType("text/plain");
        verify(httpResponse).setHeader("Content-disposition", "attachment;filename*=utf-8''Document Name");
    }


    @Test
    void testDownloadDoc7() throws IOException {
        // Arrange
        when(documentOperations.downloadDocument(Mockito.<String>any())).thenReturn("AXAXAXAX".getBytes("UTF-8"));
        HttpServletResponseWrapper httpResponse = mock(HttpServletResponseWrapper.class);
        when(httpResponse.getOutputStream()).thenReturn(null);
        doNothing().when(httpResponse).setCharacterEncoding(Mockito.<String>any());
        doNothing().when(httpResponse).setContentType(Mockito.<String>any());
        doNothing().when(httpResponse).setHeader(Mockito.<String>any(), Mockito.<String>any());

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.downloadDoc(httpResponse, "Document Name"));
        verify(documentOperations).downloadDocument("Document Name");
        verify(httpResponse).getOutputStream();
        verify(httpResponse).setCharacterEncoding("utf-8");
        verify(httpResponse).setContentType("text/plain");
        verify(httpResponse).setHeader("Content-disposition", "attachment;filename*=utf-8''Document Name");
    }

    @Test
    void testUploadImage_thenReturnName() throws UnsupportedEncodingException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any())).thenReturn(buildResult);

        // Act
        DocumentResponse actualUploadImageResult = documentApplicationServiceImpl.uploadImage("AXAXAXAX".getBytes("UTF-8"),
            "Image Name");

        // Assert
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
        assertEquals("Name", actualUploadImageResult.getName());
        assertEquals("https://example.org/example", actualUploadImageResult.getSignedUrl());
    }

    @Test
    void testUploadImage_thenThrowImsBusinessException() throws UnsupportedEncodingException {
        // Arrange
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        byte[] content = "AXAXAXAX".getBytes("UTF-8");
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.uploadImage(content, "Image Name"));
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
    }

    @Test
    void testUploadExcel_thenReturnName() throws UnsupportedEncodingException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any())).thenReturn(buildResult);

        // Act
        DocumentResponse actualUploadExcelResult = documentApplicationServiceImpl.uploadExcel("AXAXAXAX".getBytes("UTF-8"),
            "Excel Name");

        // Assert
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
        assertEquals("Name", actualUploadExcelResult.getName());
        assertEquals("https://example.org/example", actualUploadExcelResult.getSignedUrl());
    }

    @Test
    void testUploadExcel_thenThrowImsBusinessException() throws UnsupportedEncodingException {
        // Arrange
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        byte[] content = "AXAXAXAX".getBytes("UTF-8");

        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.uploadExcel(content, "Excel Name"));
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
    }

    @Test
    void testGetSignedUrl_thenReturnHttpsExampleOrgExample() {
        // Arrange
        when(documentOperations.getSignedUrl(Mockito.<String>any())).thenReturn("https://example.org/example");

        // Act
        String actualSignedUrl = documentApplicationServiceImpl.getSignedUrl("https://example.org/example");

        // Assert
        verify(documentOperations).getSignedUrl("https://example.org/example");
        assertEquals("https://example.org/example", actualSignedUrl);
    }

    @Test
    void testGetSignedUrl_thenThrowImsBusinessException() {
        // Arrange
        when(documentOperations.getSignedUrl(Mockito.<String>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> documentApplicationServiceImpl.getSignedUrl("https://example.org/example"));
        verify(documentOperations).getSignedUrl("https://example.org/example");
    }

    @Test
    void testUploadFile_whenByteArrayInputStreamWithAxaxaxaxBytesIsUtf8_thenReturnName() throws IOException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any())).thenReturn(buildResult);

        // Act
        DocumentResponse actualUploadFileResult = documentApplicationServiceImpl
            .uploadFile(new MockMultipartFile("Name", new ByteArrayInputStream("AXAXAXAX".getBytes("UTF-8"))), "foo.txt", true);

        // Assert
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
        assertEquals("Name", actualUploadFileResult.getName());
        assertEquals("https://example.org/example", actualUploadFileResult.getSignedUrl());
    }


    @Test
    void testUploadFile_whenByteArrayInputStreamWithEmptyArrayOfByte_thenReturnName() throws IOException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentOperations.uploadDocument(Mockito.<UploadDocumentRequest>any())).thenReturn(buildResult);

        // Act
        DocumentResponse actualUploadFileResult = documentApplicationServiceImpl
            .uploadFile(new MockMultipartFile("Name", new ByteArrayInputStream(new byte[]{})), "foo.txt", true);

        // Assert
        verify(documentOperations).uploadDocument(isA(UploadDocumentRequest.class));
        assertEquals("Name", actualUploadFileResult.getName());
        assertEquals("https://example.org/example", actualUploadFileResult.getSignedUrl());
    }

    @Test
    void testUploadFile_whenNull_thenThrowImsBusinessException() {
        // Arrange, Act and Assert
        assertThrows(ImsBusinessException.class, () -> documentApplicationServiceImpl.uploadFile(null, "foo.txt", true));
    }
}
