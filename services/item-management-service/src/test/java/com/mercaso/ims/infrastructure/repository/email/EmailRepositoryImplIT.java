package com.mercaso.ims.infrastructure.repository.email;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.domain.email.enums.EmailType;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.email.EmailTestUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

/**
 * Integration tests for EmailRepositoryImpl
 */
class EmailRepositoryImplIT extends AbstractIT {

    @Test
    void testSave_shouldSaveAndReturnEmail() {
        // Arrange
        Email email = EmailTestUtil.buildEmail();

        // Act
        Email result = emailRepository.save(email);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(email.getEntityType(), result.getEntityType());
        assertEquals(email.getEntityId(), result.getEntityId());
        assertEquals(email.getEmailType(), result.getEmailType());
        assertEquals(email.getEmail(), result.getEmail());
        assertEquals(email.getExtension(), result.getExtension());
    }

    @Test
    void testFindById_whenEntityExists_shouldReturnEntity() {
        // Arrange
        Email email = EmailTestUtil.buildEmail();
        Email saved = emailRepository.save(email);

        // Act
        Email result = emailRepository.findById(saved.getId());

        // Assert
        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());
        assertEquals(saved.getEntityType(), result.getEntityType());
        assertEquals(saved.getEmail(), result.getEmail());
    }

    @Test
    void testFindById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        Email result = emailRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testUpdate_shouldUpdateAndReturnEmail() {
        // Arrange
        Email email = EmailTestUtil.buildEmail();
        Email saved = emailRepository.save(email);

        String newEmail = "<EMAIL>";
        saved.setEmail(newEmail);

        // Act
        Email result = emailRepository.update(saved);

        // Assert
        assertNotNull(result);
        assertEquals(newEmail, result.getEmail());
        assertEquals(saved.getId(), result.getId());
    }

    @Test
    void testUpdate_whenEntityDoesNotExist_shouldThrowException() {
        // Arrange
        Email email = EmailTestUtil.buildEmail();
        email.setId(UUID.randomUUID());

        // Act & Assert
        assertThrows(ImsBusinessException.class, () -> emailRepository.update(email));
    }

    @Test
    void testDeleteById_shouldMarkAsDeleted() {
        // Arrange
        Email email = EmailTestUtil.buildEmail();
        Email saved = emailRepository.save(email);

        // Act
        Email result = emailRepository.deleteById(saved.getId());

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDeletedAt());
        assertNotNull(result.getDeletedBy());
    }

    @Test
    void testDeleteById_whenEntityDoesNotExist_shouldReturnNull() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();

        // Act
        Email result = emailRepository.deleteById(nonExistentId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindByEntityTypeAndEntityId_shouldReturnMatchingEmails() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();

        Email email1 = EmailTestUtil.buildEmail(entityType, entityId, EmailType.WORK);
        Email email2 = EmailTestUtil.buildEmail(entityType, entityId, EmailType.PERSONAL);
        Email email3 = EmailTestUtil.buildEmail("Customer", entityId, EmailType.BUSINESS);

        emailRepository.save(email1);
        emailRepository.save(email2);
        emailRepository.save(email3);

        // Act
        List<Email> result = emailRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(e -> entityType.equals(e.getEntityType()) && entityId.equals(e.getEntityId())));
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndEmailType_shouldReturnMatchingEmails() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        EmailType emailType = EmailType.WORK;

        Email email1 = EmailTestUtil.buildEmail(entityType, entityId, emailType);
        Email email2 = EmailTestUtil.buildEmail(entityType, entityId, EmailType.PERSONAL);

        emailRepository.save(email1);
        emailRepository.save(email2);

        // Act
        List<Email> result = emailRepository.findByEntityTypeAndEntityIdAndEmailType(entityType, entityId, emailType);

        // Assert
        assertEquals(1, result.size());
        assertEquals(emailType, result.get(0).getEmailType());
    }

    @Test
    void testFindByEmail_shouldReturnMatchingEmails() {
        // Arrange
        String emailAddress = "<EMAIL>";
        Email email1 = EmailTestUtil.buildEmail(emailAddress);
        Email email2 = EmailTestUtil.buildEmail(emailAddress);
        Email email3 = EmailTestUtil.buildEmail("<EMAIL>");

        emailRepository.save(email1);
        emailRepository.save(email2);
        emailRepository.save(email3);

        // Act
        List<Email> result = emailRepository.findByEmail(emailAddress);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(e -> emailAddress.equals(e.getEmail())));
    }

    @Test
    void testFindByEntityType_shouldReturnMatchingEmails() {
        // Arrange
        String entityType = "Driver";
        Email email1 = EmailTestUtil.buildEmail(entityType, EmailType.WORK);
        Email email2 = EmailTestUtil.buildEmail(entityType, EmailType.PERSONAL);
        Email email3 = EmailTestUtil.buildEmail("Customer", EmailType.BUSINESS);

        emailRepository.save(email1);
        emailRepository.save(email2);
        emailRepository.save(email3);

        // Act
        List<Email> result = emailRepository.findByEntityType(entityType);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(e -> entityType.equals(e.getEntityType())));
    }

    @Test
    void testFindByEntityTypeAndEntityId_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";
        UUID entityId = UUID.randomUUID();

        // Act
        List<Email> result = emailRepository.findByEntityTypeAndEntityId(entityType, entityId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityTypeAndEntityIdAndEmailType_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        EmailType emailType = EmailType.SUPPORT;

        // Act
        List<Email> result = emailRepository.findByEntityTypeAndEntityIdAndEmailType(entityType, entityId, emailType);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEmail_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String emailAddress = "<EMAIL>";

        // Act
        List<Email> result = emailRepository.findByEmail(emailAddress);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByEntityType_whenNoMatches_shouldReturnEmptyList() {
        // Arrange
        String entityType = "NonExistentType";

        // Act
        List<Email> result = emailRepository.findByEntityType(entityType);

        // Assert
        assertTrue(result.isEmpty());
    }
}
