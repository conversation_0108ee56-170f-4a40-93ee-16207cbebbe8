package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.AbstractIT;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled
class CategoryRestApiTest extends AbstractIT {

    @Test
    void shouldSuccessWhenGetAllCategory() throws Exception {

        String categories = categoryRestApiUtil.getAllCategories();
        Assertions.assertNotNull(categories);

    }
}