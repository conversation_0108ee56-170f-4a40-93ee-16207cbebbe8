package com.mercaso.ims.interfaces;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPriceResultDto;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.utils.item.ItemCommandUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ItemPriceRestApiIT extends AbstractIT {


    @Test
    void shouldSuccessWhenBatchUpdateItemRegPrice() throws Exception {

        Mockito.when(imsAlertConfig.getMaximumMargin()).thenReturn(BigDecimal.valueOf(0.5));
        Mockito.when(imsAlertConfig.getMinimumMargin()).thenReturn(BigDecimal.valueOf(0));
        Mockito.when(imsAlertConfig.getMaximumPrice()).thenReturn(BigDecimal.valueOf(500));
        Mockito.when(imsAlertConfig.getMinimumPrice()).thenReturn(BigDecimal.valueOf(1));

        String sku1 = "sku1" + System.currentTimeMillis();

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo = buildItemDataWithVendor(sku1, DOWNEY_WHOLESALE, vendorItemNumber);
        UUID randomId = UUID.randomUUID();

        List<UUID> ids = List.of(itemDo.getId(), randomId);
        List<UpdateItemRegPriceCommand> commands = ItemCommandUtil.buildBatchUpdateItemRegPrice(ids);
        BatchUpdateItemPriceResultDto result = itemPriceRestApiUtil.batchUpdateItemRegPrice(commands);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(1, result.getFailedSkuNumbers().size());
    }


}
