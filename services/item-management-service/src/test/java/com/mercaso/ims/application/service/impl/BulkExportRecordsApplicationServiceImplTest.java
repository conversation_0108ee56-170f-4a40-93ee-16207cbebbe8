package com.mercaso.ims.application.service.impl;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.mapper.bulkexportrecords.BulkExportRecordsDtoApplicationMapper;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BulkExportRecordsApplicationServiceImplTest {

    @Mock
    private BulkExportRecordsService bulkExportRecordsService;

    @Mock
    private BulkExportRecordsDtoApplicationMapper bulkExportRecordsDtoApplicationMapper;

    @InjectMocks
    private BulkExportRecordsApplicationServiceImpl bulkExportRecordsApplicationService;

    @Mock
    private DocumentApplicationService documentApplicationService;

    private BulkExportRecordsCommand command;
    private BulkExportRecords bulkExportRecords;
    private BulkExportRecords savedBulkExportRecords;
    private BulkExportRecordsDto expectedDto;
    private UUID recordId;
    private BulkExportRecords bulkExportRecordsFile;
    private String fileName;
    private String signedUrl;

    @BeforeEach
    void setUp() {
        // Prepare test data
        Instant now = Instant.now();

        command = BulkExportRecordsCommand.builder()
            .fileName("test-export.csv")
            .customFilter("{\"status\":\"ACTIVE\"}")
            .searchTime(now)
            .sendEmailTime(now.plusSeconds(3600))
            .build();

        bulkExportRecords = BulkExportRecords.builder()
            .fileName("test-export.csv")
            .customFilter("{\"status\":\"ACTIVE\"}")
            .searchTime(now)
            .sendEmailTime(now.plusSeconds(3600))
            .build();

        savedBulkExportRecords = BulkExportRecords.builder()
            .id(UUID.randomUUID())
            .fileName("test-export.csv")
            .customFilter("{\"status\":\"ACTIVE\"}")
            .searchTime(now)
            .sendEmailTime(now.plusSeconds(3600))
            .build();

        expectedDto = BulkExportRecordsDto.builder()
            .id(savedBulkExportRecords.getId())
            .fileName("test-export.csv")
            .customFilter("{\"status\":\"ACTIVE\"}")
            .searchTime(now)
            .sendEmailTime(now.plusSeconds(3600))
            .build();

        recordId = UUID.randomUUID();
        fileName = "test-export.xlsx";
        signedUrl = "https://example.com/test-export.xlsx";

        bulkExportRecordsFile = BulkExportRecords.builder()
                .id(recordId)
                .fileName(fileName)
                .build();
    }

    @Test
    void save_ShouldCreateBulkExportRecordsAndReturnDto() {
        // Arrange
        when(bulkExportRecordsService.save(any(BulkExportRecords.class))).thenReturn(savedBulkExportRecords);
        when(bulkExportRecordsDtoApplicationMapper.domainToDto(savedBulkExportRecords)).thenReturn(expectedDto);

        // Act
        BulkExportRecordsDto result = bulkExportRecordsApplicationService.save(command);

        // Assert
        assertNotNull(result);
        assertEquals(expectedDto.getId(), result.getId());
        assertEquals(expectedDto.getFileName(), result.getFileName());
        assertEquals(expectedDto.getCustomFilter(), result.getCustomFilter());
        assertEquals(expectedDto.getSearchTime(), result.getSearchTime());
        assertEquals(expectedDto.getSendEmailTime(), result.getSendEmailTime());

        // Verify interactions
        verify(bulkExportRecordsService).save(any(BulkExportRecords.class));
        verify(bulkExportRecordsDtoApplicationMapper).domainToDto(savedBulkExportRecords);
    }

        @Test
        void getBulkExportRecordsFile_ShouldReturnDocumentResponse_WhenRecordExists() {
            // Arrange
            when(bulkExportRecordsService.findById(recordId)).thenReturn(bulkExportRecordsFile);
            when(documentApplicationService.getSignedUrl(fileName)).thenReturn(signedUrl);

            // Act
            DocumentResponse result = bulkExportRecordsApplicationService.getBulkExportRecordsFile(recordId);

            // Assert
            assertNotNull(result);
            assertEquals(fileName, result.getName());
            assertEquals(signedUrl, result.getSignedUrl());
        }

        @Test
        void getBulkExportRecordsFile_ShouldThrowException_WhenRecordNotFound() {
            // Arrange
            when(bulkExportRecordsService.findById(recordId)).thenReturn(null);

            // Act & Assert
            assertThrows(ImsBusinessException.class, () ->
                    bulkExportRecordsApplicationService.getBulkExportRecordsFile(recordId));
        }

}
