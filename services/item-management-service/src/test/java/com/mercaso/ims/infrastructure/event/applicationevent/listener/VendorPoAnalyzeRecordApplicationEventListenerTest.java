package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.command.UpdateVendorPoAnalyzeRecordCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.dto.VendorPoAnalyzeRecordDto;
import com.mercaso.ims.application.dto.event.CreateAnalyzeExpenseRecordApplicationEvent;
import com.mercaso.ims.application.dto.payload.CreateVendorPoAnalyzeExpenseRecordPayloadDto;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.application.service.VendorPoAnalyzeRecordApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisSource;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisStatus;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorPoAnalyzeRecordApplicationEventListener.class})
class VendorPoAnalyzeRecordApplicationEventListenerTest extends AbstractTest {

    @MockBean
    private ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @Autowired
    private VendorPoAnalyzeRecordApplicationEventListener vendorPoAnalyzeRecordApplicationEventListener;

    @MockBean
    private VendorPoAnalyzeRecordApplicationService vendorPoAnalyzeRecordApplicationService;


    @Test
    void testHandleApprovedAnalyzeRecordApplicationEvent_thenCallsCreate() {
        // Arrange
        VendorPoAnalyzeRecordDto buildResult = VendorPoAnalyzeRecordDto.builder()
            .analysisExpensePayload("Analysis Expense Payload")
            .analysisSource(AnalysisSource.AWS)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemCostCollectionId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .originalFileName("foo.txt")
            .rcptDate("2020-03-01")
            .rcptId("42")
            .status(AnalysisStatus.PENDING)
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorName("Vendor Name")
            .build();
        when(vendorPoAnalyzeRecordApplicationService.update(Mockito.<UpdateVendorPoAnalyzeRecordCommand>any()))
            .thenReturn(buildResult);
        VendorPoAnalyzeRecordDto buildResult2 = VendorPoAnalyzeRecordDto.builder()
            .analysisExpensePayload("Analysis Expense Payload")
            .analysisSource(AnalysisSource.AWS)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemCostCollectionId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .originalFileName("foo.txt")
            .rcptDate("2020-03-01")
            .rcptId("42")
            .status(AnalysisStatus.PENDING)
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorName("Vendor Name")
            .build();
        when(vendorPoAnalyzeRecordApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult2);
        ItemCostCollectionDto buildResult3 = ItemCostCollectionDto.builder()
            .approvedCount(3)
            .collectionNumber("42")
            .fileName("foo.txt")
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .pendingCount(3)
            .rejectedCount(3)
            .source(ItemCostCollectionSources.MANUAL_UPLOADED)
            .type(ItemCostCollectionTypes.CSV_FILE)
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorName("Vendor Name")
            .build();
        when(itemCostCollectionApplicationService.create(Mockito.<CreateItemCostCollectionCommand>any()))
            .thenReturn(buildResult3);
        CreateVendorPoAnalyzeExpenseRecordPayloadDto.CreateVendorPoAnalyzeExpenseRecordPayloadDtoBuilder builderResult = CreateVendorPoAnalyzeExpenseRecordPayloadDto
            .builder();
        VendorPoAnalyzeRecordDto data = VendorPoAnalyzeRecordDto.builder()
            .analysisExpensePayload("Analysis Expense Payload")
            .analysisSource(AnalysisSource.AWS)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemCostCollectionId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .originalFileName("foo.txt")
            .rcptDate("2020-03-01")
            .rcptId("42")
            .status(AnalysisStatus.PENDING)
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorName("Vendor Name")
            .build();
        CreateVendorPoAnalyzeExpenseRecordPayloadDto payload = builderResult.data(data)
            .vendorPoAnalyzeRecordId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .build();

        // Act
        vendorPoAnalyzeRecordApplicationEventListener
            .handleApprovedAnalyzeRecordApplicationEvent(new CreateAnalyzeExpenseRecordApplicationEvent("Source", payload));

        // Assert
        verify(itemCostCollectionApplicationService).create(isA(CreateItemCostCollectionCommand.class));
        verify(vendorPoAnalyzeRecordApplicationService).findById(isA(UUID.class));
        verify(vendorPoAnalyzeRecordApplicationService).updateWithItemCostCollection(isA(UUID.class),
            isA(ItemCostCollectionDto.class));
    }
}
