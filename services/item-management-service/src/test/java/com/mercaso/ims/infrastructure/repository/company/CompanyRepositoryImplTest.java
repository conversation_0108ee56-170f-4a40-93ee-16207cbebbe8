package com.mercaso.ims.infrastructure.repository.company;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.company.Company;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.company.jpa.CompanyJpaDao;
import com.mercaso.ims.infrastructure.repository.company.jpa.dataobject.CompanyDo;
import com.mercaso.ims.infrastructure.repository.company.jpa.mapper.CompanyDoMapper;
import com.mercaso.ims.utils.company.CompanyUtil;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {CompanyRepositoryImpl.class})
class CompanyRepositoryImplTest extends AbstractTest {

    @MockBean
    private CompanyDoMapper companyDoMapper;

    @MockBean
    private CompanyJpaDao companyJpaDao;

    @Autowired
    private CompanyRepositoryImpl companyRepositoryImpl;

    @Test
    void testSaveWithCompany_givenCompanyDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenReturn(null);
        when(companyDoMapper.domainToDo(Mockito.<Company>any())).thenReturn(companyDo);

        CompanyDo companyDo2 = CompanyUtil.buildCompanyDo();
        when(companyJpaDao.save(Mockito.<CompanyDo>any())).thenReturn(companyDo2);

        // Act
        Company actualSaveResult = companyRepositoryImpl.save(null);

        // Assert
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyDoMapper).domainToDo(isNull());
        verify(companyJpaDao).save(isA(CompanyDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSaveWithCompany_thenThrowImsBusinessException() {
        // Arrange
        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        when(companyDoMapper.domainToDo(Mockito.<Company>any())).thenReturn(companyDo);
        when(companyJpaDao.save(Mockito.<CompanyDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> companyRepositoryImpl.save(null));
        verify(companyDoMapper).domainToDo(isNull());
        verify(companyJpaDao).save(isA(CompanyDo.class));
    }

    @Test
    void testFindByIdWithUuid_givenCompanyDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenReturn(null);

        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        Optional<CompanyDo> ofResult = Optional.of(companyDo);
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        Company actualFindByIdResult = companyRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> companyRepositoryImpl.findById(uuid));
        verify(companyJpaDao).findById(isA(UUID.class));
    }

    @Test
    void testUpdateWithCompany_givenCompanyDoCompanyIdIsOne_thenReturnNull() {
        // Arrange
        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenReturn(null);
        when(companyDoMapper.domainToDo(Mockito.<Company>any())).thenReturn(companyDo);

        CompanyDo companyDo2 = CompanyUtil.buildCompanyDo();
        Optional<CompanyDo> ofResult = Optional.of(companyDo2);

        CompanyDo companyDo3 = CompanyUtil.buildCompanyDo();
        when(companyJpaDao.save(Mockito.<CompanyDo>any())).thenReturn(companyDo3);
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        Company company = mock(Company.class);
        when(company.getId()).thenReturn(UUID.randomUUID());

        // Act
        Company actualUpdateResult = companyRepositoryImpl.update(company);

        // Assert
        verify(company).getId();
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyDoMapper).domainToDo(isA(Company.class));
        verify(companyJpaDao).findById(isA(UUID.class));
        verify(companyJpaDao).save(isA(CompanyDo.class));
        assertNull(actualUpdateResult);
    }

    @Test
    void testDeleteById_givenCompanyDoMapperDoToDomainReturnNull_thenCallsDoToDomain() {
        // Arrange
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenReturn(null);

        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        Optional<CompanyDo> ofResult = Optional.of(companyDo);

        CompanyDo companyDo2 = CompanyUtil.buildCompanyDo();
        when(companyJpaDao.save(Mockito.<CompanyDo>any())).thenReturn(companyDo2);
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        Company actualDeleteByIdResult = companyRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyJpaDao).findById(isA(UUID.class));
        verify(companyJpaDao).save(isA(CompanyDo.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testDeleteById_givenCompanyJpaDaoFindByIdReturnEmpty_thenReturnNull() {
        // Arrange
        Optional<CompanyDo> emptyResult = Optional.empty();
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        Company actualDeleteByIdResult = companyRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(companyJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        Optional<CompanyDo> ofResult = Optional.of(companyDo);
        when(companyJpaDao.save(Mockito.<CompanyDo>any())).thenThrow(new ImsBusinessException("Code"));
        when(companyJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> companyRepositoryImpl.deleteById(uuid));
        verify(companyJpaDao).findById(isA(UUID.class));
        verify(companyJpaDao).save(isA(CompanyDo.class));
    }

    @Test
    void testFindByCompanyId_givenCompanyDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenReturn(null);

        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        when(companyJpaDao.findByCompanyId(Mockito.<Long>any())).thenReturn(companyDo);

        // Act
        Company actualFindByCompanyIdResult = companyRepositoryImpl.findByCompanyId(1L);

        // Assert
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyJpaDao).findByCompanyId(1L);
        assertNull(actualFindByCompanyIdResult);
    }

    @Test
    void testFindByCompanyId_thenThrowImsBusinessException() {
        // Arrange
        when(companyDoMapper.doToDomain(Mockito.<CompanyDo>any())).thenThrow(new ImsBusinessException("Code"));

        CompanyDo companyDo = CompanyUtil.buildCompanyDo();
        when(companyJpaDao.findByCompanyId(Mockito.<Long>any())).thenReturn(companyDo);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> companyRepositoryImpl.findByCompanyId(1L));
        verify(companyDoMapper).doToDomain(isA(CompanyDo.class));
        verify(companyJpaDao).findByCompanyId(1L);
    }
}
