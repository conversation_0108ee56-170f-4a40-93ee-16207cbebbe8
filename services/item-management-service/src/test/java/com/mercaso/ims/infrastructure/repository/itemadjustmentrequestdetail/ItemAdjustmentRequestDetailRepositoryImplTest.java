package com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.ItemAdjustmentRequestDetailJpaDao;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.dataobject.ItemAdjustmentRequestDetailDo;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.mapper.ItemAdjustmentRequestDetailDoMapper;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAdjustmentRequestDetailRepositoryImpl.class})
class ItemAdjustmentRequestDetailRepositoryImplTest extends AbstractTest {

    @MockBean
    private ItemAdjustmentRequestDetailDoMapper itemAdjustmentRequestDetailDoMapper;

    @MockBean
    private ItemAdjustmentRequestDetailJpaDao itemAdjustmentRequestDetailJpaDao;

    @Autowired
    private ItemAdjustmentRequestDetailRepositoryImpl itemAdjustmentRequestDetailRepositoryImpl;

    @Test
    void testSaveWithItemAdjustmentRequestDetail_thenReturnNull() {
        // Arrange
        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        when(itemAdjustmentRequestDetailJpaDao.save(Mockito.<ItemAdjustmentRequestDetailDo>any()))
            .thenReturn(itemAdjustmentRequestDetailDo);

        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo2 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        when(itemAdjustmentRequestDetailDoMapper.doToDomain(Mockito.<ItemAdjustmentRequestDetailDo>any())).thenReturn(null);
        when(itemAdjustmentRequestDetailDoMapper.domainToDo(Mockito.<ItemAdjustmentRequestDetail>any()))
            .thenReturn(itemAdjustmentRequestDetailDo2);

        // Act
        ItemAdjustmentRequestDetail actualSaveResult = itemAdjustmentRequestDetailRepositoryImpl.save(null);

        // Assert
        verify(itemAdjustmentRequestDetailDoMapper).doToDomain(isA(ItemAdjustmentRequestDetailDo.class));
        verify(itemAdjustmentRequestDetailDoMapper).domainToDo(isNull());
        verify(itemAdjustmentRequestDetailJpaDao).save(isA(ItemAdjustmentRequestDetailDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSaveWithItemAdjustmentRequestDetail_thenThrowImsBusinessException() {
        // Arrange
        when(itemAdjustmentRequestDetailDoMapper.domainToDo(Mockito.<ItemAdjustmentRequestDetail>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemAdjustmentRequestDetailRepositoryImpl.save(null));
        verify(itemAdjustmentRequestDetailDoMapper).domainToDo(isNull());
    }

    @Test
    void testFindByIdWithUuid_thenReturnNull() {
        // Arrange
        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        Optional<ItemAdjustmentRequestDetailDo> ofResult = Optional.of(itemAdjustmentRequestDetailDo);
        when(itemAdjustmentRequestDetailJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemAdjustmentRequestDetailDoMapper.doToDomain(Mockito.<ItemAdjustmentRequestDetailDo>any())).thenReturn(null);

        // Act
        ItemAdjustmentRequestDetail actualFindByIdResult = itemAdjustmentRequestDetailRepositoryImpl
            .findById(UUID.randomUUID());

        // Assert
        verify(itemAdjustmentRequestDetailDoMapper).doToDomain(isA(ItemAdjustmentRequestDetailDo.class));
        verify(itemAdjustmentRequestDetailJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        Optional<ItemAdjustmentRequestDetailDo> ofResult = Optional.of(itemAdjustmentRequestDetailDo);
        when(itemAdjustmentRequestDetailJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemAdjustmentRequestDetailDoMapper.doToDomain(Mockito.<ItemAdjustmentRequestDetailDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class,
            () -> itemAdjustmentRequestDetailRepositoryImpl.findById(uuid));
        verify(itemAdjustmentRequestDetailDoMapper).doToDomain(isA(ItemAdjustmentRequestDetailDo.class));
        verify(itemAdjustmentRequestDetailJpaDao).findById(isA(UUID.class));
    }

    @Test
    void testUpdateWithItemAdjustmentRequestDetail_thenReturnNull() {
        // Arrange
        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        Optional<ItemAdjustmentRequestDetailDo> ofResult = Optional.of(itemAdjustmentRequestDetailDo);

        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo2 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        when(itemAdjustmentRequestDetailJpaDao.save(Mockito.<ItemAdjustmentRequestDetailDo>any()))
            .thenReturn(itemAdjustmentRequestDetailDo2);
        when(itemAdjustmentRequestDetailJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo3 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetailDo();
        when(itemAdjustmentRequestDetailDoMapper.doToDomain(Mockito.<ItemAdjustmentRequestDetailDo>any())).thenReturn(null);
        when(itemAdjustmentRequestDetailDoMapper.domainToDo(Mockito.<ItemAdjustmentRequestDetail>any()))
            .thenReturn(itemAdjustmentRequestDetailDo3);
        ItemAdjustmentRequestDetail domain = mock(ItemAdjustmentRequestDetail.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        ItemAdjustmentRequestDetail actualUpdateResult = itemAdjustmentRequestDetailRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(itemAdjustmentRequestDetailDoMapper).doToDomain(isA(ItemAdjustmentRequestDetailDo.class));
        verify(itemAdjustmentRequestDetailDoMapper).domainToDo(isA(ItemAdjustmentRequestDetail.class));
        verify(itemAdjustmentRequestDetailJpaDao).findById(isA(UUID.class));
        verify(itemAdjustmentRequestDetailJpaDao).save(isA(ItemAdjustmentRequestDetailDo.class));
        assertNull(actualUpdateResult);
    }

    @Test
    void testDeleteById() {
        // Arrange, Act and Assert
        assertNull(itemAdjustmentRequestDetailRepositoryImpl.deleteById(UUID.randomUUID()));
    }


    @Test
    void testSaveList_givenItemAdjustmentRequestDetailDoMapper_thenReturnEmpty() {
        // Arrange
        when(itemAdjustmentRequestDetailJpaDao.saveAll(Mockito.<Iterable<ItemAdjustmentRequestDetailDo>>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemAdjustmentRequestDetail> actualSaveListResult = itemAdjustmentRequestDetailRepositoryImpl
            .saveList(new ArrayList<>());

        // Assert
        verify(itemAdjustmentRequestDetailJpaDao).saveAll(isA(Iterable.class));
        assertTrue(actualSaveListResult.isEmpty());
    }


    @Test
    void testFindByItemAdjustmentStatus_thenReturnEmpty() {
        // Arrange
        when(itemAdjustmentRequestDetailJpaDao.findAllByStatus(Mockito.<ItemAdjustmentStatus>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemAdjustmentRequestDetail> actualFindByItemAdjustmentStatusResult = itemAdjustmentRequestDetailRepositoryImpl
            .findByItemAdjustmentStatus(ItemAdjustmentStatus.VALIDATION_FAILURE);

        // Assert
        verify(itemAdjustmentRequestDetailJpaDao).findAllByStatus(ItemAdjustmentStatus.VALIDATION_FAILURE);
        assertTrue(actualFindByItemAdjustmentStatusResult.isEmpty());
    }


    @Test
    void testFindByItemAdjustmentRequestId_thenReturnEmpty() {
        // Arrange
        when(itemAdjustmentRequestDetailJpaDao.findAllByRequestId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemAdjustmentRequestDetail> actualFindByItemAdjustmentRequestIdResult = itemAdjustmentRequestDetailRepositoryImpl
            .findByItemAdjustmentRequestId(UUID.randomUUID());

        // Assert
        verify(itemAdjustmentRequestDetailJpaDao).findAllByRequestId(isA(UUID.class));
        assertTrue(actualFindByItemAdjustmentRequestIdResult.isEmpty());
    }


    @Test
    void testFindAllBySku_givenItemAdjustmentRequestDetailDoMapper_thenReturnEmpty() {
        // Arrange
        when(itemAdjustmentRequestDetailJpaDao.findAllBySku(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemAdjustmentRequestDetail> actualFindAllBySkuResult = itemAdjustmentRequestDetailRepositoryImpl
            .findAllBySku("42");

        // Assert
        verify(itemAdjustmentRequestDetailJpaDao).findAllBySku("42");
        assertTrue(actualFindAllBySkuResult.isEmpty());
    }
}
