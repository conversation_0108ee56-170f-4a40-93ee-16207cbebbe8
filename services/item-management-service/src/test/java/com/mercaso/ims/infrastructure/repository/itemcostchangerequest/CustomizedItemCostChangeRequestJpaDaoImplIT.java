package com.mercaso.ims.infrastructure.repository.itemcostchangerequest;

import static com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus.APPROVED;
import static com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus.INVALID;
import static com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus.REJECTED;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomizedItemCostChangeRequestJpaDaoImplIT extends AbstractIT {

    @Test
    void testFetchItemCostChangeRequestDtoList() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection = itemCostCollectionRepository.save(itemCostCollection);
        ItemCostChangeRequest itemCostChangeRequest1 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest1.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequest1.setStatus(INVALID);
        itemCostChangeRequestRepository.save(itemCostChangeRequest1);

        ItemDo itemDo = buildItemDataWithVendor("CostCollection_test", "CostCollection_vendor_test", "66451_test");
        ItemCostChangeRequest itemCostChangeRequest2 = ItemCostChangeRequestUtil.buildItemCostChangeRequest(itemCostCollection.getId(),
            itemDo.getPrimaryVendorId(),
            itemDo.getId()
        );
        itemCostChangeRequest2.setSkuNumber("CostCollection_test");
        itemCostChangeRequest2.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequest2.setStatus(APPROVED);

        itemCostChangeRequestRepository.save(itemCostChangeRequest2);
        ItemCostChangeRequest itemCostChangeRequest3 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest3.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequest3.setStatus(REJECTED);

        itemCostChangeRequestRepository.save(itemCostChangeRequest3);

        ItemCostChangeRequestQuery query = ItemCostChangeRequestQuery.builder()
            .page(0)
            .pageSize(10)
            .itemCostCollectionId(itemCostCollection.getId())
            .build();

        query.setItemStatus(Arrays.asList("ACTIVE"));
        query.setItemSkuNumbers(List.of("CostCollection_test"));
        List<ItemCostChangeRequestDto> result = customizedItemCostChangeRequestJpaDao.fetchItemCostChangeRequestDtoList(
            query);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals("ACTIVE", result.get(0).getItemStatus());
    }

    @Test
    void testCountQuery() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection = itemCostCollectionRepository.save(itemCostCollection);
        ItemCostChangeRequest itemCostChangeRequest1 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest1.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequestRepository.save(itemCostChangeRequest1);
        ItemCostChangeRequest itemCostChangeRequest2 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest2.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequestRepository.save(itemCostChangeRequest2);
        ItemCostChangeRequestQuery query = ItemCostChangeRequestQuery.builder()
            .page(0)
            .pageSize(10)
            .itemCostCollectionId(itemCostCollection.getId())
            .build();

        long result = customizedItemCostChangeRequestJpaDao.countQuery(
            query);
        Assertions.assertEquals(2l, result);

    }
}
