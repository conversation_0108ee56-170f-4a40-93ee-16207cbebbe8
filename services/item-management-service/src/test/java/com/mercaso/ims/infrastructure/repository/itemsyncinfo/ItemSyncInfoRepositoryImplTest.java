package com.mercaso.ims.infrastructure.repository.itemsyncinfo;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.ItemAdjustmentSyncStatusJpaDao;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.dataobject.ItemAdjustmentSyncStatusDo;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.mapper.ItemAdjustmentSyncStatusDoMapper;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAdjustmentSyncStatusRepositoryImpl.class})
class ItemSyncInfoRepositoryImplTest extends AbstractTest {

    @MockBean
    private ItemAdjustmentSyncStatusDoMapper itemSyncInfoDoMapper;

    @MockBean
    private ItemAdjustmentSyncStatusJpaDao itemSyncInfoJpaDao;

    @Autowired
    private ItemAdjustmentSyncStatusRepositoryImpl itemSyncInfoRepositoryImpl;

    @Test
    void testSave() {
        // Arrange
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(null);
        when(itemSyncInfoDoMapper.domainToDo(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(itemSyncInfoDo);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo2 = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo2.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo2.setCreatedUserName("janedoe");
        itemSyncInfoDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo2.setDeletedUserName("janedoe");
        itemSyncInfoDo2.setId(UUID.randomUUID());
        itemSyncInfoDo2.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setUpdatedBy("2020-03-01");
        itemSyncInfoDo2.setUpdatedUserName("janedoe");
        when(itemSyncInfoJpaDao.save(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(itemSyncInfoDo2);

        // Act
        ItemAdjustmentSyncStatus actualSaveResult = itemSyncInfoRepositoryImpl.save(null);

        // Assert
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
        verify(itemSyncInfoDoMapper).domainToDo(isNull());
        verify(itemSyncInfoJpaDao).save(isA(ItemAdjustmentSyncStatusDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSave2() {
        // Arrange
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        when(itemSyncInfoDoMapper.domainToDo(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(itemSyncInfoDo);
        when(itemSyncInfoJpaDao.save(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemSyncInfoRepositoryImpl.save(null));
        verify(itemSyncInfoDoMapper).domainToDo(isNull());
        verify(itemSyncInfoJpaDao).save(isA(ItemAdjustmentSyncStatusDo.class));
    }

    @Test
    void testFindById() {
        // Arrange
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(null);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        Optional<ItemAdjustmentSyncStatusDo> ofResult = Optional.of(itemSyncInfoDo);
        when(itemSyncInfoJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        ItemAdjustmentSyncStatus actualFindByIdResult = itemSyncInfoRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
        verify(itemSyncInfoJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindById2() {
        // Arrange
        when(itemSyncInfoJpaDao.findById(Mockito.<UUID>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID id = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> itemSyncInfoRepositoryImpl.findById(id));
        verify(itemSyncInfoJpaDao).findById(isA(UUID.class));
    }

    @Test
    void testUpdate() {
        // Arrange
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(null);
        when(itemSyncInfoDoMapper.domainToDo(Mockito.<ItemAdjustmentSyncStatus>any())).thenReturn(itemSyncInfoDo);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo2 = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo2.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo2.setCreatedUserName("janedoe");
        itemSyncInfoDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo2.setDeletedUserName("janedoe");
        itemSyncInfoDo2.setId(UUID.randomUUID());
        itemSyncInfoDo2.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setUpdatedBy("2020-03-01");
        itemSyncInfoDo2.setUpdatedUserName("janedoe");

        ItemAdjustmentSyncStatusDo itemSyncInfoDo3 = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo3.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo3.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo3.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo3.setCreatedUserName("janedoe");
        itemSyncInfoDo3.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo3.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo3.setDeletedUserName("janedoe");
        itemSyncInfoDo3.setId(UUID.randomUUID());
        itemSyncInfoDo3.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo3.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo3.setUpdatedBy("2020-03-01");
        itemSyncInfoDo3.setUpdatedUserName("janedoe");
        when(itemSyncInfoJpaDao.save(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(itemSyncInfoDo3);
        when(itemSyncInfoJpaDao.findByBusinessEventId(Mockito.<UUID>any())).thenReturn(itemSyncInfoDo2);
        ItemAdjustmentSyncStatus domain = mock(ItemAdjustmentSyncStatus.class);
        when(domain.getBusinessEventId()).thenReturn(UUID.randomUUID());

        // Act
        ItemAdjustmentSyncStatus actualUpdateResult = itemSyncInfoRepositoryImpl.update(domain);

        // Assert
        verify(domain).getBusinessEventId();
        verify(itemSyncInfoJpaDao).findByBusinessEventId(isA(UUID.class));
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
        verify(itemSyncInfoDoMapper).domainToDo(isA(ItemAdjustmentSyncStatus.class));
        verify(itemSyncInfoJpaDao).save(isA(ItemAdjustmentSyncStatusDo.class));
        assertNull(actualUpdateResult);
    }

    @Test
    void testDeleteById() {
        // Arrange
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(null);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        Optional<ItemAdjustmentSyncStatusDo> ofResult = Optional.of(itemSyncInfoDo);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo2 = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo2.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo2.setCreatedUserName("janedoe");
        itemSyncInfoDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo2.setDeletedUserName("janedoe");
        itemSyncInfoDo2.setId(UUID.randomUUID());
        itemSyncInfoDo2.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo2.setUpdatedBy("2020-03-01");
        itemSyncInfoDo2.setUpdatedUserName("janedoe");
        when(itemSyncInfoJpaDao.save(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(itemSyncInfoDo2);
        when(itemSyncInfoJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        ItemAdjustmentSyncStatus actualDeleteByIdResult = itemSyncInfoRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
        verify(itemSyncInfoJpaDao).findById(isA(UUID.class));
        verify(itemSyncInfoJpaDao).save(isA(ItemAdjustmentSyncStatusDo.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testDeleteById2() {
        // Arrange
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        Optional<ItemAdjustmentSyncStatusDo> ofResult = Optional.of(itemSyncInfoDo);
        when(itemSyncInfoJpaDao.save(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenThrow(new ImsBusinessException("Code"));
        when(itemSyncInfoJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act and Assert
        UUID id = UUID.randomUUID();
        Assert.assertThrows(ImsBusinessException.class, () -> itemSyncInfoRepositoryImpl.deleteById(id));
        verify(itemSyncInfoJpaDao).findById(isA(UUID.class));
        verify(itemSyncInfoJpaDao).save(isA(ItemAdjustmentSyncStatusDo.class));
    }

    @Test
    void testDeleteById3() {
        // Arrange
        Optional<ItemAdjustmentSyncStatusDo> emptyResult = Optional.empty();
        when(itemSyncInfoJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        ItemAdjustmentSyncStatus actualDeleteByIdResult = itemSyncInfoRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(itemSyncInfoJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testFindByBusinessEventId() {
        // Arrange
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenReturn(null);

        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        when(itemSyncInfoJpaDao.findByBusinessEventId(Mockito.<UUID>any())).thenReturn(itemSyncInfoDo);

        // Act
        ItemAdjustmentSyncStatus actualFindByBusinessEventIdResult = itemSyncInfoRepositoryImpl
            .findByBusinessEventId(UUID.randomUUID());

        // Assert
        verify(itemSyncInfoJpaDao).findByBusinessEventId(isA(UUID.class));
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
        assertNull(actualFindByBusinessEventIdResult);
    }

    @Test
    void testFindByBusinessEventId2() {
        // Arrange
        when(itemSyncInfoDoMapper.doToDomain(Mockito.<ItemAdjustmentSyncStatusDo>any())).thenThrow(new ImsBusinessException("Code"));

        ItemAdjustmentSyncStatusDo itemSyncInfoDo = new ItemAdjustmentSyncStatusDo();
        itemSyncInfoDo.setBusinessEventId(UUID.randomUUID());
        itemSyncInfoDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemSyncInfoDo.setCreatedUserName("janedoe");
        itemSyncInfoDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemSyncInfoDo.setDeletedUserName("janedoe");
        itemSyncInfoDo.setId(UUID.randomUUID());
        itemSyncInfoDo.setSyncShopifyStatus(SyncShopifyStatus.SUCCESS);
        itemSyncInfoDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemSyncInfoDo.setUpdatedBy("2020-03-01");
        itemSyncInfoDo.setUpdatedUserName("janedoe");
        when(itemSyncInfoJpaDao.findByBusinessEventId(Mockito.<UUID>any())).thenReturn(itemSyncInfoDo);

        // Act and Assert
        UUID id = UUID.randomUUID();
        Assert.assertThrows(ImsBusinessException.class, () -> itemSyncInfoRepositoryImpl.findByBusinessEventId(id));
        verify(itemSyncInfoJpaDao).findByBusinessEventId(isA(UUID.class));
        verify(itemSyncInfoDoMapper).doToDomain(isA(ItemAdjustmentSyncStatusDo.class));
    }

    @Test
    void testFindBySyncShopifyStatus() {
        // Arrange
        when(itemSyncInfoJpaDao.findTop500BySyncShopifyStatusOrderByCreatedAt(Mockito.<SyncShopifyStatus>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemAdjustmentSyncStatus> actualFindBySyncShopifyStatusResult = itemSyncInfoRepositoryImpl
            .findBySyncShopifyStatus(SyncShopifyStatus.SUCCESS);

        // Assert
        verify(itemSyncInfoJpaDao).findTop500BySyncShopifyStatusOrderByCreatedAt(SyncShopifyStatus.SUCCESS);
        assertTrue(actualFindBySyncShopifyStatusResult.isEmpty());
    }
}
