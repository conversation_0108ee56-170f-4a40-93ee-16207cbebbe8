package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomizedItemCostCollectionJpaDaoImplIT extends AbstractIT {

    @Test
    void testGetfetchItemCostCollectionDtoList() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollectionRepository.save(itemCostCollection);
        ItemCostCollectionQuery query = ItemCostCollectionQuery.builder()
            .page(0)
            .pageSize(10)
            .build();

        List<ItemCostCollectionDetailDto> result = customizedItemCostCollectionJpaDao.fetchItemCostCollectionDtoList(query);
        Assertions.assertNotNull(result);
    }

    @Test
    void testCountQuery() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollectionRepository.save(itemCostCollection);
        ItemCostCollectionQuery query = ItemCostCollectionQuery.builder()
            .page(0)
            .pageSize(10)
            .build();
        long result = customizedItemCostCollectionJpaDao.countQuery(query);
        Assertions.assertTrue(result > 0);
    }

    @Test
    void testGetItemCostCollectionDto() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection = itemCostCollectionRepository.save(itemCostCollection);
        ItemCostChangeRequest itemCostChangeRequest1 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest1.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequest1.setStatus(ItemCostChangeRequestStatus.INVALID);
        itemCostChangeRequestRepository.save(itemCostChangeRequest1);
        ItemCostChangeRequest itemCostChangeRequest2 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest2.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequestRepository.save(itemCostChangeRequest2);

        ItemCostCollectionDetailDto result = customizedItemCostCollectionJpaDao.getItemCostCollectionDto(itemCostCollection.getId());
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getInvalidCount());
    }
}