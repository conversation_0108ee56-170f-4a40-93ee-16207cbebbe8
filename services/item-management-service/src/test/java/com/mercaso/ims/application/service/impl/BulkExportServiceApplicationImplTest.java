package com.mercaso.ims.application.service.impl;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.bulkexportrecords.enums.SendStatus;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.infrastructure.excel.factory.BulkExportStrategyFactory;
import com.mercaso.ims.infrastructure.excel.strategy.BulkExportStrategy;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BulkExportServiceApplicationImplTest {

    @Mock
    private DocumentApplicationService documentApplicationService;

    @Mock
    private BusinessEventService businessEventService;

    @Mock
    private BulkExportRecordsApplicationService bulkExportRecordsApplicationService;

    @Mock
    private BulkExportStrategyFactory exportStrategyFactory;

    @Mock
    private BulkExportStrategy bulkExportStrategy;

    @InjectMocks
    private BulkExportServiceApplicationImpl bulkExportService;

    private String customFilter;
    private ExportType exportType;
    private byte[] mockExcelContent;
    private BulkExportRecordsDto mockBulkExportRecordsDto;
    private DocumentResponse mockDocumentResponse;

    @BeforeEach
    void setUp() {
        customFilter = "{\"status\":\"ACTIVE\"}";
        exportType = ExportType.ITEM;
        mockExcelContent = "test excel content".getBytes();

        mockBulkExportRecordsDto = BulkExportRecordsDto.builder()
                .id(UUID.randomUUID())
                .fileName("documents/test-file.xlsx")
                .customFilter(customFilter)
                .exportType(exportType)
                .sendStatus(SendStatus.IN_PROGRESS)
                .build();

        mockDocumentResponse = DocumentResponse.builder()
                .name("test-file.xlsx")
                .signedUrl("https://example.com/test-file.xlsx")
                .build();
    }

    @Test
    void getBulkReportFile_ShouldGenerateAndSaveReport_WhenValidParameters() {
        // Arrange
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);
        when(bulkExportRecordsApplicationService.save(any(BulkExportRecordsCommand.class)))
                .thenReturn(mockBulkExportRecordsDto);

        // Act
        bulkExportService.getBulkReportFile(customFilter, exportType);

        // Assert
        verify(exportStrategyFactory).getStrategy(exportType);
        verify(bulkExportStrategy).execute(customFilter);
        verify(documentApplicationService).uploadExcel(eq(mockExcelContent), any(String.class));

        ArgumentCaptor<BulkExportRecordsCommand> commandCaptor = ArgumentCaptor.forClass(BulkExportRecordsCommand.class);
        verify(bulkExportRecordsApplicationService).save(commandCaptor.capture());

        BulkExportRecordsCommand capturedCommand = commandCaptor.getValue();
        assertNotNull(capturedCommand);
        assertEquals(customFilter, capturedCommand.getCustomFilter());
        assertEquals(exportType, capturedCommand.getExportType());
        assertEquals(SendStatus.IN_PROGRESS, capturedCommand.getSendStatus());
        assertTrue(capturedCommand.getFileName().startsWith("documents/"));
        assertTrue(capturedCommand.getFileName().endsWith(".xlsx"));
        assertNotNull(capturedCommand.getSearchTime());

        ArgumentCaptor<BulkExportRecordsPayloadDto> payloadCaptor = ArgumentCaptor.forClass(BulkExportRecordsPayloadDto.class);
        verify(businessEventService).dispatch(payloadCaptor.capture());

        BulkExportRecordsPayloadDto capturedPayload = payloadCaptor.getValue();
        assertNotNull(capturedPayload);
        assertEquals(mockBulkExportRecordsDto.getId(), capturedPayload.getBulkExportRecordsId());
        assertEquals(mockBulkExportRecordsDto, capturedPayload.getData());
    }

    @Test
    void getBulkReportFile_ShouldHandleStrategyException_WhenStrategyExecutionFails() {
        // Arrange
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenThrow(new RuntimeException("Strategy execution failed"));

        // Act & Assert
        assertThrows(ImsBusinessException.class,() -> bulkExportService.getBulkReportFile(customFilter, exportType));

        verify(exportStrategyFactory).getStrategy(exportType);
        verify(bulkExportStrategy).execute(customFilter);
        verify(documentApplicationService, never()).uploadExcel(any(), any());
        verify(bulkExportRecordsApplicationService, never()).save(any());
    }

    @Test
    void getBulkReportFile_ShouldHandleDocumentUploadException_WhenUploadFails() {
        // Arrange
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenThrow(new RuntimeException("Upload failed"));

        // Act & Assert
        assertThrows(ImsBusinessException.class,() -> bulkExportService.getBulkReportFile(customFilter, exportType));

        verify(exportStrategyFactory).getStrategy(exportType);
        verify(bulkExportStrategy).execute(customFilter);
        verify(documentApplicationService).uploadExcel(eq(mockExcelContent), any(String.class));
        verify(bulkExportRecordsApplicationService, never()).save(any());
        ArgumentCaptor<BulkExportRecordsPayloadDto> payloadCaptor = ArgumentCaptor.forClass(BulkExportRecordsPayloadDto.class);
        verify(businessEventService,never()).dispatch(payloadCaptor.capture());
    }

    @Test
    void getBulkReportFile_ShouldGenerateCorrectFileName_WithTimestampAndRandomId() {
        // Arrange
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);
        when(bulkExportRecordsApplicationService.save(any(BulkExportRecordsCommand.class)))
                .thenReturn(mockBulkExportRecordsDto);

        // Act
        bulkExportService.getBulkReportFile(customFilter, exportType);

        // Assert
        ArgumentCaptor<String> fileNameCaptor = ArgumentCaptor.forClass(String.class);
        verify(documentApplicationService).uploadExcel(any(byte[].class), fileNameCaptor.capture());

        String capturedFileName = fileNameCaptor.getValue();
        assertNotNull(capturedFileName);
        assertTrue(capturedFileName.contains("Version_"));
        assertTrue(capturedFileName.contains("Custom_Filter_Bulk_Export"));
        assertTrue(capturedFileName.endsWith(".xlsx"));
    }

    @Test
    void getFileName_ShouldReturnCorrectFormat_WhenCalled() throws Exception {
        // Arrange
        Method getFileNameMethod = BulkExportServiceApplicationImpl.class
                .getDeclaredMethod("getFileName", String.class, String.class);
        getFileNameMethod.setAccessible(true);

        String date = "20240101120000";
        String baseName = "Test_Export";

        // Act
        String result = (String) getFileNameMethod.invoke(bulkExportService, date, baseName);

        // Assert
        assertNotNull(result);
        assertTrue(result.startsWith("Version_20240101120000_Test_Export_"));
        assertEquals(5, result.split("_").length); // Version, date, baseName, randomId
    }

    @Test
    void generateBulkExportFile_ShouldReturnExcelContent_WhenSuccessful() throws Exception {
        // Arrange
        Method generateBulkExportFileMethod = BulkExportServiceApplicationImpl.class
                .getDeclaredMethod("generateBulkExportFile", String.class, String.class, ExportType.class);
        generateBulkExportFileMethod.setAccessible(true);

        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);

        // Act
        byte[] result = (byte[]) generateBulkExportFileMethod.invoke(
                bulkExportService, "test-file.xlsx", customFilter, exportType);

        // Assert
        assertNotNull(result);
        assertArrayEquals(mockExcelContent, result);
        verify(exportStrategyFactory).getStrategy(exportType);
        verify(bulkExportStrategy).execute(customFilter);
        verify(documentApplicationService).uploadExcel(mockExcelContent, "test-file.xlsx");
    }

    @Test
    void getBulkReportFile_ShouldUseCorrectExportType_ForDifferentTypes() {
        // Arrange
        ExportType supplierExportType = ExportType.SUPPLIER;
        when(exportStrategyFactory.getStrategy(supplierExportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);
        when(bulkExportRecordsApplicationService.save(any(BulkExportRecordsCommand.class)))
                .thenReturn(mockBulkExportRecordsDto);

        // Act
        bulkExportService.getBulkReportFile(customFilter, supplierExportType);

        // Assert
        verify(exportStrategyFactory).getStrategy(supplierExportType);

        ArgumentCaptor<BulkExportRecordsCommand> commandCaptor = ArgumentCaptor.forClass(BulkExportRecordsCommand.class);
        verify(bulkExportRecordsApplicationService).save(commandCaptor.capture());

        BulkExportRecordsCommand capturedCommand = commandCaptor.getValue();
        assertEquals(supplierExportType, capturedCommand.getExportType());
    }

    @Test
    void getBulkReportFile_ShouldHandleNullCustomFilter() {
        // Arrange
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(null)).thenReturn(mockExcelContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);
        when(bulkExportRecordsApplicationService.save(any(BulkExportRecordsCommand.class)))
                .thenReturn(mockBulkExportRecordsDto);

        // Act
        bulkExportService.getBulkReportFile(null, exportType);

        // Assert
        verify(bulkExportStrategy).execute(null);

        ArgumentCaptor<BulkExportRecordsCommand> commandCaptor = ArgumentCaptor.forClass(BulkExportRecordsCommand.class);
        verify(bulkExportRecordsApplicationService).save(commandCaptor.capture());

        BulkExportRecordsCommand capturedCommand = commandCaptor.getValue();
        assertNull(capturedCommand.getCustomFilter());
    }

    @Test
    void getBulkReportFile_ShouldHandleEmptyExcelContent() {
        // Arrange
        byte[] emptyContent = new byte[0];
        when(exportStrategyFactory.getStrategy(exportType)).thenReturn(bulkExportStrategy);
        when(bulkExportStrategy.execute(customFilter)).thenReturn(emptyContent);
        when(documentApplicationService.uploadExcel(any(byte[].class), any(String.class)))
                .thenReturn(mockDocumentResponse);
        when(bulkExportRecordsApplicationService.save(any(BulkExportRecordsCommand.class)))
                .thenReturn(mockBulkExportRecordsDto);

        // Act
        bulkExportService.getBulkReportFile(customFilter, exportType);

        // Assert
        verify(documentApplicationService).uploadExcel(eq(emptyContent), any(String.class));
        verify(bulkExportRecordsApplicationService).save(any(BulkExportRecordsCommand.class));
        verify(businessEventService).dispatch(any(BulkExportRecordsPayloadDto.class));
    }
}