package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.mapper.itemvendorrebate.ItemVendorRebateDtoApplicationMapper;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemVendorRebateQueryApplicationServiceImpl.class})
class ItemVendorRebateQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private ItemVendorRebateDtoApplicationMapper itemVendorRebateDtoApplicationMapper;

    @Autowired
    private ItemVendorRebateQueryApplicationServiceImpl itemVendorRebateQueryApplicationServiceImpl;

    @MockBean
    private ItemVendorRebateService itemVendorRebateService;


    @Test
    void testFindByVendorItemId() {
        // Arrange
        UUID vendorItemId = UUID.randomUUID();
        when(itemVendorRebateService.findByVendorItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemVendorRebateDto> actualFindByVendorItemIdResult = itemVendorRebateQueryApplicationServiceImpl
            .findByVendorItemId(vendorItemId);

        // Assert
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        assertTrue(actualFindByVendorItemIdResult.isEmpty());
    }
}