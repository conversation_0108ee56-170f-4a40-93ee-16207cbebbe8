package com.mercaso.ims.infrastructure.repository.item.jpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class CustomizedItemRepositoryImplIT extends AbstractIT {

    @Test
    void getItemListDtoListV2_searchBy_like_title_success() {
        ItemDo itemDo = buildItemData("Title_JC160227_test_V2");
        Map<String, String> customFilter = Map.of("title", itemDo.getTitle().substring(0, 3).toLowerCase());
        ItemQuery build = ItemQuery.builder()
            .page(1)
            .pageSize(10)
            .customFilter(customFilter)
            .build();

        List<ItemSerachDto> itemDtoList = customizedItemJpaDao.getItemDtoList(build);

        assertFalse(itemDtoList.isEmpty());
    }

    @Test
    void getItemListDtoListV2_searchBy_vendor_item_sku_number_success() {
        buildItemDataWithVendor("JC160228_test_V2", "vendorName_JC160228_test_V2", "160228_test_V2");
        Map<String, String> customFilter = Map.of("primaryVendorSkuNumber", "160228_test_V2");
        ItemQuery build = ItemQuery.builder()
            .page(1)
            .pageSize(10)
            .customFilter(customFilter)
            .build();

        List<ItemSerachDto> itemDtoList = customizedItemJpaDao.getItemDtoList(build);

        assertEquals(1, itemDtoList.size());

    }

    @Test
    void getItemSeracDtoListV2_searchBy_skuNum_success() {
        String sku = "getItemSeracDtoListV2_" + RandomStringUtils.randomAlphabetic(5);
        Map<String, String> customFilter = Map.of("skuNumber", sku);

        buildItemData(sku);
        ItemQuery itemQuery = ItemQuery.builder()
            .page(1)
            .pageSize(10)
            .customFilter(customFilter)
            .build();
        List<ItemSerachDto> itemDtoList = customizedItemJpaDao.getItemDtoList(itemQuery);

        assertEquals(1, itemDtoList.size());
        assertEquals(Double.valueOf(1.1), itemDtoList.getFirst().getWidth());
    }

}
