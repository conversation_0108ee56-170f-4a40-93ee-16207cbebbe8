package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {FinaleApplicationServiceImpl.class})
class FinaleApplicationServiceImplTest extends AbstractTest {

    @MockBean
    FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @MockBean
    FeatureFlagsManager featureFlagsManager;
    @MockBean
    ItemQueryApplicationService itemQueryApplicationService;
    @MockBean
    PgAdvisoryLock pgAdvisoryLock;
    @Autowired
    FinaleApplicationServiceImpl finaleApplicationServiceImpl;


    @Test
    void testSyncItemToFinale() {
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        finaleApplicationServiceImpl.syncItemToFinaleById(UUID.randomUUID());
        verify(finaleExternalApiAdaptor).getFinaleProduct(anyString(), any(StatusId.class));
    }

    @Test
    void testGetAllSuppliers() {
        // Arrange
        List<FinaleVendorInfoDto> expectedSuppliers = Arrays.asList(
            FinaleVendorInfoDto.builder()
                .partyId("100001")
                .partyUrl("/mercaso/api/partygroup/100001")
                .statusId("PARTY_ENABLED")
                .lastUpdatedDate("2022-09-20T22:08:08")
                .createdDate("2022-09-20T22:08:08")
                .build(),
            FinaleVendorInfoDto.builder()
                .partyId("100002")
                .partyUrl("/mercaso/api/partygroup/100002")
                .statusId("PARTY_ENABLED")
                .lastUpdatedDate("2022-09-20T22:08:25")
                .createdDate("2022-09-20T22:08:25")
                .build()
        );

        when(finaleExternalApiAdaptor.getAllSuppliers()).thenReturn(expectedSuppliers);

        // Act
        List<FinaleVendorInfoDto> result = finaleApplicationServiceImpl.getAllSuppliers();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("100001", result.get(0).getPartyId());
        assertEquals("100002", result.get(1).getPartyId());
        verify(finaleExternalApiAdaptor).getAllSuppliers();
    }
}