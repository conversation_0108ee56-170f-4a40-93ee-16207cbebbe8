package com.mercaso.ims.infrastructure.external.shopify;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto.DataDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto.ProductsDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Image;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.ProductDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Variant;
import com.mercaso.ims.infrastructure.external.shopify.dto.VariantImageDto;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled
class ShopifyAdaptorIT extends AbstractIT {


    @Test
    void testAddShopifyProductAssets() {

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        DataDto dataDto = new DataDto();
        ProductsDto productsDto = new ProductsDto();
        dataDto.setProducts(productsDto);
        ShopifyGraphQLQueryResponseDto build = ShopifyGraphQLQueryResponseDto.builder()
            .data(dataDto)
            .build();
        ShopifyProductDto shopifyProductDto = ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto, build);
        shopifyAdaptor.createShopifyProduct(shopifyProductDto);

        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = shopifyAdaptor.queryProduct("DW168080-12");

        ProductDto product = shopifyProductDto.getProduct();
        List<Variant> variants = product.getVariants();
        for (Variant variant : variants) {
            variant.setPrice(BigDecimal.valueOf(300));
        }

        ShopifyProductDto shopifyProductDtoUpdate = ShopifyRequestDtoFactory.buildShopifyProductDto(itemDto,
            shopifyGraphQLQueryResponseDto);

        shopifyAdaptor.modifyShopifyProduct(shopifyProductDtoUpdate);

        VariantImageDto build1 = VariantImageDto.builder()
            .image(Image.builder()
                .src(
                    "https://files.plytix.com/api/v1.1/file/public_files/pim/assets/c4/c4/a3/62/62a3c4c45d155f1bc7e49a42/images/e0/a1/62/66/6662a1e0c9d9290bf3ece5ee/DW06118-20.png")
                .productId(shopifyProductDtoUpdate.getProduct().getId())
                .variantIds(shopifyProductDtoUpdate.getProduct().getVariants().stream().map(Variant::getId).toList())
                .build()).build();
        VariantImageDto variantImageDto = shopifyAdaptor.modifyShopifyProductImage(build1);

        shopifyAdaptor.deleteShopifyProduct(shopifyProductDtoUpdate.getProduct().getId());
        assertNotNull(variantImageDto);


    }
}
