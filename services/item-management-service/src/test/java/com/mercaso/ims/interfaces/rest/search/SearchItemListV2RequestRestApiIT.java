package com.mercaso.ims.interfaces.rest.search;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemListDto;
import org.junit.jupiter.api.Test;

class SearchItemListV2RequestRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchItemListV2ByUpdateAtRequest() throws Exception {

        int pageSize = 10;
        for (int i = 0; i < pageSize; i++) {
            buildItemDataWithVendor("JC66451_update_at_test" + i,
                "vendorName_JC66451_update_at_test",
                "66451_update_at_test" + i);
        }
        ItemListDto itemListDto = searchItemRequestRestApiUtil.searchItemListV2ByUpdateAtRequest(pageSize);
        assertNotNull(itemListDto);
        assertEquals(pageSize, itemListDto.getData().size());
    }


}