package com.mercaso.ims.interfaces;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.api.client.util.Lists;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.infrastructure.util.FileUtil;
import java.io.File;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.textract.model.ExpenseDocument;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisResponse;

@Disabled
class AnalyzeVendorPoRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenUploadAnalyzeDowneyFileRequest() throws Exception {

        ExpenseDocument build = ExpenseDocument.builder().summaryFields(Lists.newArrayList()).build();
        GetExpenseAnalysisResponse analyzeExpenseResponse = GetExpenseAnalysisResponse.builder().expenseDocuments(build).build();

        String fileName = getFilePath();

        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("DW120424631426.pdf");
        documentResponse.setSignedUrl("testUrl");
        when(documentOperations.uploadDocument(any())).thenReturn(documentResponse);
        when(documentOperations.downloadDocument(any())).thenReturn(FileUtil.readFileToByteArray(new File(fileName)));
        when(awsAnalyzeExpenseAdaptor.analyzeExpense(any())).thenReturn(analyzeExpenseResponse);

        analyzeVendorPoRestApiUtil.uploadAnalyzeFile(new File(fileName));
        verify(awsAnalyzeExpenseAdaptor).analyzeExpense(any());

    }

    private String getFilePath() {
        return getClass().getClassLoader().getResource("pdf/DW120424631426.pdf").getPath();

    }

}