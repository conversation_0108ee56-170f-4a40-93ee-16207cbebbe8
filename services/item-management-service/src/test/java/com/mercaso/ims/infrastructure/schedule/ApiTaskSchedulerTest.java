package com.mercaso.ims.infrastructure.schedule;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.service.ApiTaskQueueService;
import com.mercaso.ims.infrastructure.apitaskprocess.ApiTaskProcessor;
import com.mercaso.ims.infrastructure.apitaskprocess.ApiTaskProcessorRegistry;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ApiTaskSchedulerTest {

    @Mock
    private ApiTaskQueueService apiTaskQueueService;

    @Mock
    private List<ApiTaskProcessor<?>> taskProcessors;

    @Mock
    private ApiTaskProcessorRegistry processorRegistry;

    @Mock
    private RateLimiterRegistry rateLimiterRegistry;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private EntityManagerFactory managerFactory;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ApiTaskScheduler apiTaskScheduler;

    private EntityManager entityManager;
    private ApiTaskQueue mockTask;
    @SuppressWarnings("unchecked")
    private ApiTaskProcessor<Object> mockProcessor;
    private RateLimiter mockRateLimiter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        entityManager = Mockito.mock(EntityManager.class);
        mockProcessor = Mockito.mock(ApiTaskProcessor.class);
        mockRateLimiter = Mockito.mock(RateLimiter.class);

        // Setup common mock behavior
        when(managerFactory.createEntityManager()).thenReturn(entityManager);
        when(rateLimiterRegistry.rateLimiter(anyString())).thenReturn(mockRateLimiter);

        // Setup default rate limiter behavior to execute runnables
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(mockRateLimiter).executeRunnable(any(Runnable.class));

        // Create mock task
        mockTask = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products/123")
                .httpMethod("GET")
                .requestPayload("{\"productId\":\"123\"}")
                .status(TaskStatus.PENDING)
                .priority(1)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now().minusSeconds(60))
                .build();
    }

    @Test
    void testProcessTasks_LockNotAcquired() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(false);

        apiTaskScheduler.processTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, never()).getExecutableTasks(anyInt());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_Success() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(mockTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(mockProcessor.needsResponse()).thenReturn(true);

        try {
            when(mockProcessor.executeTaskWithLogging(mockTask)).thenReturn("success response");
            when(objectMapper.writeValueAsString("success response")).thenReturn("{\"result\":\"success\"}");
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).getExecutableTasks(50);
        verify(processorRegistry, times(1)).findProcessorForTaskType("FINALE_GET_PRODUCT");
        verify(apiTaskQueueService, times(1)).markTaskAsStarted(mockTask.getId());
        verify(apiTaskQueueService, times(1)).markTaskAsCompleted(eq(mockTask.getId()), anyString());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_NoExecutableTasks() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Collections.emptyList());

        apiTaskScheduler.processTasks();

        verify(apiTaskQueueService, times(1)).getExecutableTasks(50);
        verify(processorRegistry, never()).findProcessorForTaskType(anyString());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_TaskWithNullTaskType() {
        ApiTaskQueue taskWithNullType = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType(null)
                .build();

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(taskWithNullType));

        apiTaskScheduler.processTasks();

        verify(processorRegistry, never()).findProcessorForTaskType(anyString());
        verify(apiTaskQueueService, never()).markTaskAsStarted(any());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_NoProcessorFound() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(mockTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(null);

        apiTaskScheduler.processTasks();

        verify(processorRegistry, times(1)).findProcessorForTaskType("FINALE_GET_PRODUCT");
        verify(apiTaskQueueService, never()).markTaskAsStarted(any());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_RateLimitExceeded() {
        RequestNotPermitted requestNotPermitted = Mockito.mock(RequestNotPermitted.class);

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(mockTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");

        doThrow(requestNotPermitted)
                .when(mockRateLimiter).executeRunnable(any(Runnable.class));

        apiTaskScheduler.processTasks();

        // When rate limit is exceeded, the task is not started, so we shouldn't expect markTaskAsStarted to be called
        verify(apiTaskQueueService, never()).markTaskAsStarted(any());
        verify(apiTaskQueueService, never()).markTaskAsCompleted(any(), anyString());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testResetStuckTasks_LockNotAcquired() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(false);

        apiTaskScheduler.resetStuckTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, never()).resetStuckTasks(any(Duration.class));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testResetStuckTasks_Success() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.resetStuckTasks(any(Duration.class))).thenReturn(5);

        apiTaskScheduler.resetStuckTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).resetStuckTasks(Duration.ofMinutes(30));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testResetStuckTasks_NoStuckTasks() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.resetStuckTasks(any(Duration.class))).thenReturn(0);

        apiTaskScheduler.resetStuckTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).resetStuckTasks(Duration.ofMinutes(30));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testResetStuckTasks_Exception() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.resetStuckTasks(any(Duration.class)))
                .thenThrow(new RuntimeException("Database error"));

        apiTaskScheduler.resetStuckTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).resetStuckTasks(Duration.ofMinutes(30));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testCleanupCompletedTasks_LockNotAcquired() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(false);

        apiTaskScheduler.cleanupCompletedTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, never()).cleanupCompletedTasks(any(Duration.class));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testCleanupCompletedTasks_Success() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.cleanupCompletedTasks(any(Duration.class))).thenReturn(10);

        apiTaskScheduler.cleanupCompletedTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).cleanupCompletedTasks(Duration.ofDays(7));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testCleanupCompletedTasks_NoTasksToCleanup() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.cleanupCompletedTasks(any(Duration.class))).thenReturn(0);

        apiTaskScheduler.cleanupCompletedTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).cleanupCompletedTasks(Duration.ofDays(7));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testCleanupCompletedTasks_Exception() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.cleanupCompletedTasks(any(Duration.class)))
                .thenThrow(new RuntimeException("Database error"));

        apiTaskScheduler.cleanupCompletedTasks();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(apiTaskQueueService, times(1)).cleanupCompletedTasks(Duration.ofDays(7));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testInitProcessorMap_Success() {
        ApiTaskProcessor<?> processor1 = Mockito.mock(ApiTaskProcessor.class);
        ApiTaskProcessor<?> processor2 = Mockito.mock(ApiTaskProcessor.class);

        when(processor1.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(processor2.getTaskType()).thenReturn("FINALE_UPDATE_ITEM");

        // Mock the taskProcessors list to return the processors
        when(taskProcessors.stream()).thenReturn(Arrays.asList(processor1, processor2).stream());

        apiTaskScheduler.initProcessorMap();

        // Verify that the method can be called multiple times without issues
        apiTaskScheduler.initProcessorMap();

        // Verify the processors were accessed (only once per call since the map is built once)
        verify(processor1, atLeastOnce()).getTaskType();
        verify(processor2, atLeastOnce()).getTaskType();
    }

    @Test
    void testProcessTasks_TaskExecutionFailureWithRetry() {
        // Create a mock task that hasn't reached max retries
        ApiTaskQueue retryableTask = Mockito.mock(ApiTaskQueue.class);
        UUID taskId = UUID.randomUUID();

        when(retryableTask.getId()).thenReturn(taskId);
        when(retryableTask.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(retryableTask.hasReachedMaxRetries()).thenReturn(false);
        when(retryableTask.getCurrentRetryCount()).thenReturn(1);

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(retryableTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(mockProcessor.isRetryableException(any(Exception.class))).thenReturn(true);
        when(mockProcessor.calculateRetryDelay(anyInt())).thenReturn(60L);

        try {
            when(mockProcessor.executeTaskWithLogging(retryableTask))
                    .thenThrow(new RuntimeException("API call failed"));
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        verify(apiTaskQueueService, times(1)).markTaskAsStarted(taskId);
        verify(apiTaskQueueService, atLeastOnce()).markTaskForRetry(eq(taskId), anyString(), any(Duration.class));
        verify(apiTaskQueueService, never()).markTaskAsFailed(any(), anyString());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_TaskExecutionFailurePermanent() {
        // Create a mock task that has reached max retries
        ApiTaskQueue failedTask = Mockito.mock(ApiTaskQueue.class);
        UUID taskId = UUID.randomUUID();

        when(failedTask.getId()).thenReturn(taskId);
        when(failedTask.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(failedTask.hasReachedMaxRetries()).thenReturn(true);
        when(failedTask.getCurrentRetryCount()).thenReturn(3);

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(failedTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(mockProcessor.isRetryableException(any(Exception.class))).thenReturn(true);

        try {
            when(mockProcessor.executeTaskWithLogging(failedTask))
                    .thenThrow(new RuntimeException("Permanent failure"));
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        verify(apiTaskQueueService, times(1)).markTaskAsStarted(taskId);
        verify(apiTaskQueueService, atLeastOnce()).markTaskAsFailed(eq(taskId), anyString());
        verify(apiTaskQueueService, never()).markTaskForRetry(any(), anyString(), any(Duration.class));
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_TaskWithoutResponse() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(mockTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(mockProcessor.needsResponse()).thenReturn(false);

        try {
            when(mockProcessor.executeTaskWithLogging(mockTask)).thenReturn("success response");
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        verify(apiTaskQueueService, times(1)).markTaskAsStarted(mockTask.getId());
        verify(apiTaskQueueService, times(1)).markTaskAsCompleted(mockTask.getId(), null);
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_UnknownTaskTypeUsesDefaultRateLimiter() {
        ApiTaskQueue unknownTask = ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("UNKNOWN_TASK_TYPE")
                .apiEndpoint("/api/unknown")
                .httpMethod("GET")
                .requestPayload("{}")
                .status(TaskStatus.PENDING)
                .build();

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(unknownTask));
        when(processorRegistry.findProcessorForTaskType("UNKNOWN_TASK_TYPE")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("UNKNOWN_TASK_TYPE");
        when(mockProcessor.needsResponse()).thenReturn(false);

        try {
            when(mockProcessor.executeTaskWithLogging(unknownTask)).thenReturn("success");
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        verify(rateLimiterRegistry, times(1)).rateLimiter(anyString());
        verify(apiTaskQueueService, times(1)).markTaskAsCompleted(unknownTask.getId(), null);
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }

    @Test
    void testProcessTasks_ImsBusinessExceptionUnwrappingForRetry() {
        // Create a mock task that hasn't reached max retries
        ApiTaskQueue retryableTask = Mockito.mock(ApiTaskQueue.class);
        UUID taskId = UUID.randomUUID();

        when(retryableTask.getId()).thenReturn(taskId);
        when(retryableTask.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(retryableTask.hasReachedMaxRetries()).thenReturn(false);
        when(retryableTask.getCurrentRetryCount()).thenReturn(1);

        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString()))
                .thenReturn(true);
        when(apiTaskQueueService.getExecutableTasks(50)).thenReturn(Arrays.asList(retryableTask));
        when(processorRegistry.findProcessorForTaskType("FINALE_GET_PRODUCT")).thenReturn(mockProcessor);
        when(mockProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        
        // Set up processor to consider RuntimeException (underlying cause) as retryable
        when(mockProcessor.isRetryableException(any(RuntimeException.class))).thenReturn(true);
        when(mockProcessor.isRetryableException(any(ImsBusinessException.class))).thenReturn(false);
        when(mockProcessor.calculateRetryDelay(anyInt())).thenReturn(60L);

        // Simulate the AbstractApiTaskProcessor wrapping an exception in ImsBusinessException
        RuntimeException underlyingException = new RuntimeException("Network timeout");
        ImsBusinessException wrappedException = new ImsBusinessException("Task execution failed", underlyingException);

        try {
            when(mockProcessor.executeTaskWithLogging(retryableTask))
                    .thenThrow(wrappedException);
        } catch (Exception e) {
            // This won't happen in the test
        }

        apiTaskScheduler.processTasks();

        // Verify that the task was scheduled for retry because the underlying cause (RuntimeException) is retryable
        verify(apiTaskQueueService, times(1)).markTaskAsStarted(taskId);
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(entityManager, times(1)).close();
    }
}
