package com.mercaso.ims.interfaces.rest.search;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemCostChangeRequestListDto;
import com.mercaso.ims.application.dto.ItemCostCollectionListDto;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.itemcostchangerequest.ItemCostChangeRequestUtil;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SearchItemCostCollectionRequestRestApiIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSearchItemAdjustmentRequest() throws Exception {
        VendorDo vendorDo = buildVendorDoData(DOWNEY_WHOLESALE);

        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setVendorId(vendorDo.getId());
        itemCostCollection.setVendorName(vendorDo.getVendorName());
        itemCostCollectionRepository.save(itemCostCollection);
        ItemCostCollectionListDto itemCostCollectionListDto = searchItemCostCollectionRestApiUtil.searchItemCostCollection(null,
            null,
            vendorDo.getId());

        Assertions.assertNotNull(itemCostCollectionListDto);

    }

    @Test
    void shouldSuccessWhenSearchItemCostRequestList() throws Exception {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection = itemCostCollectionRepository.save(itemCostCollection);
        ItemCostChangeRequest itemCostChangeRequest1 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest1.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequestRepository.save(itemCostChangeRequest1);
        ItemCostChangeRequest itemCostChangeRequest2 = ItemCostChangeRequestUtil.buildItemCostChangeRequest();
        itemCostChangeRequest2.setItemCostCollectionId(itemCostCollection.getId());
        itemCostChangeRequestRepository.save(itemCostChangeRequest2);
        ItemCostChangeRequestListDto itemCostCollectionListDto = searchItemCostCollectionRestApiUtil.searchItemCostRequestList(
            itemCostCollection.getId());

        Assertions.assertNotNull(itemCostCollectionListDto);
        Assertions.assertEquals(2, itemCostCollectionListDto.getTotalCount());

    }


}