package com.mercaso.ims.infrastructure.repository.attribute;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.enums.AttributeFormat;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.AttributeJpaDao;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.dataobject.AttributeDo;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.mapper.AttributeDoMapper;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {AttributeRepositoryImpl.class})
class AttributeRepositoryImplTest extends AbstractTest {

    @MockBean
    private AttributeDoMapper attributeDoMapper;

    @MockBean
    private AttributeJpaDao attributeJpaDao;

    @Autowired
    private AttributeRepositoryImpl attributeRepositoryImpl;


    @Test
    void testSave() {
        // Arrange
        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        when(attributeDoMapper.doToDomain(Mockito.<AttributeDo>any())).thenReturn(null);
        when(attributeDoMapper.domainToDo(Mockito.<Attribute>any())).thenReturn(attributeDo);

        AttributeDo attributeDo2 = new AttributeDo();
        attributeDo2.setAttributeEnumValues(new ArrayList<>());
        attributeDo2.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo2.setCategoryId(UUID.randomUUID());
        attributeDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo2.setCreatedUserName("janedoe");
        attributeDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo2.setDeletedUserName("janedoe");
        attributeDo2.setDescription("The characteristics of someone or something");
        attributeDo2.setId(UUID.randomUUID());
        attributeDo2.setName("Name");
        attributeDo2.setStatus(AttributeStatus.DRAFT);
        attributeDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setUpdatedBy("2020-03-01");
        attributeDo2.setUpdatedUserName("janedoe");
        when(attributeJpaDao.save(Mockito.<AttributeDo>any())).thenReturn(attributeDo2);

        // Act
        Attribute actualSaveResult = attributeRepositoryImpl.save(null);

        // Assert
        verify(attributeDoMapper).doToDomain(isA(AttributeDo.class));
        verify(attributeDoMapper).domainToDo(isNull());
        verify(attributeJpaDao).save(isA(AttributeDo.class));
        assertNull(actualSaveResult);
    }


    @Test
    void testSave2() {
        // Arrange
        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        when(attributeDoMapper.domainToDo(Mockito.<Attribute>any())).thenReturn(attributeDo);
        when(attributeJpaDao.save(Mockito.<AttributeDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> attributeRepositoryImpl.save(null));
        verify(attributeDoMapper).domainToDo(isNull());
        verify(attributeJpaDao).save(isA(AttributeDo.class));
    }


    @Test
    void testFindById() {
        // Arrange
        when(attributeDoMapper.doToDomain(Mockito.<AttributeDo>any())).thenReturn(null);

        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        Optional<AttributeDo> ofResult = Optional.of(attributeDo);
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        Attribute actualFindByIdResult = attributeRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(attributeDoMapper).doToDomain(isA(AttributeDo.class));
        verify(attributeJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindById2() {
        // Arrange
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID id = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeRepositoryImpl.findById(id));
        verify(attributeJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdate() {
        // Arrange
        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        when(attributeDoMapper.doToDomain(Mockito.<AttributeDo>any())).thenReturn(null);
        when(attributeDoMapper.domainToDo(Mockito.<Attribute>any())).thenReturn(attributeDo);

        AttributeDo attributeDo2 = new AttributeDo();
        attributeDo2.setAttributeEnumValues(new ArrayList<>());
        attributeDo2.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo2.setCategoryId(UUID.randomUUID());
        attributeDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo2.setCreatedUserName("janedoe");
        attributeDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo2.setDeletedUserName("janedoe");
        attributeDo2.setDescription("The characteristics of someone or something");
        attributeDo2.setId(UUID.randomUUID());
        attributeDo2.setName("Name");
        attributeDo2.setStatus(AttributeStatus.DRAFT);
        attributeDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setUpdatedBy("2020-03-01");
        attributeDo2.setUpdatedUserName("janedoe");
        Optional<AttributeDo> ofResult = Optional.of(attributeDo2);

        AttributeDo attributeDo3 = new AttributeDo();
        attributeDo3.setAttributeEnumValues(new ArrayList<>());
        attributeDo3.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo3.setCategoryId(UUID.randomUUID());
        attributeDo3.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo3.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo3.setCreatedUserName("janedoe");
        attributeDo3.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo3.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo3.setDeletedUserName("janedoe");
        attributeDo3.setDescription("The characteristics of someone or something");
        attributeDo3.setId(UUID.randomUUID());
        attributeDo3.setName("Name");
        attributeDo3.setStatus(AttributeStatus.DRAFT);
        attributeDo3.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo3.setUpdatedBy("2020-03-01");
        attributeDo3.setUpdatedUserName("janedoe");
        when(attributeJpaDao.save(Mockito.<AttributeDo>any())).thenReturn(attributeDo3);
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        Attribute domain = mock(Attribute.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        Attribute actualUpdateResult = attributeRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(attributeDoMapper).doToDomain(isA(AttributeDo.class));
        verify(attributeDoMapper).domainToDo(isA(Attribute.class));
        verify(attributeJpaDao).findById(isA(UUID.class));
        verify(attributeJpaDao).save(isA(AttributeDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testDeleteById() {
        // Arrange
        when(attributeDoMapper.doToDomain(Mockito.<AttributeDo>any())).thenReturn(null);

        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        Optional<AttributeDo> ofResult = Optional.of(attributeDo);

        AttributeDo attributeDo2 = new AttributeDo();
        attributeDo2.setAttributeEnumValues(new ArrayList<>());
        attributeDo2.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo2.setCategoryId(UUID.randomUUID());
        attributeDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo2.setCreatedUserName("janedoe");
        attributeDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo2.setDeletedUserName("janedoe");
        attributeDo2.setDescription("The characteristics of someone or something");
        attributeDo2.setId(UUID.randomUUID());
        attributeDo2.setName("Name");
        attributeDo2.setStatus(AttributeStatus.DRAFT);
        attributeDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo2.setUpdatedBy("2020-03-01");
        attributeDo2.setUpdatedUserName("janedoe");
        when(attributeJpaDao.save(Mockito.<AttributeDo>any())).thenReturn(attributeDo2);
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        UUID uuid = UUID.randomUUID();
        Attribute actualDeleteByIdResult = attributeRepositoryImpl.deleteById(uuid);

        // Assert
        verify(attributeDoMapper).doToDomain(isA(AttributeDo.class));
        verify(attributeJpaDao).findById(isA(UUID.class));
        verify(attributeJpaDao).save(isA(AttributeDo.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById2() {
        // Arrange
        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        Optional<AttributeDo> ofResult = Optional.of(attributeDo);
        when(attributeJpaDao.save(Mockito.<AttributeDo>any())).thenThrow(new ImsBusinessException("Code"));
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> attributeRepositoryImpl.deleteById(uuid));
        verify(attributeJpaDao).findById(isA(UUID.class));
        verify(attributeJpaDao).save(isA(AttributeDo.class));
    }


    @Test
    void testDeleteById3() {
        // Arrange
        Optional<AttributeDo> emptyResult = Optional.empty();
        when(attributeJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        Attribute actualDeleteByIdResult = attributeRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(attributeJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testFindByName() {
        // Arrange
        when(attributeJpaDao.findByName(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<Attribute> actualFindByNameResult = attributeRepositoryImpl.findByName("Name");

        // Assert
        verify(attributeJpaDao).findByName("Name");
        assertTrue(actualFindByNameResult.isEmpty());
    }


    @Test
    void testFindByCategoryIdAndName() {
        // Arrange
        when(attributeDoMapper.doToDomain(Mockito.<AttributeDo>any())).thenReturn(null);

        AttributeDo attributeDo = new AttributeDo();
        attributeDo.setAttributeEnumValues(new ArrayList<>());
        attributeDo.setAttributeFormat(AttributeFormat.TEXT);
        attributeDo.setCategoryId(UUID.randomUUID());
        attributeDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeDo.setCreatedUserName("janedoe");
        attributeDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeDo.setDeletedUserName("janedoe");
        attributeDo.setDescription("The characteristics of someone or something");
        attributeDo.setId(UUID.randomUUID());
        attributeDo.setName("Name");
        attributeDo.setStatus(AttributeStatus.DRAFT);
        attributeDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeDo.setUpdatedBy("2020-03-01");
        attributeDo.setUpdatedUserName("janedoe");
        when(attributeJpaDao.findByCategoryIdAndName(Mockito.<UUID>any(), Mockito.<String>any())).thenReturn(attributeDo);

        // Act
        Attribute actualFindByCategoryIdAndNameResult = attributeRepositoryImpl.findByCategoryIdAndName(UUID.randomUUID(),
            "Name");

        // Assert
        verify(attributeDoMapper).doToDomain(isA(AttributeDo.class));
        verify(attributeJpaDao).findByCategoryIdAndName(isA(UUID.class), eq("Name"));
        assertNull(actualFindByCategoryIdAndNameResult);
    }


    @Test
    void testFindByCategoryIdAndName2() {
        // Arrange
        when(attributeJpaDao.findByCategoryIdAndName(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenThrow(new ImsBusinessException("Code"));
        UUID id = UUID.randomUUID();

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> attributeRepositoryImpl.findByCategoryIdAndName(id, "Name"));
        verify(attributeJpaDao).findByCategoryIdAndName(isA(UUID.class), eq("Name"));
    }


    @Test
    void testFindByCategoryId() {
        // Arrange
        when(attributeJpaDao.findByCategoryId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<Attribute> actualFindByCategoryIdResult = attributeRepositoryImpl.findByCategoryId(UUID.randomUUID());

        // Assert
        verify(attributeJpaDao).findByCategoryId(isA(UUID.class));
        assertTrue(actualFindByCategoryIdResult.isEmpty());
    }


    @Test
    void testFindAllByIdIn() {
        // Arrange
        when(attributeJpaDao.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        // Act
        List<Attribute> actualFindAllByIdInResult = attributeRepositoryImpl.findAllByIdIn(new ArrayList<>());

        // Assert
        verify(attributeJpaDao).findAllByIdIn(isA(List.class));
        assertTrue(actualFindAllByIdInResult.isEmpty());
    }


    @Test
    void testFindAll() {
        // Arrange
        when(attributeJpaDao.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<Attribute> actualFindAllResult = attributeRepositoryImpl.findAll();

        // Assert
        verify(attributeJpaDao).findAll();
        assertTrue(actualFindAllResult.isEmpty());
    }


    @Test
    void testFindByFuzzyName() {
        // Arrange
        when(attributeJpaDao.findByNameContainsIgnoreCase(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<Attribute> actualFindByFuzzyNameResult = attributeRepositoryImpl.findByFuzzyName("Name");

        // Assert
        verify(attributeJpaDao).findByNameContainsIgnoreCase("Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
    }
}
