package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.mapper.brand.BrandDtoApplicationMapper;
import com.mercaso.ims.domain.brand.service.BrandService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {BrandQueryApplicationServiceImpl.class})
class BrandQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private BrandDtoApplicationMapper brandDtoApplicationMapper;

    @Autowired
    private BrandQueryApplicationServiceImpl brandQueryApplicationServiceImpl;

    @MockBean
    private BrandService brandService;

    @Test
    void testSearchOrFilterBrands() {
        // Arrange
        when(brandService.findByFuzzyName(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<BrandDto> actualSearchOrFilterBrandsResult = brandQueryApplicationServiceImpl.queryOrFilterBrands("Name");

        // Assert
        verify(brandService).findByFuzzyName("Name");
        assertTrue(actualSearchOrFilterBrandsResult.isEmpty());
    }


    @Test
    void testSearchOrFilterBrandsAssert() {
        // Arrange
        when(brandService.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<BrandDto> actualSearchOrFilterBrandsResult = brandQueryApplicationServiceImpl.queryOrFilterBrands("");

        // Assert
        verify(brandService).findAll();
        assertTrue(actualSearchOrFilterBrandsResult.isEmpty());
    }
}
