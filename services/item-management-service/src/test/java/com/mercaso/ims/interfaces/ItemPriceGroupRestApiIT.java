package com.mercaso.ims.interfaces;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.BatchBindingItemToPriceGroupCommand;
import com.mercaso.ims.application.command.BatchUnbindingItemFromPriceGroupCommand;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.command.UpdateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.BatchBindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.BatchUnbindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ItemPriceGroupRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenCreateItemPriceGroup() throws Exception {
        String groupName = RandomStringUtils.randomAlphabetic(5);

        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();

        ItemPriceGroupDto result = itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);

        ItemPriceGroup itemPriceGroup = itemPriceGroupRepository.findById(result.getId());

        Assertions.assertNotNull(itemPriceGroup);
        Assertions.assertEquals(groupName, itemPriceGroup.getGroupName());
    }


    @Test
    void shouldSuccessWhenUpdateItemPriceGroup() throws Exception {
        String groupName = RandomStringUtils.randomAlphabetic(5);
        BigDecimal newPrice = BigDecimal.valueOf(12);

        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();

        ItemPriceGroupDto savedItemPriceGroup = itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);

        UpdateItemPriceGroupCommand updateItemPriceGroupCommand = UpdateItemPriceGroupCommand.builder()
            .id(savedItemPriceGroup.getId())
            .groupName(groupName)
            .price(newPrice).build();
        ItemPriceGroupDto result = itemPriceGroupRestApiUtil.updateItemPriceGroup(updateItemPriceGroupCommand);
        ItemPriceGroup itemPriceGroup = itemPriceGroupRepository.findById(result.getId());

        Assertions.assertNotNull(itemPriceGroup);
        Assertions.assertEquals(groupName, itemPriceGroup.getGroupName());
        Assertions.assertEquals(newPrice, itemPriceGroup.getPrice());
    }

    @Test
    void shouldSuccessWhenGetItemPriceGroup() throws Exception {
        Mockito.when(imsAlertConfig.getMaximumMargin()).thenReturn(BigDecimal.valueOf(0.5));
        Mockito.when(imsAlertConfig.getMinimumMargin()).thenReturn(BigDecimal.valueOf(0));
        Mockito.when(imsAlertConfig.getMaximumPrice()).thenReturn(BigDecimal.valueOf(500));
        Mockito.when(imsAlertConfig.getMinimumPrice()).thenReturn(BigDecimal.valueOf(1));
        String groupName = RandomStringUtils.randomAlphabetic(5);

        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();

        ItemPriceGroupDto savedItemPriceGroup = itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);

        String sku = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo1 = buildItemData(sku);

        BatchBindingItemToPriceGroupCommand bindingCommand = BatchBindingItemToPriceGroupCommand.builder()
            .itemPriceGroupId(savedItemPriceGroup.getId())
            .itemIds(Arrays.asList(itemDo1.getId())).build();

        itemPriceGroupRestApiUtil.batchBindingItemPriceGroup(bindingCommand);

        ItemPriceGroupDto result = itemPriceGroupRestApiUtil.getItemPriceGroupDetail(savedItemPriceGroup.getId());

        Assertions.assertNotNull(result);
        Assertions.assertEquals(groupName, result.getGroupName());
        Assertions.assertEquals(sku, result.getItems().getFirst().getSkuNumber());
    }


    @Test
    void shouldSuccessWhenBatchBindingItemPriceGroup() throws Exception {
        Mockito.when(imsAlertConfig.getMaximumMargin()).thenReturn(BigDecimal.valueOf(0.5));
        Mockito.when(imsAlertConfig.getMinimumMargin()).thenReturn(BigDecimal.valueOf(0));
        Mockito.when(imsAlertConfig.getMaximumPrice()).thenReturn(BigDecimal.valueOf(500));
        Mockito.when(imsAlertConfig.getMinimumPrice()).thenReturn(BigDecimal.valueOf(1));
        String sku1 = RandomStringUtils.randomAlphabetic(8);

        String vendorItemNumber = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo = buildItemDataWithVendor(sku1, DOWNEY_WHOLESALE, vendorItemNumber);
        ItemPriceGroup itemPriceGroup = buildItemPriceGroup();

        BatchBindingItemToPriceGroupCommand command = BatchBindingItemToPriceGroupCommand.builder()
            .itemPriceGroupId(itemPriceGroup.getId())
            .itemIds(Arrays.asList(itemDo.getId())).build();

        BatchBindingItemPriceGroupResultDto result = itemPriceGroupRestApiUtil.batchBindingItemPriceGroup(command);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(0, result.getFailedSkuNumbers().size());

        ItemRegPrice regPrice = itemRegPriceService.findByItemId(itemDo.getId());
        Assertions.assertEquals(itemPriceGroup.getId(), regPrice.getItemPriceGroupId());
        Assertions.assertEquals(0, regPrice.getRegPrice().compareTo(itemPriceGroup.getPrice()));

    }


    @Test
    void shouldSuccessWhenBatchUnbindingItemPriceGroup() throws Exception {
        Mockito.when(imsAlertConfig.getMaximumMargin()).thenReturn(BigDecimal.valueOf(0.5));
        Mockito.when(imsAlertConfig.getMinimumMargin()).thenReturn(BigDecimal.valueOf(0));
        Mockito.when(imsAlertConfig.getMaximumPrice()).thenReturn(BigDecimal.valueOf(500));
        Mockito.when(imsAlertConfig.getMinimumPrice()).thenReturn(BigDecimal.valueOf(1));
        String sku1 = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo1 = buildItemData(sku1);

        ItemPriceGroup itemPriceGroup = buildItemPriceGroup();

        BatchBindingItemToPriceGroupCommand bindingCommand = BatchBindingItemToPriceGroupCommand.builder()
            .itemPriceGroupId(itemPriceGroup.getId())
            .itemIds(Collections.singletonList(itemDo1.getId())).build();

        itemPriceGroupRestApiUtil.batchBindingItemPriceGroup(bindingCommand);

        BatchUnbindingItemFromPriceGroupCommand command = BatchUnbindingItemFromPriceGroupCommand.builder()
            .itemPriceGroupId(itemPriceGroup.getId())
            .itemIds(Collections.singletonList(itemDo1.getId())).build();
        BatchUnbindingItemPriceGroupResultDto resultDto = itemPriceGroupRestApiUtil.batchUnbindingItemPriceGroup(command);
        Assertions.assertEquals(1, resultDto.getUpdatedCount());

        ItemRegPrice result = itemRegPriceService.findByItemId(itemDo1.getId());

        Assertions.assertNotNull(result);
        Assertions.assertNull(result.getItemPriceGroupId());
    }

    @Test
    void shouldSuccessWhenDeleteItemPriceGroup() throws Exception {
        String groupName = RandomStringUtils.randomAlphabetic(5);
        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();
        ItemPriceGroupDto savedItemPriceGroup = itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);
        
        ItemPriceGroup itemPriceGroup = itemPriceGroupRepository.findById(savedItemPriceGroup.getId());
        Assertions.assertNotNull(itemPriceGroup);
        
        itemPriceGroupRestApiUtil.deleteItemPriceGroup(savedItemPriceGroup.getId());
        
        ItemPriceGroup deletedItemPriceGroup = itemPriceGroupRepository.findById(savedItemPriceGroup.getId());
        Assertions.assertNull(deletedItemPriceGroup);
    }

}
