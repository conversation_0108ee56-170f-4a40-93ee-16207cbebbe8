package com.mercaso.ims.infrastructure.apitaskprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.domain.taskqueue.service.ApiTaskQueueService;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.lang.reflect.Method;
import java.util.UUID;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

/**
 * Unit tests for TaskRouter
 */
@ContextConfiguration(classes = {TaskRouter.class})
class TaskRouterTest extends AbstractTest {

    @MockBean
    private SynchronousTaskProcessor synchronousTaskProcessor;

    @MockBean
    private ApiTaskQueueService apiTaskQueueService;

    @MockBean
    private ObjectMapper objectMapper;

    @Autowired
    private TaskRouter taskRouter;

    private ProceedingJoinPoint mockJoinPoint;
    private MethodSignature mockSignature;
    private Method mockMethod;
    private RateLimitedTask mockAnnotation;
    private ApiTaskQueue mockTask;

    @BeforeEach
    void setUp() {
        // Mock basic components
        mockJoinPoint = mock(ProceedingJoinPoint.class);
        mockSignature = mock(MethodSignature.class);
        mockMethod = mock(Method.class);
        mockAnnotation = mock(RateLimitedTask.class);
        mockTask = mock(ApiTaskQueue.class);

        // Setup common method signature mocks
        when(mockJoinPoint.getSignature()).thenReturn(mockSignature);
        when(mockSignature.getMethod()).thenReturn(mockMethod);
        when(mockMethod.getDeclaringClass()).thenReturn((Class) TestClass.class);
        when(mockMethod.getName()).thenReturn("testMethod");
        when(mockSignature.getParameterNames()).thenReturn(new String[]{"param1", "param2"});
        when(mockJoinPoint.getArgs()).thenReturn(new Object[]{"value1", "value2"});

        // Setup common annotation mocks
        when(mockAnnotation.taskType()).thenReturn(TaskType.FINALE_GET_PRODUCT);
        when(mockAnnotation.endpoint()).thenReturn("");
        when(mockAnnotation.httpMethod()).thenReturn("GET");
        when(mockAnnotation.priority()).thenReturn(0);
        when(mockAnnotation.maxRetryCount()).thenReturn(3);
        when(mockAnnotation.synchronous()).thenReturn(false);
        when(mockAnnotation.bypass()).thenReturn(false);
    }

    @Test
    void shouldBypassTaskQueueWhenBypassIsTrue() throws Throwable {
        // Arrange
        when(mockAnnotation.bypass()).thenReturn(true);
        Object expectedResult = "bypass result";
        when(mockJoinPoint.proceed()).thenReturn(expectedResult);

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertEquals(expectedResult, result);
        verify(mockJoinPoint, times(1)).proceed();
        verify(synchronousTaskProcessor, never()).executeTask(any());
        verify(apiTaskQueueService, never()).createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    void shouldRouteTaskBasedOnFeatureFlagAndSynchronousSettings(boolean synchronous, String expectedExecution) throws Throwable {
        // Arrange
        when(mockAnnotation.synchronous()).thenReturn(synchronous);

        if ("ASYNC".equals(expectedExecution)) {
            // Setup for async execution
            UUID expectedTaskId = UUID.randomUUID();
            when(mockTask.getId()).thenReturn(expectedTaskId);
            when(apiTaskQueueService.createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                    .thenReturn(mockTask);
            when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"payload\"}");

            // Act
            Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

            // Assert
            assertEquals(expectedTaskId, result);
            verify(apiTaskQueueService, times(1)).createTask(
                    TaskType.FINALE_GET_PRODUCT.getType(),
                    "TestClass.testMethod",
                    "GET",
                    "{\"test\":\"payload\"}",
                    0,
                    3
            );
            verify(synchronousTaskProcessor, never()).executeTask(any());
        } else {
            // Setup for sync execution
            Object expectedResult = "sync result";
            when(synchronousTaskProcessor.executeTask(any())).thenReturn(expectedResult);

            // Act
            Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

            // Assert
            assertEquals(expectedResult, result);
            verify(synchronousTaskProcessor, times(1)).executeTask(any());
            verify(apiTaskQueueService, never()).createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt());
        }
    }

    @Test
    void shouldUseCustomEndpointWhenProvided() throws Throwable {
        // Arrange
        String customEndpoint = "/api/custom/endpoint";
        when(mockAnnotation.endpoint()).thenReturn(customEndpoint);
        when(mockAnnotation.synchronous()).thenReturn(false); // Must be false for async execution
        UUID expectedTaskId = UUID.randomUUID();
        when(mockTask.getId()).thenReturn(expectedTaskId);
        when(apiTaskQueueService.createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(mockTask);
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"payload\"}");

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertEquals(expectedTaskId, result);
        verify(apiTaskQueueService, times(1)).createTask(
                TaskType.FINALE_GET_PRODUCT.getType(),
                customEndpoint,
                "GET",
                "{\"test\":\"payload\"}",
                0,
                3
        );
    }

    @Test
    void shouldUseAnnotationParametersCorrectly() throws Throwable {
        // Arrange
        when(mockAnnotation.httpMethod()).thenReturn("POST");
        when(mockAnnotation.priority()).thenReturn(5);
        when(mockAnnotation.maxRetryCount()).thenReturn(2);
        when(mockAnnotation.synchronous()).thenReturn(false); // Must be false for async execution
        UUID expectedTaskId = UUID.randomUUID();
        when(mockTask.getId()).thenReturn(expectedTaskId);
        when(apiTaskQueueService.createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(mockTask);
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"payload\"}");

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertEquals(expectedTaskId, result);
        verify(apiTaskQueueService, times(1)).createTask(
                TaskType.FINALE_GET_PRODUCT.getType(),
                "TestClass.testMethod",
                "POST",
                "{\"test\":\"payload\"}",
                5,
                2
        );
    }

    @Test
    void shouldThrowExceptionWhenJsonSerializationFails() throws Throwable {
        // Arrange
        when(mockAnnotation.synchronous()).thenReturn(false);
        when(objectMapper.writeValueAsString(any())).thenThrow(new JsonProcessingException("Serialization failed") {});

        // Act & Assert
        assertThrows(ImsBusinessException.class, () -> taskRouter.routeTask(mockJoinPoint, mockAnnotation));
        verify(apiTaskQueueService, never()).createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    void shouldHandleUnknownTaskTypeGracefully() throws Throwable {
        // Arrange
        TaskType unknownTaskType = mock(TaskType.class);
        when(unknownTaskType.getType()).thenReturn("UNKNOWN_TASK");
        when(unknownTaskType.getRateLimiterName()).thenThrow(new IllegalArgumentException("Unknown task type"));
        when(mockAnnotation.taskType()).thenReturn(unknownTaskType);
        when(mockAnnotation.synchronous()).thenReturn(false);

        UUID expectedTaskId = UUID.randomUUID();
        when(mockTask.getId()).thenReturn(expectedTaskId);
        when(apiTaskQueueService.createTask(anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(mockTask);
        when(objectMapper.writeValueAsString(any())).thenReturn("{\"test\":\"payload\"}");

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertNotNull(result);
        assertEquals(expectedTaskId, result);
        verify(apiTaskQueueService, times(1)).createTask(
                "UNKNOWN_TASK",
                "TestClass.testMethod",
                "GET",
                "{\"test\":\"payload\"}",
                0,
                3
        );
    }

    @Test
    void shouldCreateTaskExecutionContextCorrectly() throws Throwable {
        // Arrange
        when(mockAnnotation.endpoint()).thenReturn("/custom/endpoint");
        when(mockAnnotation.synchronous()).thenReturn(true);
        when(mockAnnotation.bypass()).thenReturn(true);
        Object expectedResult = "bypass result";
        when(mockJoinPoint.proceed()).thenReturn(expectedResult);

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertEquals(expectedResult, result);
        // Verify the join point was called (indicating context was created properly)
        verify(mockJoinPoint, times(1)).proceed();
    }

    @Test
    void shouldHandleMethodWithNoParameters() throws Throwable {
        // Arrange
        when(mockSignature.getParameterNames()).thenReturn(new String[]{});
        when(mockJoinPoint.getArgs()).thenReturn(new Object[]{});
        when(mockAnnotation.bypass()).thenReturn(true);
        Object expectedResult = "bypass result";
        when(mockJoinPoint.proceed()).thenReturn(expectedResult);

        // Act
        Object result = taskRouter.routeTask(mockJoinPoint, mockAnnotation);

        // Assert
        assertEquals(expectedResult, result);
        verify(mockJoinPoint, times(1)).proceed();
    }

    // Helper test class for mocking
    private static class TestClass {
        public void testMethod(String param1, String param2) {
            // Test method for mocking purposes
        }
    }
}