package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.BatchReviewExceptionRecordCommand;
import com.mercaso.ims.application.dto.BatchReviewExceptionRecordResultDto;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ExceptionRecordRestApiIT extends AbstractIT {


    @Test
    void testBatchConfirm() throws Exception {
        UUID id = UUID.randomUUID();

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();

        ExceptionRecord savedExceptionRecord = exceptionRecordRepository.save(exceptionRecord);

        List<UUID> ids = List.of(savedExceptionRecord.getId(), id);

        BatchReviewExceptionRecordCommand command = BatchReviewExceptionRecordCommand.builder()
            .status(ExceptionRecordStatus.CONFIRMED)
            .ids(ids)
            .build();

        BatchReviewExceptionRecordResultDto result = exceptionRecordRestApiUtil.batchReview(command);
        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(id, result.getFailedIds().get(0));
    }


    @Test
    void testBatchDispute() throws Exception {
        UUID id = UUID.randomUUID();
        String note = "Dispute note";

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();

        ExceptionRecord savedExceptionRecord = exceptionRecordRepository.save(exceptionRecord);

        List<UUID> ids = List.of(savedExceptionRecord.getId(), id);

        BatchReviewExceptionRecordCommand command = BatchReviewExceptionRecordCommand.builder()
            .status(ExceptionRecordStatus.DISPUTED)
            .ids(ids)
            .note(note)
            .build();

        BatchReviewExceptionRecordResultDto result = exceptionRecordRestApiUtil.batchReview(command);
        Assertions.assertEquals(1, result.getUpdatedCount());
        Assertions.assertEquals(id, result.getFailedIds().get(0));

        ExceptionRecord resultRecord = exceptionRecordRepository.findById(savedExceptionRecord.getId());

        Assertions.assertEquals(ExceptionRecordStatus.DISPUTED, resultRecord.getStatus());
        Assertions.assertEquals(note, resultRecord.getNote());

    }
}