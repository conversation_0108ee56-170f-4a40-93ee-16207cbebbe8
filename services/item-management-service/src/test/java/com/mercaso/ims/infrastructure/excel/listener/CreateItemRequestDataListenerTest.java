package com.mercaso.ims.infrastructure.excel.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateOrUpdateItemRequestData;
import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class CreateItemRequestDataListenerTest {

    @MockBean
    private ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;

    @MockBean
    private ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;

    @MockBean
    private ItemRepository itemRepository;

    @MockBean
    private VendorRepository vendorRepository;

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(new CreateOrUpdateItemRequestData());

        // Assert
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData2() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        CreateOrUpdateItemRequestData createItemRequestData = new CreateOrUpdateItemRequestData();
        createItemRequestData.setStatus("active");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
        assertEquals(AvailabilityStatus.ACTIVE,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData3() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        CreateOrUpdateItemRequestData createItemRequestData = new CreateOrUpdateItemRequestData();
        createItemRequestData.setStatus("draft");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertEquals("", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCategory());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
        assertEquals(AvailabilityStatus.DRAFT,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData4() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        CreateOrUpdateItemRequestData createItemRequestData = new CreateOrUpdateItemRequestData();
        createItemRequestData.setStatus("deleted");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
        assertEquals(AvailabilityStatus.DELETED,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData5() {
        //   Diffblue Cover was unable to create a Spring-specific test for this Spring method.

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        CreateOrUpdateItemRequestData createItemRequestData = new CreateOrUpdateItemRequestData();
        createItemRequestData.setStatus("archived");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertEquals("", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCategory());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
        assertEquals(AvailabilityStatus.ARCHIVED,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData6() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class),
            null, mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        CreateOrUpdateItemRequestData createItemRequestData = new CreateOrUpdateItemRequestData();
        createItemRequestData.setStatus("Create Item Request Data");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCompanyId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getLocationId());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getVendorItemNumber());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        assertNull(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
        assertEquals(AvailabilityStatus.UNKNOWN,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemStatus());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData7() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));
        BigDecimal regPricePack = new BigDecimal("2.3");
        BigDecimal primaryJitVendorItemCost = new BigDecimal("2.3");

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(new CreateOrUpdateItemRequestData("Status",
                "42",
                "42",
                "Primary Vendor",
                "JIT Vendor",
                "Sku",

                "New Description",
                "New Description",
                "New Description",
                "New Description",
                3,
                "Item Size",
                "Item Unit Measure",
                true,
                "Department",
                "Category",
                "Sub Category",
                "Class Type",
                "Brand",
                regPricePack,
                "Case Upc",
                "Each Upc",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                primaryJitVendorItemCost,
                primaryJitVendorItemCost,
                "Primary Vendor Item Aisle",
                new BigDecimal("2.3"),
                true,
                "https://example.org/example",
                "Tags",
                null, null));

        // Assert
        assertEquals("Brand", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getBrand());
        assertEquals("Case Upc", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertEquals("Category", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCategory());
        assertEquals("Class Type", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getClassType());
        assertEquals("Department", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getDepartment());
        assertEquals("Each Upc", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertEquals("Item Size", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertEquals("Item Unit Measure",
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertEquals("New Description", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertEquals("Primary Vendor Item Aisle", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertEquals("Primary Vendor", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertEquals("JIT Vendor", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertEquals("Sku", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertEquals("Sub Category", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSubCategory());
        assertEquals("Tags", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertEquals("https://example.org/example",
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertEquals(3, actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize().intValue());
        assertTrue(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertTrue(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        BigDecimal expectedPrimaryVendorItemCost = new BigDecimal("2.3");
        assertEquals(expectedPrimaryVendorItemCost,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        BigDecimal expectedPromoPricePackNoCrv = new BigDecimal("2.3");
        assertEquals(expectedPromoPricePackNoCrv,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        BigDecimal expectedRegPricePackNoCrv = new BigDecimal("2.3");
        assertEquals(expectedRegPricePackNoCrv,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
    }

    @Test
    void testConvertToCreateItemAdjustmentRequestDetailCommandWithCreateOrUpdateItemRequestData8() {

        // Arrange
        CreateItemRequestDataListener createItemRequestDataListener = new CreateItemRequestDataListener(
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, mock(ItemAdjustmentRequestDetailApplicationService.class),
            mock(ItemAdjustmentRequestApplicationService.class), mock(ItemRepository.class), mock(VendorRepository.class), mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));
        CreateOrUpdateItemRequestData createItemRequestData = mock(CreateOrUpdateItemRequestData.class);
        when(createItemRequestData.getCrvFlag()).thenReturn(true);
        when(createItemRequestData.getPromoFlag()).thenReturn(true);
        when(createItemRequestData.getPackSize()).thenReturn(3);
        when(createItemRequestData.getBrand()).thenReturn("Brand");
        when(createItemRequestData.getCaseUpc()).thenReturn("Case Upc");
        when(createItemRequestData.getCategory()).thenReturn("Category");
        when(createItemRequestData.getClassType()).thenReturn("Class Type");
        when(createItemRequestData.getCompanyId()).thenReturn("42");
        when(createItemRequestData.getDepartment()).thenReturn("Department");
        when(createItemRequestData.getEachUpc()).thenReturn("Each Upc");
        when(createItemRequestData.getImageUrl()).thenReturn("https://example.org/example");
        when(createItemRequestData.getItemDescription()).thenReturn("Item Description");
        when(createItemRequestData.getItemSize()).thenReturn("Item Size");
        when(createItemRequestData.getItemUnitMeasure()).thenReturn("Item Unit Measure");
        when(createItemRequestData.getLocationId()).thenReturn("42");
        when(createItemRequestData.getNewDescription()).thenReturn("New Description");
        when(createItemRequestData.getPrimaryPoVendor()).thenReturn("Primary Vendor");
        when(createItemRequestData.getPrimaryVendorItemAisle()).thenReturn("Primary Vendor Item Aisle");
        when(createItemRequestData.getPrimaryVendorItemNumber()).thenReturn("42");
        when(createItemRequestData.getPrimaryJitVendor()).thenReturn("Secondary Vendor");
        when(createItemRequestData.getSku()).thenReturn("Sku");
        when(createItemRequestData.getStatus()).thenReturn("Status");
        when(createItemRequestData.getSubCategory()).thenReturn("Sub Category");
        when(createItemRequestData.getTags()).thenReturn("Tags");
        when(createItemRequestData.getPrimaryPoVendorItemCost()).thenReturn(new BigDecimal("2.3"));
        when(createItemRequestData.getPromoPrice()).thenReturn(new BigDecimal("2.3"));
        when(createItemRequestData.getRegPricePack()).thenReturn(new BigDecimal("2.3"));

        // Act
        CreateItemAdjustmentRequestDetailCommand actualConvertToCreateItemAdjustmentRequestDetailCommandResult = createItemRequestDataListener
            .convertToCreateItemAdjustmentRequestDetailCommand(createItemRequestData);

        // Assert
        assertEquals("Case Upc", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCaseUpc());
        assertEquals("Category", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCategory());
        assertEquals("Class Type", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getClassType());
        assertEquals("Department", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getDepartment());
        assertEquals("Each Upc", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getEachUpc());
        assertEquals("Item Description", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTitle());
        assertEquals("Item Size", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemSize());
        assertEquals("Item Unit Measure",
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getItemUnitMeasure());
        assertEquals("New Description", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getNewDescription());
        assertEquals("Primary Vendor Item Aisle", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getAisle());
        assertEquals("Primary Vendor", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendor());
        assertEquals("Secondary Vendor", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryJitVendor());
        assertEquals("Sku", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSku());
        assertEquals("Sub Category", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getSubCategory());
        assertEquals("Tags", actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getTags());
        assertEquals("https://example.org/example",
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getImageUrl());
        assertEquals(3, actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPackageSize().intValue());
        assertTrue(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getCrvFlag());
        assertTrue(actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoFlag());
        BigDecimal expectedPrimaryVendorItemCost = new BigDecimal("2.3");
        assertEquals(expectedPrimaryVendorItemCost,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPrimaryPoVendorItemCost());
        BigDecimal expectedPromoPricePackNoCrv = new BigDecimal("2.3");
        assertEquals(expectedPromoPricePackNoCrv,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getPromoPricePackNoCrv());
        BigDecimal expectedRegPricePackNoCrv = new BigDecimal("2.3");
        assertEquals(expectedRegPricePackNoCrv,
            actualConvertToCreateItemAdjustmentRequestDetailCommandResult.getRegPricePackNoCrv());
    }

    @Test
    void testNewCreateItemRequestDataListener() {
        // Arrange
        UUID itemAdjustmentRequestId = AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID;

        // Act
        CreateItemRequestDataListener actualCreateItemRequestDataListener = new CreateItemRequestDataListener(
            itemAdjustmentRequestId, itemAdjustmentRequestDetailApplicationService, itemAdjustmentRequestApplicationService,
            itemRepository, vendorRepository, mock(
            VendorItemRepository.class), mock(CategoryApplicationService.class), mock(FeatureFlagsManager.class), mock(BrandRepository.class));

        // Assert
        assertEquals(0, actualCreateItemRequestDataListener.getCreatedRowCount().intValue());
        assertEquals(0, actualCreateItemRequestDataListener.getDeletedRowCount().intValue());
        assertEquals(0, actualCreateItemRequestDataListener.getModifiedRowCoun().intValue());
        assertEquals(ItemAdjustmentType.CREATE, actualCreateItemRequestDataListener.getItemAdjustmentType());
        Map<String, Object> categoryData = actualCreateItemRequestDataListener.getCategoryData();
        assertEquals(Float.PRECISION, categoryData.size());
        assertTrue(categoryData.containsKey("Apparel & Accessories"));
        assertTrue(categoryData.containsKey("Auto & Electronics"));
        assertTrue(categoryData.containsKey("Baby"));
        assertTrue(categoryData.containsKey("Beverage"));
        assertTrue(categoryData.containsKey("Candy & Snacks"));
        assertTrue(categoryData.containsKey("Cleaning & Laundry"));
        assertSame(itemAdjustmentRequestApplicationService,
            actualCreateItemRequestDataListener.getItemAdjustmentRequestApplicationService());
        assertSame(itemAdjustmentRequestDetailApplicationService,
            actualCreateItemRequestDataListener.getItemAdjustmentRequestDetailApplicationService());
        assertSame(itemRepository, actualCreateItemRequestDataListener.getItemRepository());
        assertSame(vendorRepository, actualCreateItemRequestDataListener.getVendorRepository());
        assertSame(itemAdjustmentRequestId, actualCreateItemRequestDataListener.getItemAdjustmentRequestId());
    }
}
