package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.mapper.vendor.VendorDtoApplicationMapper;
import com.mercaso.ims.domain.vendor.service.VendorService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorQueryApplicationServiceImpl.class})
class VendorQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private VendorDtoApplicationMapper vendorDtoApplicationMapper;

    @Autowired
    private VendorQueryApplicationServiceImpl vendorQueryApplicationServiceImpl;

    @MockBean
    private VendorService vendorService;


    @Test
    void testQueryOrFilterVendors() {
        // Arrange
        when(vendorService.findByFuzzyName(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<VendorDto> actualQueryOrFilterVendorsResult = vendorQueryApplicationServiceImpl
            .queryOrFilterVendors("Vendor Name");

        // Assert
        verify(vendorService).findByFuzzyName("Vendor Name");
        assertTrue(actualQueryOrFilterVendorsResult.isEmpty());
    }


    @Test
    void testQueryOrFilterVendors2() {
        // Arrange
        when(vendorService.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<VendorDto> actualQueryOrFilterVendorsResult = vendorQueryApplicationServiceImpl.queryOrFilterVendors("");

        // Assert
        verify(vendorService).findAll();
        assertTrue(actualQueryOrFilterVendorsResult.isEmpty());
    }
}
