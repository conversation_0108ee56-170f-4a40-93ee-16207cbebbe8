package com.mercaso.ims.application.queryservice.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.CustomizedItemCostCollectionJpaDao;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionSearchUtil;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemCostCollectionQueryApplicationServiceImpl.class})
class ItemCostCollectionQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private CustomizedItemCostCollectionJpaDao customizedItemCostCollectionJpaDao;

    @Autowired
    private ItemCostCollectionQueryApplicationServiceImpl itemCostCollectionQueryApplicationServiceImpl;

    @Test
    void testFindByItemCostCollectionId() {
        UUID id = UUID.randomUUID();
        ItemCostCollectionDetailDto collectionSearchDto = ItemCostCollectionSearchUtil.buildItemCostCollectionDto(id);

        when(customizedItemCostCollectionJpaDao.getItemCostCollectionDto(any(UUID.class))).thenReturn(collectionSearchDto);

        ItemCostCollectionDetailDto result = itemCostCollectionQueryApplicationServiceImpl.findByItemCostCollectionId(id);
        Assertions.assertEquals(collectionSearchDto.getId(), result.getId());
        Assertions.assertEquals(collectionSearchDto.getCollectionNumber(), result.getCollectionNumber());

    }
}