package com.mercaso.ims.infrastructure.repository.vendor;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.VendorJpaDao;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.mapper.VendorDoMapper;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorRepositoryImpl.class})
class VendorRepositoryImplTest extends AbstractTest {

    @MockBean
    private VendorDoMapper vendorDoMapper;

    @MockBean
    private VendorJpaDao vendorJpaDao;

    @Autowired
    private VendorRepositoryImpl vendorRepositoryImpl;

    @Test
    void testSave() {
        // Arrange
        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenReturn(null);
        when(vendorDoMapper.domainToDo(Mockito.<Vendor>any())).thenReturn(vendorDo);

        VendorDo vendorDo2 = new VendorDo();
        vendorDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo2.setCreatedUserName("janedoe");
        vendorDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo2.setDeletedUserName("janedoe");
        vendorDo2.setId(UUID.randomUUID());
        vendorDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setUpdatedBy("2020-03-01");
        vendorDo2.setUpdatedUserName("janedoe");
        vendorDo2.setVendorCompanyName("Vendor Company Name");
        vendorDo2.setVendorContactName("Vendor Contact Name");
        vendorDo2.setVendorContactTel("Vendor Contact Tel");
        vendorDo2.setVendorName("Vendor Name");
        vendorDo2.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorJpaDao.save(Mockito.<VendorDo>any())).thenReturn(vendorDo2);

        // Act
        Vendor actualSaveResult = vendorRepositoryImpl.save(null);

        // Assert
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorDoMapper).domainToDo(isNull());
        verify(vendorJpaDao).save(isA(VendorDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSave2() {
        // Arrange
        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorDoMapper.domainToDo(Mockito.<Vendor>any())).thenReturn(vendorDo);
        when(vendorJpaDao.save(Mockito.<VendorDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorRepositoryImpl.save(null));
        verify(vendorDoMapper).domainToDo(isNull());
        verify(vendorJpaDao).save(isA(VendorDo.class));
    }

    @Test
    void testFindById() {
        // Arrange
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenReturn(null);

        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        Optional<VendorDo> ofResult = Optional.of(vendorDo);
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        Vendor actualFindByIdResult = vendorRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindById2() {
        // Arrange
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenThrow(new ImsBusinessException("Code"));
        UUID uuid = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorRepositoryImpl.findById(uuid));
        verify(vendorJpaDao).findById(isA(UUID.class));
    }

    @Test
    void testUpdate() {
        // Arrange
        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenReturn(null);
        when(vendorDoMapper.domainToDo(Mockito.<Vendor>any())).thenReturn(vendorDo);

        VendorDo vendorDo2 = new VendorDo();
        vendorDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo2.setCreatedUserName("janedoe");
        vendorDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo2.setDeletedUserName("janedoe");
        vendorDo2.setId(UUID.randomUUID());
        vendorDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setUpdatedBy("2020-03-01");
        vendorDo2.setUpdatedUserName("janedoe");
        vendorDo2.setVendorCompanyName("Vendor Company Name");
        vendorDo2.setVendorContactName("Vendor Contact Name");
        vendorDo2.setVendorContactTel("Vendor Contact Tel");
        vendorDo2.setVendorName("Vendor Name");
        vendorDo2.setVendorStatus(VendorStatus.ACTIVE);
        Optional<VendorDo> ofResult = Optional.of(vendorDo2);

        VendorDo vendorDo3 = new VendorDo();
        vendorDo3.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo3.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo3.setCreatedUserName("janedoe");
        vendorDo3.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo3.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo3.setDeletedUserName("janedoe");
        vendorDo3.setId(UUID.randomUUID());
        vendorDo3.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo3.setUpdatedBy("2020-03-01");
        vendorDo3.setUpdatedUserName("janedoe");
        vendorDo3.setVendorCompanyName("Vendor Company Name");
        vendorDo3.setVendorContactName("Vendor Contact Name");
        vendorDo3.setVendorContactTel("Vendor Contact Tel");
        vendorDo3.setVendorName("Vendor Name");
        vendorDo3.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorJpaDao.save(Mockito.<VendorDo>any())).thenReturn(vendorDo3);
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        Vendor vendor = mock(Vendor.class);
        when(vendor.getId()).thenReturn(UUID.randomUUID());

        // Act
        Vendor actualUpdateResult = vendorRepositoryImpl.update(vendor);

        // Assert
        verify(vendor).getId();
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorDoMapper).domainToDo(isA(Vendor.class));
        verify(vendorJpaDao).findById(isA(UUID.class));
        verify(vendorJpaDao).save(isA(VendorDo.class));
        assertNull(actualUpdateResult);
    }

    @Test
    void testDeleteById() {
        // Arrange
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenReturn(null);

        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        Optional<VendorDo> ofResult = Optional.of(vendorDo);

        VendorDo vendorDo2 = new VendorDo();
        vendorDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo2.setCreatedUserName("janedoe");
        vendorDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo2.setDeletedUserName("janedoe");
        vendorDo2.setId(UUID.randomUUID());
        vendorDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo2.setUpdatedBy("2020-03-01");
        vendorDo2.setUpdatedUserName("janedoe");
        vendorDo2.setVendorCompanyName("Vendor Company Name");
        vendorDo2.setVendorContactName("Vendor Contact Name");
        vendorDo2.setVendorContactTel("Vendor Contact Tel");
        vendorDo2.setVendorName("Vendor Name");
        vendorDo2.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorJpaDao.save(Mockito.<VendorDo>any())).thenReturn(vendorDo2);
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        // Act
        Vendor actualDeleteByIdResult = vendorRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorJpaDao).findById(isA(UUID.class));
        verify(vendorJpaDao).save(isA(VendorDo.class));
        assertNull(actualDeleteByIdResult);
    }

    @Test
    void testDeleteById2() {
        // Arrange
        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        Optional<VendorDo> ofResult = Optional.of(vendorDo);
        when(vendorJpaDao.save(Mockito.<VendorDo>any())).thenThrow(new ImsBusinessException("Code"));
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        UUID uuid = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorRepositoryImpl.deleteById(uuid));
        verify(vendorJpaDao).findById(isA(UUID.class));
        verify(vendorJpaDao).save(isA(VendorDo.class));
    }


    @Test
    void testDeleteById3() {
        // Arrange
        Optional<VendorDo> emptyResult = Optional.empty();
        when(vendorJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        Vendor actualDeleteByIdResult = vendorRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(vendorJpaDao).findById(isA(UUID.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testFindByVendorName() {
        // Arrange
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenReturn(null);

        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorJpaDao.findByVendorName(Mockito.<String>any())).thenReturn(vendorDo);

        // Act
        Vendor actualFindByVendorNameResult = vendorRepositoryImpl.findByVendorName("Vendor Name");

        // Assert
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorJpaDao).findByVendorName("Vendor Name");
        assertNull(actualFindByVendorNameResult);
    }


    @Test
    void testFindByVendorName2() {
        // Arrange
        when(vendorDoMapper.doToDomain(Mockito.<VendorDo>any())).thenThrow(new ImsBusinessException("Code"));

        VendorDo vendorDo = new VendorDo();
        vendorDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorDo.setCreatedUserName("janedoe");
        vendorDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorDo.setDeletedUserName("janedoe");
        vendorDo.setId(UUID.randomUUID());
        vendorDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorDo.setUpdatedBy("2020-03-01");
        vendorDo.setUpdatedUserName("janedoe");
        vendorDo.setVendorCompanyName("Vendor Company Name");
        vendorDo.setVendorContactName("Vendor Contact Name");
        vendorDo.setVendorContactTel("Vendor Contact Tel");
        vendorDo.setVendorName("Vendor Name");
        vendorDo.setVendorStatus(VendorStatus.ACTIVE);
        when(vendorJpaDao.findByVendorName(Mockito.<String>any())).thenReturn(vendorDo);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorRepositoryImpl.findByVendorName("Vendor Name"));
        verify(vendorDoMapper).doToDomain(isA(VendorDo.class));
        verify(vendorJpaDao).findByVendorName("Vendor Name");
    }


    @Test
    void testFindAllByIdIn() {
        // Arrange
        when(vendorJpaDao.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        // Act
        List<Vendor> actualFindAllByIdInResult = vendorRepositoryImpl.findAllByIdIn(new ArrayList<>());

        // Assert
        verify(vendorJpaDao).findAllByIdIn(isA(List.class));
        assertTrue(actualFindAllByIdInResult.isEmpty());
    }


    @Test
    void testFindAll() {
        // Arrange
        when(vendorJpaDao.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<Vendor> actualFindAllResult = vendorRepositoryImpl.findAll();

        // Assert
        verify(vendorJpaDao).findAll();
        assertTrue(actualFindAllResult.isEmpty());
    }


    @Test
    void testFindByFuzzyName() {
        // Arrange
        when(vendorJpaDao.findByVendorNameContainsIgnoreCase(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<Vendor> actualFindByFuzzyNameResult = vendorRepositoryImpl.findByFuzzyName("Vendor Name");

        // Assert
        verify(vendorJpaDao).findByVendorNameContainsIgnoreCase("Vendor Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
    }
}
