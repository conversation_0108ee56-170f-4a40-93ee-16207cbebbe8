package com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecord;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisSource;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.VendorPoAnalyzeRecordJpaDao;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.dataobject.VendorPoAnalyzeRecordDo;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.mapper.VendorPoAnalyzeRecordDoMapper;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorPoAnalyzeRecordRepositoryImpl.class})
class VendorPoAnalyzeRecordRepositoryImplTest extends AbstractTest {

    @MockBean
    private VendorPoAnalyzeRecordDoMapper vendorPoAnalyzeRecordDoMapper;

    @MockBean
    private VendorPoAnalyzeRecordJpaDao vendorPoAnalyzeRecordJpaDao;

    @Autowired
    private VendorPoAnalyzeRecordRepositoryImpl vendorPoAnalyzeRecordRepositoryImpl;

    @Test
    void testSaveWithVendorPoAnalyzeRecord_thenReturnNull() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordJpaDao.save(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(vendorPoAnalyzeRecordDo);

        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo2 = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo2.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo2.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo2.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo2.setRcptId("42");
        vendorPoAnalyzeRecordDo2.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo2.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(null);
        when(vendorPoAnalyzeRecordDoMapper.domainToDo(Mockito.<VendorPoAnalyzeRecord>any()))
            .thenReturn(vendorPoAnalyzeRecordDo2);

        // Act
        VendorPoAnalyzeRecord actualSaveResult = vendorPoAnalyzeRecordRepositoryImpl.save(null);

        // Assert
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordDoMapper).domainToDo(isNull());
        verify(vendorPoAnalyzeRecordJpaDao).save(isA(VendorPoAnalyzeRecordDo.class));
        assertNull(actualSaveResult);
    }


    @Test
    void testSaveWithVendorPoAnalyzeRecord_thenThrowImsBusinessException() {
        // Arrange
        when(vendorPoAnalyzeRecordDoMapper.domainToDo(Mockito.<VendorPoAnalyzeRecord>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> vendorPoAnalyzeRecordRepositoryImpl.save(null));
        verify(vendorPoAnalyzeRecordDoMapper).domainToDo(isNull());
    }


    @Test
    void testFindByIdWithUuid_givenVendorPoAnalyzeRecordDoMapperDoToDomainReturnNull() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        Optional<VendorPoAnalyzeRecordDo> ofResult = Optional.of(vendorPoAnalyzeRecordDo);
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(null);

        // Act
        VendorPoAnalyzeRecord actualFindByIdResult = vendorPoAnalyzeRecordRepositoryImpl
            .findById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIdWithUuid_givenVendorPoAnalyzeRecordJpaDaoFindByIdReturnEmpty() {
        // Arrange
        Optional<VendorPoAnalyzeRecordDo> emptyResult = Optional.empty();
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(emptyResult);

        // Act
        VendorPoAnalyzeRecord actualFindByIdResult = vendorPoAnalyzeRecordRepositoryImpl
            .findById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        Optional<VendorPoAnalyzeRecordDo> ofResult = Optional.of(vendorPoAnalyzeRecordDo);
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any()))
            .thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> vendorPoAnalyzeRecordRepositoryImpl.findById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdateWithVendorPoAnalyzeRecord_thenReturnNull() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        Optional<VendorPoAnalyzeRecordDo> ofResult = Optional.of(vendorPoAnalyzeRecordDo);

        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo2 = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo2.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo2.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo2.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo2.setRcptId("42");
        vendorPoAnalyzeRecordDo2.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo2.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordJpaDao.save(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(vendorPoAnalyzeRecordDo2);
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo3 = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo3.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo3.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo3.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo3.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo3.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo3.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo3.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo3.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo3.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo3.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo3.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo3.setRcptId("42");
        vendorPoAnalyzeRecordDo3.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo3.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo3.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo3.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo3.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo3.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(null);
        when(vendorPoAnalyzeRecordDoMapper.domainToDo(Mockito.<VendorPoAnalyzeRecord>any()))
            .thenReturn(vendorPoAnalyzeRecordDo3);
        VendorPoAnalyzeRecord domain = mock(VendorPoAnalyzeRecord.class);
        when(domain.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Act
        VendorPoAnalyzeRecord actualUpdateResult = vendorPoAnalyzeRecordRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordDoMapper).domainToDo(isA(VendorPoAnalyzeRecord.class));
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
        verify(vendorPoAnalyzeRecordJpaDao).save(isA(VendorPoAnalyzeRecordDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testDeleteById_thenReturnNull() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        Optional<VendorPoAnalyzeRecordDo> ofResult = Optional.of(vendorPoAnalyzeRecordDo);

        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo2 = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo2.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo2.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo2.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo2.setRcptId("42");
        vendorPoAnalyzeRecordDo2.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo2.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordJpaDao.save(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(vendorPoAnalyzeRecordDo2);
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(null);

        // Act
        VendorPoAnalyzeRecord actualDeleteByIdResult = vendorPoAnalyzeRecordRepositoryImpl
            .deleteById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
        verify(vendorPoAnalyzeRecordJpaDao).save(isA(VendorPoAnalyzeRecordDo.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo.setRcptId("42");
        vendorPoAnalyzeRecordDo.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo.setVendorName("Vendor Name");
        Optional<VendorPoAnalyzeRecordDo> ofResult = Optional.of(vendorPoAnalyzeRecordDo);

        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo2 = new VendorPoAnalyzeRecordDo();
        vendorPoAnalyzeRecordDo2.setAnalysisExpensePayload("Analysis Result Payload");
        vendorPoAnalyzeRecordDo2.setAnalysisSource(AnalysisSource.AWS);
        vendorPoAnalyzeRecordDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setCreatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        vendorPoAnalyzeRecordDo2.setDeletedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setOriginalFileName("foo.txt");
        vendorPoAnalyzeRecordDo2.setRcptDate("2020-03-01");
        vendorPoAnalyzeRecordDo2.setRcptId("42");
        vendorPoAnalyzeRecordDo2.setStatus(AnalysisStatus.PENDING);
        vendorPoAnalyzeRecordDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        vendorPoAnalyzeRecordDo2.setUpdatedBy("2020-03-01");
        vendorPoAnalyzeRecordDo2.setUpdatedUserName("janedoe");
        vendorPoAnalyzeRecordDo2.setVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        vendorPoAnalyzeRecordDo2.setVendorName("Vendor Name");
        when(vendorPoAnalyzeRecordJpaDao.save(Mockito.<VendorPoAnalyzeRecordDo>any())).thenReturn(vendorPoAnalyzeRecordDo2);
        when(vendorPoAnalyzeRecordJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(vendorPoAnalyzeRecordDoMapper.doToDomain(Mockito.<VendorPoAnalyzeRecordDo>any()))
            .thenThrow(new ImsBusinessException("1"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> vendorPoAnalyzeRecordRepositoryImpl.deleteById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(vendorPoAnalyzeRecordDoMapper).doToDomain(isA(VendorPoAnalyzeRecordDo.class));
        verify(vendorPoAnalyzeRecordJpaDao).findById(isA(UUID.class));
        verify(vendorPoAnalyzeRecordJpaDao).save(isA(VendorPoAnalyzeRecordDo.class));
    }
}
