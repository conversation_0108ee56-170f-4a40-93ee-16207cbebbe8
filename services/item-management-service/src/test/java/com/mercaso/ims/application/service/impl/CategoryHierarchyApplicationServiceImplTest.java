package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.CATEGORY_RELATIONSHIP_IS_EMPTY;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.CreateCategoryHierarchCommand;
import com.mercaso.ims.application.command.UpdatedCategoryHierarchCommand;
import com.mercaso.ims.application.service.CategoryHierarchyApplicationService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {CategoryHierarchyApplicationServiceImpl.class})
class CategoryHierarchyApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private CategoryHierarchyApplicationService categoryHierarchyApplicationService;

    @MockBean
    private CategoryHierarchyService categoryHierarchyService;

    private UUID categoryId;
    private UUID ancestorId;
    private UpdatedCategoryHierarchCommand updatedCategoryHierarchCommand;
    private CategoryHierarchy category1;
    private CategoryHierarchy category2;
    private CategoryHierarchy category3;

    @BeforeEach
    void setUp() {
        categoryId = UUID.randomUUID();
        ancestorId = UUID.randomUUID();
        updatedCategoryHierarchCommand = new UpdatedCategoryHierarchCommand(categoryId, null, ancestorId, 1);

        category1 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .depth(1)
            .sortOrder(1)
            .build();

        category2 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .depth(1)
            .sortOrder(2)
            .build();

        category3 = CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .depth(1)
            .sortOrder(3)
            .build();
    }

    @Test
    void should_create_self_hierarchy_when_ancestor_is_null() {
        CreateCategoryHierarchCommand command = CreateCategoryHierarchCommand.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(null)
            .sortOrder(1)
            .build();

        CategoryHierarchy expectedHierarchy = CategoryHierarchy.builder()
            .categoryId(command.getCategoryId())
            .ancestorCategoryId(command.getCategoryId())
            .depth(0)
            .sortOrder(command.getSortOrder())
            .build();

        categoryHierarchyApplicationService.createCategoryHierarchy(command);

        verify(categoryHierarchyService).save(expectedHierarchy);
    }

    @Test
    void should_throw_exception_when_ancestor_hierarchy_not_found() {
        CreateCategoryHierarchCommand command = CreateCategoryHierarchCommand.builder()
            .categoryId(UUID.randomUUID())
            .ancestorCategoryId(UUID.randomUUID())
            .sortOrder(1)
            .build();

        when(categoryHierarchyService.findByCategoryId(command.getAncestorCategoryId()))
            .thenReturn(Collections.emptyList());

        assertThrows(ImsBusinessException.class,
            () -> categoryHierarchyApplicationService.createCategoryHierarchy(command));
    }

    @Test
    void should_create_hierarchies_correctly_with_single_ancestor() {
        UUID grandAncestorId = UUID.randomUUID();
        int sortOrder = 1;

        CreateCategoryHierarchCommand command = CreateCategoryHierarchCommand.builder()
            .categoryId(categoryId)
            .ancestorCategoryId(ancestorId)
            .sortOrder(sortOrder)
            .build();

        CategoryHierarchy ancestorHierarchy = CategoryHierarchy.builder()
            .categoryId(ancestorId)
            .ancestorCategoryId(grandAncestorId)
            .depth(1)
            .sortOrder(1)
            .build();

        when(categoryHierarchyService.findByCategoryId(ancestorId))
            .thenReturn(Collections.singletonList(ancestorHierarchy));

        categoryHierarchyApplicationService.createCategoryHierarchy(command);

        ArgumentCaptor<CategoryHierarchy> hierarchyCaptor = ArgumentCaptor.forClass(CategoryHierarchy.class);
        verify(categoryHierarchyService, times(2)).save(hierarchyCaptor.capture());

        List<CategoryHierarchy> savedHierarchies = hierarchyCaptor.getAllValues();
        assertThat(savedHierarchies).hasSize(2);

        CategoryHierarchy grandAncestorHierarchy = savedHierarchies.getFirst();
        assertThat(grandAncestorHierarchy.getCategoryId()).isEqualTo(categoryId);
        assertThat(grandAncestorHierarchy.getAncestorCategoryId()).isEqualTo(grandAncestorId);
        assertThat(grandAncestorHierarchy.getDepth()).isEqualTo(2);
        assertThat(grandAncestorHierarchy.getSortOrder()).isEqualTo(sortOrder);

        CategoryHierarchy directAncestorHierarchy = savedHierarchies.get(1);
        assertThat(directAncestorHierarchy.getCategoryId()).isEqualTo(categoryId);
        assertThat(directAncestorHierarchy.getAncestorCategoryId()).isEqualTo(ancestorId);
        assertThat(directAncestorHierarchy.getDepth()).isEqualTo(1);
        assertThat(directAncestorHierarchy.getSortOrder()).isEqualTo(sortOrder);
    }

    @Test
    void should_create_hierarchies_correctly_with_multiple_ancestors() {
        UUID grandAncestorId1 = UUID.randomUUID();
        UUID grandAncestorId2 = UUID.randomUUID();
        int sortOrder = 1;

        CreateCategoryHierarchCommand command = CreateCategoryHierarchCommand.builder()
            .categoryId(categoryId)
            .ancestorCategoryId(ancestorId)
            .sortOrder(sortOrder)
            .build();

        List<CategoryHierarchy> ancestorHierarchies = Arrays.asList(
            CategoryHierarchy.builder()
                .categoryId(ancestorId)
                .ancestorCategoryId(grandAncestorId1)
                .depth(1)
                .sortOrder(1)
                .build(),
            CategoryHierarchy.builder()
                .categoryId(ancestorId)
                .ancestorCategoryId(grandAncestorId2)
                .depth(2)
                .sortOrder(1)
                .build()
        );

        when(categoryHierarchyService.findByCategoryId(ancestorId))
            .thenReturn(ancestorHierarchies);

        categoryHierarchyApplicationService.createCategoryHierarchy(command);

        ArgumentCaptor<CategoryHierarchy> hierarchyCaptor = ArgumentCaptor.forClass(CategoryHierarchy.class);
        verify(categoryHierarchyService, times(3)).save(hierarchyCaptor.capture());

        List<CategoryHierarchy> savedHierarchies = hierarchyCaptor.getAllValues();
        assertThat(savedHierarchies).hasSize(3);

        savedHierarchies.forEach(hierarchy -> {
            assertThat(hierarchy.getCategoryId()).isEqualTo(categoryId);
            assertThat(hierarchy.getSortOrder()).isEqualTo(sortOrder);
        });

        assertThat(savedHierarchies).anySatisfy(hierarchy -> {
            assertThat(hierarchy.getAncestorCategoryId()).isEqualTo(grandAncestorId1);
            assertThat(hierarchy.getDepth()).isEqualTo(2);
        });

        assertThat(savedHierarchies).anySatisfy(hierarchy -> {
            assertThat(hierarchy.getAncestorCategoryId()).isEqualTo(grandAncestorId2);
            assertThat(hierarchy.getDepth()).isEqualTo(3);
        });

        assertThat(savedHierarchies).anySatisfy(hierarchy -> {
            assertThat(hierarchy.getAncestorCategoryId()).isEqualTo(ancestorId);
            assertThat(hierarchy.getDepth()).isEqualTo(1);
        });
    }

    @Test
    void updateCategoryHierarchy_ParentNotChanged_ShouldSkipUpdateAndReorder() {
        CategoryHierarchy currentHierarchy = createHierarchy(categoryId, ancestorId, 1);
        when(categoryHierarchyService.findByCategoryId(categoryId)).thenReturn(Collections.singletonList(currentHierarchy));

        when(categoryHierarchyService.findByCategoryIdAndAncestorCategoryId(categoryId, ancestorId)).thenReturn(currentHierarchy);

        List<CategoryHierarchy> parentHierarchies = Collections.singletonList(createHierarchy(ancestorId, UUID.randomUUID(), 2));
        when(categoryHierarchyService.findByCategoryId(ancestorId)).thenReturn(parentHierarchies);

        categoryHierarchyApplicationService.updatedCategoryHierarchy(updatedCategoryHierarchCommand);

        verify(categoryHierarchyService, atLeastOnce()).delete(any());
        verify(categoryHierarchyService, never()).update(any());
    }

    @Test
    void updateCategoryHierarchy_ChangeParent_ShouldRebuildHierarchy() {
        CategoryHierarchy currentHierarchy = createHierarchy(categoryId, ancestorId, 1);

        when(categoryHierarchyService.findByCategoryId(categoryId)).thenReturn(Collections.singletonList(
            createHierarchy(categoryId, UUID.randomUUID(), 1)
        ));

        when(categoryHierarchyService.findByCategoryId(ancestorId)).thenReturn(Collections.singletonList(
            createHierarchy(ancestorId, UUID.randomUUID(), 2)
        ));

        when(categoryHierarchyService.findByCategoryIdAndAncestorCategoryId(categoryId, ancestorId)).thenReturn(currentHierarchy);

        categoryHierarchyApplicationService.updatedCategoryHierarchy(updatedCategoryHierarchCommand);

        verify(categoryHierarchyService, times(1)).delete(any(UUID.class));
        verify(categoryHierarchyService, atLeast(2)).save(any());
    }

    @Test
    void validateAndGetHierarchies_CurrentCategoryNotFound_ShouldThrowException() {
        when(categoryHierarchyService.findByCategoryId(categoryId)).thenReturn(Collections.emptyList());

        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> categoryHierarchyApplicationService.updatedCategoryHierarchy(updatedCategoryHierarchCommand));
        assertEquals(CATEGORY_RELATIONSHIP_IS_EMPTY.getCode(), exception.getCode());
    }

    @Test
    void testReorderSameLevelCategories_NormalOrder() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of(category1, category2, category3);
        CategoryHierarchy currentCategoryHierarchy = category2;
        currentCategoryHierarchy.setSortOrder(2);

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getSortOrder()); // category1
        assertEquals(2, result.get(1).getSortOrder()); // category2 (current)
        assertEquals(3, result.get(2).getSortOrder()); // category3
    }

    @Test
    void testReorderSameLevelCategories_MoveToFirstPosition() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of(category1, category2, category3);
        CategoryHierarchy currentCategoryHierarchy = category3;
        currentCategoryHierarchy.setSortOrder(1);

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getSortOrder()); // category3
        assertEquals(2, result.get(1).getSortOrder()); // category1
        assertEquals(3, result.get(2).getSortOrder()); // category2
    }

    @Test
    void testReorderSameLevelCategories_MoveToLastPosition() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of(category1, category2, category3);
        CategoryHierarchy currentCategoryHierarchy = category1;
        currentCategoryHierarchy.setSortOrder(3);

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getSortOrder()); // category2
        assertEquals(2, result.get(1).getSortOrder()); // category3
        assertEquals(3, result.get(2).getSortOrder()); // category1 (current)
    }

    @Test
    void testReorderSameLevelCategories_NoChangeInSortOrder() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of(category1, category2, category3);
        CategoryHierarchy currentCategoryHierarchy = category2;

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getSortOrder()); // category1
        assertEquals(2, result.get(1).getSortOrder()); // category2 (current)
        assertEquals(3, result.get(2).getSortOrder()); // category3
    }

    @Test
    void testReorderSameLevelCategories_EmptyList() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of();
        CategoryHierarchy currentCategoryHierarchy = category1;

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertFalse(result.isEmpty());
    }

    @Test
    void testReorderSameLevelCategories_SingleElementList() {
        // Given
        List<CategoryHierarchy> categoryHierarchyList = List.of(category1);
        CategoryHierarchy currentCategoryHierarchy = category1;

        // When
        List<CategoryHierarchy> result = currentCategoryHierarchy.reorderSameLevelCategories(categoryHierarchyList,
            currentCategoryHierarchy);

        // Then
        assertEquals(1, result.size());
        assertEquals(1, result.getFirst().getSortOrder()); // Only one category
    }

    private CategoryHierarchy createHierarchy(UUID categoryId, UUID ancestor, int depth) {
        return CategoryHierarchy.builder()
            .id(UUID.randomUUID())
            .categoryId(categoryId)
            .ancestorCategoryId(ancestor)
            .depth(depth)
            .sortOrder(1)
            .build();
    }
}