package com.mercaso.ims.infrastructure.process.parser;

import static com.mercaso.ims.infrastructure.process.parser.CostcoItemCostCollectionParser.COSTCO;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.infrastructure.excel.data.CostcoItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.processor.CostcoItemDailyUpdateSheetProcessor;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CostcoItemCostCollectionParserTest {

    @Mock
    ItemCostCollectionService itemCostCollectionService;
    @Mock
    CostcoItemDailyUpdateSheetProcessor costcoItemDailyUpdateSheetProcessor;
    @InjectMocks
    CostcoItemCostCollectionParser costcoItemCostCollectionParser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testParse() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        CostcoItemDailyUpdatedData costcoItemDailyUpdatedData = new CostcoItemDailyUpdatedData(
            "whseNumber",
            "dNumber",
            "description",
            "cat1AndDescription",
            "vendorItemNumber",
            "description1",
            "description2",
            new BigDecimal(0),
            1,
            "inTransit",
            "onOrder",
            "statusDesc",
            "bbs",
            "randomWeight",
            "upc",
            "mpkQty",
            "mpkType",
            "unitWeight",
            "asOf");
        when(itemCostCollectionService.findById(any(UUID.class))).thenReturn(itemCostCollection);
        when(costcoItemDailyUpdateSheetProcessor.process(anyString())).thenReturn(List.of(costcoItemDailyUpdatedData));

        List<ItemCostCollectionItemParsingResultDto> result = costcoItemCostCollectionParser.parse(UUID.randomUUID());
        Assertions.assertEquals(costcoItemDailyUpdatedData.getVendorItemNumber(), result.get(0).getVendorSkuNumber());
    }

    @Test
    void testIsSupported() {
        boolean result = costcoItemCostCollectionParser.isSupported(COSTCO, ItemCostCollectionSources.COSTCO_DAILY_ITEM_LISTS);
        Assertions.assertEquals(true, result);
    }
}