package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

class SearchBulkExportRecordsRestApiIT extends AbstractIT {

    private String testUserName = "testUser";

    @BeforeEach
    void setUp() {
        bulkExportRecordsTestUtil.createTestBulkExportRecords();
    }

    @Test
    void searchBulkExportRecords_WithDefaultParams_ShouldReturnResults() throws Exception {
        // When
        BulkExportRecordsSearchDto response = searchBulkExportRecordsRestApiUtil.searchBulkExportRecords();

        // Then
        assertNotNull(response);
        assertTrue(response.getTotalCount() >= 0);
        assertNotNull(response.getData());
    }

    @Test
    void searchBulkExportRecords_WithPagination_ShouldReturnCorrectPage() throws Exception {
        // When
        BulkExportRecordsSearchDto response = searchBulkExportRecordsRestApiUtil.searchBulkExportRecords(1, 10);

        // Then
        assertNotNull(response);
        assertTrue(response.getData().size() <= 10);
        assertTrue(response.getTotalCount() >= 0);
    }

    @Test
    void searchBulkExportRecords_WithDateRange_ShouldFilterByDate() throws Exception {
        // Given
        Instant startDate = Instant.now().minusSeconds(3600);
        Instant endDate = Instant.now().plusSeconds(3600);

        // When
        BulkExportRecordsSearchDto response = searchBulkExportRecordsRestApiUtil.searchBulkExportRecordsByDateRange(startDate, endDate);

        // Then
        assertNotNull(response);
        assertTrue(response.getTotalCount() >= 0);
        assertNotNull(response.getData());
    }

    @Test
    void searchBulkExportRecords_WithUserName_ShouldFilterByUser() throws Exception {
        // When
        BulkExportRecordsSearchDto response = searchBulkExportRecordsRestApiUtil.searchBulkExportRecordsByUserName(testUserName);

        // Then
        assertNotNull(response);
        assertTrue(response.getTotalCount() >= 0);
        assertNotNull(response.getData());
        // Note: The actual filtering logic depends on the implementation
        // For now, we just verify the response structure is correct
    }

    @Test
    void searchBulkExportRecords_WithAllParameters_ShouldReturnSuccess() throws Exception {
        // Given
        Instant startDate = Instant.now().minusSeconds(7200);
        Instant endDate = Instant.now().plusSeconds(3600);

        // When
        BulkExportRecordsSearchDto response = searchBulkExportRecordsRestApiUtil.searchBulkExportRecordsWithAllParams(
            1, 10, startDate, endDate, testUserName);

        // Then
        assertNotNull(response);
        assertTrue(response.getTotalCount() >= 0);
        assertNotNull(response.getData());
        assertTrue(response.getData().size() <= 10);
    }
}