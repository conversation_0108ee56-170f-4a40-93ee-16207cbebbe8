package com.mercaso.ims.infrastructure.external.dify;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class WorkflowStreamProcessorTest {

    private WorkflowStreamProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new WorkflowStreamProcessor();
    }

    @ParameterizedTest
    @MethodSource("successWorkflowTestData")
    void processStream_SuccessfulWorkflows_ReturnsSuccessResult(String eventType, String workflowId, 
            int expectedTokens, int expectedSteps, double expectedTime) throws IOException {
        // Arrange
        String streamData = String.format("""
            data: {"event": "workflow_started", "workflow_run_id": "%s"}
            
            data: {"event": "%s", "data": {"total_tokens": %d, "total_steps": %d, "elapsed_time": %.1f, "outputs": {"result": "success"}}}
            
            data: [DONE]
            """, workflowId, eventType, expectedTokens, expectedSteps, expectedTime);
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals(workflowId, result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(expectedTokens, result.get("total_tokens").asInt());
        assertEquals(expectedSteps, result.get("total_steps").asInt());
        assertEquals(expectedTime, result.get("elapsed_time").asDouble());
        assertTrue(result.has("output"));
    }

    private static Stream<Arguments> successWorkflowTestData() {
        return Stream.of(
            Arguments.of("workflow_succeeded", "test-workflow-123", 100, 5, 2.5),
            Arguments.of("workflow_finished", "test-workflow-456", 200, 10, 5.0)
        );
    }

    @ParameterizedTest
    @MethodSource("failedWorkflowTestData")
    void processStream_FailedWorkflows_ReturnsFailedResult(String eventType, String workflowId, 
            String errorData, String expectedErrorMessage) throws IOException {
        // Arrange
        String streamData = String.format("""
            data: {"event": "workflow_started", "workflow_run_id": "%s"}
            
            data: {"event": "%s", "data": %s}
            
            data: [DONE]
            """, workflowId, eventType, errorData);
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals(workflowId, result.get("workflow_run_id").asText());
        assertEquals("failed", result.get("status").asText());
        assertEquals(expectedErrorMessage, result.get("error").asText());
    }

    private static Stream<Arguments> failedWorkflowTestData() {
        return Stream.of(
            Arguments.of("workflow_failed", "test-workflow-789", "{\"error\": \"Custom error message\"}", "Custom error message"),
            Arguments.of("error", "test-workflow-error", "{\"error\": \"System error occurred\"}", "System error occurred"),
            Arguments.of("workflow_failed", "test-workflow-no-error", "{}", "Workflow execution failed")
        );
    }

    @Test
    void processStream_StartedButNoCompletion_ReturnsTimeoutResult() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-timeout"}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-timeout", result.get("workflow_run_id").asText());
        assertEquals("timeout", result.get("status").asText());
        assertEquals("No completion event received in SSE stream", result.get("error").asText());
    }

    @ParameterizedTest
    @MethodSource("ioExceptionTestData")
    void processStream_InvalidInputs_ThrowsIOException(String streamData, String testName) {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act & Assert
        IOException exception = assertThrows(IOException.class, 
            () -> processor.processStream(inputStream));
        
        assertEquals("Failed to get workflow result from SSE stream", exception.getMessage());
    }

    private static Stream<Arguments> ioExceptionTestData() {
        return Stream.of(
            Arguments.of("""
                data: {"event": "unknown_event", "data": {}}
                
                data: [DONE]
                """, "NoWorkflowStarted"),
            Arguments.of("", "EmptyStream"),
            Arguments.of("""
                
                
                
                """, "OnlyEmptyLines")
        );
    }

    @ParameterizedTest
    @MethodSource("ignoreInvalidDataTestData")
    void processStream_IgnoresInvalidData_ReturnsSuccessResult(String testDescription, String workflowId, 
            String invalidDataLines, int expectedTokens, int expectedSteps, double expectedTime) throws IOException {
        // Arrange
        String streamData = String.format("""
            data: {"event": "workflow_started", "workflow_run_id": "%s"}
            
            %s
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": %d, "total_steps": %d, "elapsed_time": %.1f, "outputs": {}}}
            
            data: [DONE]
            """, workflowId, invalidDataLines, expectedTokens, expectedSteps, expectedTime);
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals(workflowId, result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(expectedTokens, result.get("total_tokens").asInt());
        assertEquals(expectedSteps, result.get("total_steps").asInt());
        assertEquals(expectedTime, result.get("elapsed_time").asDouble());
    }

    private static Stream<Arguments> ignoreInvalidDataTestData() {
        return Stream.of(
            Arguments.of("InvalidJsonData", "test-workflow-invalid", 
                "data: invalid json data", 50, 3, 1.5),
            Arguments.of("UnknownEvent", "test-workflow-unknown", 
                "data: {\"event\": \"unknown_event\", \"data\": {\"some\": \"data\"}}", 75, 4, 2.0),
            Arguments.of("NonDataLines", "test-workflow-mixed", 
                "event: workflow_started\nid: 123\nretry: 3000", 25, 2, 1.0)
        );
    }

    @Test
    void processStream_MissingDataFields_HandlesGracefully() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-missing"}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 30}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-missing", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(30, result.get("total_tokens").asInt());
        assertEquals(0, result.get("total_steps").asInt()); // Default value for missing field
        assertEquals(0.0, result.get("elapsed_time").asDouble()); // Default value for missing field
    }

    @Test
    void processStream_MultipleCompletionEvents_UsesLastEvent() throws IOException {
        // Arrange
        String streamData = """
            data: {"event": "workflow_started", "workflow_run_id": "test-workflow-multiple"}
            
            data: {"event": "workflow_failed", "data": {"error": "First error"}}
            
            data: {"event": "workflow_succeeded", "data": {"total_tokens": 40, "total_steps": 3, "elapsed_time": 1.5, "outputs": {}}}
            
            data: [DONE]
            """;
        InputStream inputStream = new ByteArrayInputStream(streamData.getBytes());

        // Act
        ObjectNode result = processor.processStream(inputStream);

        // Assert
        assertNotNull(result);
        assertEquals("test-workflow-multiple", result.get("workflow_run_id").asText());
        assertEquals("succeeded", result.get("status").asText());
        assertEquals(40, result.get("total_tokens").asInt());
        assertFalse(result.has("error")); // Should not have error since last event was success
    }
}
