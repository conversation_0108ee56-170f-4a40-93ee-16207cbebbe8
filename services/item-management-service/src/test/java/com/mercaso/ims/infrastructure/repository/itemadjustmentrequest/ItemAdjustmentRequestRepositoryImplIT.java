package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.utils.Itemadjustmentrequest.ItemAdjustmentRequestUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemAdjustmentRequestRepositoryImplIT extends AbstractIT {


    @Test
    void testFindByIdIn() {
        ItemAdjustmentRequest itemAdjustmentRequest1 = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(null);
        ItemAdjustmentRequest itemAdjustmentRequest2 = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(null);

        UUID id1 = itemAdjustmentRequestRepository.save(itemAdjustmentRequest1).getId();
        UUID id2 = itemAdjustmentRequestRepository.save(itemAdjustmentRequest2).getId();

        List<ItemAdjustmentRequest> result = itemAdjustmentRequestRepository.findByIdIn(List.of(id1, id2));
        Assertions.assertEquals(2, result.size());
    }

    @Test
    void testSave() {
        ItemAdjustmentRequest request = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(null);

        request = itemAdjustmentRequestRepository.save(request);
        UUID id = request.getId();

        ItemAdjustmentRequest result = itemAdjustmentRequestRepository.findById(id);
        Assertions.assertEquals(id, result.getId());
    }

    @Test
    void testFindById() {
        ItemAdjustmentRequest request = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(null);

        request = itemAdjustmentRequestRepository.save(request);

        ItemAdjustmentRequest result = itemAdjustmentRequestRepository.findById(request.getId());
        Assertions.assertEquals(request.getId(), result.getId());
    }

    @Test
    void testDeleteById() {
        ItemAdjustmentRequest request = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(null);

        request = itemAdjustmentRequestRepository.save(request);
        UUID id = request.getId();

        itemAdjustmentRequestRepository.deleteById(id);
        ItemAdjustmentRequest result = itemAdjustmentRequestRepository.findById(id);
        Assertions.assertEquals(null, result);
    }
}