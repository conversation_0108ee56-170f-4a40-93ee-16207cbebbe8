package com.mercaso.ims.infrastructure.apitaskprocess.finale;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for FinaleCreateItemProcessor
 */
class FinaleCreateItemProcessorTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private FinaleExternalApiClient finaleExternalApiClient;

    private FinaleCreateItemProcessor processor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new FinaleCreateItemProcessor(
                objectMapper, finaleExternalApiClient);
    }

    @Test
    void getTaskType_ReturnsCorrectTaskType() {
        // Act
        String result = processor.getTaskType();

        // Assert
        assertEquals(TaskType.FINALE_CREATE_ITEM.getType(), result);
        assertEquals("FINALE_CREATE_ITEM", result);
    }

    @Test
    void canProcess_ReturnsTrue() {
        // Act
        boolean result = processor.canProcess("FINALE_CREATE_ITEM");

        // Assert
        assertTrue(result);
    }

    @Test
    void canProcess_ReturnsFalse() {
        // Act
        boolean result = processor.canProcess("FINALE_UPDATE_ITEM");

        // Assert
        assertFalse(result);
    }

    @Test
    void canProcess_InvalidTaskType_ReturnsFalse() {
        // Act
        boolean result = processor.canProcess("INVALID_TASK_TYPE");

        // Assert
        assertFalse(result);
    }

    @Test
    void needsResponse_ReturnsFalse() {
        // Act
        boolean result = processor.needsResponse();

        // Assert
        assertFalse(result);
    }

    @Test
    void executeTask_SuccessfulExecution_ReturnsNull() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        doNothing().when(finaleExternalApiClient).createFinaleItem(anyString(), anyString());

        // Act
        Void result = processor.executeTask(task);

        // Assert
        assertNull(result);
        verify(finaleExternalApiClient).createFinaleItem("TEST-SKU-123", null);
        verify(objectMapper).readValue(anyString(), eq(TaskRequestPayload.class));
    }

    @Test
    void executeTask_JsonProcessingException_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        JsonProcessingException expectedException = new JsonProcessingException("Invalid JSON") {};

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenThrow(expectedException);

        // Act & Assert
        JsonProcessingException exception = assertThrows(JsonProcessingException.class,
                () -> processor.executeTask(task));

        assertEquals(expectedException, exception);
        verify(finaleExternalApiClient, never()).createFinaleItem(anyString(), anyString());
    }

    @Test
    void validateTask_ValidTask_NoExceptionThrown() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        assertDoesNotThrow(() -> processor.validateTask(task));
    }

    @Test
    void validateTask_MissingSkuNumber_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithoutSku();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("SKU number is required"));
    }

    @Test
    void validateTask_EmptySkuNumber_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithEmptySku();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("SKU number is required"));
    }

    @Test
    void validateTask_JsonProcessingError_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        JsonProcessingException expectedException = new JsonProcessingException("Invalid JSON") {};

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenThrow(expectedException);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("Invalid request payload for create item task"));
        assertTrue(exception.getMessage().contains("Invalid JSON"));
    }

    @Test
    void validateTask_SkuParameterWithFallbackName_NoExceptionThrown() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithSkuFallback();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        assertDoesNotThrow(() -> processor.validateTask(task));
    }

    // Helper methods
    private ApiTaskQueue createTestTask() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_CREATE_ITEM")
                .apiEndpoint("/finale/create-item")
                .httpMethod("POST")
                .status(TaskStatus.PENDING)
                .requestPayload("{\"methodName\":\"createFinaleItem\",\"className\":\"FinaleExternalApiClient\"}")
                .createdAt(Instant.now())
                .build();
    }

    private TaskRequestPayload createValidRequestPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("skuNumber", "TEST-SKU-123");

        return TaskRequestPayload.builder()
                .methodName("createFinaleItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithoutSku() {
        Map<String, Object> parameters = new HashMap<>();

        return TaskRequestPayload.builder()
                .methodName("createFinaleItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithEmptySku() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("skuNumber", "");

        return TaskRequestPayload.builder()
                .methodName("createFinaleItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithSkuFallback() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("sku", "TEST-SKU-123"); // Using fallback parameter name

        return TaskRequestPayload.builder()
                .methodName("createFinaleItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }
}
