package com.mercaso.ims.infrastructure.repository.item;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ItemType;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.domain.item.enums.SalesStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.ItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.item.jpa.mapper.ItemDoMapper;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.ItemGradeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.ItemUPCJpaDao;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.utils.item.ItemUtil;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemRepositoryImpl.class})
class ItemRepositoryImplTest extends AbstractTest {

    @MockBean
    private ItemDoMapper itemDoMapper;

    @MockBean
    private ItemJpaDao itemJpaDao;

    @MockBean
    private ItemGradeJpaDao itemGradeJpaDao;

    @Autowired
    private ItemRepositoryImpl itemRepositoryImpl;

    @MockBean
    private ItemUPCJpaDao itemUPCJpaDao;


    @Test
    void testSaveWithItem_givenItemDoAvailabilityStatusIsActive_thenReturnNull() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        when(itemJpaDao.save(Mockito.any())).thenReturn(itemDo);

        ItemDo itemDo2 = new ItemDo();
        itemDo2.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo2.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setCategory("Category");
        itemDo2.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setClazz("Clazz");
        itemDo2.setCompanyId(1L);
        itemDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo2.setCreatedUserName("createUser");
        itemDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo2.setDeletedUserName("createUser");
        itemDo2.setDepartment("Department");
        itemDo2.setDescription("The characteristics of someone or something");
        itemDo2.setDetail("Detail");
        itemDo2.setHandle("Handle");
        itemDo2.setHeight(10.0d);
        itemDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setItemAttributes(new ArrayList<>());
        itemDo2.setItemImages(new ArrayList<>());
        itemDo2.setItemTags(new ArrayList<>());
        itemDo2.setItemType(ItemType.SELF_OPERATED);
        itemDo2.setItemUPCs(new ArrayList<>());
        itemDo2.setLength(10.0d);
        itemDo2.setLocationId(1L);
        itemDo2.setName("Name");
        itemDo2.setNewDescription("New Description");
        itemDo2.setNote("Note");
        itemDo2.setPackageSize(3);
        itemDo2.setPackageType(PackageType.UNKNOWN);
        itemDo2.setPhoto("<EMAIL>");
        itemDo2.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setSalesStatus(SalesStatus.LISTING);
        itemDo2.setShelfLife("Shelf Life");
        itemDo2.setSkuNumber("42");
        itemDo2.setSubCategory("Sub Category");
        itemDo2.setTitle("Dr");
        itemDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setUpdatedBy("2020-03-01");
        itemDo2.setUpdatedUserName("createUser");
        itemDo2.setWidth(10.0d);
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(null);
        when(itemDoMapper.domainToDo(Mockito.any())).thenReturn(itemDo2);

        // Act
        Item actualSaveResult = itemRepositoryImpl.save(null);

        // Assert
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemDoMapper).domainToDo(isNull());
        verify(itemJpaDao).save(isA(ItemDo.class));
        assertNull(actualSaveResult);
    }

    @Test
    void testSaveWithItem_thenThrowImsBusinessException() {
        // Arrange
        when(itemDoMapper.domainToDo(Mockito.any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemRepositoryImpl.save(null));
        verify(itemDoMapper).domainToDo(isNull());
    }


    @Test
    void testFindByIdWithUuid_givenItemDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        Optional<ItemDo> ofResult = Optional.of(itemDo);
        when(itemJpaDao.findById(Mockito.any())).thenReturn(ofResult);
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(null);

        // Act
        Item actualFindByIdResult = itemRepositoryImpl.findById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        Optional<ItemDo> ofResult = Optional.of(itemDo);
        when(itemJpaDao.findById(Mockito.any())).thenReturn(ofResult);
        when(itemDoMapper.doToDomain(Mockito.any()))
            .thenThrow(new ImsBusinessException("[ItemRepository] find itemDo by id :{}"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> itemRepositoryImpl.findById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdateWithItem_givenItemDoAvailabilityStatusIsActive_thenReturnNull() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        Optional<ItemDo> ofResult = Optional.of(itemDo);

        ItemDo itemDo2 = new ItemDo();
        itemDo2.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo2.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setCategory("Category");
        itemDo2.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setClazz("Clazz");
        itemDo2.setCompanyId(1L);
        itemDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo2.setCreatedUserName("createUser");
        itemDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo2.setDeletedUserName("createUser");
        itemDo2.setDepartment("Department");
        itemDo2.setDescription("The characteristics of someone or something");
        itemDo2.setDetail("Detail");
        itemDo2.setHandle("Handle");
        itemDo2.setHeight(10.0d);
        itemDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setItemAttributes(new ArrayList<>());
        itemDo2.setItemImages(new ArrayList<>());
        itemDo2.setItemTags(new ArrayList<>());
        itemDo2.setItemType(ItemType.SELF_OPERATED);
        itemDo2.setItemUPCs(new ArrayList<>());
        itemDo2.setLength(10.0d);
        itemDo2.setLocationId(1L);
        itemDo2.setName("Name");
        itemDo2.setNewDescription("New Description");
        itemDo2.setNote("Note");
        itemDo2.setPackageSize(3);
        itemDo2.setPackageType(PackageType.UNKNOWN);
        itemDo2.setPhoto("<EMAIL>");
        itemDo2.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setSalesStatus(SalesStatus.LISTING);
        itemDo2.setShelfLife("Shelf Life");
        itemDo2.setSkuNumber("42");
        itemDo2.setSubCategory("Sub Category");
        itemDo2.setTitle("Dr");
        itemDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setUpdatedBy("2020-03-01");
        itemDo2.setUpdatedUserName("createUser");
        itemDo2.setWidth(10.0d);
        when(itemJpaDao.save(Mockito.any())).thenReturn(itemDo2);
        when(itemJpaDao.findById(Mockito.any())).thenReturn(ofResult);

        ItemDo itemDo3 = new ItemDo();
        itemDo3.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo3.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo3.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo3.setCategory("Category");
        itemDo3.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo3.setClazz("Clazz");
        itemDo3.setCompanyId(1L);
        itemDo3.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo3.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo3.setCreatedUserName("createUser");
        itemDo3.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo3.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo3.setDeletedUserName("createUser");
        itemDo3.setDepartment("Department");
        itemDo3.setDescription("The characteristics of someone or something");
        itemDo3.setDetail("Detail");
        itemDo3.setHandle("Handle");
        itemDo3.setHeight(10.0d);
        itemDo3.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo3.setItemAttributes(new ArrayList<>());
        itemDo3.setItemImages(new ArrayList<>());
        itemDo3.setItemTags(new ArrayList<>());
        itemDo3.setItemType(ItemType.SELF_OPERATED);
        itemDo3.setItemUPCs(new ArrayList<>());
        itemDo3.setLength(10.0d);
        itemDo3.setLocationId(1L);
        itemDo3.setName("Name");
        itemDo3.setNewDescription("New Description");
        itemDo3.setNote("Note");
        itemDo3.setPackageSize(3);
        itemDo3.setPackageType(PackageType.UNKNOWN);
        itemDo3.setPhoto("<EMAIL>");
        itemDo3.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo3.setSalesStatus(SalesStatus.LISTING);
        itemDo3.setShelfLife("Shelf Life");
        itemDo3.setSkuNumber("42");
        itemDo3.setSubCategory("Sub Category");
        itemDo3.setTitle("Dr");
        itemDo3.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo3.setUpdatedBy("2020-03-01");
        itemDo3.setUpdatedUserName("createUser");
        itemDo3.setWidth(10.0d);
        Item item = ItemUtil.buildItem();
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(item);
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(null);
        when(itemDoMapper.domainToDo(Mockito.any())).thenReturn(itemDo3);
        Item domain = mock(Item.class);
        when(domain.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Act
        Item actualUpdateResult = itemRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemDoMapper).domainToDo(isA(Item.class));
        verify(itemJpaDao).findById(isA(UUID.class));
        verify(itemJpaDao).save(isA(ItemDo.class));
        assertNull(actualUpdateResult);
    }

    @Test
    void testDeleteById_givenItemDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        Optional<ItemDo> ofResult = Optional.of(itemDo);

        ItemDo itemDo2 = new ItemDo();
        itemDo2.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo2.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setCategory("Category");
        itemDo2.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setClazz("Clazz");
        itemDo2.setCompanyId(1L);
        itemDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo2.setCreatedUserName("createUser");
        itemDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo2.setDeletedUserName("createUser");
        itemDo2.setDepartment("Department");
        itemDo2.setDescription("The characteristics of someone or something");
        itemDo2.setDetail("Detail");
        itemDo2.setHandle("Handle");
        itemDo2.setHeight(10.0d);
        itemDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setItemAttributes(new ArrayList<>());
        itemDo2.setItemImages(new ArrayList<>());
        itemDo2.setItemTags(new ArrayList<>());
        itemDo2.setItemType(ItemType.SELF_OPERATED);
        itemDo2.setItemUPCs(new ArrayList<>());
        itemDo2.setLength(10.0d);
        itemDo2.setLocationId(1L);
        itemDo2.setName("Name");
        itemDo2.setNewDescription("New Description");
        itemDo2.setNote("Note");
        itemDo2.setPackageSize(3);
        itemDo2.setPackageType(PackageType.UNKNOWN);
        itemDo2.setPhoto("<EMAIL>");
        itemDo2.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setSalesStatus(SalesStatus.LISTING);
        itemDo2.setShelfLife("Shelf Life");
        itemDo2.setSkuNumber("42");
        itemDo2.setSubCategory("Sub Category");
        itemDo2.setTitle("Dr");
        itemDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setUpdatedBy("2020-03-01");
        itemDo2.setUpdatedUserName("createUser");
        itemDo2.setWidth(10.0d);
        when(itemJpaDao.save(Mockito.any())).thenReturn(itemDo2);
        when(itemJpaDao.findById(Mockito.any())).thenReturn(ofResult);
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(null);

        // Act
        Item actualDeleteByIdResult = itemRepositoryImpl.deleteById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemJpaDao).findById(isA(UUID.class));
        verify(itemJpaDao).save(isA(ItemDo.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        Optional<ItemDo> ofResult = Optional.of(itemDo);

        ItemDo itemDo2 = new ItemDo();
        itemDo2.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo2.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setCategory("Category");
        itemDo2.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setClazz("Clazz");
        itemDo2.setCompanyId(1L);
        itemDo2.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo2.setCreatedUserName("createUser");
        itemDo2.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo2.setDeletedUserName("createUser");
        itemDo2.setDepartment("Department");
        itemDo2.setDescription("The characteristics of someone or something");
        itemDo2.setDetail("Detail");
        itemDo2.setHandle("Handle");
        itemDo2.setHeight(10.0d);
        itemDo2.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setItemAttributes(new ArrayList<>());
        itemDo2.setItemImages(new ArrayList<>());
        itemDo2.setItemTags(new ArrayList<>());
        itemDo2.setItemType(ItemType.SELF_OPERATED);
        itemDo2.setItemUPCs(new ArrayList<>());
        itemDo2.setLength(10.0d);
        itemDo2.setLocationId(1L);
        itemDo2.setName("Name");
        itemDo2.setNewDescription("New Description");
        itemDo2.setNote("Note");
        itemDo2.setPackageSize(3);
        itemDo2.setPackageType(PackageType.UNKNOWN);
        itemDo2.setPhoto("<EMAIL>");
        itemDo2.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo2.setSalesStatus(SalesStatus.LISTING);
        itemDo2.setShelfLife("Shelf Life");
        itemDo2.setSkuNumber("42");
        itemDo2.setSubCategory("Sub Category");
        itemDo2.setTitle("Dr");
        itemDo2.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo2.setUpdatedBy("2020-03-01");
        itemDo2.setUpdatedUserName("createUser");
        itemDo2.setWidth(10.0d);
        when(itemJpaDao.save(Mockito.any())).thenReturn(itemDo2);
        when(itemJpaDao.findById(Mockito.any())).thenReturn(ofResult);
        when(itemDoMapper.doToDomain(Mockito.any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> itemRepositoryImpl.deleteById(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        verify(itemJpaDao).findById(isA(UUID.class));
        verify(itemJpaDao).save(isA(ItemDo.class));
    }


    @Test
    void testFindBySku_givenItemDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        when(itemJpaDao.findBySkuNumber(Mockito.any())).thenReturn(itemDo);
        when(itemDoMapper.doToDomain(Mockito.any())).thenReturn(null);

        // Act
        Item actualFindBySkuResult = itemRepositoryImpl.findBySku("Sku");

        // Assert
        verify(itemJpaDao).findBySkuNumber("Sku");
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
        assertNull(actualFindBySkuResult);
    }


    @Test
    void testFindBySku_thenThrowImsBusinessException() {
        // Arrange
        ItemDo itemDo = new ItemDo();
        itemDo.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
        itemDo.setBackupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setBrandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setCategory("Category");
        itemDo.setCategoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setClazz("Clazz");
        itemDo.setCompanyId(1L);
        itemDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemDo.setCreatedUserName("createUser");
        itemDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemDo.setDeletedUserName("createUser");
        itemDo.setDepartment("Department");
        itemDo.setDescription("The characteristics of someone or something");
        itemDo.setDetail("Detail");
        itemDo.setHandle("Handle");
        itemDo.setHeight(10.0d);
        itemDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setItemAttributes(new ArrayList<>());
        itemDo.setItemImages(new ArrayList<>());
        itemDo.setItemTags(new ArrayList<>());
        itemDo.setItemType(ItemType.SELF_OPERATED);
        itemDo.setItemUPCs(new ArrayList<>());
        itemDo.setLength(10.0d);
        itemDo.setLocationId(1L);
        itemDo.setName("Name");
        itemDo.setNewDescription("New Description");
        itemDo.setNote("Note");
        itemDo.setPackageSize(3);
        itemDo.setPackageType(PackageType.UNKNOWN);
        itemDo.setPhoto("<EMAIL>");
        itemDo.setPrimaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemDo.setSalesStatus(SalesStatus.LISTING);
        itemDo.setShelfLife("Shelf Life");
        itemDo.setSkuNumber("42");
        itemDo.setSubCategory("Sub Category");
        itemDo.setTitle("Dr");
        itemDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemDo.setUpdatedBy("2020-03-01");
        itemDo.setUpdatedUserName("createUser");
        itemDo.setWidth(10.0d);
        when(itemJpaDao.findBySkuNumber(Mockito.any())).thenReturn(itemDo);
        when(itemDoMapper.doToDomain(Mockito.any())).thenThrow(new ImsBusinessException("findBySku :{}"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemRepositoryImpl.findBySku("Sku"));
        verify(itemJpaDao).findBySkuNumber("Sku");
        verify(itemDoMapper).doToDomain(isA(ItemDo.class));
    }


    @Test
    void testFindByCreatedAtBetween_givenItemDoMapper_thenReturnPageImpl() {
        // Arrange
        when(itemJpaDao.findByCreatedAtBetweenAndPhotoIsLikeIgnoreCase(Mockito.any(),
            Mockito.any(),
            Mockito.any(),
            Mockito.any())).thenReturn(new PageImpl<>(new ArrayList<>()));
        Instant begin = LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant();

        // Act
        Page<Item> actualFindByCreatedAtBetweenResult = itemRepositoryImpl.findByCreatedAtBetween(begin,
            LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant(), "<EMAIL>", null);

        // Assert
        verify(itemJpaDao).findByCreatedAtBetweenAndPhotoIsLikeIgnoreCase(isA(Instant.class), isA(Instant.class),
            eq("<EMAIL>"), isNull());
        assertInstanceOf(PageImpl.class, actualFindByCreatedAtBetweenResult);
        assertTrue(actualFindByCreatedAtBetweenResult.toList().isEmpty());
    }


    @Test
    void testFindAll_givenItemDoMapper_thenReturnPageImpl() {
        // Arrange
        when(itemJpaDao.findAll(Mockito.<Pageable>any())).thenReturn(new PageImpl<>(new ArrayList<>()));

        // Act
        Page<Item> actualFindAllResult = itemRepositoryImpl.findAll(null);

        // Assert
        verify(itemJpaDao).findAll((Pageable) isNull());
        assertInstanceOf(PageImpl.class, actualFindAllResult);
        assertTrue(actualFindAllResult.toList().isEmpty());
    }


    @Test
    void testFindAllByIdIn_givenItemDoMapper_thenReturnEmpty() {
        // Arrange
        when(itemJpaDao.findAllByIdIn(Mockito.any())).thenReturn(new ArrayList<>());

        // Act
        List<Item> actualFindAllByIdInResult = itemRepositoryImpl.findAllByIdIn(new ArrayList<>());

        // Assert
        verify(itemJpaDao).findAllByIdIn(isA(List.class));
        assertTrue(actualFindAllByIdInResult.isEmpty());
    }


    @Test
    void testFindAllByUpcAndUpcType_givenItemJpaDao_thenReturnEmpty() {
        // Arrange
        when(itemUPCJpaDao.findAllByUpcNumberAndItemUpcType(Mockito.any(), Mockito.any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<Item> actualFindAllByUpcAndUpcTypeResult = itemRepositoryImpl.findAllByUpcAndUpcType("Upc",
            ItemUpcType.VARIANT_BARCODE);

        // Assert
        verify(itemUPCJpaDao).findAllByUpcNumberAndItemUpcType("Upc", ItemUpcType.VARIANT_BARCODE);
        assertTrue(actualFindAllByUpcAndUpcTypeResult.isEmpty());
    }


    @Test
    void testFindAllByUpcAndUpcType_thenCallsFindAllByIdIn() {
        // Arrange
        when(itemJpaDao.findAllByIdIn(Mockito.any())).thenReturn(new ArrayList<>());

        ItemUPCDo itemUPCDo = new ItemUPCDo();
        itemUPCDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemUPCDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemUPCDo.setCreatedUserName("createUser");
        itemUPCDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemUPCDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemUPCDo.setDeletedUserName("createUser");
        itemUPCDo.setId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemUPCDo.setItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        itemUPCDo.setItemUpcType(ItemUpcType.VARIANT_BARCODE);
        itemUPCDo.setUpcNumber("42");
        itemUPCDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemUPCDo.setUpdatedBy("2020-03-01");
        itemUPCDo.setUpdatedUserName("createUser");

        ArrayList<ItemUPCDo> itemUPCDoList = new ArrayList<>();
        itemUPCDoList.add(itemUPCDo);
        when(itemUPCJpaDao.findAllByUpcNumberAndItemUpcType(Mockito.any(), Mockito.any()))
            .thenReturn(itemUPCDoList);

        // Act
        List<Item> actualFindAllByUpcAndUpcTypeResult = itemRepositoryImpl.findAllByUpcAndUpcType("Upc",
            ItemUpcType.VARIANT_BARCODE);

        // Assert
        verify(itemJpaDao).findAllByIdIn(isA(List.class));
        verify(itemUPCJpaDao).findAllByUpcNumberAndItemUpcType("Upc", ItemUpcType.VARIANT_BARCODE);
        assertTrue(actualFindAllByUpcAndUpcTypeResult.isEmpty());
    }

    @Test
    void testFindByUpdatedAtBetween_givenItemDoMapper_thenReturnPageImpl() {
        // Arrange
        when(itemJpaDao.findByUpdatedAtBetween(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(new PageImpl<>(new ArrayList<>()));
        Instant begin = LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant();

        // Act
        Page<Item> actualFindByUpdatedAtBetweenResult = itemRepositoryImpl.findByUpdatedAtBetween(begin,
            LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant(), null);

        // Assert
        verify(itemJpaDao).findByUpdatedAtBetween(isA(Instant.class), isA(Instant.class), isNull());
        assertInstanceOf(PageImpl.class, actualFindByUpdatedAtBetweenResult);
        assertTrue(actualFindByUpdatedAtBetweenResult.toList().isEmpty());
    }
}
