package com.mercaso.ims.infrastructure.apitaskprocess;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for TaskExecutionContext
 */
class TaskExecutionContextTest {

    @BeforeEach
    void setUp() {
        // Ensure clean state before each test
        TaskExecutionContext.clearTaskExecution();
    }

    @AfterEach
    void tearDown() {
        // Clean up after each test
        TaskExecutionContext.clearTaskExecution();
    }

    @Test
    @DisplayName("Should initially not be in task execution context")
    void initialState_ShouldNotBeInTaskExecution() {
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should mark and clear task execution context correctly")
    void markAndClear_ShouldWorkCorrectly() {
        // Initially not in task execution
        assertFalse(TaskExecutionContext.isInTaskExecution());

        // Mark as task execution
        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Clear task execution
        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should handle nested task execution contexts")
    void nestedContexts_ShouldWorkCorrectly() {
        assertFalse(TaskExecutionContext.isInTaskExecution());

        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Marking again should still be true
        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Clear once
        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should execute runnable within task context")
    void executeInTaskContext_Runnable_ShouldWorkCorrectly() {
        assertFalse(TaskExecutionContext.isInTaskExecution());

        final boolean[] wasInContext = {false};

        TaskExecutionContext.executeInTaskContext(() -> {
            wasInContext[0] = TaskExecutionContext.isInTaskExecution();
        });

        assertTrue(wasInContext[0]);
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should execute supplier within task context")
    void executeInTaskContext_Supplier_ShouldWorkCorrectly() {
        assertFalse(TaskExecutionContext.isInTaskExecution());

        String result = TaskExecutionContext.executeInTaskContext(() -> {
            boolean inContext = TaskExecutionContext.isInTaskExecution();
            return inContext ? "IN_CONTEXT" : "NOT_IN_CONTEXT";
        });

        assertEquals("IN_CONTEXT", result);
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should handle exceptions in executeInTaskContext")
    void executeInTaskContext_WithException_ShouldCleanupContext() {
        assertFalse(TaskExecutionContext.isInTaskExecution());

        assertThrows(RuntimeException.class, () -> {
            TaskExecutionContext.executeInTaskContext(() -> {
                assertTrue(TaskExecutionContext.isInTaskExecution());
                throw new RuntimeException("Test exception");
            });
        });

        // Context should be cleaned up even after exception
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should be thread-local - different threads should have independent contexts")
    void threadLocal_ShouldBeIndependent() throws ExecutionException, InterruptedException {
        assertFalse(TaskExecutionContext.isInTaskExecution());

        // Mark current thread as in task execution
        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Check in another thread
        CompletableFuture<Boolean> otherThreadResult = CompletableFuture.supplyAsync(TaskExecutionContext::isInTaskExecution);

        // Other thread should not be in task execution context
        assertFalse(otherThreadResult.get());

        // Current thread should still be in task execution context
        assertTrue(TaskExecutionContext.isInTaskExecution());

        // Clean up
        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }

    @Test
    @DisplayName("Should handle multiple clear calls gracefully")
    void multipleClearCalls_ShouldBeGraceful() {
        TaskExecutionContext.markAsTaskExecution();
        assertTrue(TaskExecutionContext.isInTaskExecution());

        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());

        // Multiple clear calls should not cause issues
        TaskExecutionContext.clearTaskExecution();
        TaskExecutionContext.clearTaskExecution();
        assertFalse(TaskExecutionContext.isInTaskExecution());
    }
}
