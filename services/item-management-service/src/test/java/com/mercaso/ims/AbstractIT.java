package com.mercaso.ims;


import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.queryservice.VendorQueryApplicationService;
import com.mercaso.ims.application.service.*;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.enums.BrandStatus;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryRepository;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.domain.category.service.CategoryService;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.service.CategoryHierarchyService;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecordRepository;
import com.mercaso.ims.domain.email.EmailRepository;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecordRepository;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequestRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequestRepository;
import com.mercaso.ims.domain.itemcostchangerequest.service.ItemCostChangeRequestService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollectionRepository;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroupRepository;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceRepository;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrendRepository;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.domain.phone.PhoneNumberRepository;
import com.mercaso.ims.domain.testorder.TestOrderRepository;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.domain.vendorpoanalyzerecord.service.VendorPoAnalyzeRecordService;
import com.mercaso.ims.infrastructure.config.ImsAlertConfig;
import com.mercaso.ims.infrastructure.config.SlackConfig;
import com.mercaso.ims.infrastructure.external.aws_ocr.AwsAnalyzeExpenseAdaptor;
import com.mercaso.ims.infrastructure.external.downey.DowneyAdaptor;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.repository.businessevent.CustomizedBusinessEventJapDao;
import com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.CustomizedExceptionRecordJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.CustomizedItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.ItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.ItemAttributeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.CustomizedItemCostChangeRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.CustomizedItemCostCollectionJpaDao;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.ItemGradeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.dataobject.ItemGradeDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.ItemVersionJpaDao;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.dataobject.ItemVersionDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.VendorJpaDao;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemJpaDao;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import com.mercaso.ims.utils.bulkexportrecords.BulkExportRecordsTestUtil;
import com.mercaso.ims.utils.interfacesutils.AnalyzeVendorPoRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.BrandRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.BulkExportRecordsRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.BulkExportRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.CategoryRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.CategoryV2RestApiUtil;
import com.mercaso.ims.utils.interfacesutils.DifyWorkflowRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ExceptionRecordRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemAdjustmentRequestRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemAuditHistoryV2RestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemCostChangeRequestRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemPriceGroupRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemPriceRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.ItemVendorRebateRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.MerchandiseReportRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryAttributeRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryBrandRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryItemPriceGroupRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryItemRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryItemSalesTrendRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryVendorItemRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.QueryVendorRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchBulkExportRecordsRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchCategoryRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchCategoryTreeRequestRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchExceptionRecordRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchItemAdjustmentRequestRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchItemCostCollectionRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchItemPriceGroupRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.SearchItemRequestRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.VendorItemRestApiUtil;
import com.mercaso.ims.utils.interfacesutils.VendorRestApiUtil;
import com.mercaso.ims.utils.item.ItemDoUtil;
import com.mercaso.ims.utils.itempricegroup.ItemPriceGroupUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;


@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
})
@SpringBootTest(
  classes = Application.class,
  webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
  properties = {
    "spring.task.scheduling.enabled=false",
    "spring.datasource.hikari.maximum-pool-size=20",
    "spring.datasource.hikari.minimum-idle=2",
    "spring.datasource.hikari.connection-timeout=60000",
    "spring.datasource.hikari.leak-detection-threshold=0"
  }
)
@EmbeddedKafka(
  count = 1,
  brokerProperties = {
    "zookeeper.session.timeout.ms=180000",
    "zookeeper.connection.timeout.ms=180000"
  }
)
@AutoConfigureMockMvc
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public abstract class AbstractIT {


    private final String departmentStr = "department";
    private final String categoryStr = "category";
    private final String subCategoryStr = "subCategory";
    private final String clazzStr = "clazzStr";
    private final String brandNameStr = "brandName";
    private final String vendorNameStr = "vendorName";
    private final String vendorItemNumberStr = "vendorItemNumber";

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ItemAdjustmentRequestRestApiUtil itemAdjustmentRequestRestApiUtil;
    @Autowired
    protected ItemAdjustmentRequestDetailRepository itemAdjustmentRequestDetailRepository;
    @Autowired
    protected ItemRepository itemRepository;
    @Autowired
    protected ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;
    @Autowired
    protected ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService;
    @MockBean
    protected DocumentOperations documentOperations;
    @MockBean
    protected FeatureFlagsManager featureFlagsManager;
    @Autowired
    protected MerchandiseReportRestApiUtil merchandiseReportRestApiUtil;

    @Autowired
    protected AttributeService attributeService;
    @Autowired
    protected ItemApplicationService itemApplicationService;

    @Autowired
    protected CustomizedBusinessEventJapDao customizedBusinessEventJapDao;

    @Autowired
    protected ItemAttributeJpaDao attributeJpaDao;

    @MockBean
    protected ShopifyAdaptor shopifyAdaptor;
    @Autowired
    protected SearchItemRequestRestApiUtil searchItemRequestRestApiUtil;
    @Autowired
    protected ItemRestApiUtil itemRestApiUtil;
    @Autowired
    protected SearchCategoryTreeRequestRestApiUtil searchCategoryTreeRequestRestApiUtil;
    @Autowired
    protected ItemAuditHistoryV2RestApiUtil itemAuditHistoryV2RestApiUtil;
    @Autowired
    protected ItemAdjustmentRequestRepository itemAdjustmentRequestRepository;
    @Autowired
    protected QueryItemRestApiUtil queryItemRestApiUtil;
    @Autowired
    protected CategoryApplicationService categoryApplicationService;
    @Autowired
    protected CategoryService categoryService;
    @Autowired
    protected CategoryHierarchyService categoryHierarchyService;
    @Autowired
    protected BrandService brandService;
    @Autowired
    protected ItemService itemService;
    @Autowired
    protected ItemJpaDao itemJpaDao;
    @Autowired
    protected VendorJpaDao vendorJpaDao;
    @Autowired
    protected VendorItemJpaDao vendorItemJpaDao;
    @Autowired
    protected CustomizedItemJpaDao customizedItemJpaDao;

    @Autowired
    protected CategoryRestApiUtil categoryRestApiUtil;

    @Autowired
    protected CategoryV2RestApiUtil categoryV2RestApiUtil;

    @MockBean
    protected FinaleAdaptor finaleAdaptor;

    @MockBean
    protected FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    @Autowired
    protected QueryVendorRestApiUtil queryVendorRestApiUtil;

    @Autowired
    protected QueryBrandRestApiUtil queryBrandRestApiUtil;

    @Autowired
    protected QueryAttributeRestApiUtil queryAttributeRestApiUtil;
    @Autowired
    protected ItemRegPriceService itemRegPriceService;
    @Autowired
    protected VendorItemService vendorItemService;

    @Autowired
    protected BrandRestApiUtil brandRestApiUtil;

    @Autowired
    protected VendorItemRestApiUtil vendorItemRestApiUtil;

    @MockBean
    protected ImsAlertConfig imsAlertConfig;

    @MockBean
    protected SlackConfig slackConfig;

    @Autowired
    protected VendorService vendorService;

    @Autowired
    protected VendorRestApiUtil vendorRestApiUtil;

    @Autowired
    protected ItemCostChangeRequestRepository itemCostChangeRequestRepository;
    @Autowired
    protected ItemCostCollectionRepository itemCostCollectionRepository;

    @Autowired
    protected ItemPriceRestApiUtil itemPriceRestApiUtil;

    @Autowired
    protected ItemCostCollectionApplicationService itemCostCollectionApplicationService;

    @Autowired
    protected ItemCostChangeRequestApplicationService itemCostChangeRequestApplicationService;

    @Autowired
    protected AnalyzeVendorPoRestApiUtil analyzeVendorPoRestApiUtil;

    @MockBean
    protected AwsAnalyzeExpenseAdaptor awsAnalyzeExpenseAdaptor;

    @Autowired
    protected VendorQueryApplicationService vendorQueryApplicationService;

    @Autowired
    protected ItemCostChangeRequestRestApiUtil itemCostChangeRequestRestApiUtil;

    @Autowired
    protected ItemCostChangeRequestService itemCostChangeRequestService;

    @Autowired
    protected VendorPoAnalyzeRecordService vendorPoAnalyzeRecordService;

    @Autowired
    protected CustomizedItemCostCollectionJpaDao customizedItemCostCollectionJpaDao;

    @Autowired
    protected CustomizedItemCostChangeRequestJpaDao customizedItemCostChangeRequestJpaDao;

    @Autowired
    protected ItemPriceGroupRepository itemPriceGroupRepository;

    @Autowired
    protected ItemPriceGroupRestApiUtil itemPriceGroupRestApiUtil;

    @Autowired
    protected ExceptionRecordRepository exceptionRecordRepository;

    @Autowired
    protected ExceptionRecordRestApiUtil exceptionRecordRestApiUtil;

    @MockBean
    protected DowneyAdaptor downeyAdaptor;

    @Autowired
    protected CustomizedExceptionRecordJpaDao customizedExceptionRecordJpaDao;

    @Autowired
    protected QueryItemPriceGroupRestApiUtil queryItemPriceGroupRestApiUtil;

    @Autowired
    protected SearchItemPriceGroupRestApiUtil searchItemPriceGroupRestApiUtil;

    @Autowired
    protected VendorRepository vendorRepository;

    @Autowired
    protected ItemPromoPriceRepository itemPromoPriceRepositoryImpl;
    @Autowired
    protected TestOrderRepository testOrderRepository;

    @Autowired
    protected ItemSalesTrendRepository itemSalesTrendRepository;

    @Autowired
    protected CategoryRepository categoryRepository;

    @Autowired
    protected SearchExceptionRecordRestApiUtil searchExceptionRecordRestApiUtil;

    @Autowired
    protected SearchItemCostCollectionRestApiUtil searchItemCostCollectionRestApiUtil;

    @Autowired
    protected SearchItemAdjustmentRequestRestApiUtil searchItemAdjustmentRequestRestApiUtil;

    @Autowired
    protected ItemGradeJpaDao itemGradeJpaDao;

    @Autowired
    protected QueryVendorItemRestApiUtil queryVendorItemRestApiUtil;

    @Autowired
    protected QueryItemSalesTrendRestApiUtil queryItemSalesTrendRestApiUtil;

    @Autowired
    protected SearchCategoryRestApiUtil searchCategoryRestApiUtil;

    @Autowired
    protected SearchBulkExportRecordsRestApiUtil searchBulkExportRecordsRestApiUtil;

    @Autowired
    protected BulkExportRecordsTestUtil bulkExportRecordsTestUtil;

    @Autowired
    protected BulkExportRecordsRestApiUtil bulkExportRecordsRestApiUtil;

    @Autowired
    protected BulkExportRestApiUtil bulkExportRestApiUtil;
    @Autowired
    protected ItemVersionJpaDao itemVersionJpaDao;

    @Autowired
    protected DifyWorkflowRecordRepository difyWorkflowRecordRepository;

    @Autowired
    protected DifyWorkflowRestApiUtil difyWorkflowRestApiUtil;

    @Autowired
    protected ItemVendorRebateRestApiUtil itemVendorRebateRestApiUtil;

    @Autowired
    protected ItemVendorRebateRepository itemVendorRebateRepository;

    @Autowired
    protected PhoneNumberRepository phoneNumberRepository;

    @Autowired
    protected EmailRepository emailRepository;

    @Autowired
    protected com.mercaso.ims.domain.address.AddressRepository addressRepository;

    @MockBean
    protected ItemVersionApplicationService itemVersionApplicationService;

    protected ItemDo buildItemData(String skuNumber) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorNameStr,
            vendorItemNumberStr, new ArrayList<>());
    }

    protected ItemDo buildItemDataWithVendor(String skuNumber, String vendorName, String vendorItemNumber) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorName,
            vendorItemNumber, new ArrayList<>());

    }

    protected ItemDo buildItemData(String skuNumber,
        String department,
        String category,
        String subCategory,
        String clazz,
        String brandName,
        String vendorName,
        String vendorItemNumber) {
        return buildItemData(skuNumber,
            department,
            category,
            subCategory,
            clazz,
            brandName,
            vendorName,
            vendorItemNumber, new ArrayList<>());
    }


    protected ItemDo buildItemData(String skuNumber,
        String department,
        String category,
        String subCategory,
        String clazz,
        String brandName,
        String vendorName,
        String vendorItemNumber,
        List<ItemUPCDo> itemUPCs) {

        Brand brand = buildBrandData(brandName);
        UUID brandId = brand.getId();

        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorId = vendorDo.getId();

        ItemDo itemDo = itemJpaDao.findBySkuNumber(skuNumber);
        if (itemDo == null) {
            itemDo = ItemDoUtil.buildItemDo(skuNumber,
                vendorId,
                department,
                category,
                subCategory,
                clazz,
                brandId,
                itemUPCs);
            itemDo = itemJpaDao.save(itemDo);
            ItemGradeDo itemGradeDo = new ItemGradeDo();
            itemGradeDo.setItemId(itemDo.getId().toString());
            itemGradeDo.setSkuNumber(itemDo.getSkuNumber());
            itemGradeDo.setTotalRevenue(BigDecimal.valueOf(100));
            itemGradeDo.setGrade(ItemParetoGrade.A);
            itemGradeJpaDao.save(itemGradeDo);
            buildVendorItemData(vendorItemNumber, vendorId, itemDo.getId());
            buildBottleSizeItemAttributeData(24f, "oz", itemDo.getId());
            buildItemRegPriceData(itemDo.getId());

            ItemVersionDo itemVersionDo = new ItemVersionDo();
            itemVersionDo.setItemData(SerializationUtils.serialize(itemDo));
            itemVersionDo.setVersionNumber(1);
            itemVersionDo.setSkuNumber(itemDo.getSkuNumber());
            itemVersionDo.setItemId(itemDo.getId());
            itemVersionJpaDao.save(itemVersionDo);
        }

        return itemDo;
    }

    protected void buildBottleSizeItemAttributeData(Float value, String unit, UUID itemId) {

        ItemAttributeDo attributeDo = new ItemAttributeDo();
        attributeDo.setAttributeId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        attributeDo.setItemId(itemId);
        attributeDo.setValue(value.toString());
        attributeDo.setUnit(unit);
        attributeJpaDao.save(attributeDo);
    }

    protected VendorDo buildVendorDoData(String vendorName) {
        VendorDo vendorDo = vendorJpaDao.findByVendorName(vendorName);
        if (vendorDo == null) {
            vendorDo = VendorUtil.buildVendorDo(vendorName);
            return vendorJpaDao.save(vendorDo);
        }
        return vendorDo;
    }

    protected Brand buildBrandData(String brandName) {
        Brand brand = brandService.findByName(brandName);
        if (null == brand) {
            brand = Brand.builder()
                .name(brandName)
                .logo("")
                .description("")
                .status(BrandStatus.ACTIVE)
                .build();
            brand = brandService.save(brand);
        }
        return brand;
    }

    protected ItemRegPrice buildItemRegPriceData(UUID itemId) {
        ItemRegPrice itemRegPrice = itemRegPriceService.findByItemId(itemId);

        if (null == itemRegPrice) {
            itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(itemId);
            itemRegPrice = itemRegPriceService.save(itemRegPrice);
        }
        return itemRegPrice;
    }

    protected VendorItem buildVendorItemData(String vendorItemNUmber, UUID vendorId, UUID itemId) {
        List<VendorItem> vendorItems = vendorItemService.findByVendorIDAndVendorSkuNum(vendorId, vendorItemNUmber);
        VendorItem vendorItem = null;
        if (vendorItems == null || vendorItems.isEmpty()) {
            vendorItem = VendorItemUtil.buildVendorItem(vendorItemNUmber, vendorId, itemId);
            return vendorItemService.save(vendorItem);
        }
        return vendorItems.getFirst();
    }

    protected ItemDo buildItemDataWithUpc(String skuNumber, List<ItemUPCDo> upcDos) {
        return buildItemData(skuNumber,
            departmentStr,
            categoryStr,
            subCategoryStr,
            clazzStr,
            brandNameStr,
            vendorNameStr,
            vendorItemNumberStr, upcDos);

    }

    protected Vendor buildVendorData(String vendorName) {
        Vendor vendor = VendorUtil.buildVendor(vendorName);
        return vendorService.save(vendor);
    }

    protected Vendor buildDirectVendorData(String vendorName) {
        Vendor vendor = VendorUtil.buildVendor(vendorName);
        vendor.setexternalPicking(false);
        return vendorService.save(vendor);
    }


    protected ItemPriceGroup buildItemPriceGroup() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        itemPriceGroup.setId(null);
        return itemPriceGroupRepository.save(itemPriceGroup);
    }

    protected void batchBuildCategory() {
        Category beverage = Category.builder()
            .name("Beverage")
            .status(CategoryStatus.ACTIVE)
            .build();
        beverage = categoryService.save(beverage);
        CategoryHierarchy beverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(beverage.getId())
            .depth(0)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(beverageCategoryHierarchy);

        Category soda = Category.builder()
            .name("Soda & Energy Drinks")
            .status(CategoryStatus.ACTIVE)
            .build();
        soda = categoryService.save(soda);
        CategoryHierarchy sodaCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(soda.getId())
            .depth(1)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(sodaCategoryHierarchy);

        Category oatMilk = Category.builder()
            .name("Oat Milk")
            .status(CategoryStatus.ACTIVE)
            .build();
        oatMilk = categoryService.save(oatMilk);
        CategoryHierarchy oatMilkCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(oatMilk.getId())
            .depth(1)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(oatMilkCategoryHierarchy);

        Category juices = Category.builder()
            .name("Juices & Nectars & Punch")
            .status(CategoryStatus.ACTIVE)
            .build();
        juices = categoryService.save(juices);
        CategoryHierarchy juicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(juices.getId())
            .depth(1)
            .sortOrder(3)
            .build();
        categoryHierarchyService.save(juicesCategoryHierarchy);

        Category fruit = Category.builder()
            .name("Fruit Juices")
            .status(CategoryStatus.ACTIVE)
            .build();
        fruit = categoryService.save(fruit);
        CategoryHierarchy fruitJuicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(juices.getId())
            .categoryId(fruit.getId())
            .depth(1)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(fruitJuicesCategoryHierarchy);
        CategoryHierarchy fruitBeverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(fruit.getId())
            .depth(2)
            .sortOrder(1)
            .build();
        categoryHierarchyService.save(fruitBeverageCategoryHierarchy);

        Category alternative = Category.builder()
            .name("Alternative Milk")
            .status(CategoryStatus.ACTIVE)
            .build();
        alternative = categoryService.save(alternative);
        CategoryHierarchy alternativeJuicesCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(juices.getId())
            .categoryId(alternative.getId())
            .depth(1)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(alternativeJuicesCategoryHierarchy);
        CategoryHierarchy alternativeBeverageCategoryHierarchy = CategoryHierarchy.builder()
            .ancestorCategoryId(beverage.getId())
            .categoryId(alternative.getId())
            .depth(2)
            .sortOrder(2)
            .build();
        categoryHierarchyService.save(alternativeBeverageCategoryHierarchy);

    }

}
