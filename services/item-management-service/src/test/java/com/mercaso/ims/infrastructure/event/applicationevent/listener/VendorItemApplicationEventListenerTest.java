package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.ExceptionRecordDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.VendorItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.VendorItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemDeletedPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ExceptionRecordApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ItemVersionApplicationService;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendoritem.VendorItemSpecification;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordDtoUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.vendor.VendorItemDtoUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class VendorItemApplicationEventListenerTest {

    @Mock
    ItemApplicationService itemApplicationService;
    @Mock
    ItemService itemService;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @Mock
    VendorItemSpecification vendorItemSpecification;
    @Mock
    ExceptionRecordApplicationService exceptionRecordApplicationService;
    @Mock
    FinaleApplicationService finaleApplicationService;
    @InjectMocks
    VendorItemApplicationEventListener vendorItemApplicationEventListener;
    @Mock
    ItemQueryApplicationService itemQueryApplicationService;
    @Mock
    ItemVersionApplicationService itemVersionApplicationService;
    @Mock
    ItemPriceApplicationService itemPriceApplicationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void shouldHandleVendorItemAmendApplicationEventAndCreateExceptionRecords() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();

        ItemDto itemDto = ItemDTOUtil.buildItemDTO(itemId, UUID.randomUUID());
        itemDto.getVendorItemDtos().get(0).setVendorName(VendorConstant.EXOTIC_BLVD);
        itemDto.getVendorItemDtos().get(0).setBackupCost(new BigDecimal("8.75"));

        ExceptionRecordDto exceptionRecordDto = ExceptionRecordDtoUtil.buildExceptionRecordDto();
        VendorItemDto previous = VendorItemDtoUtil.buildVendorItemDto();
        previous.setItemId(itemId);

        VendorItemDto current = VendorItemDtoUtil.buildVendorItemDto();
        current.setItemId(itemId);
        current.setVendorItemId(vendorItemId);
        current.setCost(new BigDecimal(232));
        current.setBackupCost(new BigDecimal("8.75"));

        VendorItemAmendPayloadDto vendorItemAmendPayloadDto = VendorItemAmendPayloadDto.builder()
            .vendorItemId(vendorItemId)
            .previous(previous)
            .current(current)
            .build();

        Item item = ItemUtil.buildItem();
        item.setBackupVendorId(current.getVendorItemId());

        // Mock setup
        when(itemApplicationService.refreshPrimaryBackupVendor(any(UUID.class))).thenReturn(itemDto);
        when(itemService.findById(any(UUID.class))).thenReturn(item);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);
        when(exceptionRecordApplicationService.create(any(CreateExceptionRecordCommand.class))).thenReturn(exceptionRecordDto);
        when(vendorItemSpecification.getExceptionMessage(any(), any(), any())).thenReturn("getExceptionMessageResponse");

        // Act
        vendorItemApplicationEventListener.handleVendorItemAmendApplicationEvent(new VendorItemAmendApplicationEvent("source",
            vendorItemAmendPayloadDto));

        // Assert
        verify(itemApplicationService).refreshPrimaryBackupVendor(itemId);
        verify(exceptionRecordApplicationService, times(2)).create(any(CreateExceptionRecordCommand.class));
    }

    // ... existing code ...

    @Test
    void shouldRefreshPrimaryBackupVendorWhenVendorItemDeleted() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
        vendorItemDto.setItemId(itemId);
        vendorItemDto.setVendorItemId(vendorItemId);
        vendorItemDto.setVendorId(vendorId);
        vendorItemDto.setVendorName("Regular Vendor");
        vendorItemDto.setVendorItemType(VendorItemType.DIRECT.getTypeName());

        VendorItemDeletedPayloadDto vendorItemDeletedPayloadDto = VendorItemDeletedPayloadDto.builder()
                .vendorItemId(vendorItemId)
                .data(vendorItemDto)
                .build();

        ItemDto itemDto = ItemDTOUtil.buildItemDTO(itemId, vendorId);
        itemDto.setVendorItemDtos(List.of(vendorItemDto));
        itemDto.setPrimaryVendorId(vendorId);

        Item item = ItemUtil.buildItem();
        item.setPrimaryVendorId(vendorId);

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);

        // Act
        VendorItemDeletedApplicationEvent event = new VendorItemDeletedApplicationEvent("source", vendorItemDeletedPayloadDto);
        vendorItemApplicationEventListener.handleVendorItemDeletedApplicationEvent(event);

        // Assert
        verify(itemApplicationService).refreshPrimaryBackupVendor(itemId);
        verify(itemService).findById(itemId);
        verify(itemQueryApplicationService).findById(any(UUID.class));
        verify(itemPriceApplicationService, never()).updateRegPrice(any(UpdateItemRegPriceCommand.class));
    }

    @Test
    void shouldNotUpdatePriceWhenNonExoticBlvdVendorDeleted() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();

        VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
        vendorItemDto.setItemId(itemId);
        vendorItemDto.setVendorItemId(vendorItemId);
        vendorItemDto.setVendorName("Other Vendor"); // Non-EXOTIC_BLVD vendor

        VendorItemDeletedPayloadDto vendorItemDeletedPayloadDto = VendorItemDeletedPayloadDto.builder()
                .vendorItemId(vendorItemId)
                .data(vendorItemDto)
                .build();

        ItemDto itemDto = ItemDTOUtil.buildItemDTO(itemId, UUID.randomUUID());
        itemDto.setVendorItemDtos(List.of(vendorItemDto));

        Item item = ItemUtil.buildItem();

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);

        // Act
        VendorItemDeletedApplicationEvent event = new VendorItemDeletedApplicationEvent("source", vendorItemDeletedPayloadDto);
        vendorItemApplicationEventListener.handleVendorItemDeletedApplicationEvent(event);

        // Assert
        verify(itemApplicationService).refreshPrimaryBackupVendor(itemId);
        verify(itemService).findById(itemId);
        verify(itemQueryApplicationService).findById(any(UUID.class));
        verify(itemPriceApplicationService, never()).updateRegPrice(any(UpdateItemRegPriceCommand.class));
    }

    @Test
    void shouldUpdatePriceWhenExoticBlvdVendorDeletedAndOnlyOneVendorRemains() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        VendorItemDto deletedVendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
        deletedVendorItemDto.setItemId(itemId);
        deletedVendorItemDto.setVendorItemId(vendorItemId);
        deletedVendorItemDto.setVendorId(vendorId);
        deletedVendorItemDto.setVendorName(VendorConstant.EXOTIC_BLVD);
        deletedVendorItemDto.setBackupCost(new BigDecimal("8.75"));
        deletedVendorItemDto.setVendorItemType(VendorItemType.JIT.getTypeName());

        VendorItemDeletedPayloadDto vendorItemDeletedPayloadDto = VendorItemDeletedPayloadDto.builder()
                .vendorItemId(vendorItemId)
                .data(deletedVendorItemDto)
                .build();

        // Create remaining vendor item (EXOTIC_BLVD)
        VendorItemDto remainingVendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
        remainingVendorItemDto.setVendorName(VendorConstant.EXOTIC_BLVD);
        remainingVendorItemDto.setVendorItemType(VendorItemType.DIRECT.getTypeName());
        remainingVendorItemDto.setBackupCost(new BigDecimal("10.00"));

        ItemDto itemDto = ItemDTOUtil.buildItemDTO(itemId, vendorId);
        itemDto.setVendorItemDtos(List.of(remainingVendorItemDto)); // Only one remaining vendor item

        Item item = ItemUtil.buildItem();

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);

        // Act
        VendorItemDeletedApplicationEvent event = new VendorItemDeletedApplicationEvent("source", vendorItemDeletedPayloadDto);
        vendorItemApplicationEventListener.handleVendorItemDeletedApplicationEvent(event);

        // Assert
        verify(itemApplicationService).refreshPrimaryBackupVendor(itemId);
        verify(itemService).findById(itemId);
        verify(itemQueryApplicationService).findById(any(UUID.class));
        verify(itemPriceApplicationService).updateRegPrice(any(UpdateItemRegPriceCommand.class));
    }



    @Test
    void shouldNotUpdatePriceWhenExoticBlvdVendorDeletedWithMultipleVendors() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();

        VendorItemDto exoticBlvdVendorItem = VendorItemDtoUtil.buildVendorItemDto();
        exoticBlvdVendorItem.setVendorName(VendorConstant.EXOTIC_BLVD);

        VendorItemDto otherVendorItem = VendorItemDtoUtil.buildVendorItemDto();
        otherVendorItem.setVendorName("Other Vendor");

        VendorItemDto vendorItemDto = VendorItemDtoUtil.buildVendorItemDto();
        vendorItemDto.setItemId(itemId);
        vendorItemDto.setVendorItemId(vendorItemId);
        vendorItemDto.setVendorName(VendorConstant.EXOTIC_BLVD);

        VendorItemDeletedPayloadDto vendorItemDeletedPayloadDto = VendorItemDeletedPayloadDto.builder()
                .vendorItemId(vendorItemId)
                .data(vendorItemDto)
                .build();

        ItemDto itemDto = ItemDTOUtil.buildItemDTO(itemId, UUID.randomUUID());
        // Multiple vendors - should not trigger EXOTIC_BLVD price update
        itemDto.setVendorItemDtos(List.of(exoticBlvdVendorItem, otherVendorItem));

        Item item = ItemUtil.buildItem();

        when(itemService.findById(any(UUID.class))).thenReturn(item);
        when(itemQueryApplicationService.findById(any(UUID.class))).thenReturn(itemDto);

        // Act
        VendorItemDeletedApplicationEvent event = new VendorItemDeletedApplicationEvent("source", vendorItemDeletedPayloadDto);
        vendorItemApplicationEventListener.handleVendorItemDeletedApplicationEvent(event);

        // Assert
        verify(itemApplicationService).refreshPrimaryBackupVendor(any(UUID.class));
        verify(itemService).findById(any(UUID.class));
        verify(itemQueryApplicationService).findById(any(UUID.class));
        // Should not call updateRegPrice when multiple vendors exist
        verify(itemPriceApplicationService, never()).updateRegPrice(any(UpdateItemRegPriceCommand.class));
    }

}