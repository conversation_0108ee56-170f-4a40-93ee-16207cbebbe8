package com.mercaso.ims.infrastructure.repository.vendor;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class VendorRepositoryImplIT extends AbstractIT {


    @Test
    void testSave() {
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        Vendor vendorNameTest = VendorUtil.buildVendor(vendorName);
        Vendor save = vendorRepository.save(vendorNameTest);
        assertNotNull(save);
        assertNotNull(save.getId());
        Assertions.assertEquals(vendorName, save.getVendorName());
    }


    @Test
    void testFindByVendorName() {
        String vendorName = RandomStringUtils.randomAlphabetic(6);
        buildVendorData(vendorName);
        Vendor vendorNameTest = vendorRepository.findByVendorName(vendorName);
        assertNotNull(vendorNameTest);
        Assertions.assertEquals(vendorName, vendorNameTest.getVendorName());
    }


    @Test
    void testFindByFuzzyName() {
        String vendorNameTest = RandomStringUtils.randomAlphabetic(10);
        String vendorName = vendorNameTest.substring(0, 5);

        buildVendorData(vendorNameTest);
        List<Vendor> vendors = vendorRepository.findByFuzzyName(vendorName);
        assertFalse(vendors.isEmpty());
        Assertions.assertEquals(vendorNameTest, vendors.getFirst().getVendorName());
    }
}
