package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.application.dto.ItemPriceGroupDto.ItemPriceGroupDtoBuilder;
import com.mercaso.ims.application.mapper.itempricegroup.ItemPriceGroupDtoApplicationMapping;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.service.ItemPriceGroupService;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemPriceGroupQueryApplicationServiceImpl.class})
class ItemPriceGroupQueryApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private ItemPriceGroupQueryApplicationServiceImpl itemPriceGroupQueryApplicationServiceImpl;

    @MockBean
    private ItemPriceGroupService itemPriceGroupService;

    @MockBean
    private ItemPriceGroupDtoApplicationMapping itemPriceGroupDtoApplicationMapping;


    @Test
    @Tag("MaintainedByDiffblue")
    void testQueryOrFilterItemPriceGroups_thenReturnEmpty() {
        // Arrange
        when(itemPriceGroupService.findByFuzzyName(Mockito.<String>any())).thenReturn(List.of());

        // Act
        List<ItemPriceGroupDto> actualQueryOrFilterItemPriceGroupsResult = itemPriceGroupQueryApplicationServiceImpl
            .queryOrFilterItemPriceGroups("Item Price Group Name");

        // Assert
        verify(itemPriceGroupService).findByFuzzyName("Item Price Group Name");
        assertTrue(actualQueryOrFilterItemPriceGroupsResult.isEmpty());
    }


    @Test
    @Tag("MaintainedByDiffblue")
    void testQueryOrFilterItemPriceGroups_thenReturnSizeIsOne() {
        // Arrange
        when(itemPriceGroupService.findByFuzzyName(Mockito.<String>any())).thenReturn(Lists.newArrayList(mock(ItemPriceGroup.class)));
        ItemPriceGroupDtoBuilder builderResult = ItemPriceGroupDto.builder();
        ItemPriceGroupDtoBuilder idResult = builderResult
            .createdAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant())
            .createdBy("Jan 1, 2020 8:00am GMT+0100")
            .createdUserName("janedoe")
            .groupName("Group Name")
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemPriceGroupDtoBuilder itemsResult = idResult.items(new ArrayList<>());
        ItemPriceGroupDto buildResult = itemsResult.price(new BigDecimal("2.3")).build();
        when(itemPriceGroupDtoApplicationMapping.domainToDto(Mockito.<ItemPriceGroup>any())).thenReturn(buildResult);

        // Act
        List<ItemPriceGroupDto> actualQueryOrFilterItemPriceGroupsResult = itemPriceGroupQueryApplicationServiceImpl
            .queryOrFilterItemPriceGroups("Item Price Group Name");

        // Assert
        verify(itemPriceGroupDtoApplicationMapping).domainToDto(isA(ItemPriceGroup.class));
        verify(itemPriceGroupService).findByFuzzyName("Item Price Group Name");
        assertEquals(1, actualQueryOrFilterItemPriceGroupsResult.size());
        ItemPriceGroupDto getResult = actualQueryOrFilterItemPriceGroupsResult.get(0);
        assertEquals("Group Name", getResult.getGroupName());
        assertEquals("Jan 1, 2020 8:00am GMT+0100", getResult.getCreatedBy());
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", getResult.getId().toString());
        assertEquals("janedoe", getResult.getCreatedUserName());
        Instant createdAt = getResult.getCreatedAt();
        assertEquals(0, createdAt.getNano());
        assertEquals(0L, createdAt.getEpochSecond());
        assertTrue(getResult.getItems().isEmpty());
        BigDecimal expectedPrice = new BigDecimal("2.3");
        assertEquals(expectedPrice, getResult.getPrice());
    }
}
