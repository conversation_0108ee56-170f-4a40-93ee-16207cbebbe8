package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.CleanItemUpcCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPromoPriceDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.mapper.item.ItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemPriceApplicationService;
import com.mercaso.ims.application.service.ItemVersionApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.category.CategoryConstant;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemImage;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorSpecification;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.FileUtil;
import com.mercaso.ims.utils.brand.BrandUtil;
import com.mercaso.ims.utils.item.ItemCommandUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.io.File;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.multipart.MultipartFile;

@ContextConfiguration(classes = {ItemApplicationServiceImpl.class})
class ItemApplicationServiceImplTest extends AbstractTest {


    @MockBean
    private BusinessEventService businessEventService;

    @Autowired
    private ItemApplicationServiceImpl itemApplicationServiceImpl;

    @MockBean
    private ItemDtoApplicationMapper itemDtoApplicationMapper;


    @MockBean
    private ItemPriceApplicationService itemPriceApplicationService;
    @MockBean
    private ItemQueryApplicationService itemQueryApplicationService;

    @MockBean
    private ItemService itemService;


    @MockBean
    private ItemRegPriceService itemRegPriceService;

    @MockBean
    private ItemPromoPriceService itemPromoPriceService;


    @MockBean
    private DocumentApplicationService documentApplicationService;

    @MockBean
    private BrandService brandService;
    @MockBean
    private VendorItemService vendorItemService;
    @MockBean
    private VendorItemApplicationService vendorItemApplicationService;
    @MockBean
    private VendorService vendorService;

    @MockBean
    private VendorSpecification vendorSpecification;

    @MockBean
    private ItemVersionApplicationService itemVersionApplicationService;


    @BeforeEach
    void setUp() {
        when(vendorSpecification.isSatisfiedBackupVendor(Mockito.<UUID>any())).thenReturn(true);

    }

    @Test
    void shouldThrowExceptionWhenCreateAsSkuIsNull() {
        UUID id = UUID.randomUUID();
        CreateItemCommand command = ItemCommandUtil.buildCreateItemCommand(id);
        command.setSkuNumber(null);
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemApplicationServiceImpl.create(command));
    }

    @Test
    void shouldThrowExceptionWhenCreateAsItemExist() {
        UUID id = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        CreateItemCommand command = ItemCommandUtil.buildCreateItemCommand(id);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemApplicationServiceImpl.create(command));
    }


    @Test
    void shouldSuccessfullyCreateItemWithPromoPrices() {
        // Arrange
        UUID id = UUID.randomUUID();
        Item item = ItemUtil.buildItem();
        CreateItemCommand command = ItemCommandUtil.buildCreateItemCommand(id);
        ItemPromoPriceDto promo = ItemPromoPriceDto.builder()
            .promoFlag(true)
            .promoPrice(BigDecimal.TEN).build();
        List<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        itemPromoPrices.add(promo);
        command.setItemPromoPrices(itemPromoPrices);
        Brand brand = BrandUtil.buildBrand(id);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.save(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(null);
        when(itemService.findById(any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        when(brandService.findById(any())).thenReturn(brand);

        when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);

        // Act and Assert
        itemApplicationServiceImpl.create(command);

        verify(itemService).save(any());
        verify(businessEventService).dispatch(any(ItemCreatedPayloadDto.class));
    }


    @Test
    void shouldSuccessfullyUpdateItemWithImageUpload() {
        // Arrange

        UUID id = UUID.randomUUID();

        String url = "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416";
        Item item = ItemUtil.buildItem();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl(url);
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(itemRegPriceService.update(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice);
        when(documentApplicationService.getSignedUrl(any())).thenReturn(url);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id,
            categoryId,
            brandId,
            primaryVendorId);

        // Act and Assert
        itemApplicationServiceImpl.update(updateItemCommand);
        verify(itemService).save(any());
        verify(businessEventService).dispatch(any(ItemAmendPayloadDto.class));

    }


    @Test
    void shouldThrowExceptionWhenUpdateAsItemNotExist() {
        // Arrange
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(null);
        UUID id = UUID.randomUUID();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl("https://example.org/example");
        when(documentApplicationService.uploadExcel(any(), any())).thenReturn(documentResponse);
        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id, categoryId, brandId, primaryVendorId);
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemApplicationServiceImpl.update(updateItemCommand));
        verify(itemService).findById(id);
    }


    @Test
    void shouldSuccessfullyDeleteItemById() {
        // Arrange
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(mock(Item.class));
        when(itemService.delete(Mockito.<UUID>any())).thenReturn(mock(Item.class));
        UUID id = UUID.randomUUID();
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(id)).thenReturn(itemDto);

        // Act and Assert
        itemApplicationServiceImpl.deleteItemById(new DeleteItemCommand(id, UUID.randomUUID()));

        verify(businessEventService).dispatch(isA(ItemDeletedPayloadDto.class));
    }

    @Test
    void shouldSuccessWhenUpdateItemRegPrice() {
        UUID id = UUID.randomUUID();

        String url = "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416";
        Item item = ItemUtil.buildItem();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl(url);
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.update(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice);
        when(documentApplicationService.getSignedUrl(any())).thenReturn(url);
        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id,
            categoryId,
            brandId,
            primaryVendorId);
        updateItemCommand.setRegPrice(BigDecimal.TEN);
        // Act and Assert
        itemApplicationServiceImpl.update(updateItemCommand);
        verify(itemService).save(any());

    }


    @Test
    void shouldSuccessWhenUpdateItemPromoPrice() {
        // Arrange

        UUID id = UUID.randomUUID();

        String url = "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416";
        Item item = ItemUtil.buildItem();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl(url);
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        ItemPromoPrice itemPromoPrice = mock(ItemPromoPrice.class);

        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice);
        when(itemRegPriceService.update(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(List.of(itemPromoPrice));
        when(documentApplicationService.getSignedUrl(any())).thenReturn(url);
        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id,
            categoryId,
            brandId,
            primaryVendorId);
        updateItemCommand.setDepartment(CategoryConstant.BEVERAGE);
        ItemPromoPriceDto promo = ItemPromoPriceDto.builder()
            .promoFlag(true)
            .promoPrice(BigDecimal.TEN).build();
        List<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        itemPromoPrices.add(promo);
        updateItemCommand.setItemPromoPrices(itemPromoPrices);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        // Act and Assert
        when(vendorSpecification.isSatisfiedBackupVendor(any(UUID.class))).thenReturn(true);
        itemApplicationServiceImpl.update(updateItemCommand);
        verify(itemService).save(any());
        verify(itemPromoPriceService).save(isA(ItemPromoPrice.class));

    }

    @Test
    void shouldCreateNewPromoPriceWhenUpdateItemPromoPriceIfPromoPriceIsNotNullAndItemPromoPriceNotExist() {
        UUID id = UUID.randomUUID();

        String url = "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416";
        Item item = ItemUtil.buildItem();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl(url);
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);

        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.update(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);

        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(List.of());
        when(documentApplicationService.getSignedUrl(any())).thenReturn(url);
        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id,
            categoryId,
            brandId,
            primaryVendorId);
        ItemPromoPriceDto promo = ItemPromoPriceDto.builder()
            .promoFlag(true)
            .promoPrice(BigDecimal.TEN).build();
        List<ItemPromoPriceDto> itemPromoPrices = new ArrayList<>();
        itemPromoPrices.add(promo);
        updateItemCommand.setItemPromoPrices(itemPromoPrices);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        // Act and Assert
        itemApplicationServiceImpl.update(updateItemCommand);
        verify(itemService).save(any());
        verify(itemPromoPriceService).save(isA(ItemPromoPrice.class));


    }

    @Test
    void doNothingWhenUpdateItemPromoPriceIfPromoPriceIsNullAndItemPromoPriceNotExist() {
        UUID id = UUID.randomUUID();

        String url = "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416";
        Item item = ItemUtil.buildItem();
        UUID categoryId = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID primaryVendorId = UUID.randomUUID();
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("name.pdf");
        documentResponse.setSignedUrl(url);
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        when(itemService.findBySku(Mockito.<String>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemRegPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPrice);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(List.of());
        when(itemRegPriceService.update(any(), any(), any(), any(), any())).thenReturn(itemRegPrice);
        when(documentApplicationService.getSignedUrl(any())).thenReturn(url);

        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(id,
            categoryId,
            brandId,
            primaryVendorId);

        // Act and Assert
        itemApplicationServiceImpl.update(updateItemCommand);
        verify(itemService).save(any());
        verify(itemPromoPriceService, times(0)).save(isA(ItemPromoPrice.class));

    }

    @Test
    void shouldThrowExceptionWhenRefreshPrimaryBackupVendorItemNotFound() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        when(itemService.findById(itemId)).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId));
        verify(itemService).findById(itemId);
    }

    @Test
    void shouldReturnPreviousItemWhenNoVendorChanges() {
        // Arrange
        UUID primaryVendorId = UUID.randomUUID();
        UUID backupVendorId = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        // Create a new item with the correct ID to match the mock setup
        item.setPrimaryVendorId(primaryVendorId);
        item.setBackupVendorId(backupVendorId);

        UUID itemId = item.getId();

        ItemDto previousItemDto = ItemDTOUtil.buildItemDTO(itemId, primaryVendorId);
        previousItemDto.setPrimaryVendorId(primaryVendorId);
        previousItemDto.setBackupVendorId(backupVendorId);

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(itemId)).thenReturn(previousItemDto);
        when(vendorItemService.findByItemID(itemId)).thenReturn(Collections.emptyList());
        when(itemService.save(any())).thenReturn(item);

        // Act
        ItemDto result = itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId);

        // Assert
        assertEquals(previousItemDto, result);
    }

    @Test
    void shouldSaveAndDispatchEventWhenPrimaryVendorChanged() {
        // Arrange
        UUID oldPrimaryVendorId = UUID.randomUUID();
        UUID newPrimaryVendorId = UUID.randomUUID();
        UUID backupVendorId = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        // Create a new item with the correct ID to match the mock setup
        item.setPrimaryVendorId(oldPrimaryVendorId);
        item.setBackupVendorId(backupVendorId);

        UUID itemId = item.getId();

        ItemDto previousItemDto = ItemDTOUtil.buildItemDTO(itemId, oldPrimaryVendorId);
        previousItemDto.setPrimaryVendorId(oldPrimaryVendorId);
        previousItemDto.setBackupVendorId(backupVendorId);

        ItemDto currentItemDto = ItemDTOUtil.buildItemDTO(itemId, newPrimaryVendorId);
        currentItemDto.setPrimaryVendorId(newPrimaryVendorId);
        currentItemDto.setBackupVendorId(backupVendorId);

        // Mock vendor items to trigger primary vendor change
        VendorItem directVendorItem = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(newPrimaryVendorId)
            .itemId(itemId)
            .vendorItemType(VendorItemType.DIRECT.getTypeName())
            .packPlusCrvCost(BigDecimal.valueOf(10.0))
            .build();

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(itemId))
            .thenReturn(previousItemDto)  // First call for previous state
            .thenReturn(currentItemDto);  // Second call for current state after save
        when(vendorItemService.findByItemID(itemId)).thenReturn(List.of(directVendorItem));
        when(itemService.save(any(Item.class))).thenReturn(item);

        // Act
        ItemDto result = itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId);

        // Assert
        assertEquals(currentItemDto, result);
        verify(itemService).save(any(Item.class));
        verify(businessEventService).dispatch(any(ItemAmendPayloadDto.class));
    }

    @Test
    void shouldSaveAndDispatchEventWhenBackupVendorChanged() {
        // Arrange
        UUID primaryVendorId = UUID.randomUUID();
        UUID oldBackupVendorId = UUID.randomUUID();
        UUID newBackupVendorId = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        // Create a new item with the correct ID to match the mock setup
        item.setPrimaryVendorId(primaryVendorId);
        item.setBackupVendorId(oldBackupVendorId);

        UUID itemId = item.getId();

        ItemDto previousItemDto = ItemDTOUtil.buildItemDTO(itemId, primaryVendorId);
        previousItemDto.setPrimaryVendorId(primaryVendorId);
        previousItemDto.setBackupVendorId(oldBackupVendorId);
        ItemRegPriceDto itemRegPriceDto = ItemRegPriceDto.builder().regPrice(BigDecimal.valueOf(20.0)).build();
        previousItemDto.setItemRegPrice(itemRegPriceDto);

        ItemDto currentItemDto = ItemDTOUtil.buildItemDTO(itemId, primaryVendorId);
        currentItemDto.setPrimaryVendorId(primaryVendorId);
        currentItemDto.setBackupVendorId(newBackupVendorId);

        // Mock vendor items to trigger backup vendor change
        VendorItem jitVendorItem = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(newBackupVendorId)
            .itemId(itemId)
            .vendorItemType(VendorItemType.JIT.getTypeName())
            .backupPackPlusCrvCost(BigDecimal.valueOf(15.0))
            .backupCostFreshnessTime(Instant.now())
            .availability(true)
            .build();

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(itemId))
            .thenReturn(previousItemDto)  // First call for previous state
            .thenReturn(currentItemDto);  // Second call for current state after save
        when(vendorItemService.findByItemID(itemId)).thenReturn(List.of(jitVendorItem));
        when(itemService.save(any(Item.class))).thenReturn(item);

        // Act
        ItemDto result = itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId);

        // Assert
        assertEquals(currentItemDto, result);
        verify(itemService).save(any(Item.class));
        verify(businessEventService).dispatch(any(ItemAmendPayloadDto.class));
    }

    @Test
    void shouldSaveAndDispatchEventWhenBothVendorsChanged() {
        // Arrange
        UUID oldPrimaryVendorId = UUID.randomUUID();
        UUID newPrimaryVendorId = UUID.randomUUID();
        UUID oldBackupVendorId = UUID.randomUUID();
        UUID newBackupVendorId = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        // Create a new item with the correct ID to match the mock setup
        item.setPrimaryVendorId(oldPrimaryVendorId);
        item.setBackupVendorId(oldBackupVendorId);

        UUID itemId = item.getId();

        ItemDto previousItemDto = ItemDTOUtil.buildItemDTO(itemId, oldPrimaryVendorId);
        previousItemDto.setPrimaryVendorId(oldPrimaryVendorId);
        previousItemDto.setBackupVendorId(oldBackupVendorId);
        ItemRegPriceDto itemRegPriceDto = ItemRegPriceDto.builder().regPrice(BigDecimal.valueOf(20.0)).build();
        previousItemDto.setItemRegPrice(itemRegPriceDto);

        ItemDto currentItemDto = ItemDTOUtil.buildItemDTO(itemId, newPrimaryVendorId);
        currentItemDto.setPrimaryVendorId(newPrimaryVendorId);
        currentItemDto.setBackupVendorId(newBackupVendorId);

        // Mock vendor items to trigger both vendor changes
        VendorItem directVendorItem = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(newPrimaryVendorId)
            .itemId(itemId)
            .vendorItemType(VendorItemType.DIRECT.getTypeName())
            .packPlusCrvCost(BigDecimal.valueOf(10.0))
            .build();

        VendorItem jitVendorItem = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(newBackupVendorId)
            .itemId(itemId)
            .vendorItemType(VendorItemType.JIT.getTypeName())
            .backupPackPlusCrvCost(BigDecimal.valueOf(15.0))
            .backupCostFreshnessTime(Instant.now())
            .availability(true)
            .build();

        when(itemService.findById(itemId)).thenReturn(item);
        when(itemQueryApplicationService.findById(itemId))
            .thenReturn(previousItemDto)  // First call for previous state
            .thenReturn(currentItemDto);  // Second call for current state after save
        when(vendorItemService.findByItemID(itemId)).thenReturn(List.of(directVendorItem, jitVendorItem));
        when(itemService.save(any(Item.class))).thenReturn(item);

        // Act
        ItemDto result = itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId);

        // Assert
        assertEquals(currentItemDto, result);
        verify(itemService).save(any(Item.class));
        verify(businessEventService).dispatch(any(ItemAmendPayloadDto.class));
    }

    @Test
    void shouldSuccessfullyBindPhotoToItem() {
        Item item = ItemUtil.buildItem();
        String fileName = getClass().getClassLoader().getResource("file/coke.webp").getPath();
        byte[] bytes = FileUtil.readFileToByteArray(new File(fileName));

        assertTrue(FileUtil.isImageFile(bytes));
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        when(itemService.save(any())).thenReturn(item);
        when(itemPromoPriceService.findByItemId(Mockito.<UUID>any())).thenReturn(List.of());
        MultipartFile file = new MockMultipartFile("file", "coke.webp", "image/jpeg", bytes);
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("coke.webp");
        documentResponse.setSignedUrl("https://example.org/example");
        when(documentApplicationService.uploadImage(any(), any())).thenReturn(documentResponse);
        //
        itemApplicationServiceImpl.bindingPhoto(UUID.randomUUID(), file);

        // Assert that nothing has changed
        verify(businessEventService, times(1)).dispatch(isA(ItemAmendPayloadDto.class));
    }

    @Test
    void shouldSuccessfullyCleanItemUPCs() {

        UUID itemId = UUID.randomUUID();
        UUID itemAdjustmentRequestDetailId = UUID.randomUUID();

        CleanItemUpcCommand command = new CleanItemUpcCommand(itemId, itemAdjustmentRequestDetailId);

        Item item = ItemUtil.buildItem();
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();

        Mockito.when(itemQueryApplicationService.findById(itemId)).thenReturn(itemDto);
        Mockito.when(itemService.save(any())).thenReturn(item);
        Mockito.when(itemService.findById(any())).thenReturn(item);

        itemApplicationServiceImpl.cleanItemUPCs(command);

        Mockito.verify(itemService, Mockito.times(1)).save(item);
        Mockito.verify(businessEventService, Mockito.times(1)).dispatch(Mockito.any(ItemAmendPayloadDto.class));
    }

    @Test
    void shouldHandleBatchUpdateItemPhotoWithFailure() {
        // Arrange
        when(itemQueryApplicationService.findById(Mockito.<UUID>any()))
            .thenThrow(new ImsBusinessException("Failed to update status for item {}"));

        ArrayList<BatchUpdateItemPhotoCommand> commands = new ArrayList<>();
        BatchUpdateItemPhotoCommand buildResult = BatchUpdateItemPhotoCommand.builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .build();
        commands.add(buildResult);

        // Act
        BatchUpdateItemPhotoResultDto actualBatchUpdateItemPhotoResult = itemApplicationServiceImpl
            .batchUpdateItemPhoto(commands);

        // Assert
        verify(itemQueryApplicationService).findById(isA(UUID.class));
        List<String> failedSkuNumbers = actualBatchUpdateItemPhotoResult.getFailedSkuNumbers();
        assertEquals(1, failedSkuNumbers.size());
        assertEquals(0, actualBatchUpdateItemPhotoResult.getUpdatedCount());
    }


    @Test
    void shouldHandleBatchUpdateItemPhotoWhenItemNotFound() {
        // Arrange
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(null);
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult);

        ArrayList<BatchUpdateItemPhotoCommand> commands = new ArrayList<>();
        BatchUpdateItemPhotoCommand buildResult2 = BatchUpdateItemPhotoCommand.builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .build();
        commands.add(buildResult2);

        // Act
        BatchUpdateItemPhotoResultDto actualBatchUpdateItemPhotoResult = itemApplicationServiceImpl
            .batchUpdateItemPhoto(commands);

        // Assert
        verify(itemQueryApplicationService).findById(isA(UUID.class));
        verify(itemService).findById(isA(UUID.class));
        List<String> failedSkuNumbers = actualBatchUpdateItemPhotoResult.getFailedSkuNumbers();
        assertEquals(1, failedSkuNumbers.size());
        assertEquals(0, actualBatchUpdateItemPhotoResult.getUpdatedCount());
    }


    @Test
    void shouldReturnEmptyResultWhenBatchUpdateItemPhotoWithEmptyList() {
        // Arrange and Act
        BatchUpdateItemPhotoResultDto actualBatchUpdateItemPhotoResult = itemApplicationServiceImpl
            .batchUpdateItemPhoto(new ArrayList<>());

        // Assert
        assertEquals(0, actualBatchUpdateItemPhotoResult.getUpdatedCount());
        assertTrue(actualBatchUpdateItemPhotoResult.getFailedSkuNumbers().isEmpty());
    }

    @Test
    void shouldSuccessfullyBatchUpdateItemPhotoAndReturnUpdatedCount() {
        // Arrange
        when(businessEventService.dispatch(Mockito.<BusinessEventPayloadDto<AmendDto<ItemDto>>>any())).thenReturn(null);
        Item item = mock(Item.class);
        doNothing().when(item).setPhoto(Mockito.<String>any());
        doNothing().when(item).updateItemImages(Mockito.<List<ItemImage>>any());
        when(itemService.save(Mockito.<Item>any())).thenReturn(null);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult);

        ArrayList<BatchUpdateItemPhotoCommand> commands = new ArrayList<>();
        BatchUpdateItemPhotoCommand buildResult2 = BatchUpdateItemPhotoCommand.builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .build();
        commands.add(buildResult2);

        // Act
        BatchUpdateItemPhotoResultDto actualBatchUpdateItemPhotoResult = itemApplicationServiceImpl
            .batchUpdateItemPhoto(commands);

        // Assert
        verify(itemQueryApplicationService, atLeast(1)).findById(isA(UUID.class));
        verify(businessEventService).dispatch(isA(BusinessEventPayloadDto.class));
        verify(item).updateItemImages(isA(List.class));
        verify(itemService).findById(isA(UUID.class));
        verify(itemService).save(isA(Item.class));
        assertEquals(1, actualBatchUpdateItemPhotoResult.getUpdatedCount());
        assertTrue(actualBatchUpdateItemPhotoResult.getFailedSkuNumbers().isEmpty());
    }

    @Test
    void shouldSuccessfullyRefreshPrimaryAndBackupVendor() {
        UUID itemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();

        Item item = ItemUtil.buildItem();
        // Create a new item with the correct ID to match the mock setup
        item.setPrimaryVendorId(vendorId);
        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        ItemRegPriceDto builderResult = ItemRegPriceDto.builder().regPrice(BigDecimal.valueOf(20D)).build();
        itemDto.setItemRegPrice(builderResult);
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setVendorId(vendorId);
        Vendor vendor = VendorUtil.buildVendor(vendorId);
        Mockito.when(itemQueryApplicationService.findById(itemId)).thenReturn(itemDto);
        Mockito.when(itemService.save(any())).thenReturn(item);
        Mockito.when(itemService.findById(any())).thenReturn(item);
        when(vendorItemService.findByItemID(any())).thenReturn(List.of(vendorItem));
        when(vendorService.findAllExternalPickingVendor(any())).thenReturn(List.of(vendor));

        itemApplicationServiceImpl.refreshPrimaryBackupVendor(itemId);

        // Assert
        verify(itemService).save(any());
        verify(businessEventService).dispatch(any(ItemAmendPayloadDto.class));
    }

}
