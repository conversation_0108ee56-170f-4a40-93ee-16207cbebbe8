package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.queryservice.VendorQueryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.VendorPoAnalyzeRecordApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.aws_ocr.AwsAnalyzeExpenseAdaptor;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.multipart.MultipartFile;


@ContextConfiguration(classes = {AwsAnalyzeApplicationServiceImpl.class})
class AwsAnalyzeApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private AwsAnalyzeApplicationServiceImpl awsAnalyzeApplicationServiceImpl;

    @MockBean
    private AwsAnalyzeExpenseAdaptor awsAnalyzeExpenseAdaptor;

    @MockBean
    private BusinessEventService businessEventService;

    @MockBean
    private DocumentApplicationService documentApplicationService;

    @MockBean
    private VendorPoAnalyzeRecordApplicationService vendorPoAnalyzeRecordApplicationService;

    @MockBean
    private VendorQueryApplicationService vendorQueryApplicationService;


    @Test
    void testAnalyzeDowneyExpense() throws IOException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentApplicationService.uploadFile(Mockito.<MultipartFile>any(), Mockito.<String>any(), anyBoolean()))
            .thenReturn(buildResult);
        when(awsAnalyzeExpenseAdaptor.analyzeExpense(Mockito.<String>any()))
            .thenThrow(new ImsBusinessException("[analyzeExpense] documentResponse: {}."));

        MockMultipartFile file = new MockMultipartFile("Name", new ByteArrayInputStream("AXAXAXAX".getBytes("UTF-8")));

        byte[] fileBytes = file.getBytes();
        String originalFilename = file.getOriginalFilename();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> awsAnalyzeApplicationServiceImpl
            .analyzeDowneyExpense(fileBytes, originalFilename));

        ArgumentCaptor<String> docTypeCaptor = ArgumentCaptor.forClass(String.class);
        verify(documentApplicationService).uploadFileContent(eq(file.getBytes()), docTypeCaptor.capture(), eq(".pdf"), eq(false));
        String capturedDocType = docTypeCaptor.getValue();
        assertTrue(capturedDocType.startsWith("Downey-Rcpt-Order"), "docType should start with Downey-Rcpt-Order");
    }


    @Test
    void testAnalyzeDowneyExpense_givenAwsAnalyzeExpenseAdaptorAnalyzeExpenseReturnNull() throws IOException {
        // Arrange
        DocumentResponse buildResult = DocumentResponse.builder()
            .name("Name")
            .signedUrl("https://example.org/example")
            .build();
        when(documentApplicationService.uploadFile(Mockito.<MultipartFile>any(), Mockito.<String>any(), anyBoolean()))
            .thenReturn(buildResult);
        when(awsAnalyzeExpenseAdaptor.analyzeExpense(Mockito.<String>any())).thenReturn(null);

        MockMultipartFile file = new MockMultipartFile("Name", new ByteArrayInputStream("AXAXAXAX".getBytes("UTF-8")));
        byte[] fileBytes = file.getBytes();
        String originalFilename = file.getOriginalFilename();
        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> awsAnalyzeApplicationServiceImpl
            .analyzeDowneyExpense(fileBytes, originalFilename));

        ArgumentCaptor<String> docTypeCaptor = ArgumentCaptor.forClass(String.class);
        verify(documentApplicationService).uploadFileContent(eq(file.getBytes()), docTypeCaptor.capture(), eq(".pdf"), eq(false));
        String capturedDocType = docTypeCaptor.getValue();
        assertTrue(capturedDocType.startsWith("Downey-Rcpt-Order"), "docType should start with Downey-Rcpt-Order");

    }


    @Test
    void testAnalyzeDowneyExpense_whenNull() {
        // Arrange, Act and Assert
        assertThrows(ImsBusinessException.class, () -> awsAnalyzeApplicationServiceImpl.analyzeDowneyExpense(null, null));
    }
}
