package com.mercaso.ims.infrastructure.external.shopify;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ShopifyAccessTokenBalancer.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class ShopifyAccessTokenBalancerTest {

    @MockBean
    private ShopifyAccessTokenBalancer shopifyAccessTokenBalancer;

    @Test
    void testGetNextAccessToken() {
        // Arrange
        when(shopifyAccessTokenBalancer.getNextAccessToken()).thenReturn("ABC123");

        // Act
        String actualNextAccessToken = shopifyAccessTokenBalancer.getNextAccessToken();

        // Assert
        verify(shopifyAccessTokenBalancer).getNextAccessToken();
        assertEquals("ABC123", actualNextAccessToken);
    }
}
