package com.mercaso.ims.infrastructure.apitaskprocess.finale;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiClient;
import com.mercaso.ims.infrastructure.external.finale.dto.UpdateSupplierItemRequestDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for FinaleUpdateVendorItemProcessor
 */
class FinaleUpdateVendorItemProcessorTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private FinaleExternalApiClient finaleExternalApiClient;

    private FinaleUpdateVendorItemProcessor processor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new FinaleUpdateVendorItemProcessor(
                objectMapper, finaleExternalApiClient);
    }

    @Test
    void getTaskType_ReturnsCorrectTaskType() {
        // Act
        String result = processor.getTaskType();

        // Assert
        assertEquals(TaskType.FINALE_UPDATE_VENDOR_ITEM.getType(), result);
        assertEquals("FINALE_UPDATE_VENDOR_ITEM", result);
    }

    @Test
    void canProcess_ReturnsTrue() {
        // Act
        boolean result = processor.canProcess("FINALE_UPDATE_VENDOR_ITEM");

        // Assert
        assertTrue(result);
    }

    @Test
    void needsResponse_ReturnsFalse() {
        // Act
        boolean result = processor.needsResponse();

        // Assert
        assertFalse(result);
    }

    @Test
    void executeTask_SuccessfulExecution_ReturnsNull() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();
        UpdateSupplierItemRequestDto updateDto = createUpdateSupplierItemRequestDto();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);
        when(objectMapper.readValue("{\"productUrl\":\"test-url\"}", UpdateSupplierItemRequestDto.class))
                .thenReturn(updateDto);

        doNothing().when(finaleExternalApiClient).updateVendorItem(anyString(), anyString(), any(UpdateSupplierItemRequestDto.class));

        // Act
        Void result = processor.executeTask(task);

        // Assert
        assertNull(result);
        verify(finaleExternalApiClient).updateVendorItem("TEST-SKU-123", "activate", updateDto);
        verify(objectMapper).readValue(anyString(), eq(TaskRequestPayload.class));
        verify(objectMapper).readValue("{\"productUrl\":\"test-url\"}", UpdateSupplierItemRequestDto.class);
    }

    @Test
    void executeTask_FinaleApiClientThrowsException_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();
        UpdateSupplierItemRequestDto updateDto = createUpdateSupplierItemRequestDto();
        Exception expectedException = new RuntimeException("Finale API error");

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);
        when(objectMapper.readValue("{\"productUrl\":\"test-url\"}", UpdateSupplierItemRequestDto.class))
                .thenReturn(updateDto);

        doThrow(expectedException).when(finaleExternalApiClient)
                .updateVendorItem(anyString(), anyString(), any(UpdateSupplierItemRequestDto.class));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> processor.executeTask(task));

        assertEquals(expectedException, exception);
        verify(finaleExternalApiClient).updateVendorItem("TEST-SKU-123", "activate", updateDto);
    }

    @Test
    void executeTask_JsonProcessingException_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();
        JsonProcessingException expectedException = new JsonProcessingException("Invalid JSON") {};

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);
        when(objectMapper.readValue("{\"productUrl\":\"test-url\"}", UpdateSupplierItemRequestDto.class))
                .thenThrow(expectedException);

        // Act & Assert
        JsonProcessingException exception = assertThrows(JsonProcessingException.class,
                () -> processor.executeTask(task));

        assertEquals(expectedException, exception);
        verify(finaleExternalApiClient, never()).updateVendorItem(anyString(), anyString(), any(UpdateSupplierItemRequestDto.class));
    }

    @Test
    void validateTask_ValidTask_NoExceptionThrown() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createValidRequestPayload();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        assertDoesNotThrow(() -> processor.validateTask(task));
    }

    @Test
    void validateTask_MissingSkuNumber_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithoutSku();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("SKU number is required"));
    }

    @Test
    void validateTask_EmptySkuNumber_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithEmptySku();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("SKU number is required"));
    }

    @Test
    void validateTask_JsonProcessingError_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        JsonProcessingException expectedException = new JsonProcessingException("Invalid JSON") {};

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenThrow(expectedException);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("Invalid request payload for update vendor item task"));
        assertTrue(exception.getMessage().contains("Invalid JSON"));
    }

    @Test
    void validateTask_SkuParameterWithFallbackName_NoExceptionThrown() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithSkuFallback();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        assertDoesNotThrow(() -> processor.validateTask(task));
    }

    @Test
    void validateTask_MissingUpdateSupplierItemRequestDto_ThrowsException() throws Exception {
        // Arrange
        ApiTaskQueue task = createTestTask();
        TaskRequestPayload requestPayload = createRequestPayloadWithoutUpdateDto();

        when(objectMapper.readValue(anyString(), eq(TaskRequestPayload.class)))
                .thenReturn(requestPayload);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.validateTask(task));

        assertTrue(exception.getMessage().contains("updateSupplierItemRequestDto is required"));
    }

    // Helper methods
    private ApiTaskQueue createTestTask() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_UPDATE_VENDOR_ITEM")
                .apiEndpoint("/finale/vendor-item")
                .httpMethod("PUT")
                .status(TaskStatus.PENDING)
                .requestPayload("{\"methodName\":\"updateVendorItem\",\"className\":\"FinaleExternalApiClient\"}")
                .createdAt(Instant.now())
                .build();
    }

    private TaskRequestPayload createValidRequestPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("skuNumber", "TEST-SKU-123");
        parameters.put("status", "activate");
        parameters.put("updateSupplierItemRequestDto", "{\"productUrl\":\"test-url\"}");

        return TaskRequestPayload.builder()
                .methodName("updateVendorItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithoutSku() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("status", "activate");
        parameters.put("updateSupplierItemRequestDto", "{\"productUrl\":\"test-url\"}");

        return TaskRequestPayload.builder()
                .methodName("updateVendorItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithEmptySku() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("skuNumber", "   ");
        parameters.put("status", "activate");
        parameters.put("updateSupplierItemRequestDto", "{\"productUrl\":\"test-url\"}");

        return TaskRequestPayload.builder()
                .methodName("updateVendorItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithSkuFallback() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("sku", "TEST-SKU-123"); // Using fallback parameter name
        parameters.put("status", "activate");
        parameters.put("updateSupplierItemRequestDto", "{\"productUrl\":\"test-url\"}");

        return TaskRequestPayload.builder()
                .methodName("updateVendorItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private TaskRequestPayload createRequestPayloadWithoutUpdateDto() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("skuNumber", "TEST-SKU-123");
        parameters.put("status", "activate");
        // Missing updateSupplierItemRequestDto

        return TaskRequestPayload.builder()
                .methodName("updateVendorItem")
                .className("FinaleExternalApiClient")
                .parameters(parameters)
                .build();
    }

    private UpdateSupplierItemRequestDto createUpdateSupplierItemRequestDto() {
        return UpdateSupplierItemRequestDto.builder()
                .productUrl("test-url")
                .productId("test-product-id")
                .build();
    }
}