package com.mercaso.ims.infrastructure.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class GoogleDriveLinkConverterTest {

    @Test
    void testGetDirectImageUrl() {

        String testUrl = "https://drive.google.com/file/d/1X-NrTvAu7a11TLDYoruSZ3Rw0cSagExr/view?usp=sharing";
        String result = GoogleDriveLinkConverter.getDirectImageUrl(testUrl);
        Assertions.assertEquals("https://drive.google.com/uc?export=view&id=1X-NrTvAu7a11TLDYoruSZ3Rw0cSagExr", result);

    }

    @Test
    void testIsGoogleShareLink() {

        String testUrl1 = "https://drive.google.com/file/d/YOUR_FILE_ID/view?usp=sharing";
        String testUrl2 = "https://docs.google.com/document/d/YOUR_DOC_ID/edit";
        String testUrl3 = "https://drive.google.com/some/other/path";
        String testUrl4 = "https://www.google.com";
        Assertions.assertTrue(GoogleDriveLinkConverter.isGoogleShareLink(testUrl1));
        Assertions.assertFalse(GoogleDriveLinkConverter.isGoogleShareLink(testUrl2));
        Assertions.assertFalse(GoogleDriveLinkConverter.isGoogleShareLink(testUrl3));
        Assertions.assertFalse(GoogleDriveLinkConverter.isGoogleShareLink(testUrl4));

    }

}

