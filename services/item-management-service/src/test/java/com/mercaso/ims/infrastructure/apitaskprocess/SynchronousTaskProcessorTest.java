package com.mercaso.ims.infrastructure.apitaskprocess;

import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskExecutionContextPayload;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;



import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@DisplayName("SynchronousTaskProcessor Unit Tests")
@Disabled
class SynchronousTaskProcessorTest {

    @Mock
    private RateLimiterRegistry rateLimiterRegistry;

    @Mock
    private RateLimiter rateLimiter;

    @Mock
    private TaskExecutionContextPayload taskExecutionContext;

    private SynchronousTaskProcessor synchronousTaskProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        synchronousTaskProcessor = new SynchronousTaskProcessor(rateLimiterRegistry);
    }

    @Test
    @DisplayName("Should execute task successfully without rate limiting")
    void executeTask_SuccessfulExecution_ShouldReturnResult() throws Throwable {
        // Given
        String expectedResult = "success";
        String rateLimiterName = "test-limiter";
        String fullMethodName = "TestClass.testMethod";

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn(fullMethodName);
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenReturn(expectedResult);

        // When
        Object result = synchronousTaskProcessor.executeTask(taskExecutionContext);

        // Then
        assertEquals(expectedResult, result);
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(taskExecutionContext).executeOriginalMethod();
    }


    @Test
    @DisplayName("Should throw ImsBusinessException when original method execution fails")
    void executeTask_OriginalMethodThrowsException_ShouldThrowImsBusinessException() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        String fullMethodName = "TestClass.testMethod";
        RuntimeException originalException = new RuntimeException("Original method failed");

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn(fullMethodName);
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenThrow(originalException);

        // When & Then
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            synchronousTaskProcessor.executeTask(taskExecutionContext);
        });

        assertNotNull(exception.getMessage());
        // Note: ImsBusinessException doesn't preserve cause, so we just verify the message
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(taskExecutionContext).executeOriginalMethod();
    }

    @Test
    @DisplayName("Should throw ImsBusinessException when rate limit exceeded")
    void executeTask_RateLimitExceeded_ShouldThrowImsBusinessException() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        String fullMethodName = "TestClass.testMethod";
        RequestNotPermitted requestNotPermitted = mock(RequestNotPermitted.class);

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn(fullMethodName);
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);

        // Rate limiter throws RequestNotPermitted
        when(rateLimiter.executeSupplier(any())).thenThrow(requestNotPermitted);

        // When & Then
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            synchronousTaskProcessor.executeTask(taskExecutionContext);
        });

        // Verify the exception message indicates rate limit exceeded
        assertTrue(exception.getMessage().contains("Rate limit exceeded for method: " + fullMethodName));
        assertTrue(exception.getMessage().contains("request rejected to prevent thread blocking"));
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(rateLimiter, times(1)).executeSupplier(any()); // Called once, then fails fast

        // Note: In real scenarios, @Retryable annotation will handle retries automatically
        // This test verifies the fail-fast behavior for a single attempt
    }

    @Test
    @DisplayName("Should fail fast on rate limit without blocking thread")
    void executeTask_RateLimitExceeded_ShouldFailFast() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        String fullMethodName = "TestClass.testMethod";
        RequestNotPermitted requestNotPermitted = mock(RequestNotPermitted.class);

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn(fullMethodName);
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenThrow(requestNotPermitted);

        // When & Then
        long startTime = System.currentTimeMillis();
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            synchronousTaskProcessor.executeTask(taskExecutionContext);
        });
        long endTime = System.currentTimeMillis();

        // Verify the method fails fast (should take less than 100ms)
        assertTrue((endTime - startTime) < 100, "Method should fail fast without blocking");

        // Verify the exception message indicates rate limit exceeded
        assertTrue(exception.getMessage().contains("Rate limit exceeded for method: " + fullMethodName));
        assertTrue(exception.getMessage().contains("request rejected to prevent thread blocking"));

        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(rateLimiter, times(1)).executeSupplier(any()); // Called once, then fails fast
    }

    @Test
    @DisplayName("Should handle null context gracefully")
    void executeTask_NullContext_ShouldThrowNullPointerException() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            synchronousTaskProcessor.executeTask(null);
        });
    }

    @Test
    @DisplayName("Should handle null rate limiter name")
    void executeTask_NullRateLimiterName_ShouldHandleGracefully() throws Throwable {
        // Given
        when(taskExecutionContext.getRateLimiterName()).thenReturn(null);
        when(taskExecutionContext.getFullMethodName()).thenReturn("TestClass.testMethod");
        when(rateLimiterRegistry.rateLimiter(null)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenReturn("success");

        // When
        Object result = synchronousTaskProcessor.executeTask(taskExecutionContext);

        // Then
        assertEquals("success", result);
        verify(rateLimiterRegistry).rateLimiter(null);
    }

    @Test
    @DisplayName("Should handle empty rate limiter name")
    void executeTask_EmptyRateLimiterName_ShouldHandleGracefully() throws Throwable {
        // Given
        String emptyRateLimiterName = "";
        when(taskExecutionContext.getRateLimiterName()).thenReturn(emptyRateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn("TestClass.testMethod");
        when(rateLimiterRegistry.rateLimiter(emptyRateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenReturn("success");

        // When
        Object result = synchronousTaskProcessor.executeTask(taskExecutionContext);

        // Then
        assertEquals("success", result);
        verify(rateLimiterRegistry).rateLimiter(emptyRateLimiterName);
    }

    @Test
    @DisplayName("Should handle null return value from original method")
    void executeTask_NullReturnValue_ShouldReturnNull() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn("TestClass.testMethod");
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenReturn(null);

        // When
        Object result = synchronousTaskProcessor.executeTask(taskExecutionContext);

        // Then
        assertNull(result);
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(taskExecutionContext).executeOriginalMethod();
    }

    @Test
    @DisplayName("Should handle complex object return value")
    void executeTask_ComplexObjectReturnValue_ShouldReturnObject() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        Object complexObject = new java.util.HashMap<String, Object>() {{
            put("key1", "value1");
            put("key2", 123);
        }};

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn("TestClass.testMethod");
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);
        when(rateLimiter.executeSupplier(any())).thenAnswer(invocation -> {
            return invocation.getArgument(0, java.util.function.Supplier.class).get();
        });
        when(taskExecutionContext.executeOriginalMethod()).thenReturn(complexObject);

        // When
        Object result = synchronousTaskProcessor.executeTask(taskExecutionContext);

        // Then
        assertEquals(complexObject, result);
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(taskExecutionContext).executeOriginalMethod();
    }

    @Test
    @DisplayName("Should verify logging behavior for rate limit exceeded")
    void executeTask_RateLimitExceeded_ShouldLogWarningMessage() throws Throwable {
        // Given
        String rateLimiterName = "test-limiter";
        String fullMethodName = "TestClass.testMethod";
        RequestNotPermitted requestNotPermitted = mock(RequestNotPermitted.class);

        when(taskExecutionContext.getRateLimiterName()).thenReturn(rateLimiterName);
        when(taskExecutionContext.getFullMethodName()).thenReturn(fullMethodName);
        when(rateLimiterRegistry.rateLimiter(rateLimiterName)).thenReturn(rateLimiter);

        // Rate limiter throws RequestNotPermitted
        when(rateLimiter.executeSupplier(any())).thenThrow(requestNotPermitted);

        // When & Then
        ImsBusinessException exception = assertThrows(ImsBusinessException.class, () -> {
            synchronousTaskProcessor.executeTask(taskExecutionContext);
        });

        // Verify the exception and logging behavior
        assertTrue(exception.getMessage().contains("Rate limit exceeded for method: " + fullMethodName));
        assertTrue(exception.getMessage().contains("request rejected to prevent thread blocking"));

        // Note: In a real scenario, you might want to use a logging framework test appender
        // to verify the actual log messages, but for this unit test we focus on behavior
        verify(rateLimiterRegistry).rateLimiter(rateLimiterName);
        verify(rateLimiter, times(1)).executeSupplier(any()); // Called once, then fails fast
        verify(taskExecutionContext, never()).executeOriginalMethod(); // Should not be called due to rate limit
    }


}
