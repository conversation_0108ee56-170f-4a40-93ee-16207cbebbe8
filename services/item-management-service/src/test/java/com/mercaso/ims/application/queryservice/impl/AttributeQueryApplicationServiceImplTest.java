package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.AttributeDto;
import com.mercaso.ims.application.mapper.attribute.AttributeDtoApplicationMapper;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {AttributeQueryApplicationServiceImpl.class})
class AttributeQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private AttributeDtoApplicationMapper attributeDtoApplicationMapper;

    @Autowired
    private AttributeQueryApplicationServiceImpl attributeQueryApplicationServiceImpl;

    @MockBean
    private AttributeService attributeService;


    @Test
    void testQueryOrFilterAttributes() {
        // Arrange
        when(attributeService.findByFuzzyName(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<AttributeDto> actualQueryOrFilterAttributesResult = attributeQueryApplicationServiceImpl
            .queryOrFilterAttributes("Name");

        // Assert
        verify(attributeService).findByFuzzyName("Name");
        assertTrue(actualQueryOrFilterAttributesResult.isEmpty());
    }


    @Test
    void testQueryOrFilterAttributes2() {
        // Arrange
        when(attributeService.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<AttributeDto> actualQueryOrFilterAttributesResult = attributeQueryApplicationServiceImpl
            .queryOrFilterAttributes("");

        // Assert
        verify(attributeService).findAll();
        assertTrue(actualQueryOrFilterAttributesResult.isEmpty());
    }
}
