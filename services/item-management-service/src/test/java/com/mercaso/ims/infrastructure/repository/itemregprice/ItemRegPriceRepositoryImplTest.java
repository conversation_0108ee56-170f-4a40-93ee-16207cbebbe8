package com.mercaso.ims.infrastructure.repository.itemregprice;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.ItemRegPriceJpaDao;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.dataobject.ItemRegPriceDo;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.mapper.ItemRegPriceDoMapper;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemRegPriceRepositoryImpl.class})
class ItemRegPriceRepositoryImplTest extends AbstractTest {

    @MockBean
    private ItemRegPriceDoMapper itemRegPriceDoMapper;

    @MockBean
    private ItemRegPriceJpaDao itemRegPriceJpaDao;

    @Autowired
    private ItemRegPriceRepositoryImpl itemRegPriceRepositoryImpl;

    @Test
    void testSaveWithItemRegPrice_thenReturnNull() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.save(Mockito.<ItemRegPriceDo>any())).thenReturn(itemRegPriceDo);

        ItemRegPriceDo itemRegPriceDo2 = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenReturn(null);
        when(itemRegPriceDoMapper.domainToDo(Mockito.<ItemRegPrice>any())).thenReturn(itemRegPriceDo2);

        // Act
        ItemRegPrice actualSaveResult = itemRegPriceRepositoryImpl.save(null);

        // Assert
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceDoMapper).domainToDo(isNull());
        verify(itemRegPriceJpaDao).save(isA(ItemRegPriceDo.class));
        assertNull(actualSaveResult);
    }


    @Test
    void testSaveWithItemRegPrice_thenThrowImsBusinessException() {
        // Arrange
        when(itemRegPriceDoMapper.domainToDo(Mockito.<ItemRegPrice>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemRegPriceRepositoryImpl.save(null));
        verify(itemRegPriceDoMapper).domainToDo(isNull());
    }


    @Test
    void testFindByIdWithUuid_thenReturnNull() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        Optional<ItemRegPriceDo> ofResult = Optional.of(itemRegPriceDo);
        when(itemRegPriceJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenReturn(null);

        // Act
        ItemRegPrice actualFindByIdResult = itemRegPriceRepositoryImpl.findById(UUID.randomUUID());

        // Assert
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIdWithUuid_thenThrowImsBusinessException() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        Optional<ItemRegPriceDo> ofResult = Optional.of(itemRegPriceDo);
        when(itemRegPriceJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> itemRegPriceRepositoryImpl.findById(uuid));
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findById(isA(UUID.class));
    }


    @Test
    void testUpdateWithItemRegPrice_thenReturnNull() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        Optional<ItemRegPriceDo> ofResult = Optional.of(itemRegPriceDo);

        ItemRegPriceDo itemRegPriceDo2 = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.save(Mockito.<ItemRegPriceDo>any())).thenReturn(itemRegPriceDo2);
        when(itemRegPriceJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);

        ItemRegPriceDo itemRegPriceDo3 = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenReturn(null);
        when(itemRegPriceDoMapper.domainToDo(Mockito.<ItemRegPrice>any())).thenReturn(itemRegPriceDo3);
        ItemRegPrice domain = mock(ItemRegPrice.class);
        when(domain.getId()).thenReturn(UUID.randomUUID());

        // Act
        ItemRegPrice actualUpdateResult = itemRegPriceRepositoryImpl.update(domain);

        // Assert
        verify(domain).getId();
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceDoMapper).domainToDo(isA(ItemRegPrice.class));
        verify(itemRegPriceJpaDao).findById(isA(UUID.class));
        verify(itemRegPriceJpaDao).save(isA(ItemRegPriceDo.class));
        assertNull(actualUpdateResult);
    }


    @Test
    void testDeleteById_givenItemRegPriceDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        Optional<ItemRegPriceDo> ofResult = Optional.of(itemRegPriceDo);

        ItemRegPriceDo itemRegPriceDo2 = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.save(Mockito.<ItemRegPriceDo>any())).thenReturn(itemRegPriceDo2);
        when(itemRegPriceJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenReturn(null);

        // Act
        ItemRegPrice actualDeleteByIdResult = itemRegPriceRepositoryImpl.deleteById(UUID.randomUUID());

        // Assert
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findById(isA(UUID.class));
        verify(itemRegPriceJpaDao).save(isA(ItemRegPriceDo.class));
        assertNull(actualDeleteByIdResult);
    }


    @Test
    void testDeleteById_thenThrowImsBusinessException() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        Optional<ItemRegPriceDo> ofResult = Optional.of(itemRegPriceDo);

        ItemRegPriceDo itemRegPriceDo2 = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.save(Mockito.<ItemRegPriceDo>any())).thenReturn(itemRegPriceDo2);
        when(itemRegPriceJpaDao.findById(Mockito.<UUID>any())).thenReturn(ofResult);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> itemRegPriceRepositoryImpl.deleteById(uuid));
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findById(isA(UUID.class));
        verify(itemRegPriceJpaDao).save(isA(ItemRegPriceDo.class));
    }


    @Test
    void testFindByItemId_givenItemRegPriceDoMapperDoToDomainReturnNull_thenReturnNull() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPriceDo);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenReturn(null);

        // Act
        ItemRegPrice actualFindByItemIdResult = itemRegPriceRepositoryImpl.findByItemId(UUID.randomUUID());

        // Assert
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findByItemId(isA(UUID.class));
        assertNull(actualFindByItemIdResult);
    }


    @Test
    void testFindByItemId_thenThrowImsBusinessException() {
        // Arrange
        ItemRegPriceDo itemRegPriceDo = ItemRegPriceUtil.buildItemRegPriceDo();
        when(itemRegPriceJpaDao.findByItemId(Mockito.<UUID>any())).thenReturn(itemRegPriceDo);
        when(itemRegPriceDoMapper.doToDomain(Mockito.<ItemRegPriceDo>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        UUID uuid = UUID.randomUUID();
        assertThrows(ImsBusinessException.class, () -> itemRegPriceRepositoryImpl.findByItemId(uuid));
        verify(itemRegPriceDoMapper).doToDomain(isA(ItemRegPriceDo.class));
        verify(itemRegPriceJpaDao).findByItemId(isA(UUID.class));
    }


    @Test
    void testFindByItemIds_givenItemRegPriceDoMapper_whenArrayList_thenReturnEmpty() {
        // Arrange
        when(itemRegPriceJpaDao.findByItemIdIn(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemRegPrice> actualFindByItemIdsResult = itemRegPriceRepositoryImpl.findByItemIds(new ArrayList<>());

        // Assert
        verify(itemRegPriceJpaDao).findByItemIdIn(isA(List.class));
        assertTrue(actualFindByItemIdsResult.isEmpty());
    }
}
