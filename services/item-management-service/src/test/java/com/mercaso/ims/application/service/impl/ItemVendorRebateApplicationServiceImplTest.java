package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.mapper.itemvendorrebate.ItemVendorRebateDtoApplicationMapper;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateFactory;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ItemVendorRebateApplicationServiceImplTest {

    @Mock
    ItemVendorRebateService itemVendorRebateService;
    @Mock
    ItemVendorRebateFactory itemVendorRebateFactory;
    @Mock
    VendorItemService vendorItemService;
    @Mock
    ItemVendorRebateDtoApplicationMapper itemVendorRebateDtoApplicationMapper;
    @InjectMocks
    ItemVendorRebateApplicationServiceImpl itemVendorRebateApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateRebate_Success() {
        // Arrange
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant startDate = Instant.now();
        Instant endDate = LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC);
        BigDecimal rebatePerUnit = new BigDecimal("10.50");

        CreateItemVendorRebateCommand command = CreateItemVendorRebateCommand.builder()
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(endDate)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        VendorItem vendorItem = VendorItem.builder()
            .id(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .build();

        ItemVendorRebate createdRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(endDate)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebate savedRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(endDate)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebateDto expectedDto = ItemVendorRebateDto.builder()
            .id(savedRebate.getId())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(endDate)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        when(vendorItemService.findById(vendorItemId)).thenReturn(vendorItem);
        when(itemVendorRebateService.findByVendorItemId(vendorItemId)).thenReturn(Collections.emptyList());
        when(itemVendorRebateFactory.create(vendorItemId, vendorId, itemId, startDate, endDate, rebatePerUnit))
            .thenReturn(createdRebate);
        when(itemVendorRebateService.save(createdRebate)).thenReturn(savedRebate);
        when(itemVendorRebateDtoApplicationMapper.domainToDto(savedRebate)).thenReturn(expectedDto);

        // Act
        ItemVendorRebateDto result = itemVendorRebateApplicationServiceImpl.createRebate(command);

        // Assert
        assertEquals(expectedDto.getId(), result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertEquals(vendorId, result.getVendorId());
        assertEquals(itemId, result.getItemId());
        assertEquals(startDate, result.getStartDate());
        assertEquals(endDate, result.getEndDate());
        assertEquals(rebatePerUnit, result.getRebatePerUnit());
        assertEquals(ItemVendorRebateStatus.ACTIVE, result.getItemVendorRebateStatus());

        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        verify(itemVendorRebateFactory).create(vendorItemId, vendorId, itemId, startDate, endDate, rebatePerUnit);
        verify(itemVendorRebateService).save(createdRebate);
        verify(itemVendorRebateDtoApplicationMapper).domainToDto(savedRebate);
    }

    @Test
    void testCreateRebate_VendorItemNotFound() {
        // Arrange
        UUID vendorItemId = UUID.randomUUID();
        CreateItemVendorRebateCommand command = CreateItemVendorRebateCommand.builder()
            .vendorItemId(vendorItemId)
            .vendorId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .startDate(Instant.now())
            .endDate(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC))
            .rebatePerUnit(new BigDecimal("10.50"))
            .build();

        when(vendorItemService.findById(vendorItemId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> itemVendorRebateApplicationServiceImpl.createRebate(command));

        assertEquals(ErrorCodeEnums.VENDOR_ITEM_NOT_FOUND.getCode(), exception.getCode());
        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService, never()).findByVendorItemId(any());
        verify(itemVendorRebateFactory, never()).create(any(), any(), any(), any(), any(), any());
        verify(itemVendorRebateService, never()).save(any());
    }

    @Test
    void testCreateRebate_HasOverlappingRebates() {
        // Arrange
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant startDate = Instant.now();
        Instant endDate = LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC);

        CreateItemVendorRebateCommand command = CreateItemVendorRebateCommand.builder()
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(endDate)
            .rebatePerUnit(new BigDecimal("10.50"))
            .build();

        VendorItem vendorItem = VendorItem.builder()
            .id(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .build();

        // Create overlapping rebate (continuous rebate with null end date)
        ItemVendorRebate existingRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .startDate(Instant.now().minus(10, ChronoUnit.DAYS))
            .endDate(null) // Continuous rebate
            .rebatePerUnit(new BigDecimal("5.00"))
            .build();

        when(vendorItemService.findById(vendorItemId)).thenReturn(vendorItem);
        when(itemVendorRebateService.findByVendorItemId(vendorItemId)).thenReturn(List.of(existingRebate));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> itemVendorRebateApplicationServiceImpl.createRebate(command));

        assertEquals(ErrorCodeEnums.HAS_OVERLAPPING_REBATES.getCode(), exception.getCode());
        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        verify(itemVendorRebateFactory, never()).create(any(), any(), any(), any(), any(), any());
        verify(itemVendorRebateService, never()).save(any());
    }

    @Test
    void testUpdateRebate_Success() {
        // Arrange
        UUID rebateId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant newStartDate = Instant.now();
        Instant newEndDate = LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(2).toInstant(ZoneOffset.UTC);
        BigDecimal newRebatePerUnit = new BigDecimal("15.75");

        UpdateItemVendorRebateCommand command = UpdateItemVendorRebateCommand.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(newStartDate)
            .endDate(newEndDate)
            .rebatePerUnit(newRebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebate existingRebate = ItemVendorRebate.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(Instant.now().minus(10, ChronoUnit.DAYS))
            .endDate(Instant.now().plus(20, ChronoUnit.DAYS))
            .rebatePerUnit(new BigDecimal("10.00"))
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        VendorItem vendorItem = VendorItem.builder()
            .id(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .build();

        ItemVendorRebate updatedRebate = ItemVendorRebate.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(newStartDate)
            .endDate(newEndDate)
            .rebatePerUnit(newRebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebateDto expectedDto = ItemVendorRebateDto.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(newStartDate)
            .endDate(newEndDate)
            .rebatePerUnit(newRebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        when(itemVendorRebateService.findById(rebateId)).thenReturn(existingRebate);
        when(vendorItemService.findById(vendorItemId)).thenReturn(vendorItem);
        when(itemVendorRebateService.findByVendorItemId(vendorItemId)).thenReturn(List.of(existingRebate));
        when(itemVendorRebateService.update(existingRebate)).thenReturn(updatedRebate);
        when(itemVendorRebateDtoApplicationMapper.domainToDto(updatedRebate)).thenReturn(expectedDto);

        // Act
        ItemVendorRebateDto result = itemVendorRebateApplicationServiceImpl.updateRebate(command);

        // Assert
        assertEquals(rebateId, result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertEquals(newStartDate, result.getStartDate());
        assertEquals(newEndDate, result.getEndDate());
        assertEquals(newRebatePerUnit, result.getRebatePerUnit());

        verify(itemVendorRebateService).findById(rebateId);
        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        verify(itemVendorRebateService).update(existingRebate);
        verify(itemVendorRebateDtoApplicationMapper).domainToDto(updatedRebate);
    }

    @Test
    void testUpdateRebate_RebateNotFound() {
        // Arrange
        UUID rebateId = UUID.randomUUID();
        UpdateItemVendorRebateCommand command = UpdateItemVendorRebateCommand.builder()
            .id(rebateId)
            .vendorItemId(UUID.randomUUID())
            .vendorId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .startDate(Instant.now())
            .endDate(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC))
            .rebatePerUnit(new BigDecimal("10.50"))
            .build();

        when(itemVendorRebateService.findById(rebateId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> itemVendorRebateApplicationServiceImpl.updateRebate(command));

        assertEquals(ErrorCodeEnums.VENDOR_REBATE_NOT_FOUND.getCode(), exception.getCode());
        verify(itemVendorRebateService).findById(rebateId);
        verify(vendorItemService, never()).findById(any());
        verify(itemVendorRebateService, never()).update(any());
    }

    @Test
    void testUpdateRebate_VendorItemNotFound() {
        // Arrange
        UUID rebateId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();
        UpdateItemVendorRebateCommand command = UpdateItemVendorRebateCommand.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .startDate(Instant.now())
            .endDate(LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC))
            .rebatePerUnit(new BigDecimal("10.50"))
            .build();

        ItemVendorRebate existingRebate = ItemVendorRebate.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .build();

        when(itemVendorRebateService.findById(rebateId)).thenReturn(existingRebate);
        when(vendorItemService.findById(vendorItemId)).thenReturn(null);

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> itemVendorRebateApplicationServiceImpl.updateRebate(command));

        assertEquals(ErrorCodeEnums.VENDOR_ITEM_NOT_FOUND.getCode(), exception.getCode());
        verify(itemVendorRebateService).findById(rebateId);
        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService, never()).update(any());
    }

    @Test
    void testUpdateRebate_HasOverlappingRebates() {
        // Arrange
        UUID rebateId = UUID.randomUUID();
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant newStartDate = Instant.now();
        Instant newEndDate = LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).plusMonths(1).toInstant(ZoneOffset.UTC);

        UpdateItemVendorRebateCommand command = UpdateItemVendorRebateCommand.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(newStartDate)
            .endDate(newEndDate)
            .rebatePerUnit(new BigDecimal("10.50"))
            .build();

        ItemVendorRebate existingRebate = ItemVendorRebate.builder()
            .id(rebateId)
            .vendorItemId(vendorItemId)
            .build();

        VendorItem vendorItem = VendorItem.builder()
            .id(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .build();

        // Create another rebate that will overlap
        ItemVendorRebate anotherRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .startDate(Instant.now().minus(5, ChronoUnit.DAYS))
            .endDate(null) // Continuous rebate
            .rebatePerUnit(new BigDecimal("5.00"))
            .build();

        when(itemVendorRebateService.findById(rebateId)).thenReturn(existingRebate);
        when(vendorItemService.findById(vendorItemId)).thenReturn(vendorItem);
        when(itemVendorRebateService.findByVendorItemId(vendorItemId))
            .thenReturn(List.of(existingRebate, anotherRebate));

        // Act & Assert
        ImsBusinessException exception = assertThrows(ImsBusinessException.class,
            () -> itemVendorRebateApplicationServiceImpl.updateRebate(command));

        assertEquals(ErrorCodeEnums.HAS_OVERLAPPING_REBATES.getCode(), exception.getCode());
        verify(itemVendorRebateService).findById(rebateId);
        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        verify(itemVendorRebateService, never()).update(any());
    }

    @Test
    void testDeleteRebate_Success() {
        // Arrange
        UUID rebateId = UUID.randomUUID();

        // Act
        itemVendorRebateApplicationServiceImpl.deleteRebate(rebateId);

        // Assert
        verify(itemVendorRebateService).delete(rebateId);
    }

    @Test
    void testCreateRebate_SuccessWithContinuousRebate() {
        // Arrange
        UUID vendorItemId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        UUID itemId = UUID.randomUUID();
        Instant startDate = Instant.now();
        BigDecimal rebatePerUnit = new BigDecimal("10.50");

        CreateItemVendorRebateCommand command = CreateItemVendorRebateCommand.builder()
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(null) // Continuous rebate
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        VendorItem vendorItem = VendorItem.builder()
            .id(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .build();

        ItemVendorRebate createdRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(null)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebate savedRebate = ItemVendorRebate.builder()
            .id(UUID.randomUUID())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(null)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        ItemVendorRebateDto expectedDto = ItemVendorRebateDto.builder()
            .id(savedRebate.getId())
            .vendorItemId(vendorItemId)
            .vendorId(vendorId)
            .itemId(itemId)
            .startDate(startDate)
            .endDate(null)
            .rebatePerUnit(rebatePerUnit)
            .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
            .build();

        when(vendorItemService.findById(vendorItemId)).thenReturn(vendorItem);
        when(itemVendorRebateService.findByVendorItemId(vendorItemId)).thenReturn(Collections.emptyList());
        when(itemVendorRebateFactory.create(vendorItemId, vendorId, itemId, startDate, null, rebatePerUnit))
            .thenReturn(createdRebate);
        when(itemVendorRebateService.save(createdRebate)).thenReturn(savedRebate);
        when(itemVendorRebateDtoApplicationMapper.domainToDto(savedRebate)).thenReturn(expectedDto);

        // Act
        ItemVendorRebateDto result = itemVendorRebateApplicationServiceImpl.createRebate(command);

        // Assert
        assertEquals(expectedDto.getId(), result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertEquals(startDate, result.getStartDate());
        assertNull(result.getEndDate()); // Continuous rebate
        assertEquals(rebatePerUnit, result.getRebatePerUnit());

        verify(vendorItemService).findById(vendorItemId);
        verify(itemVendorRebateService).findByVendorItemId(vendorItemId);
        verify(itemVendorRebateFactory).create(vendorItemId, vendorId, itemId, startDate, null, rebatePerUnit);
        verify(itemVendorRebateService).save(createdRebate);
        verify(itemVendorRebateDtoApplicationMapper).domainToDto(savedRebate);
    }
}
