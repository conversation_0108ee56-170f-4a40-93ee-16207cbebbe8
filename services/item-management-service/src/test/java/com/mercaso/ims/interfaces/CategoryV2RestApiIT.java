package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.application.command.UpdateCategoryCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled
class CategoryV2RestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenGetAllCategory() throws Exception {
        batchBuildCategory();

        List<CategoryTreeDto> allCategories = categoryV2RestApiUtil.getAllCategories();

        assertNotNull(allCategories);
    }

    @Test
    void shouldSuccessWhenCreateCategory() throws Exception {
        String categoryName = RandomStringUtils.randomAlphabetic(8);
        CreateCategoryCommand command = CreateCategoryCommand.builder()

            .categoryName(categoryName)
            .ancestorCategoryId(null)
            .categoryStatus(CategoryStatus.ACTIVE)
            .build();

        CategoryDto categoryDto = categoryV2RestApiUtil.createCategory(command);

        Category category = categoryService.findById(categoryDto.getCategoryId());
        assertEquals(categoryName, category.getName());
    }

    @Test
    void shouldThrowErrorWhenCreateCategory() {
        batchBuildCategory();

        CreateCategoryCommand categoryName = CreateCategoryCommand.builder()
            .categoryName("Beverage")
            .ancestorCategoryId(null)
            .categoryStatus(CategoryStatus.ACTIVE)
            .build();
        assertThrows(Exception.class,
            () -> categoryV2RestApiUtil.createCategory(categoryName));
    }

    @Test
    void shouldSuccessWhenUpdateCategory() throws Exception {
        batchBuildCategory();

        List<CategoryTreeDto> allCategories = categoryV2RestApiUtil.getAllCategories();

        CategoryTreeDto categoryTreeDto = allCategories.getFirst();

        String categoryName = RandomStringUtils.randomAlphabetic(8);

        UpdateCategoryCommand command = UpdateCategoryCommand.builder()
            .categoryName(categoryName)
            .ancestorCategoryId(null)
            .categoryStatus(CategoryStatus.ACTIVE)
            .sortOrder(1)
            .build();

        categoryV2RestApiUtil.updateCategory(categoryTreeDto.getCategoryId(), command);
        Category category = categoryService.findById(categoryTreeDto.getCategoryId());
        assertEquals(categoryName, category.getName());

    }

    @Test
    void shouldThrowErrorWhenUpdateCategory() {

        UpdateCategoryCommand categoryName = UpdateCategoryCommand.builder()
            .categoryName("Beverage")
            .ancestorCategoryId(null)
            .categoryStatus(CategoryStatus.ACTIVE)
            .build();
        assertThrows(Exception.class,
            () -> categoryV2RestApiUtil.updateCategory(UUID.randomUUID(), categoryName));
    }

    @Test
    void shouldSuccessWhenDeleteCategory() throws Exception {
        // Create a new category to delete
        String categoryName = RandomStringUtils.randomAlphabetic(8);
        CreateCategoryCommand command = CreateCategoryCommand.builder()
            .categoryName(categoryName)
            .ancestorCategoryId(null)
            .categoryStatus(CategoryStatus.ACTIVE)
            .build();

        CategoryDto categoryDto = categoryV2RestApiUtil.createCategory(command);
        UUID categoryId = categoryDto.getCategoryId();
        
        // Delete the category
        categoryV2RestApiUtil.deleteCategory(categoryId);
        
        // Verify the category is deleted by checking if it throws an exception when trying to find it
        assertThrows(Exception.class, () -> categoryService.findById(categoryId));
    }

    @Test
    void shouldThrowErrorWhenDeleteNonExistentCategory() {
        // Try to delete a category with a random UUID that doesn't exist
        UUID randomId = UUID.randomUUID();
        assertThrows(Exception.class, () -> categoryV2RestApiUtil.deleteCategory(randomId));
    }
}