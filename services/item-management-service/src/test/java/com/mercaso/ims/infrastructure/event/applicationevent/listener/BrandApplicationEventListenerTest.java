package com.mercaso.ims.infrastructure.event.applicationevent.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.dto.event.BrandUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.payload.BrandUpdatedPayloadDto;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import com.mercaso.ims.utils.item.ItemUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BrandApplicationEventListenerTest {

    @Mock
    private ItemApplicationService itemApplicationService;

    @Mock
    private ItemService itemService;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private BrandApplicationEventListener brandApplicationEventListener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Nested
    @DisplayName("handleBrandUpdatedApplicationEvent Tests")
    class HandleBrandUpdatedApplicationEventTests {

        private UUID brandId;
        private BrandUpdatedPayloadDto payloadDto;
        private BrandUpdatedApplicationEvent event;

        @BeforeEach
        void setUp() {
            brandId = UUID.randomUUID();
            payloadDto = BrandUpdatedPayloadDto.builder()
                .brandId(brandId)
                .build();
            event = new BrandUpdatedApplicationEvent("BrandService", payloadDto);
        }

        @Test
        @DisplayName("should execute async task when items exist for brand")
        @Timeout(value = 5, unit = TimeUnit.SECONDS)
        void shouldExecuteAsyncTaskWhenItemsExistForBrand() {
            // Given
            Item item1 = createMockItem();
            Item item2 = createMockItem();
            when(itemService.findByBrandId(brandId)).thenReturn(Arrays.asList(item1, item2));

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verify(itemApplicationService, times(2)).update(any(UpdateItemCommand.class));
        }

        @Test
        @DisplayName("should not execute async task when no items exist for brand")
        void shouldNotExecuteAsyncTaskWhenNoItemsExistForBrand() {
            // Given
            when(itemService.findByBrandId(brandId)).thenReturn(Collections.emptyList());

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verifyNoInteractions(taskExecutor);
            verify(itemApplicationService, never()).update(any(UpdateItemCommand.class));
        }

        @Test
        @DisplayName("should not execute async task when items list is null")
        void shouldNotExecuteAsyncTaskWhenItemsListIsNull() {
            // Given
            when(itemService.findByBrandId(brandId)).thenReturn(null);

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verifyNoInteractions(taskExecutor);
            verify(itemApplicationService, never()).update(any(UpdateItemCommand.class));
        }

        @Test
        @DisplayName("should create correct UpdateItemCommand for each item")
        @Timeout(value = 5, unit = TimeUnit.SECONDS)
        void shouldCreateCorrectUpdateItemCommandForEachItem() {
            // Given
            Item item1 = ItemUtil.buildItem();
            Item item2 = ItemUtil.buildItem();

            when(itemService.findByBrandId(brandId)).thenReturn(Arrays.asList(item1, item2));

            // Mock the executor to run the task synchronously for testing
            doAnswer(invocation -> {
                Runnable task = invocation.getArgument(0);
                task.run();
                return null;
            }).when(taskExecutor).execute(any(Runnable.class));

            ArgumentCaptor<UpdateItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateItemCommand.class);

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);


            // Then
            verify(itemApplicationService, times(2)).update(commandCaptor.capture());

            UpdateItemCommand command1 = commandCaptor.getAllValues().get(0);
            UpdateItemCommand command2 = commandCaptor.getAllValues().get(1);

            assertEquals(brandId, UUID.fromString(command1.getBrandId()));
            assertEquals(brandId, UUID.fromString(command2.getBrandId()));
        }

        @Test
        @DisplayName("should handle null payload gracefully")
        void shouldHandleNullPayloadGracefully() {
            // Given
            BrandUpdatedApplicationEvent eventWithNullPayload = new BrandUpdatedApplicationEvent("BrandService", null);

            // When & Then
            assertThrows(NullPointerException.class, () -> {
                brandApplicationEventListener.handleBrandUpdatedApplicationEvent(eventWithNullPayload);
            });
        }

        @Test
        @DisplayName("should handle null brandId in payload gracefully")
        void shouldHandleNullBrandIdGracefully() {
            // Given
            BrandUpdatedPayloadDto payloadWithNullBrandId = BrandUpdatedPayloadDto.builder()
                .brandId(null)
                .build();
            BrandUpdatedApplicationEvent eventWithNullBrandId = new BrandUpdatedApplicationEvent("BrandService", payloadWithNullBrandId);

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(eventWithNullBrandId);

            // Then
            verify(itemService).findByBrandId(null);
            verifyNoInteractions(taskExecutor);
            verify(itemApplicationService, never()).update(any(UpdateItemCommand.class));
        }

        @Test
        @DisplayName("should handle single item correctly")
        @Timeout(value = 5, unit = TimeUnit.SECONDS)
        void shouldHandleSingleItemCorrectly() {
            // Given
            Item item = createMockItemWithId();
            when(itemService.findByBrandId(brandId)).thenReturn(Collections.singletonList(item));

            // Mock the executor to run the task synchronously for testing
            doAnswer(invocation -> {
                Runnable task = invocation.getArgument(0);
                task.run();
                return null;
            }).when(taskExecutor).execute(any(Runnable.class));

            ArgumentCaptor<UpdateItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateItemCommand.class);

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verify(itemApplicationService, times(1)).update(commandCaptor.capture());

            UpdateItemCommand command = commandCaptor.getValue();
            assertEquals(brandId, UUID.fromString(command.getBrandId()));
        }

        @Test
        @DisplayName("should handle large number of items efficiently")
        @Timeout(value = 10, unit = TimeUnit.SECONDS)
        void shouldHandleLargeNumberOfItemsEfficiently() {
            // Given
            int itemCount = 100;
            java.util.List<Item> items = new java.util.ArrayList<>();
            for (int i = 0; i < itemCount; i++) {
                items.add(createMockItem());
            }
            when(itemService.findByBrandId(brandId)).thenReturn(items);

            // When
            brandApplicationEventListener.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verify(itemApplicationService, times(itemCount)).update(any(UpdateItemCommand.class));
        }

        private Item createMockItem() {
            return ItemUtil.buildItem();
        }

        private Item createMockItemWithId() {
            return ItemUtil.buildItem();
        }
    }

    @Nested
    @DisplayName("Async Execution Tests")
    class AsyncExecutionTests {

        private UUID brandId;
        private BrandUpdatedPayloadDto payloadDto;
        private BrandUpdatedApplicationEvent event;

        @BeforeEach
        void setUp() {
            brandId = UUID.randomUUID();
            payloadDto = BrandUpdatedPayloadDto.builder()
                .brandId(brandId)
                .build();
            event = new BrandUpdatedApplicationEvent("BrandService", payloadDto);
        }

        @Test
        @DisplayName("should execute task asynchronously using CompletableFuture")
        @Timeout(value = 5, unit = TimeUnit.SECONDS)
        void shouldExecuteTaskAsynchronouslyUsingCompletableFuture() {
            // Given
            Item item = ItemUtil.buildItem();
            when(itemService.findByBrandId(brandId)).thenReturn(Collections.singletonList(item));


            // Create a new instance with real executor for this test
            BrandApplicationEventListener listenerWithRealExecutor = new BrandApplicationEventListener(
                itemApplicationService, itemService);

            // When
            listenerWithRealExecutor.handleBrandUpdatedApplicationEvent(event);

            // Then
            verify(itemService).findByBrandId(brandId);
            verify(itemApplicationService).update(any(UpdateItemCommand.class));
        }

        @Test
        @DisplayName("should handle concurrent brand update events")
        @Timeout(value = 10, unit = TimeUnit.SECONDS)
        void shouldHandleConcurrentBrandUpdateEvents() {
            // Given
            UUID brandId1 = UUID.randomUUID();
            UUID brandId2 = UUID.randomUUID();

            Item item1 = ItemUtil.buildItem();
            Item item2 = ItemUtil.buildItem();

            when(itemService.findByBrandId(brandId1)).thenReturn(Collections.singletonList(item1));
            when(itemService.findByBrandId(brandId2)).thenReturn(Collections.singletonList(item2));

            BrandUpdatedPayloadDto payload1 = BrandUpdatedPayloadDto.builder().brandId(brandId1).build();
            BrandUpdatedPayloadDto payload2 = BrandUpdatedPayloadDto.builder().brandId(brandId2).build();

            BrandUpdatedApplicationEvent event1 = new BrandUpdatedApplicationEvent("BrandService", payload1);
            BrandUpdatedApplicationEvent event2 = new BrandUpdatedApplicationEvent("BrandService", payload2);

            BrandApplicationEventListener listenerWithRealExecutor = new BrandApplicationEventListener(
                itemApplicationService, itemService);

            // When - trigger both events concurrently
            listenerWithRealExecutor.handleBrandUpdatedApplicationEvent(event1);
            listenerWithRealExecutor.handleBrandUpdatedApplicationEvent(event2);


            // Then
            verify(itemService).findByBrandId(brandId1);
            verify(itemService).findByBrandId(brandId2);
            verify(itemApplicationService, times(2)).update(any(UpdateItemCommand.class));
        }
    }
}