package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPriceResultDto;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.ItemBoundToPriceGroupPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemApplicationService;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.enums.AttributeType;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.service.ItemPriceGroupService;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.itempricegroup.ItemPriceGroupUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemPriceApplicationServiceImpl.class})
class ItemPriceApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private BusinessEventService businessEventService;

    @MockBean
    private Executor executor;

    @Autowired
    private ItemPriceApplicationServiceImpl itemPriceApplicationServiceImpl;

    @MockBean
    private ItemPromoPriceService itemPromoPriceService;

    @MockBean
    private ItemQueryApplicationService itemQueryApplicationService;

    @MockBean
    private ItemRegPriceService itemRegPriceService;
    @MockBean
    private ItemPriceGroupService itemPriceGroupService;
    @MockBean
    private ItemService itemService;
    @MockBean
    private ItemApplicationService itemApplicationService;


    @Test
    void testBatchUpdateRegPrice() {
        // Arrange
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ItemAttributeDto buildResult = ItemAttributeDto.builder()
            .attributeId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .attributeName("Attribute Name")
            .attributeType(AttributeType.KEY_ATTRIBUTES)
            .sort(1)
            .unit("Unit")
            .value("42")
            .build();
        itemAttributes.add(buildResult);
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");

        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder itemAttributesResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemAttributes(itemAttributes);
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult2 = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult2);
        when(itemApplicationService.update(any(UpdateItemCommand.class)))
                .thenThrow(new ImsBusinessException("Code"));
        ArrayList<UpdateItemRegPriceCommand> commands = new ArrayList<>();
        UpdateItemRegPriceCommand buildResult3 = UpdateItemRegPriceCommand
            .builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .regPrice(new BigDecimal("2.3")).build();
        commands.add(buildResult3);

        // Act
        BatchUpdateItemPriceResultDto actualBatchUpdateRegPriceResult = itemPriceApplicationServiceImpl
            .batchUpdateRegPrice(commands);

        // Assert
        verify(itemQueryApplicationService, atLeast(1)).findById(isA(UUID.class));
        verify(itemApplicationService).update(isA(UpdateItemCommand.class));
        List<String> failedSkuNumbers = actualBatchUpdateRegPriceResult.getFailedSkuNumbers();
        assertEquals(1, failedSkuNumbers.size());
        assertEquals(0, actualBatchUpdateRegPriceResult.getUpdatedCount());
    }


    @Test
    void testBatchUpdateRegPrice_thenReturnFailedIdsSizeIsOne() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");

        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder idResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult);
        when(itemApplicationService.update(any(UpdateItemCommand.class)))
                .thenThrow(new ImsBusinessException("Update failed"));

        ArrayList<UpdateItemRegPriceCommand> commands = new ArrayList<>();
        UpdateItemRegPriceCommand buildResult2 = UpdateItemRegPriceCommand
            .builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .regPrice(new BigDecimal("2.3")).build();
        commands.add(buildResult2);

        // Act
        BatchUpdateItemPriceResultDto actualBatchUpdateRegPriceResult = itemPriceApplicationServiceImpl
            .batchUpdateRegPrice(commands);

        // Assert

        List<String> failedSkuNumbers = actualBatchUpdateRegPriceResult.getFailedSkuNumbers();
        assertEquals(1, failedSkuNumbers.size());
        assertEquals(0, actualBatchUpdateRegPriceResult.getUpdatedCount());
    }


    @Test
    void testBatchUpdateRegPrice_thenReturnUpdatedCountIsOne() {
        // Arrange
        when(itemApplicationService.update(any(UpdateItemCommand.class))).thenReturn(null);
        ArrayList<UpdateItemRegPriceCommand> commands = new ArrayList<>();
        UpdateItemRegPriceCommand buildResult3 = UpdateItemRegPriceCommand
            .builder()
            .itemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .regPrice(new BigDecimal("2.3")).build();
        commands.add(buildResult3);

        // Act
        BatchUpdateItemPriceResultDto actualBatchUpdateRegPriceResult = itemPriceApplicationServiceImpl
            .batchUpdateRegPrice(commands);

        // Assert
        verify(itemApplicationService).update(isA(UpdateItemCommand.class));
        assertEquals(1, actualBatchUpdateRegPriceResult.getUpdatedCount());
        assertTrue(actualBatchUpdateRegPriceResult.getFailedSkuNumbers().isEmpty());
    }


    @Test
    void testBatchUpdateRegPrice_whenArrayList_thenReturnFailedIdsEmpty() {
        // Arrange

        // Act
        BatchUpdateItemPriceResultDto actualBatchUpdateRegPriceResult = itemPriceApplicationServiceImpl
            .batchUpdateRegPrice(new ArrayList<>());

        // Assert
        assertEquals(0, actualBatchUpdateRegPriceResult.getUpdatedCount());
        assertTrue(actualBatchUpdateRegPriceResult.getFailedSkuNumbers().isEmpty());
    }


    @Test
    void testBatchUpdateRegPrice_whenArrayList_thenReturnFailedIdsEmpty2() {
        // Arrange
        Attribute attribute = mock(Attribute.class);
        when(attribute.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Act
        BatchUpdateItemPriceResultDto actualBatchUpdateRegPriceResult = itemPriceApplicationServiceImpl
            .batchUpdateRegPrice(new ArrayList<>());

        // Assert
        assertEquals(0, actualBatchUpdateRegPriceResult.getUpdatedCount());
        assertTrue(actualBatchUpdateRegPriceResult.getFailedSkuNumbers().isEmpty());
    }

    @Test
    void testBindingItemPriceGroup() {
        ArrayList<ItemAttributeDto> itemAttributes = new ArrayList<>();
        ItemAttributeDto buildResult = ItemAttributeDto.builder()
            .attributeId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .attributeName("Attribute Name")
            .attributeType(AttributeType.KEY_ATTRIBUTES)
            .sort(1)
            .unit("Unit")
            .value("42")
            .build();
        itemAttributes.add(buildResult);
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .backupVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .bodyHtml("Not all who wander are lost");
        BrandDto brand = BrandDto.builder()
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .brandName("Brand Name")
            .build();
        ItemDto.ItemDtoBuilder categoryNameResult = bodyHtmlResult.brand(brand)
            .brandId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .category("Category")
            .categoryId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder itemAttributesResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .department("Department")
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle")
            .height(10.0d)
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .itemAttributes(itemAttributes);
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = builderResult.crv(new BigDecimal("2.3"))
            .crvFlag(true)
            .itemRegPriceId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .length(10.0d)
            .locationId(1)
            .name("Name")
            .newDescription("New Description")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking")
            .primaryVendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto primaryVendorItem = noteResult
            .statusChangeReason("Just cause")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto.ItemDtoBuilder typeResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .subCategory("Sub Category")
            .title("Dr")
            .type("Type");
        ItemDto buildResult2 = typeResult.vendorItemDtos(new ArrayList<>()).width(10.0d).build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult2);
        when(itemRegPriceService.update(Mockito.<UUID>any(), Mockito.<BigDecimal>any(), Mockito.<Boolean>any(),
            Mockito.<Integer>any(), Mockito.<ItemAttribute>any())).thenReturn(null);
        Attribute attribute = mock(Attribute.class);
        when(attribute.getName()).thenReturn("Name");
        when(attribute.getId()).thenReturn(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);
        Attribute attribute2 = mock(Attribute.class);
        when(attribute2.getId()).thenReturn(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID);

        when(businessEventService.dispatch(Mockito.<BusinessEventPayloadDto<AmendDto<ItemDto>>>any())).thenReturn(null);

        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();

        when(itemPriceGroupService.findById(any())).thenReturn(itemPriceGroup);

        UUID id = UUID.randomUUID();

        // Act
        itemPriceApplicationServiceImpl.bndingItemPriceGroup(id, id);

        // Assert
        verify(businessEventService).dispatch(isA(ItemBoundToPriceGroupPayloadDto.class));

    }


}
