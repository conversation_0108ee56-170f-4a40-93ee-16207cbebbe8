package com.mercaso.ims.infrastructure.repository.exceptionrecord;

import static com.mercaso.ims.domain.vendor.VendorConstant.DOWNEY_WHOLESALE;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.ItemPriceExceptionRecordDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordDto;
import com.mercaso.ims.application.query.ItemPriceExceptionRecordQuery;
import com.mercaso.ims.application.query.VendorItemCostExceptionRecordQuery;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import com.mercaso.ims.utils.exceptionrecord.ExceptionRecordUtil;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomizedExceptionRecordJpaDaoIT extends AbstractIT {

    @Test
    void testFetchItemPriceExceptionRecordDtoList() {

        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        String title = itemDo.getTitle().substring(2);

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();
        exceptionRecord.setEntityId(itemDo.getId());
        exceptionRecordRepository.save(exceptionRecord);

        List<ItemPriceExceptionRecordDto> result = customizedExceptionRecordJpaDao.fetchItemPriceExceptionRecordDtoList(
            ItemPriceExceptionRecordQuery.builder()
                .skuNumber(sku)
                .itemTitle(title)
                .build()
        );

        Assertions.assertNotNull(result);
        Assertions.assertEquals(itemDo.getId(), result.get(0).getItemId());
    }

    @Test
    void testItemPriceExceptionRecordCountQuery() {

        String sku = RandomStringUtils.randomAlphabetic(8);

        ItemDo itemDo = buildItemData(sku);

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildExceptionRecord();
        exceptionRecord.setEntityId(itemDo.getId());
        exceptionRecordRepository.save(exceptionRecord);

        Long result = customizedExceptionRecordJpaDao.itemPriceExceptionRecordCountQuery(
            ItemPriceExceptionRecordQuery.builder()
                .skuNumber(sku)
                .build()
        );

        Assertions.assertEquals(1l, result);
    }

    @Test
    void testFetchVendorItemCostExceptionRecordDtoList() {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemDataWithVendor(sku, DOWNEY_WHOLESALE, vendorItemNumber);

        VendorItemDo vendorItem = vendorItemJpaDao.findByVendorIdAndItemId(itemDo.getPrimaryVendorId(), itemDo.getId());

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildCostExceptionRecord();
        exceptionRecord.setEntityId(vendorItem.getId());
        exceptionRecordRepository.save(exceptionRecord);

        List<VendorItemCostExceptionRecordDto> result = customizedExceptionRecordJpaDao.fetchVendorItemCostExceptionRecordDtoList(
            VendorItemCostExceptionRecordQuery.builder()
                .skuNumber(sku)
                .build()
        );

        Assertions.assertNotNull(result);
        Assertions.assertEquals(itemDo.getId(), result.get(0).getItemId());
    }

    @Test
    void testVendorItemCostExceptionRecordCountQuery() {

        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemDataWithVendor(sku, DOWNEY_WHOLESALE, vendorItemNumber);

        VendorItemDo vendorItem = vendorItemJpaDao.findByVendorIdAndItemId(itemDo.getPrimaryVendorId(), itemDo.getId());

        ExceptionRecord exceptionRecord = ExceptionRecordUtil.buildCostExceptionRecord();
        exceptionRecord.setEntityId(vendorItem.getId());
        exceptionRecordRepository.save(exceptionRecord);

        Long result = customizedExceptionRecordJpaDao.vendorItemCostExceptionRecordCountQuery(
            VendorItemCostExceptionRecordQuery.builder()
                .skuNumber(sku)
                .build()
        );

        Assertions.assertEquals(1l, result);
    }
}