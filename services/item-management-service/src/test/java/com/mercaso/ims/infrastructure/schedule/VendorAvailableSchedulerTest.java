package com.mercaso.ims.infrastructure.schedule;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.infrastructure.external.vernon.dto.VernonItemDto;
import com.mercaso.ims.utils.vendor.VendorUtil;
import com.mercaso.ims.utils.vendoritem.VendorItemUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class VendorAvailableSchedulerTest {

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private EntityManagerFactory managerFactory;

    @Mock
    private VernonAdaptor vernonAdaptor;

    @Mock
    private VendorService vendorService;

    @Mock
    private VendorItemService vendorItemService;

    @Mock
    private VendorItemApplicationService vendorItemApplicationService;


    @InjectMocks
    private VendorAvailableScheduler vendorAvailableScheduler;

    private EntityManager entityManager;
    private Vendor vendor;
    private List<VendorItem> vendorItems;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        entityManager = Mockito.mock(EntityManager.class);
        vendor = VendorUtil.buildVendor(UUID.randomUUID());

        vendorItems = new ArrayList<>();
        VendorItem vendorItem1 = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem1.setVendorSkuNumber("SKU123");
        vendorItem1.setVendorItemName("Test Item 1");
        vendorItem1.setPackPlusCrvCost(BigDecimal.valueOf(10.99));

        VendorItem vendorItem2 = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem2.setVendorSkuNumber("SKU456");
        vendorItem2.setVendorItemName("Test Item 2");
        vendorItem2.setPackPlusCrvCost(BigDecimal.valueOf(20.99));

        vendorItems.add(vendorItem1);
        vendorItems.add(vendorItem2);

        when(managerFactory.createEntityManager()).thenReturn(entityManager);
    }


    @Test
    void testSyncVernorItemAvailableByVernorWebsite_LockNotAcquired() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(false);

        vendorAvailableScheduler.syncVernorItemAvailableByVernorWebsite();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(vendorService, never()).findByVendorName(anyString());
    }

    @Test
    void testSyncVernorItemAvailableByVernorWebsite_Success() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(true);
        when(pgAdvisoryLock.unLock(any(EntityManager.class), anyInt(), anyString())).thenReturn(true);

        when(vendorService.findByVendorName(VendorConstant.VERNON_SALES)).thenReturn(vendor);
        when(vendorItemService.findByVendorID(vendor.getId())).thenReturn(vendorItems);

        when(vernonAdaptor.searchVernonItem("SKU123")).thenReturn(List.of(new VernonItemDto()));
        when(vernonAdaptor.searchVernonItem("SKU456")).thenReturn(Collections.emptyList());

        vendorAvailableScheduler.syncVernorItemAvailableByVernorWebsite();

        verify(pgAdvisoryLock, times(1)).tryLockWithSessionLevel(any(), anyInt(), anyString());
        verify(pgAdvisoryLock, times(1)).unLock(any(), anyInt(), anyString());
        verify(vendorService, times(1)).findByVendorName(VendorConstant.VERNON_SALES);
        verify(vendorItemService, times(1)).findByVendorID(vendor.getId());

        verify(vernonAdaptor, times(1)).searchVernonItem("SKU123");
        verify(vernonAdaptor, times(1)).searchVernonItem("SKU456");

        verify(vendorItemApplicationService, times(2)).update(any(UpdateVendorItemCommand.class));
    }

    @Test
    void testSyncVernorItemAvailableByVernorWebsite_EmptySkuNumber() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(any(EntityManager.class), anyInt(), anyString())).thenReturn(true);

        VendorItem emptySkuItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        emptySkuItem.setVendorSkuNumber("");
        List<VendorItem> itemsWithEmptySku = List.of(emptySkuItem);

        when(vendorService.findByVendorName(VendorConstant.VERNON_SALES)).thenReturn(vendor);
        when(vendorItemService.findByVendorID(vendor.getId())).thenReturn(itemsWithEmptySku);

        vendorAvailableScheduler.syncVernorItemAvailableByVernorWebsite();

        verify(vernonAdaptor, never()).searchVernonItem(anyString());

        verify(vendorItemApplicationService, times(1)).update(any(UpdateVendorItemCommand.class));
    }
}
