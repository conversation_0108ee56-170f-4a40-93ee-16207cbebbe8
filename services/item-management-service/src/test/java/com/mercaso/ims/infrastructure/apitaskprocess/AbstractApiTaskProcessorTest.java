package com.mercaso.ims.infrastructure.apitaskprocess;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AbstractApiTaskProcessorTest {

    @Mock
    private ObjectMapper objectMapper;

    private TestableAbstractApiTaskProcessor processor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processor = new TestableAbstractApiTaskProcessor(objectMapper);
    }

    @Test
    void parseRequestPayload_SuccessfulParsing_ReturnsCorrectObject() throws JsonProcessingException {
        // Arrange
        String jsonPayload = "{\"methodName\":\"testMethod\",\"className\":\"com.test.TestClass\"}";
        ApiTaskQueue task = createTestTask();
        task.setRequestPayload(jsonPayload);
        TaskRequestPayload expectedPayload = TaskRequestPayload.builder()
                .methodName("testMethod")
                .className("com.test.TestClass")
                .build();

        when(objectMapper.readValue(jsonPayload, TaskRequestPayload.class)).thenReturn(expectedPayload);

        // Act
        TaskRequestPayload result = processor.parseRequestPayload(task, TaskRequestPayload.class);

        // Assert
        assertNotNull(result);
        assertEquals("testMethod", result.getMethodName());
        assertEquals("com.test.TestClass", result.getClassName());
        verify(objectMapper).readValue(jsonPayload, TaskRequestPayload.class);
    }

    @Test
    void parseRequestPayload_NullPayload_ThrowsIllegalArgumentException() {
        // Arrange
        ApiTaskQueue task = createTestTask();
        task.setRequestPayload(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> processor.parseRequestPayload(task, TaskRequestPayload.class));

        assertTrue(exception.getMessage().contains("Request payload is null for task"));
        verifyNoInteractions(objectMapper);
    }

    @Test
    void parseRequestPayload_JsonProcessingError_ThrowsJsonProcessingException() throws JsonProcessingException {
        // Arrange
        String invalidJson = "invalid json";
        ApiTaskQueue task = createTestTask();
        task.setRequestPayload(invalidJson);
        JsonProcessingException expectedException = new JsonProcessingException("Invalid JSON") {};

        when(objectMapper.readValue(invalidJson, TaskRequestPayload.class)).thenThrow(expectedException);

        // Act & Assert
        JsonProcessingException exception = assertThrows(JsonProcessingException.class,
                () -> processor.parseRequestPayload(task, TaskRequestPayload.class));

        assertEquals(expectedException, exception);
        verify(objectMapper).readValue(invalidJson, TaskRequestPayload.class);
    }

    @Test
    void serializeResponse_SuccessfulSerialization_ReturnsJsonString() throws JsonProcessingException {
        // Arrange
        String response = "test response";
        String expectedJson = "\"test response\"";

        when(objectMapper.writeValueAsString(response)).thenReturn(expectedJson);

        // Act
        String result = processor.serializeResponse(response);

        // Assert
        assertEquals(expectedJson, result);
        verify(objectMapper).writeValueAsString(response);
    }

    @Test
    void serializeResponse_NullResponse_ReturnsNull() throws JsonProcessingException {
        // Act
        String result = processor.serializeResponse(null);

        // Assert
        assertNull(result);
        verifyNoInteractions(objectMapper);
    }

    @Test
    void serializeResponse_JsonProcessingError_ThrowsJsonProcessingException() throws JsonProcessingException {
        // Arrange
        String response = "test response";
        JsonProcessingException expectedException = new JsonProcessingException("Serialization error") {};

        when(objectMapper.writeValueAsString(response)).thenThrow(expectedException);

        // Act & Assert
        JsonProcessingException exception = assertThrows(JsonProcessingException.class,
                () -> processor.serializeResponse(response));

        assertEquals(expectedException, exception);
        verify(objectMapper).writeValueAsString(response);
    }

    // Helper methods and test classes
    private ApiTaskQueue createTestTask() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("TEST_TASK")
                .apiEndpoint("/test/endpoint")
                .httpMethod("POST")
                .status(TaskStatus.PENDING)
                .createdAt(Instant.now())
                .build();
    }

    // Test implementation of AbstractApiTaskProcessor
    private static class TestableAbstractApiTaskProcessor extends AbstractApiTaskProcessor<String> {
        private String testResponse = null;
        private JsonProcessingException testException = null;

        public TestableAbstractApiTaskProcessor(ObjectMapper objectMapper) {
            super(objectMapper);
        }

        @Override
        public String getTaskType() {
            return "TEST_TASK";
        }

        @Override
        public boolean canProcess(String taskType) {
            return "TEST_TASK".equals(taskType);
        }

        @Override
        public String executeTask(ApiTaskQueue task) throws JsonProcessingException {
            if (testException != null) {
                throw testException;
            }
            return testResponse;
        }

        @Override
        public boolean needsResponse() {
            return true;
        }

        @Override
        public long calculateRetryDelay(int retryCount) {
            return retryCount * 1000L;
        }

        @Override
        public void validateTask(ApiTaskQueue task) {
            if (task.getRequestPayload() == null && task.getTaskType() == null) {
                throw new IllegalArgumentException("Invalid task");
            }
        }
    }
}
