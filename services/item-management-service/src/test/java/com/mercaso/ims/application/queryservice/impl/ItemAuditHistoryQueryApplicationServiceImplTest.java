package com.mercaso.ims.application.queryservice.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemAuditHistoryInfoDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemadjustmentrequest.service.ItemAdjustmentRequestService;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAuditHistoryQueryApplicationServiceImpl.class})
class ItemAuditHistoryQueryApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private BusinessEventService businessEventService;

    @MockBean
    private ItemAdjustmentRequestDetailService itemAdjustmentRequestDetailService;

    @MockBean
    private ItemAdjustmentRequestService itemAdjustmentRequestService;

    @Autowired
    private ItemAuditHistoryQueryApplicationServiceImpl itemAuditHistoryQueryApplicationServiceImpl;

    @MockBean
    private ItemService itemService;

    @MockBean
    private DocumentOperations documentOperations;

    @MockBean
    private DocumentApplicationService documentApplicationService;


    @Test
    void testItemAuditHistories_givenItemGetSkuNumberThrowImsBusinessExceptionWithCode() {
        // Arrange
        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        businessEventList.add(mock(BusinessEvent.class));
        when(businessEventService.findByEntityIdAndType(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenReturn(businessEventList);
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenThrow(new ImsBusinessException("Code"));
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);

        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> itemAuditHistoryQueryApplicationServiceImpl.itemAuditHistories(id));
        verify(businessEventService).findByEntityIdAndType(isA(UUID.class), eq("Item"));
        verify(item).getSkuNumber();
        verify(itemService).findById(isA(UUID.class));
    }

    @Test
    void testItemAuditHistories_givenItemServiceFindByIdReturnNull() {
        // Arrange
        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        businessEventList.add(mock(BusinessEvent.class));
        when(businessEventService.findByEntityIdAndType(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenReturn(businessEventList);
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(null);
        UUID id = UUID.randomUUID();
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> itemAuditHistoryQueryApplicationServiceImpl.itemAuditHistories(id));
        verify(businessEventService).findByEntityIdAndType(isA(UUID.class), eq("Item"));
        verify(itemService).findById(isA(UUID.class));
    }

    @Test
    void testItemAuditHistories_givenItemService_thenReturnEmpty() {
        // Arrange
        when(businessEventService.findByEntityIdAndType(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemAuditHistoryInfoDto> actualItemAuditHistoriesResult = itemAuditHistoryQueryApplicationServiceImpl
            .itemAuditHistories(UUID.randomUUID());

        // Assert
        verify(businessEventService).findByEntityIdAndType(isA(UUID.class), eq("Item"));
        assertTrue(actualItemAuditHistoriesResult.isEmpty());
    }


    @Test
    void testItemAuditHistories_thenReturnSizeIsOne() {
        // Arrange
        BusinessEvent businessEvent = mock(BusinessEvent.class);
        when(businessEvent.getType()).thenReturn(EventTypeEnums.TEST_ORDER_CREATED);

        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        businessEventList.add(businessEvent);
        when(businessEventService.findByEntityIdAndType(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenReturn(businessEventList);
        when(itemAdjustmentRequestService.findByIdIn(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestDetailService.findAllBySku(Mockito.<String>any())).thenReturn(new ArrayList<>());
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);

        // Act
        List<ItemAuditHistoryInfoDto> actualItemAuditHistoriesResult = itemAuditHistoryQueryApplicationServiceImpl
            .itemAuditHistories(UUID.randomUUID());

        // Assert
        verify(businessEvent).getType();
        verify(businessEventService).findByEntityIdAndType(isA(UUID.class), eq("Item"));
        verify(item).getSkuNumber();
        verify(itemService).findById(isA(UUID.class));
        verify(itemAdjustmentRequestService).findByIdIn(isA(List.class));
        verify(itemAdjustmentRequestDetailService).findAllBySku("42");
        assertEquals(1, actualItemAuditHistoriesResult.size());
        assertNull(actualItemAuditHistoriesResult.get(0));
    }


    @Test
    void testItemAuditHistories_thenReturnSizeIsTwo() {
        // Arrange
        BusinessEvent businessEvent = mock(BusinessEvent.class);
        when(businessEvent.getType()).thenReturn(EventTypeEnums.TEST_ORDER_CREATED);
        BusinessEvent businessEvent2 = mock(BusinessEvent.class);
        when(businessEvent2.getType()).thenReturn(EventTypeEnums.TEST_ORDER_CREATED);

        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        businessEventList.add(businessEvent2);
        businessEventList.add(businessEvent);
        when(businessEventService.findByEntityIdAndType(Mockito.<UUID>any(), Mockito.<String>any()))
            .thenReturn(businessEventList);
        when(itemAdjustmentRequestService.findByIdIn(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());
        when(itemAdjustmentRequestDetailService.findAllBySku(Mockito.<String>any())).thenReturn(new ArrayList<>());
        Item item = mock(Item.class);
        when(item.getSkuNumber()).thenReturn("42");
        when(itemService.findById(Mockito.<UUID>any())).thenReturn(item);

        // Act
        List<ItemAuditHistoryInfoDto> actualItemAuditHistoriesResult = itemAuditHistoryQueryApplicationServiceImpl
            .itemAuditHistories(UUID.randomUUID());

        // Assert
        verify(businessEvent2).getType();
        verify(businessEvent).getType();
        verify(businessEventService).findByEntityIdAndType(isA(UUID.class), eq("Item"));
        verify(item).getSkuNumber();
        verify(itemService).findById(isA(UUID.class));
        verify(itemAdjustmentRequestService).findByIdIn(isA(List.class));
        verify(itemAdjustmentRequestDetailService).findAllBySku("42");
        assertEquals(2, actualItemAuditHistoriesResult.size());
        assertNull(actualItemAuditHistoriesResult.get(0));
        assertNull(actualItemAuditHistoriesResult.get(1));
    }
}
