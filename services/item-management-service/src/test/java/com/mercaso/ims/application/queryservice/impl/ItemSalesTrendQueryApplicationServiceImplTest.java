package com.mercaso.ims.application.queryservice.impl;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.application.mapper.itemsalestrend.ItemSalesTrendDtoApplicationMapper;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.domain.itemsalestrend.service.ItemSalesTrendService;
import com.mercaso.ims.utils.itemsalestrend.ItemSalesTrendTestUtil;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {ItemSalesTrendQueryApplicationServiceImpl.class})
class ItemSalesTrendQueryApplicationServiceImplTest extends AbstractTest {

    @Autowired
    private ItemSalesTrendQueryApplicationServiceImpl itemSalesTrendQueryApplicationServiceImpl;

    @MockBean
    private ItemSalesTrendService itemSalesTrendService;

    @MockBean
    private ItemSalesTrendDtoApplicationMapper itemSalesTrendDtoApplicationMapper;

    @Test
    void testFindByItemIdAndTimeGrain_whenServiceReturnsEmptyList_thenReturnEmptyList() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        when(itemSalesTrendService.findByItemIdAndTimeGrain(any(UUID.class), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemSalesTrendDto> result = itemSalesTrendQueryApplicationServiceImpl
            .findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendService).findByItemIdAndTimeGrain(itemId, timeGrain);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByItemIdAndTimeGrain_whenServiceReturnsData_thenReturnMappedDtos() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.WEEK;

        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 100);
        ItemSalesTrendDto itemSalesTrendDto = ItemSalesTrendTestUtil.buildItemSalesTrendDto(itemId, timeGrain, 100);

        List<ItemSalesTrend> domainList = List.of(itemSalesTrend);
        
        when(itemSalesTrendService.findByItemIdAndTimeGrain(any(UUID.class), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(domainList);
        when(itemSalesTrendDtoApplicationMapper.domainToDto(any(ItemSalesTrend.class)))
            .thenReturn(itemSalesTrendDto);

        // Act
        List<ItemSalesTrendDto> result = itemSalesTrendQueryApplicationServiceImpl
            .findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendService).findByItemIdAndTimeGrain(itemId, timeGrain);
        verify(itemSalesTrendDtoApplicationMapper).domainToDto(itemSalesTrend);
        assertEquals(1, result.size());
        assertEquals(itemSalesTrendDto.getId(), result.getFirst().getId());
        assertEquals(itemSalesTrendDto.getItemId(), result.getFirst().getItemId());
        assertEquals(itemSalesTrendDto.getSkuNumber(), result.getFirst().getSkuNumber());
        assertEquals(itemSalesTrendDto.getSalesQuantity(), result.getFirst().getSalesQuantity());
        assertEquals(itemSalesTrendDto.getTimeGrain(), result.getFirst().getTimeGrain());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withMonthlyTimeGrain_thenReturnCorrectData() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.MONTH;

        ItemSalesTrend itemSalesTrend1 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 150);
        ItemSalesTrend itemSalesTrend2 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 200);
        ItemSalesTrendDto itemSalesTrendDto1 = ItemSalesTrendTestUtil.buildItemSalesTrendDto(itemId, timeGrain, 150);
        ItemSalesTrendDto itemSalesTrendDto2 = ItemSalesTrendTestUtil.buildItemSalesTrendDto(itemId, timeGrain, 200);

        List<ItemSalesTrend> domainList = List.of(itemSalesTrend1, itemSalesTrend2);
        
        when(itemSalesTrendService.findByItemIdAndTimeGrain(any(UUID.class), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(domainList);
        when(itemSalesTrendDtoApplicationMapper.domainToDto(itemSalesTrend1))
            .thenReturn(itemSalesTrendDto1);
        when(itemSalesTrendDtoApplicationMapper.domainToDto(itemSalesTrend2))
            .thenReturn(itemSalesTrendDto2);

        // Act
        List<ItemSalesTrendDto> result = itemSalesTrendQueryApplicationServiceImpl
            .findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendService).findByItemIdAndTimeGrain(itemId, timeGrain);
        verify(itemSalesTrendDtoApplicationMapper, times(2)).domainToDto(any(ItemSalesTrend.class));
        assertEquals(2, result.size());
        assertEquals(itemSalesTrendDto1.getSalesQuantity(), result.get(0).getSalesQuantity());
        assertEquals(itemSalesTrendDto2.getSalesQuantity(), result.get(1).getSalesQuantity());
        assertEquals(ItemSalesTrendTimeGrain.MONTH, result.get(0).getTimeGrain());
        assertEquals(ItemSalesTrendTimeGrain.MONTH, result.get(1).getTimeGrain());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withNullItemId_shouldCallServiceWithNullId() {
        // Arrange
        UUID itemId = null;
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        when(itemSalesTrendService.findByItemIdAndTimeGrain(any(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemSalesTrendDto> result = itemSalesTrendQueryApplicationServiceImpl
            .findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendService).findByItemIdAndTimeGrain(itemId, timeGrain);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withUnknownTimeGrain_shouldHandleCorrectly() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.UNKNOWN;
        when(itemSalesTrendService.findByItemIdAndTimeGrain(any(UUID.class), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemSalesTrendDto> result = itemSalesTrendQueryApplicationServiceImpl
            .findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendService).findByItemIdAndTimeGrain(itemId, timeGrain);
        assertTrue(result.isEmpty());
    }
}
