package com.mercaso.ims.infrastructure.repository.category;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.google.common.collect.Lists;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryFactory;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CategoryRepositoryImplIT extends AbstractIT {


    @Test
    void shouldSuccessWhenSaveCategory() {
        String name = "testCategory";
        String icon = "icon";
        String description = "description";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        Category result = categoryRepository.save(category);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
        Assertions.assertEquals(description, result.getDescription());
        Assertions.assertEquals(icon, result.getIcon());

    }

    @Test
    void shouldSuccessWhenFindByIdCategory() {
        String name = "testCategory_create";
        String icon = "icon_create";
        String description = "description_create";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        Category result = categoryRepository.save(category);

        Category categoryById = categoryRepository.findById(result.getId());

        Assertions.assertNotNull(categoryById);
        Assertions.assertEquals(name, categoryById.getName());
        Assertions.assertEquals(description, categoryById.getDescription());
        Assertions.assertEquals(icon, categoryById.getIcon());

    }

    @Test
    void shouldSuccessWhenUpdateCategory() {
        String name = "testCategory_update";
        String icon = "icon_update";
        String description = "description_update";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        Category result = categoryRepository.save(category);

        String newName = "testName";
        result.setName("testName");

        Category update = categoryRepository.update(result);

        Assertions.assertNotNull(update);
        Assertions.assertEquals(newName, update.getName());
    }

    @Test
    void shouldThrowWhenUpdateCategory() {
        String name = "testCategory_update_throw";
        String icon = "icon_update_throw";
        String description = "description_update_throw";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = Category.builder()
            .id(UUID.randomUUID())
            .name(command.getCategoryName())
            .status(CategoryStatus.DRAFT)
            .createdAt(Instant.now())
            .build();

        assertThrows(ImsBusinessException.class, () -> categoryRepository.update(category));
    }


    @Test
    void shouldSuccessWhenDeleteCategory() {
        String name = "testCategory_delete";
        String icon = "icon_delete";
        String description = "description_delete";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        Category result = categoryRepository.save(category);

        Category update = categoryRepository.deleteById(result.getId());

        Assertions.assertNotNull(update);
        Assertions.assertNotNull(update.getDeletedAt());

        Category byId = categoryRepository.findById(result.getId());
        Assertions.assertNull(byId);
    }


    @Test
    void shouldSuccessWhenFindByNameCategory() {
        String name = RandomStringUtils.randomAlphabetic(6);
        String icon = "icon_byName";
        String description = "description_byName";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        categoryRepository.save(category);

        List<Category> byName = categoryRepository.findByName(name);
        Assertions.assertNotNull(byName);
    }

    @Test
    void shouldSuccessWhenFindByNameCategoryReturnNull() {
        String name = "testCategory_byName1";

        List<Category> byName = categoryRepository.findByName(name);
        Assertions.assertEquals(0, byName.size());
    }


    @Test
    void shouldSuccessWhenFindByNameInCategory() {
        String name = RandomStringUtils.randomAlphabetic(6);
        String icon = "icon_byNameIn";
        String description = "description_byNameIn";

        CreateCategoryCommand command = new CreateCategoryCommand(UUID.randomUUID(),
            name,
            1,
            CategoryStatus.ACTIVE,
            icon,
            description,
            Lists.newArrayList());
        Category category = CategoryFactory.create(command);
        categoryRepository.save(category);
        List<String> names = Lists.newArrayList(name);
        List<Category> allByNameIn = categoryRepository.findAllByNameIn(names);
        Assertions.assertNotNull(allByNameIn);
        Assertions.assertEquals(1, allByNameIn.size());
    }

}
