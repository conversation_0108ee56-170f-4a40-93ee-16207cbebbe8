package com.mercaso.ims.infrastructure.repository.vendorpoinvoice;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.utils.itemcostcollection.ItemCostCollectionUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemCostCollectionRepositoryImplIT extends AbstractIT {


    @Test
    void testSave() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        ItemCostCollection result = itemCostCollectionRepository.save(itemCostCollection);
        Assertions.assertEquals(itemCostCollection.getVendorId(), result.getVendorId());
    }

    @Test
    void testFindById() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        ItemCostCollection savedItemCostCollection = itemCostCollectionRepository.save(itemCostCollection);

        ItemCostCollection result = itemCostCollectionRepository.findById(savedItemCostCollection.getId());
        Assertions.assertNotNull(result);

        Assertions.assertEquals(savedItemCostCollection.getId(), result.getId());
        Assertions.assertEquals(savedItemCostCollection.getVendorId(), result.getVendorId());
    }

    @Test
    void testUpdate() {
        String crvFile = RandomStringUtils.randomAlphabetic(10);

        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        itemCostCollection.setFileName(crvFile);
        ItemCostCollection savedItemCostCollection = itemCostCollectionRepository.save(itemCostCollection);
        ItemCostCollection updatedItemCostCollection = itemCostCollectionRepository.findById(savedItemCostCollection.getId());

        ItemCostCollection result = itemCostCollectionRepository.update(updatedItemCostCollection);
        Assertions.assertNotNull(result);

        Assertions.assertEquals(crvFile, result.getFileName());
    }

    @Test
    void testDeleteById() {
        ItemCostCollection itemCostCollection = ItemCostCollectionUtil.buildItemCostCollection();
        ItemCostCollection savedItemCostCollection = itemCostCollectionRepository.save(itemCostCollection);

        ItemCostCollection result = itemCostCollectionRepository.deleteById(savedItemCostCollection.getId());
        Assertions.assertNotNull(result.getDeletedAt());
    }
}