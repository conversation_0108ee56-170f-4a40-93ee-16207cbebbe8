package com.mercaso.ims.infrastructure.repository.businessevent;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.DeleteItemCommand;
import com.mercaso.ims.application.dto.BusinessEventDto;
import com.mercaso.ims.application.query.BusinessEventQuery;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CustomizedBusinessEventJapDaoImplIT extends AbstractIT {

    @Test
    void testGetBusinessEventDtoList() {
        String sku = RandomStringUtils.randomAlphabetic(5);
        ItemDo itemDo = buildItemData(sku);

        UUID id = itemDo.getId();
        String type = "ITEM_DELETED";
        Set<UUID> entityIds = Set.of(id);

        DeleteItemCommand deleteItemCommand = DeleteItemCommand.builder()
            .id(id)
            .itemAdjustmentRequestDetailId(null)
            .build();
        itemApplicationService.deleteItemById(deleteItemCommand);

        BusinessEventQuery businessEventQuery = BusinessEventQuery.builder().entityIds(entityIds).type(type).build();
        List<BusinessEventDto> result = customizedBusinessEventJapDao.getBusinessEventDtoList(businessEventQuery);
        Assertions.assertEquals(1, result.size());
    }
}
