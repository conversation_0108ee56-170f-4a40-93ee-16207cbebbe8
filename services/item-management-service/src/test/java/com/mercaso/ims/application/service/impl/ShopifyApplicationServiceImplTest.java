package com.mercaso.ims.application.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.itemsyncinfo.service.ItemAdjustmentSyncStatusService;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto;
import com.mercaso.ims.utils.item.ItemAmendPayloadDtoUtil;
import com.mercaso.ims.utils.item.ItemCreatedPayloadDtoUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.UUID;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ShopifyApplicationServiceImpl.class})
class ShopifyApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private Executor executor;

    @MockBean
    private ItemAdjustmentSyncStatusService itemAdjustmentSyncStatusService;

    @MockBean
    private ItemQueryApplicationService itemQueryApplicationService;

    @MockBean
    private ShopifyAdaptor shopifyAdaptor;

    @MockBean
    private ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService;

    @Autowired
    private ShopifyApplicationServiceImpl shopifyApplicationServiceImpl;

    @MockBean
    private FeatureFlagsManager featureFlagsManager;

    /**
     * Method under test: {@link ShopifyApplicationServiceImpl#syncItemCreatedEvent(ItemCreatedApplicationEvent)}
     */
    @Test
    void testSyncItemCreatedEvent() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder brandIdResult = brandResult.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult = brandIdResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult);
        doNothing().when(executor).execute(Mockito.<Runnable>any());
        ItemCreatedPayloadDto.ItemCreatedPayloadDtoBuilder builderResult4 = ItemCreatedPayloadDto.builder();
        ItemDto.ItemDtoBuilder bodyHtmlResult2 = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult5 = BrandDto.builder();
        BrandDto brand2 = builderResult5.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult2 = bodyHtmlResult2.brand(brand2);
        ItemDto.ItemDtoBuilder brandIdResult2 = brandResult2.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult3 = brandIdResult2.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree2 = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult2 = categoryNameResult3.categoryTree(categoryTree2)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult2 = handleResult2.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult2 = idResult2.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult2 = itemAttributesResult2.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult2 = itemImagesResult2.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult2 = ItemRegPriceDto.builder().crv(null);
        ItemRegPriceDto itemRegPrice2 = crvResult2.itemRegPriceId(UUID.randomUUID())
            .regPrice(null)
            .regPriceIndividual(null)
            .regPricePlusCrv(null)
            .build();
        ItemDto.ItemDtoBuilder itemRegPriceResult2 = itemPromoPricesResult2.itemRegPrice(itemRegPrice2);
        ItemDto.ItemDtoBuilder itemTypeResult2 = itemRegPriceResult2.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult2 = itemTypeResult2.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult2 = priceLinkingResult2.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult2 = VendorItemDto.builder()
            .aisle("Aisle")
            .cost(null)
            .highestCost(null)
            .lowestCost(null)
            .note("Note")

            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult2 = statusChangeReasonResult2.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem2 = vendorIdResult2.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto data = primaryVendorIdResult2.primaryVendorItem(primaryVendorItem2)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        ItemCreatedPayloadDto.ItemCreatedPayloadDtoBuilder dataResult = builderResult4.data(data);
        ItemCreatedPayloadDto.ItemCreatedPayloadDtoBuilder itemAdjustmentRequestDetailIdResult = dataResult
            .itemAdjustmentRequestDetailId(UUID.randomUUID());
        ItemCreatedPayloadDto payload = itemAdjustmentRequestDetailIdResult.itemId(UUID.randomUUID()).build();

        // Act
        shopifyApplicationServiceImpl.syncItemCreatedEvent(new ItemCreatedApplicationEvent("Source", payload));

        // Assert that nothing has changed
        verify(itemQueryApplicationService).findById(isA(UUID.class));
        verify(executor).execute(isA(Runnable.class));
    }

    /**
     * Method under test: {@link ShopifyApplicationServiceImpl#syncItemAmendEvent(ItemAmendApplicationEvent)}
     */
    @Test
    void testSyncItemAmendEvent() {
        // Arrange
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult = BrandDto.builder();
        BrandDto brand = builderResult.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder brandIdResult = brandResult.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult = brandIdResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");
        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder builderResult3 = ItemRegPriceDto.builder();
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = builderResult3.crv(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder itemRegPriceIdResult = crvResult.itemRegPriceId(UUID.randomUUID());
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceResult = itemRegPriceIdResult.regPrice(new BigDecimal("2.3"));
        ItemRegPriceDto.ItemRegPriceDtoBuilder regPriceIndividualResult = regPriceResult
            .regPriceIndividual(new BigDecimal("2.3"));
        ItemRegPriceDto itemRegPrice = regPriceIndividualResult.regPricePlusCrv(new BigDecimal("2.3")).build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder aisleResult = VendorItemDto.builder().aisle("Aisle");
        VendorItemDto.VendorItemDtoBuilder costResult = aisleResult.cost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder highestCostResult = costResult.highestCost(new BigDecimal("2.3"));
        VendorItemDto.VendorItemDtoBuilder noteResult = highestCostResult.lowestCost(new BigDecimal("2.3")).note("Note");
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = noteResult
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto buildResult = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        when(itemQueryApplicationService.findById(Mockito.<UUID>any())).thenReturn(buildResult);
        doNothing().when(executor).execute(Mockito.<Runnable>any());
        ItemAmendPayloadDto.ItemAmendPayloadDtoBuilder builderResult4 = ItemAmendPayloadDto.builder();
        ItemDto.ItemDtoBuilder bodyHtmlResult2 = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult5 = BrandDto.builder();
        BrandDto brand2 = builderResult5.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult2 = bodyHtmlResult2.brand(brand2);
        ItemDto.ItemDtoBuilder brandIdResult2 = brandResult2.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult3 = brandIdResult2.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree2 = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult2 = categoryNameResult3.categoryTree(categoryTree2)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult2 = handleResult2.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult2 = idResult2.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult2 = itemAttributesResult2.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult2 = itemImagesResult2.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult2 = ItemRegPriceDto.builder().crv(null);
        ItemRegPriceDto itemRegPrice2 = crvResult2.itemRegPriceId(UUID.randomUUID())
            .regPrice(null)
            .regPriceIndividual(null)
            .regPricePlusCrv(null)
            .build();
        ItemDto.ItemDtoBuilder itemRegPriceResult2 = itemPromoPricesResult2.itemRegPrice(itemRegPrice2);
        ItemDto.ItemDtoBuilder itemTypeResult2 = itemRegPriceResult2.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult2 = itemTypeResult2.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult2 = priceLinkingResult2.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult2 = VendorItemDto.builder()
            .aisle("Aisle")
            .cost(null)
            .highestCost(null)
            .lowestCost(null)
            .note("Note")

            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult2 = statusChangeReasonResult2.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem2 = vendorIdResult2.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto current = primaryVendorIdResult2.primaryVendorItem(primaryVendorItem2)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        ItemAmendPayloadDto.ItemAmendPayloadDtoBuilder currentResult = builderResult4.current(current);
        ItemAmendPayloadDto.ItemAmendPayloadDtoBuilder itemAdjustmentRequestDetailIdResult = currentResult
            .itemAdjustmentRequestDetailId(UUID.randomUUID());
        ItemAmendPayloadDto.ItemAmendPayloadDtoBuilder itemIdResult = itemAdjustmentRequestDetailIdResult
            .itemId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder bodyHtmlResult3 = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult7 = BrandDto.builder();
        BrandDto brand3 = builderResult7.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult3 = bodyHtmlResult3.brand(brand3);
        ItemDto.ItemDtoBuilder brandIdResult3 = brandResult3.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult5 = brandIdResult3.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree3 = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult3 = categoryNameResult5.categoryTree(categoryTree3)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult3 = handleResult3.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult3 = idResult3.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult3 = itemAttributesResult3.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult3 = itemImagesResult3.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult3 = ItemRegPriceDto.builder().crv(null);
        ItemRegPriceDto itemRegPrice3 = crvResult3.itemRegPriceId(UUID.randomUUID())
            .regPrice(null)
            .regPriceIndividual(null)
            .regPricePlusCrv(null)
            .build();
        ItemDto.ItemDtoBuilder itemRegPriceResult3 = itemPromoPricesResult3.itemRegPrice(itemRegPrice3);
        ItemDto.ItemDtoBuilder itemTypeResult3 = itemRegPriceResult3.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult3 = itemTypeResult3.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult3 = priceLinkingResult3.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult3 = VendorItemDto.builder()
            .aisle("Aisle")
            .cost(null)
            .highestCost(null)
            .lowestCost(null)
            .note("Note")
            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult3 = statusChangeReasonResult3.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem3 = vendorIdResult3.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto previous = primaryVendorIdResult3.primaryVendorItem(primaryVendorItem3)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        ItemAmendPayloadDto payload = itemIdResult.previous(previous).build();

        // Act
        shopifyApplicationServiceImpl.syncItemAmendEvent(new ItemAmendApplicationEvent("Source", payload));

        // Assert that nothing has changed
        verify(executor).execute(isA(Runnable.class));
    }

    /**
     * Method under test: {@link ShopifyApplicationServiceImpl#syncItemDeleteEvent(ItemDeletedApplicationEvent)}
     */
    @Test
    void testSyncItemDeleteEvent() {
        // Arrange
        doNothing().when(executor).execute(Mockito.<Runnable>any());
        ItemDeletedPayloadDto.ItemDeletedPayloadDtoBuilder builderResult = ItemDeletedPayloadDto.builder();
        ItemDto.ItemDtoBuilder bodyHtmlResult = ItemDto.builder()
            .availabilityStatus("Availability Status")
            .bodyHtml("Not all who wander are lost");
        BrandDto.BrandDtoBuilder builderResult2 = BrandDto.builder();
        BrandDto brand = builderResult2.brandId(UUID.randomUUID()).brandName("Brand Name").build();
        ItemDto.ItemDtoBuilder brandResult = bodyHtmlResult.brand(brand);
        ItemDto.ItemDtoBuilder brandIdResult = brandResult.brandId(UUID.randomUUID());
        ItemDto.ItemDtoBuilder categoryNameResult = brandIdResult.categoryId(UUID.randomUUID())
            .categoryName("Category Name");

        CategoryDto categoryTree = new CategoryDto();
        ItemDto.ItemDtoBuilder handleResult = categoryNameResult.categoryTree(categoryTree)
            .clazz("Clazz")
            .companyId(1)
            .description("The characteristics of someone or something")
            .detail("Detail")
            .handle("Handle");
        ItemDto.ItemDtoBuilder idResult = handleResult.id(UUID.randomUUID());
        ItemDto.ItemDtoBuilder itemAttributesResult = idResult.itemAttributes(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemImagesResult = itemAttributesResult.itemImages(new ArrayList<>());
        ItemDto.ItemDtoBuilder itemPromoPricesResult = itemImagesResult.itemPromoPrices(new ArrayList<>());
        ItemRegPriceDto.ItemRegPriceDtoBuilder crvResult = ItemRegPriceDto.builder().crv(null);
        ItemRegPriceDto itemRegPrice = crvResult.itemRegPriceId(UUID.randomUUID())
            .regPrice(null)
            .regPriceIndividual(null)
            .regPricePlusCrv(null)
            .build();
        ItemDto.ItemDtoBuilder itemRegPriceResult = itemPromoPricesResult.itemRegPrice(itemRegPrice);
        ItemDto.ItemDtoBuilder itemTypeResult = itemRegPriceResult.itemTags(new ArrayList<>()).itemType("Item Type");
        ItemDto.ItemDtoBuilder priceLinkingResult = itemTypeResult.itemUPCs(new ArrayList<>())
            .locationId(1)
            .name("Name")
            .note("Note")
            .packageSize(3)
            .packageType("java.text")
            .priceLinking("Price Linking");
        ItemDto.ItemDtoBuilder primaryVendorIdResult = priceLinkingResult.primaryVendorId(UUID.randomUUID());
        VendorItemDto.VendorItemDtoBuilder statusChangeReasonResult = VendorItemDto.builder()
            .aisle("Aisle")
            .cost(null)
            .highestCost(null)
            .lowestCost(null)
            .note("Note")

            .statusChangeReason("Just cause");
        VendorItemDto.VendorItemDtoBuilder vendorIdResult = statusChangeReasonResult.vendorId(UUID.randomUUID());
        VendorItemDto primaryVendorItem = vendorIdResult.vendorItemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        ItemDto data = primaryVendorIdResult.primaryVendorItem(primaryVendorItem)
            .shelfLife("Shelf Life")
            .skuNumber("42")
            .title("Dr")
            .type("Type")
            .build();
        ItemDeletedPayloadDto.ItemDeletedPayloadDtoBuilder dataResult = builderResult.data(data);
        ItemDeletedPayloadDto.ItemDeletedPayloadDtoBuilder itemAdjustmentRequestDetailIdResult = dataResult
            .itemAdjustmentRequestDetailId(UUID.randomUUID());
        ItemDeletedPayloadDto payload = itemAdjustmentRequestDetailIdResult.itemId(UUID.randomUUID()).build();

        // Act
        shopifyApplicationServiceImpl.syncItemDeleteEvent(new ItemDeletedApplicationEvent("Source", payload));

        // Assert that nothing has changed
        verify(executor).execute(isA(Runnable.class));
    }

    @Test
    void testSyncItemCreatedEventToShopify_Success() {
        ItemCreatedPayloadDto payloadDto = ItemCreatedPayloadDtoUtil.buildItemCreatedPayloadDto(UUID.randomUUID(),
            UUID.randomUUID());
        payloadDto.setItemAdjustmentRequestDetailId(UUID.randomUUID());

        ItemDto itemDto = new ItemDto();
        itemDto.setSkuNumber("testSku");

        UUID businessEventId = UUID.randomUUID();

        ShopifyGraphQLQueryResponseDto.DataDto data = new ShopifyGraphQLQueryResponseDto.DataDto();
        data.setProducts(new ShopifyGraphQLQueryResponseDto.ProductsDto());
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = ShopifyGraphQLQueryResponseDto.builder()
            .data(data)
            .build();
        shopifyGraphQLQueryResponseDto.getData().getProducts().setEdges(new ArrayList<>());

        when(shopifyAdaptor.queryProduct(any())).thenReturn(null);

        shopifyApplicationServiceImpl.syncItemCreatedEventToShopify(payloadDto, itemDto, businessEventId);

        verify(shopifyAdaptor, times(1)).queryProduct(itemDto.getSkuNumber());
        verify(shopifyAdaptor, times(1)).createShopifyProduct(any());
        verify(shopifyAdaptor, times(0)).modifyShopifyProduct(any());
        verify(shopifyAdaptor, times(1)).modifyShopifyInventoryItem(any());
        verify(shopifyAdaptor, times(1)).createOrModifyShopifyProductMetaFields(any(), any());
        verify(itemAdjustmentSyncStatusService, times(1)).saveItemAdjustmentSyncSuccessStatus(businessEventId);
        verify(itemAdjustmentRequestDetailApplicationService,
            times(1)).syncToShopify(payloadDto.getItemAdjustmentRequestDetailId());
    }


    @Test
    void testSyncItemAmendEventToShopify_Success() {
        ItemAmendPayloadDto payloadDto = ItemAmendPayloadDtoUtil.buildItemAmendPayloadDto();
        payloadDto.setItemAdjustmentRequestDetailId(UUID.randomUUID());

        UUID itemId = UUID.randomUUID();

        ItemDto itemDto = new ItemDto();
        itemDto.setSkuNumber("testSku");
        itemDto.setId(itemId);

        UUID businessEventId = UUID.randomUUID();

        ShopifyGraphQLQueryResponseDto.DataDto data = new ShopifyGraphQLQueryResponseDto.DataDto();
        data.setProducts(new ShopifyGraphQLQueryResponseDto.ProductsDto());
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = ShopifyGraphQLQueryResponseDto.builder()
            .data(data)
            .build();
        shopifyGraphQLQueryResponseDto.getData().getProducts().setEdges(new ArrayList<>());

        when(shopifyAdaptor.queryProduct(any())).thenReturn(shopifyGraphQLQueryResponseDto);
        when(itemQueryApplicationService.findById(any())).thenReturn(itemDto);

        shopifyApplicationServiceImpl.syncItemAmendEventToShopify(payloadDto, itemDto.getId(), businessEventId);

        verify(shopifyAdaptor, times(1)).queryProduct(itemDto.getSkuNumber());
        verify(shopifyAdaptor, times(0)).createShopifyProduct(any());

        verify(shopifyAdaptor, times(1)).modifyShopifyProduct(any());
        verify(shopifyAdaptor, times(1)).createOrModifyShopifyProductMetaFields(any(), any());
        verify(itemAdjustmentSyncStatusService, times(1)).saveItemAdjustmentSyncSuccessStatus(businessEventId);
        verify(itemAdjustmentRequestDetailApplicationService,
            times(1)).syncToShopify(payloadDto.getItemAdjustmentRequestDetailId());
    }


    @Test
    void testSyncItemDeletedEventToShopify_Success() {
        ItemDeletedPayloadDto itemDeletedPayloadDto = new ItemDeletedPayloadDto();
        itemDeletedPayloadDto.setItemAdjustmentRequestDetailId(UUID.randomUUID());

        ItemDto itemDto = new ItemDto();
        itemDto.setSkuNumber("testSku");

        UUID businessEventId = UUID.randomUUID();

        ShopifyGraphQLQueryResponseDto.DataDto data = new ShopifyGraphQLQueryResponseDto.DataDto();
        data.setProducts(new ShopifyGraphQLQueryResponseDto.ProductsDto());
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto = ShopifyGraphQLQueryResponseDto.builder()
            .data(data)
            .build();
        shopifyGraphQLQueryResponseDto.getData().getProducts().setEdges(new ArrayList<>());

        when(shopifyAdaptor.queryProduct(any())).thenReturn(shopifyGraphQLQueryResponseDto);

        shopifyApplicationServiceImpl.syncItemDeletedEventToShopify(itemDeletedPayloadDto, itemDto, businessEventId);

        verify(shopifyAdaptor, times(1)).queryProduct(itemDto.getSkuNumber());
        verify(shopifyAdaptor, times(0)).deleteShopifyProduct(any());
        verify(itemAdjustmentSyncStatusService, times(1)).saveItemAdjustmentSyncSuccessStatus(businessEventId);
        verify(itemAdjustmentRequestDetailApplicationService,
            times(1)).syncToShopify(itemDeletedPayloadDto.getItemAdjustmentRequestDetailId());
    }

    @Test
    void testSyncItemDeletedEventToShopify_Exception() {

        ItemDeletedPayloadDto itemDeletedPayloadDto = new ItemDeletedPayloadDto();
        itemDeletedPayloadDto.setItemAdjustmentRequestDetailId(UUID.randomUUID());

        ItemDto itemDto = new ItemDto();
        itemDto.setSkuNumber("testSku");

        UUID businessEventId = UUID.randomUUID();

        when(shopifyAdaptor.queryProduct(itemDto.getSkuNumber())).thenThrow(new RuntimeException("Test Exception"));

        shopifyApplicationServiceImpl.syncItemDeletedEventToShopify(itemDeletedPayloadDto, itemDto, businessEventId);

        verify(shopifyAdaptor, times(1)).queryProduct(itemDto.getSkuNumber());
        verify(shopifyAdaptor, times(0)).deleteShopifyProduct(anyLong());
        verify(itemAdjustmentSyncStatusService, times(1)).saveItemAdjustmentSyncFiledStatus(businessEventId);
        verify(itemAdjustmentRequestDetailApplicationService,
            times(1)).syncToShopifyFailure(itemDeletedPayloadDto.getItemAdjustmentRequestDetailId(), "Test Exception");
    }
}
