package com.mercaso.ims.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

class ShortRandomIdUtilTest {


    @Test
    void testShortRandomId_whenMinusOne() {
        // Arrange and Act
        // Assert
        assertThrows(RuntimeException.class, () -> ShortRandomIdUtil.shortRandomId(-1));

    }

    @Test
    void testShortRandomId_whenThree() {
        // Arrange and Act
        String s = ShortRandomIdUtil.shortRandomId(3);

        assertEquals(3, s.length());
    }
}
