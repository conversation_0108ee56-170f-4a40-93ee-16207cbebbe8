apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${APPLICATION_NAME}
  name: ${APPLICATION_NAME}
  namespace: default
spec:
  replicas: $REPLICAS
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      app: ${APPLICATION_NAME}
  template:
    metadata:
      labels:
        app: ${APPLICATION_NAME}
    spec:
      serviceAccountName: ${APPLICATION_NAME}
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: ${APPLICATION_NAME}
      volumes:
        - name: skywalking-agent
          emptyDir: { }
      initContainers:
        - name: init-skywalking-agent
          image: apache/skywalking-java-agent:9.2.0-java21
          command: [ 'sh', '-c', 'cp -r /skywalking/agent/* /skywalking-agent' ]
          volumeMounts:
            - name: skywalking-agent
              mountPath: /skywalking-agent
      containers:
        - name: ${APPLICATION_NAME}
          image: ${DOCKER_REPO_URL}/mercaso/${APPLICATION_NAME}:${IMAGE_VERSION}
          imagePullPolicy: Always
          volumeMounts:
            - name: skywalking-agent
              mountPath: /skywalking-agent
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: $SPRING_PROFILES_ACTIVE
            - name: JAVA_TOOL_OPTIONS
              value: "-javaagent:/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.service_name=$APPLICATION_NAME -Dskywalking.collector.backend_service=$SKYWALKING_OAP_HOST $SKYWALKING_EXTRA_CONFIG"
          # Graceful shutdown hack - sleep 15 seconds to give the k8s network
          # config time to update and stop routing new requests to this pod.
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 15
          ports:
            - containerPort: 8080
              protocol: TCP
              name: http
            - containerPort: 8081
              protocol: TCP
              name: monitoring
            - containerPort: 1099
              protocol: TCP
              name: jmx
          resources:
            requests:
              memory: $REQUESTS_MEMORY
              cpu: $REQUESTS_CPU
            limits:
              memory: $LIMITS_MEMORY
              cpu: $LIMITS_CPU
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: monitoring
            initialDelaySeconds: 120
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: monitoring
            successThreshold: 2
            failureThreshold: 2
            periodSeconds: 5
            timeoutSeconds: 5
          startupProbe:
            httpGet:
              path: /actuator/health/readiness
              port: monitoring
            successThreshold: 1
            periodSeconds: 5
            failureThreshold: 60
      terminationGracePeriodSeconds: 180
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ${APPLICATION_NAME}
    #archetype: next-spring-boot-v1
  name: ${APPLICATION_NAME}
  namespace: default
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
    - name: monitoring
      port: 8081
      protocol: TCP
  selector:
    app: ${APPLICATION_NAME}
  type: ClusterIP
