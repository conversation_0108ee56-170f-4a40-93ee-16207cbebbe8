package com.mercaso.partnerinsight.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TenantItemInfoDto extends BaseDto {

    private String skuNumber;

    private String upc;

    private String name;

    private String photoUrl;

    private Integer packageSize;

    private Long salesQty;

    private String aggregationLevel;
}