package com.mercaso.partnerinsight.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "tenant_item_info")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update tenant_item_info set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class TenantItemInfo extends BaseEntity {

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "upc")
    private String upc;

    @Column(name = "name")
    private String name;

    @Column(name = "photo")
    private String photo;

    @Column(name = "package_size")
    private Integer packageSize;

    @Column(name = "sales_qty")
    private Long salesQty;

    @Column(name = "aggregation_level")
    private String aggregationLevel;

    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "store_id")
    private String storeId;

}