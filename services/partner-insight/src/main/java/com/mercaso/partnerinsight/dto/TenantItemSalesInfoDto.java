package com.mercaso.partnerinsight.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TenantItemSalesInfoDto extends BaseDto {

    private String aggregationLevel;

    private Long itemCount;

    private Long salesQty;

}