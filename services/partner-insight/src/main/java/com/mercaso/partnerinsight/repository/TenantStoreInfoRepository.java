package com.mercaso.partnerinsight.repository;


import com.mercaso.partnerinsight.entity.TenantStoreInfo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TenantStoreInfoRepository extends JpaRepository<TenantStoreInfo, UUID> {

    List<TenantStoreInfo> findAllByTenantIdOrderBySalesQtyDescItemCountDesc(String tenantId);
}