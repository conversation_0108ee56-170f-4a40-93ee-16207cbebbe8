package com.mercaso.partnerinsight.entity;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.time.Instant;


@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "tenant_store_sales_info")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update tenant_store_sales_info set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class TenantStoreSalesInfo extends BaseEntity {

    @Column(name = "store_id")
    private String storeId;

    @Column(name = "sales_qty")
    private Long salesQty;

    @Column(name = "sales_month")
    private Instant salesMonth;

    @Column(name = "tenant_id")
    private String tenantId;

}