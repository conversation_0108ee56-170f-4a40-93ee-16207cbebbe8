package com.mercaso.partnerinsight.service.impl;

import com.mercaso.partnerinsight.dto.TenantStoreSalesInfoDto;
import com.mercaso.partnerinsight.mapper.TenantStoreSalesInfoDtoMapper;
import com.mercaso.partnerinsight.repository.TenantStoreSalesInfoRepository;
import com.mercaso.partnerinsight.service.TenantStoreSalesInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Slf4j
public class TenantStoreSalesInfoServiceImpl implements TenantStoreSalesInfoService {

    private final TenantStoreSalesInfoRepository tenantStoreSalesInfoRepository;
    private final TenantStoreSalesInfoDtoMapper tenantStoreSalesInfoDtoMapper;

    @Override
    public List<TenantStoreSalesInfoDto> findAllByTenantIdAndStoreId(String tenantId, String storeId) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("[TenantStoreSalesInfoServiceImpl] tenantId is null, return empty list");
            return List.of();
        }
        return Optional.of(tenantStoreSalesInfoRepository.findAllByTenantIdAndStoreIdOrderBySalesMonthAsc(tenantId, storeId))
            .orElse(Collections.emptyList())
            .stream()
            .map(tenantStoreSalesInfoDtoMapper::entityToDto)
            .toList();
    }
}