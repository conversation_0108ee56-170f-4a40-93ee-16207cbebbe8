package com.mercaso.partnerinsight.util;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import org.apache.commons.lang3.StringUtils;

public class DateUtils {

    private DateUtils() {
    }

    //2021-03-24T00:00:00Z
    public static Instant getStartOfCurrentDay(Instant instant) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return startOfDay.toInstant(ZoneOffset.UTC);
    }

    public static Instant fromTimestamp(Timestamp date) {
        if (date != null) {
            return date.toInstant();
        }

        return null;
    }

    public static Instant fromStringTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        Timestamp timestamp = Timestamp.valueOf(date);
        return fromTimestamp(timestamp);
    }


}