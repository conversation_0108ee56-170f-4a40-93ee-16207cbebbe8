package com.mercaso.partnerinsight.query.repository;

import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery.SortType;
import com.mercaso.partnerinsight.util.DateUtils;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedTenantItemInfoRepositoryImpl implements CustomizedTenantItemInfoRepository{

    private final JdbcTemplate jdbcTemplate;

    private static final String TENANT_ITEM_INFO_SQL = """
        SELECT
            sku_number,
            upc AS upc,
            name AS name,
            photo AS photo,
            package_size AS package_size,
            aggregation_level AS aggregation_level,
            sales_qty AS sales_qty,
            tenant_id AS tenant_id,
            created_at AS created_at,
            created_by AS created_by
        FROM
            tenant_item_info WHERE 1=1 AND deleted_at IS NULL
        """;

    private static final String TENANT_ITEM_INFO_COUNT_SQL = """
        SELECT COUNT(*)
        FROM tenant_item_info WHERE 1=1 AND deleted_at IS NULL
        """;

    private static final String TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_SQL = """
        SELECT
                aggregation_level,
                COUNT(DISTINCT sku_number) AS sku_count,
                COALESCE(SUM(sales_qty), 0) AS sales_qty
            FROM
                tenant_item_info
            WHERE deleted_at IS NULL
        """;

    private static final String TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_GROUP_BY_SQL = """
        GROUP BY
            aggregation_level
        """;

    @Override
    public List<TenantItemInfoDto> tenantItemInfoPageQuery(TenantItemInfoQuery request) {
        log.info("[CustomizedTenantItemInfoRepository] param request: {}.", request);

        DynamicSearchCondition<TenantItemInfoQuery> dynamicSearch = new TenantItemInfoDynamicSearch();
        StringBuilder sql = new StringBuilder(TENANT_ITEM_INFO_SQL);
        sql.append(dynamicSearch.generateConditionBlock(request));
        sql.append(this.buildSortSql(request.getSort()));
        sql.append(request.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(), ps -> dynamicSearch.bindSqlParameter(ps, request),
            (rs, rowNumber) -> TenantItemInfoDto.builder()
                .skuNumber(rs.getString("sku_number"))
                .upc(rs.getString("upc"))
                .name(rs.getString("name"))
                .photoUrl(rs.getString("photo"))
                .salesQty(rs.getLong("sales_qty"))
                .aggregationLevel(rs.getString("aggregation_level"))
                .packageSize(rs.getInt("package_size"))
                .createdBy(rs.getString("created_by"))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .build());
    }

    @Override
    public long count(TenantItemInfoQuery request) {
        log.info("[CustomizedTenantItemInfoRepository] COUNT param request: {}.", request);
        DynamicSearchCondition<TenantItemInfoQuery> dynamicSearch = new TenantItemInfoDynamicSearch();

        StringBuilder sql = new StringBuilder();
        sql.append(TENANT_ITEM_INFO_COUNT_SQL);
        sql.append(dynamicSearch.generateConditionBlock(request));

        List<Long> count = jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, request),
            (rs, rowNum) -> rs.getLong(1));

        return count.getFirst();
    }

    @Override
    public List<TenantItemSalesInfoDto> tenantAggregationLevelItemSalesInfo(String loginUserTenantId) {
        log.info("[CustomizedTenantItemInfoRepository] tenantAggregationLevelItemSalesInfo param request: {}.", loginUserTenantId);

        StringBuilder sql = new StringBuilder(TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_SQL);
        sql.append(buildCategoryIdInCondition(loginUserTenantId));
        sql.append(TENANT_AGGREGATION_LEVEL_ITEM_SALES_INFO_GROUP_BY_SQL);

        return jdbcTemplate.query(sql.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                if (StringUtils.isNotBlank(loginUserTenantId)) {
                    ps.setString(index, loginUserTenantId);
                }
            }
        }, new RowMapper<TenantItemSalesInfoDto>() {
            @Override
            public TenantItemSalesInfoDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                TenantItemSalesInfoDto tenantItemSalesInfoDto = new TenantItemSalesInfoDto();
                tenantItemSalesInfoDto.setItemCount(rs.getLong("sku_count"));
                tenantItemSalesInfoDto.setSalesQty(rs.getLong("sales_qty"));
                tenantItemSalesInfoDto.setAggregationLevel(rs.getString("aggregation_level"));
                return tenantItemSalesInfoDto;
            }
        });
    }

    static class TenantItemInfoDynamicSearch extends DynamicSearchCondition<TenantItemInfoQuery> {

        public TenantItemInfoDynamicSearch() {
            super(List.of(new TenantIdCondition(), new SkuNumberCondition(), new AggregationLevelCondition()));
        }
    }

    private static class TenantIdCondition implements SearchConditionResolver<TenantItemInfoQuery> {

        @Override
        public String generateConditionBlock(TenantItemInfoQuery query) {
            if (StringUtils.isBlank(query.getTenantId())) {
                return StringUtils.EMPTY;
            }
            return " AND tenant_id = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, TenantItemInfoQuery query, int index) throws SQLException {
            if (StringUtils.isNotBlank(query.getTenantId())) {
                ps.setString(index++, query.getTenantId());
            }
            return index;
        }
    }

    private static class SkuNumberCondition implements SearchConditionResolver<TenantItemInfoQuery> {

        @Override
        public String generateConditionBlock(TenantItemInfoQuery query) {
            if (StringUtils.isBlank(query.getSkuNumber())) {
                return StringUtils.EMPTY;
            }
            return " AND sku_number = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, TenantItemInfoQuery query, int index) throws SQLException {
            if (StringUtils.isNotBlank(query.getSkuNumber())) {
                ps.setString(index++, query.getSkuNumber());
            }
            return index;
        }
    }

    private static class AggregationLevelCondition implements SearchConditionResolver<TenantItemInfoQuery> {

        @Override
        public String generateConditionBlock(TenantItemInfoQuery query) {
            if (StringUtils.isBlank(query.getAggregationLevel())) {
                return StringUtils.EMPTY;
            }
            return " AND aggregation_level = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, TenantItemInfoQuery query, int index) throws SQLException {
            if (StringUtils.isNotBlank(query.getAggregationLevel())) {
                ps.setString(index++, query.getAggregationLevel());
            }
            return index;
        }
    }


    private String buildSortSql(SortType sortType) {
        if (sortType == null) {
            return " ORDER BY sales_qty DESC ";
        }

        return switch (sortType) {
            case SALES_QTY_DESC -> " ORDER BY sales_qty DESC ";
            case SALES_QTY_ASC -> " ORDER BY sales_qty ASC ";
        };
    }

    private String buildCategoryIdInCondition(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return StringUtils.EMPTY;
        }
        return " AND tenant_id = ? ";
    }
}