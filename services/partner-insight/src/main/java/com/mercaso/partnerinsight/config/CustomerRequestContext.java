package com.mercaso.partnerinsight.config;

import java.util.Map;

public class CustomerRequestContext {

    private CustomerRequestContext() {
    }

    private static final ThreadLocal<Map<String, String> > headers = new ThreadLocal<>();

    public static Map<String, String>  getHeaders() {
        return headers.get();
    }

    public static void setHeaders( Map<String, String> headers) {
        CustomerRequestContext.headers.set(headers);
    }

    public static void clear() {
        headers.remove();
    }

}
