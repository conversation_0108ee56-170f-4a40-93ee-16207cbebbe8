package com.mercaso.partnerinsight.query.service.impl;

import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.query.repository.CustomizedTenantItemInfoRepository;
import com.mercaso.partnerinsight.query.service.TenantItemInfoSearchService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class TenantItemInfoSearchServiceImpl implements TenantItemInfoSearchService {

    private final CustomizedTenantItemInfoRepository customizedTenantItemInfoRepository;

    @Override
    public PageInfoDto<TenantItemInfoDto> searchTenantItemInfoList(TenantItemInfoQuery query) {
        log.info("[searchTenantItemInfoList] param tenantItemInfoQuery: {}.", query);
        PageInfoDto<TenantItemInfoDto> pageInfoDTO = new PageInfoDto<>();

        if (StringUtils.isBlank(query.getTenantId())) {
            log.warn("[searchTenantItemInfoList] tenantId is null");
            return pageInfoDTO;
        }

        pageInfoDTO.setData(customizedTenantItemInfoRepository.tenantItemInfoPageQuery(query));
        pageInfoDTO.setTotalCount(customizedTenantItemInfoRepository.count(query));

        return pageInfoDTO;
    }

    @Override
    public List<TenantItemSalesInfoDto> searchTenantAggregationLevelItemSalesInfo(String loginUserTenantId) {

        if (StringUtils.isBlank(loginUserTenantId)) {
            log.warn("[searchTenantAggregationLevelItemSalesInfo] tenantId is null");
            return List.of();
        }

        List<TenantItemSalesInfoDto> tenantItemSalesInfoDtos = customizedTenantItemInfoRepository.tenantAggregationLevelItemSalesInfo(
            loginUserTenantId);

        log.info("[searchTenantAggregationLevelItemSalesInfo] tenantItemSalesInfoDtos: {}.", tenantItemSalesInfoDtos);

        return tenantItemSalesInfoDtos;
    }
}