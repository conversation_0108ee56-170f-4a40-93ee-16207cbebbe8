package com.mercaso.partnerinsight.dto;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;

@Data
@NoArgsConstructor
public class PageInfoDto<T> {
    long totalCount;
    List<T> data = Lists.newArrayList();

    public PageInfoDto(long totalCount, List<T> data) {
        this.totalCount = totalCount;
        this.data = data;
    }
}