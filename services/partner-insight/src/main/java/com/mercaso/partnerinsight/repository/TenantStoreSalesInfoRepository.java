package com.mercaso.partnerinsight.repository;


import com.mercaso.partnerinsight.entity.TenantStoreSalesInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface TenantStoreSalesInfoRepository extends JpaRepository<TenantStoreSalesInfo, UUID> {

    List<TenantStoreSalesInfo> findAllByTenantIdAndStoreIdOrderBySalesMonthAsc(String tenantId, String storeId);
}