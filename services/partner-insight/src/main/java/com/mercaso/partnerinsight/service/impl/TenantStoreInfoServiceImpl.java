package com.mercaso.partnerinsight.service.impl;

import com.mercaso.partnerinsight.dto.TenantStoreInfoDto;
import com.mercaso.partnerinsight.mapper.TenantStoreInfoDtoMapper;
import com.mercaso.partnerinsight.repository.TenantStoreInfoRepository;
import com.mercaso.partnerinsight.service.TenantStoreInfoService;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class TenantStoreInfoServiceImpl implements TenantStoreInfoService {

    private final TenantStoreInfoRepository tenantStoreInfoRepository;
    private final TenantStoreInfoDtoMapper tenantStoreInfoDtoMapper;

    @Override
    public List<TenantStoreInfoDto> findAllByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("[TenantStoreInfoServiceImpl] tenantId is null, return empty list");
            return List.of();
        }
        return Optional.of(tenantStoreInfoRepository.findAllByTenantIdOrderBySalesQtyDescItemCountDesc(tenantId))
            .orElse(Collections.emptyList())
            .stream()
            .map(tenantStoreInfoDtoMapper::entityToDto)
            .toList();
    }
}