package com.mercaso.partnerinsight.resource;


import com.mercaso.partnerinsight.config.SecurityContextUtilWrapper;
import com.mercaso.partnerinsight.dto.TenantStoreSalesInfoDto;
import com.mercaso.partnerinsight.service.TenantStoreSalesInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/tenant-store-sales-info",
    produces = {MediaType.APPLICATION_JSON_VALUE}
)
@Tag(name = "Tenant Store Sales Info Controller", description = "Controller For Tenant Store Sales Info")
public class TenantStoreSalesInfoRestApi {

    private final TenantStoreSalesInfoService tenantStoreSalesInfoService;

    private final SecurityContextUtilWrapper securityContextUtilWrapper;

    @Operation(summary = "Get all tenant store info", description = "Get all tenant store info")
    @PreAuthorize("hasAuthority('pi:read:stores')")
    @GetMapping("/{storeId}")
    public List<TenantStoreSalesInfoDto> findStoreSalesInfoByTenantIdAndStoreId(@PathVariable("storeId") String storeId) {
        String loginUserTenantId = securityContextUtilWrapper.getLoginUserTenantId();
        log.info("findAllByTenantId tenantId: {}", loginUserTenantId);
        return tenantStoreSalesInfoService.findAllByTenantIdAndStoreId(loginUserTenantId, storeId);
    }

}