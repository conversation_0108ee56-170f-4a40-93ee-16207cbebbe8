package com.mercaso.partnerinsight.resource;


import com.mercaso.partnerinsight.config.SecurityContextUtilWrapper;
import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery.SortType;
import com.mercaso.partnerinsight.query.service.TenantItemInfoSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/v1/tenant-item-info",
    produces = {MediaType.APPLICATION_JSON_VALUE}
)
@Tag(name = "Tenant Item Info Controller", description = "Controller For Tenant Item Info")
public class TenantItemInfoRestApi {

    private final TenantItemInfoSearchService tenantItemInfoSearchService;

    private final SecurityContextUtilWrapper securityContextUtilWrapper;

    @Operation(summary = "Get tenant item info page", description = "Get tenant item info page")
    @PreAuthorize("hasAuthority('pi:read:items')")
    @GetMapping
    public PageInfoDto<TenantItemInfoDto> searchTenantItemInfoList(
        @RequestParam(value = "page", defaultValue = "1") @Min(value = 1, message = "Page number must be greater than 0") int page,
        @RequestParam(value = "pageSize", defaultValue = "20") @Min(value = 1, message = "Page size must be greater than 0") int pageSize,
        @RequestParam(value = "sort", required = false) SortType sort,
        @RequestParam(name = "skuNumber", required = false) String skuNumber,
        @RequestParam(name = "aggregationLevel", defaultValue = "TOTAL") String aggregationLevel) {
        String loginUserTenantId = securityContextUtilWrapper.getLoginUserTenantId();
        log.info("searchTenantItemInfoList skuNumber: {}, aggregationLevel:{}, loginUserTenantId: {}",
            skuNumber,
            aggregationLevel,
            loginUserTenantId);

        TenantItemInfoQuery tenantItemInfoQuery = TenantItemInfoQuery.builder()
            .tenantId(loginUserTenantId)
            .skuNumber(skuNumber)
            .aggregationLevel(aggregationLevel)
            .page(page)
            .pageSize(pageSize)
            .sort(sort)
            .build();

        return tenantItemInfoSearchService.searchTenantItemInfoList(tenantItemInfoQuery);
    }


    @Operation(summary = "Get tenant item sales info page", description = "Get tenant item sales info page")
    @PreAuthorize("hasAuthority('pi:read:items')")
    @GetMapping("aggregation-level/sales-info")
    public List<TenantItemSalesInfoDto> searchTenantAggregationLevelItemSalesInfo() {
        String loginUserTenantId = securityContextUtilWrapper.getLoginUserTenantId();
        log.info("searchTenantAggregationLevelItemSalesInfo loginUserTenantId: {}", loginUserTenantId);

        return tenantItemInfoSearchService.searchTenantAggregationLevelItemSalesInfo(loginUserTenantId);
    }

}