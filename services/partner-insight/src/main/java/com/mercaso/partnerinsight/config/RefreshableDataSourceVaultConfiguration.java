package com.mercaso.partnerinsight.config;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.vault.core.lease.SecretLeaseContainer;
import org.springframework.vault.core.lease.domain.RequestedSecret;
import org.springframework.vault.core.lease.event.SecretLeaseCreatedEvent;
import org.springframework.vault.core.lease.event.SecretLeaseExpiredEvent;

@Configuration
@Slf4j
@ConditionalOnProperty(name = "spring.cloud.vault.enabled", havingValue = "true")
public class RefreshableDataSourceVaultConfiguration {

    private final HikariDataSource dataSource;

    RefreshableDataSourceVaultConfiguration(
            @Value("${spring.cloud.vault.database.role}")
            String databaseRole,
            @Value("${spring.cloud.vault.database.backend}")
            String databaseBackend,
            SecretLeaseContainer leaseContainer,
            HikariDataSource dataSource) {
        this.dataSource = dataSource;

        String vaultCredsPath = String.format("%s/creds/%s", databaseBackend, databaseRole);

        leaseContainer.addLeaseListener(event -> {
            if (vaultCredsPath.equals(event.getSource().getPath())) {
                if (event instanceof SecretLeaseExpiredEvent &&
                        event.getSource().getMode() == RequestedSecret.Mode.RENEW) {

                    log.info("expire lease, rotate database credentials");
                    leaseContainer.requestRotatingSecret(vaultCredsPath);

                } else if (event instanceof SecretLeaseCreatedEvent secretLeaseCreatedEvent
                        && event.getSource().getMode() == RequestedSecret.Mode.ROTATE) {

                    log.info("create lease, rotate database credentials");

                    String username = (String) secretLeaseCreatedEvent.getSecrets()
                            .get("username");
                    String password = (String) secretLeaseCreatedEvent.getSecrets()
                            .get("password");

                    log.info("update db");
                    updateDataSource(username, password);
                }
            }
        });
    }

    private void updateDataSource(String username, String password) {
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.getHikariConfigMXBean().setUsername(username);
        dataSource.getHikariConfigMXBean().setPassword(password);
        HikariPoolMXBean hikariPoolMXBean = dataSource.getHikariPoolMXBean();
        if (hikariPoolMXBean != null) {
            log.info("Soft evicting database connections to refresh credentials.");
            hikariPoolMXBean.softEvictConnections();
        }
    }
}