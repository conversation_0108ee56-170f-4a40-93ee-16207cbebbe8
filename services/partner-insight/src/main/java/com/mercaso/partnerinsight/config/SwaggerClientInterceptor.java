package com.mercaso.partnerinsight.config;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@RequiredArgsConstructor
@Slf4j
public class SwaggerClientInterceptor implements ClientHttpRequestInterceptor {

    private static final String[] HEADERS_TO_COPY = {
        HttpHeaders.CONTENT_TYPE,
        HttpHeaders.AUTHORIZATION
    };


    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] bytes, ClientHttpRequestExecution execution)
        throws IOException {

        getHeaders().ifPresent(headers -> copyHeaders(headers, request.getHeaders()));

        return execution.execute(request, bytes);
    }

    private Optional<Map<String, String>> getHeaders() {
        return Optional.ofNullable(CustomerRequestContext.getHeaders())
            .or(this::getHeadersFromServletRequest);
    }

    private Optional<Map<String, String>> getHeadersFromServletRequest() {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
            .filter(ServletRequestAttributes.class::isInstance)
            .map(ServletRequestAttributes.class::cast)
            .map(ServletRequestAttributes::getRequest)
            .map(this::extractHeaders);
    }

    private Map<String, String> extractHeaders(HttpServletRequest request) {
        return Map.of(
            HttpHeaders.CONTENT_TYPE.toLowerCase(), Optional.ofNullable(request.getContentType()).orElse(""),
            HttpHeaders.AUTHORIZATION.toLowerCase(), Optional.ofNullable(request.getHeader(HttpHeaders.AUTHORIZATION)).orElse("")
        );
    }

    private void copyHeaders(Map<String, String> source, HttpHeaders target) {
        for (String header : HEADERS_TO_COPY) {
            String value = source.get(header.toLowerCase());
            if (StringUtils.isNotBlank(value) && !target.containsKey(header)) {
                target.add(header, value);
            }
        }
    }
}
