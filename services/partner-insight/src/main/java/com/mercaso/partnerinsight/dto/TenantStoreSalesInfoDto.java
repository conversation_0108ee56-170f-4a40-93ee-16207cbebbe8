package com.mercaso.partnerinsight.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TenantStoreSalesInfoDto extends BaseDto {

    private UUID id;

    private String storeId;

    private Long salesQty;

    private Instant salesMonth;

    private String tenantId;
}