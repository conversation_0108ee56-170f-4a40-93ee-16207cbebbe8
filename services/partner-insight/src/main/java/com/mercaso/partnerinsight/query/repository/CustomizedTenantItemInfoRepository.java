package com.mercaso.partnerinsight.query.repository;

import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomizedTenantItemInfoRepository {
    List<TenantItemInfoDto> tenantItemInfoPageQuery(TenantItemInfoQuery request);

    long count(TenantItemInfoQuery request);

    List<TenantItemSalesInfoDto> tenantAggregationLevelItemSalesInfo(String loginUserTenantId);
}