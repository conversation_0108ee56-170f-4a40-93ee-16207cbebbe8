package com.mercaso.partnerinsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Arrays;

public enum AggregationLevelEnums {
    LAST_MONTH,
    LAST_QUARTER,
    TOTAL,
    UNKNOWN,
    ;

    @JsonCreator
    public static AggregationLevelEnums fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }
}
