package com.mercaso.partnerinsight.query.service;

import com.mercaso.partnerinsight.dto.PageInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemInfoDto;
import com.mercaso.partnerinsight.dto.TenantItemSalesInfoDto;
import com.mercaso.partnerinsight.dto.quary.TenantItemInfoQuery;
import java.util.List;

public interface TenantItemInfoSearchService {

    PageInfoDto<TenantItemInfoDto> searchTenantItemInfoList(TenantItemInfoQuery query);

    List<TenantItemSalesInfoDto> searchTenantAggregationLevelItemSalesInfo(String loginUserTenantId);

}