package com.mercaso.partnerinsight.entity;

import com.mercaso.partnerinsight.enums.StoreSourceEnums;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "tenant_store_info")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update tenant_store_info set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class TenantStoreInfo extends BaseEntity {

    @Column(name = "store_id")
    private String storeId;

    @Column(name = "name")
    private String name;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "sales_qty")
    private Long salesQty;

    @Column(name = "address")
    private String address;

    @Column(name = "source")
    @Enumerated(EnumType.STRING)
    private StoreSourceEnums source;

    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "item_count")
    private Long itemCount;

}