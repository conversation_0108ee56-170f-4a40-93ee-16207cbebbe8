package com.mercaso.partnerinsight.dto;

import com.mercaso.partnerinsight.enums.StoreSourceEnums;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TenantStoreInfoDto extends BaseDto {

    private UUID id;

    private String storeId;

    private String name;

    private Double latitude;

    private Double longitude;

    private Long salesQty;

    private Long itemCount;

    private String address;

    private StoreSourceEnums source;

    private String tenantId;
}