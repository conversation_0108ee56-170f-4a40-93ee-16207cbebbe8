package com.mercaso.partnerinsight.config;

import com.mercaso.security.auth0.models.UserPrincipal;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class SecurityContextUtilWrapper {

    public static UserPrincipal getUserPrincipal() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null || authentication.getPrincipal() instanceof String) {
            return null;

        }
        return SecurityContextUtil.getUserPrincipal();
    }

    public String getLoginUserTenantId() {
        UserPrincipal user = getUserPrincipal();
        return user != null ? user.getTenantId() : null;
    }
}
