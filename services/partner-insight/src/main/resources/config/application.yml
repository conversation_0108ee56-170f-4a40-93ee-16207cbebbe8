server:
  port: 8080
  forward-headers-strategy: framework
spring:
  async:
    core_pool_size: 5
    max_pool_size: 10
    queue_capacity: 200
    alive_time: 60
  profiles:
    active: local
  application:
    name: partner-insight
  datasource:
    url: jdbc:postgresql://${host.db_server}/partner_insight
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-init-sql: "set role partner_insight_user"
      connection-timeout: 15000
      minimum-idle: 1
      maximum-pool-size: 20
      allow-pool-suspension: true
  cloud:
    vault:
      enabled: true
      scheme: http
      port: 8200
      host: vault.vault.svc.cluster.local
      authentication: KUBERNETES
      kubernetes:
        role: ${spring.application.name}
        kubernetes-path: kubernetes
        service-account-token-file: /var/run/secrets/kubernetes.io/serviceaccount/token
      # Application will fail if it cannot connect to vault, remember to disable vault for envs that don't need it
      fail-fast: true

      # Need to disable generic engine so that spring cloud knows to only pull secrets from KV engine
      generic:
        enabled: false
      kv:
        enabled: true
        backend: secret
        profile-separator: '/'
        application-name: ${spring.application.name}
      database:
        enabled: true
        role: ${spring.application.name}
        backend: database
        username-property: spring.datasource.username
        password-property: spring.datasource.password
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate.dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    enabled: true
    locations: classpath:/db/migration
    baseline-on-migrate: true
    init-sqls: SET ROLE partner_insight_user
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        readiness:
          include: readinessState
    shutdown:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: true
    vault:
      enabled: false
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  server:
    port: 8081
springdoc:
  swagger-ui:
    enabled: false

logging:
  level:
    org.springframework.web.filter.CommonsRequestLoggingFilter: DEBUG
