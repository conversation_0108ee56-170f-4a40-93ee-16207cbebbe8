CREATE TABLE IF NOT EXISTS tenant_item_info
(
    id                UUID         NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    sku_number        VARCHAR(100),
    upc               VARCHAR(255),
    name              VARCHAR(255),
    photo             TEXT,
    package_size      INT,
    sales_qty         BIGINT,
    aggregation_level VARCHAR(50),
    tenant_id         VARCHAR      NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        VA<PERSON>HAR(50)  NOT NULL,
    updated_at        TIMESTAMP             DEFAULT NULL,
    updated_by        VARCHAR(50)           DEFAULT NULL,
    deleted_at        TIMESTAMP             DEFAULT NULL,
    deleted_by        VARCHAR(50)           DEFAULT NULL
);

COMMENT ON TABLE tenant_item_info IS 'Item information';

COMMENT ON COLUMN tenant_item_info.id IS 'Unique Identifier';
COMMENT ON COLUMN tenant_item_info.upc IS 'SKU UPC';
COMMENT ON COLUMN tenant_item_info.name IS 'Item name';
COMMENT ON COLUMN tenant_item_info.photo IS 'Item photo';
COMMENT ON COLUMN tenant_item_info.package_size IS 'Package size';
COMMENT ON COLUMN tenant_item_info.sales_qty IS 'Item quantity';
COMMENT ON COLUMN tenant_item_info.aggregation_level IS 'Aggregation level(30, 90)';
COMMENT ON COLUMN tenant_item_info.tenant_id IS 'Tenant ID';
COMMENT ON COLUMN tenant_item_info.created_at IS 'Creation time';
COMMENT ON COLUMN tenant_item_info.created_by IS 'Creator';
COMMENT ON COLUMN tenant_item_info.updated_at IS 'Update time';
COMMENT ON COLUMN tenant_item_info.updated_by IS 'Updater';
COMMENT ON COLUMN tenant_item_info.deleted_at IS 'Deletion time';
COMMENT ON COLUMN tenant_item_info.deleted_by IS 'Deleter';

CREATE INDEX idx_tenant_item_tenant_id_sku_number
    ON tenant_item_info (tenant_id, upc);