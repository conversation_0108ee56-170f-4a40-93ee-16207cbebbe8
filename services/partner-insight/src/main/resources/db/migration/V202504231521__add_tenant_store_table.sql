CREATE TABLE IF NOT EXISTS tenant_store_info
(
    id         UUID        NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    store_id   VARCHAR(50),
    name       VARCHAR(255),
    latitude   DECIMAL(9, 6),
    longitude  DECIMAL(9, 6),
    sales_qty  BIGINT               DEFAULT 0,
    address    VARCHAR(255),
    source     VARCHAR(255),
    tenant_id  VARCHAR(50) NOT NULL,
    created_at TIMESTAMP   NOT NULL,
    created_by VARCHAR(50) NOT NULL,
    updated_at TIMESTAMP            DEFAULT NULL,
    updated_by VARCHAR(50)          DEFAULT NULL,
    deleted_at TIMESTAMP            DEFAULT NULL,
    deleted_by VARCHAR(50)          DEFAULT NULL
);

COMMENT ON TABLE tenant_store_info IS 'Store geographical location information';

COMMENT ON COLUMN tenant_store_info.id IS 'Unique Identifier';
COMMENT ON COLUMN tenant_store_info.store_id IS 'Store ID';
COMMENT ON COLUMN tenant_store_info.name IS 'Store Name';
COMMENT ON COLUMN tenant_store_info.latitude IS 'Latitude';
COMMENT ON COLUMN tenant_store_info.longitude IS 'Longitude';
COMMENT ON COLUMN tenant_store_info.sales_qty IS 'Sales quantity';
COMMENT ON COLUMN tenant_store_info.address IS 'Store address';
COMMENT ON COLUMN tenant_store_info.source IS 'Source of the data(mercaso,ngr)';
COMMENT ON COLUMN tenant_store_info.tenant_id IS 'Tenant ID';
COMMENT ON COLUMN tenant_store_info.created_at IS 'Creation time';
COMMENT ON COLUMN tenant_store_info.created_by IS 'Creator';
COMMENT ON COLUMN tenant_store_info.updated_at IS 'Update time';
COMMENT ON COLUMN tenant_store_info.updated_by IS 'Updater';
COMMENT ON COLUMN tenant_store_info.deleted_at IS 'Deletion time';
COMMENT ON COLUMN tenant_store_info.deleted_by IS 'Deleter';

CREATE INDEX idx_store_geo_location_tenant_store
    ON tenant_store_info (tenant_id, store_id);