CREATE TABLE IF NOT EXISTS tenant_store_sales_info
(
    id         UUID        NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    store_id   VARCHAR(50),
    sales_qty  BIGINT               DEFAULT 0,
    sales_month TIMESTAMP   NOT NULL,
    tenant_id  VARCHAR(50) NOT NULL,
    created_at TIMESTAMP   NOT NULL,
    created_by VA<PERSON>HAR(50) NOT NULL,
    updated_at TIMESTAMP            DEFAULT NULL,
    updated_by VARCHAR(50)          DEFAULT NULL,
    deleted_at TIMESTAMP            DEFAULT NULL,
    deleted_by VARCHAR(50)          DEFAULT NULL
    );

COMMENT ON TABLE tenant_store_sales_info IS 'Store sales information';

COMMENT ON COLUMN tenant_store_sales_info.id IS 'Unique Identifier';
COMMENT ON COLUMN tenant_store_sales_info.store_id IS 'Store ID';
COMMENT ON COLUMN tenant_store_sales_info.sales_qty IS 'Sales quantity';
COMMENT ON COLUMN tenant_store_sales_info.sales_month IS 'Sales quantity statistical month';
COMMENT ON COLUMN tenant_store_sales_info.tenant_id IS 'Tenant ID';
COMMENT ON COLUMN tenant_store_sales_info.created_at IS 'Creation time';
COMMENT ON COLUMN tenant_store_sales_info.created_by IS 'Creator';
COMMENT ON COLUMN tenant_store_sales_info.updated_at IS 'Update time';
COMMENT ON COLUMN tenant_store_sales_info.updated_by IS 'Updater';
COMMENT ON COLUMN tenant_store_sales_info.deleted_at IS 'Deletion time';
COMMENT ON COLUMN tenant_store_sales_info.deleted_by IS 'Deleter';

CREATE INDEX idx_store_sales_info_tenant_store
    ON tenant_store_sales_info (tenant_id, store_id);