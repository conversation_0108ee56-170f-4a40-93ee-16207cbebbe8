plugins {
    id "org.sonarqube"
    id "jacoco"
}

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml"
        def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.branch.name", branchName
        property "sonar.coverage.exclusions", "**/command/**,**/dto/**,**/entity/**,**/mapper/**,**/enums/**,**/adaptor/**,**/config/**,**/exception/**,**/infrastructure/client/**,**/*Config.java,**/*Data.java,**/*Dto.java,**/*Do.java,**/*Command.java,**/*Event.java,**/*Query.java, **/*Utils.java,**/Application.java"
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
        html.required = true
    }
    executionData = files(
            layout.buildDirectory.file("jacoco/test.exec"),
            layout.buildDirectory.file("jacoco/integrationTest.exec")
    )
}

test {
    finalizedBy jacocoTestReport
}

dependencies {
    implementation 'com.google.guava:guava:32.0.1-jre'
    implementation 'org.apache.skywalking:apm-toolkit-meter:9.2.0'
}

springBoot {
    mainClass = 'com.mercaso.partnerinsight.Application'
}

integrationTest {
    systemProperty 'spring.profiles.active', 'integration'
}