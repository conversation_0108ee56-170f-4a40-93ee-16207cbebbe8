package com.mercaso.template.test;

import com.mercaso.template.AbstractIT;
import org.junit.jupiter.api.Test;

public class Concurrent3IT extends AbstractIT {

    @Test
    void test1() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(Thread.currentThread().getName() + "=> Concurrent3_IT1");
    }

    @Test
    void test2() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(Thread.currentThread().getName() + "=> Concurrent3_IT2");
    }

    @Test
    void test3() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(Thread.currentThread().getName() + "=> Concurrent3_IT3");

    }

}
