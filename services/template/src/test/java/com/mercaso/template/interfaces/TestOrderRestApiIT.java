package com.mercaso.template.interfaces;

import com.mercaso.template.AbstractIT;
import com.mercaso.template.application.command.CreateTestOrderCommand;
import com.mercaso.template.application.dto.TestOrderDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


class TestOrderRestApiIT extends AbstractIT {

    @Test
    public void shouldSuccessWhenCreateNewTestOrder() throws Exception {
        String name = "RestApiTestName";
        int status = 11;
        CreateTestOrderCommand command = new CreateTestOrderCommand(name, status);
        TestOrderDto result = testOrderRestApiUtil.createTestOrder(command);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
        Assertions.assertEquals(status, result.getStatus());
    }


}