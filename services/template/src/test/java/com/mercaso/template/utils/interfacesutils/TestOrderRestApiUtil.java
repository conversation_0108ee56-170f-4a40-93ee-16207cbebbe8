package com.mercaso.template.utils.interfacesutils;

import com.mercaso.template.application.command.CreateTestOrderCommand;
import com.mercaso.template.application.dto.TestOrderDto;
import com.mercaso.template.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class TestOrderRestApiUtil extends IntegrationTestRestUtil {
    private static final String CREATE_TEST_ORDER_URL = "/public/v1/test-orders";


    public TestOrderRestApiUtil(Environment environment) {
        super(environment);
    }
    
    public TestOrderDto createTestOrder(CreateTestOrderCommand createTestOrderCommand) throws Exception {
        return createEntity(CREATE_TEST_ORDER_URL, createTestOrderCommand, TestOrderDto.class);
    }


}
