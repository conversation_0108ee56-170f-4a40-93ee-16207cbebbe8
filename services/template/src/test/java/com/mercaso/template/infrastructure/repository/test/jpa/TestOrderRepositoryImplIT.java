package com.mercaso.template.infrastructure.repository.test.jpa;

import com.mercaso.template.AbstractIT;
import com.mercaso.template.application.command.CreateTestOrderCommand;
import com.mercaso.template.domain.testorder.TestOrder;
import com.mercaso.template.domain.testorder.TestOrderFactory;
import com.mercaso.template.domain.testorder.TestOrderRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class TestOrderRepositoryImplIT extends AbstractIT {

    @Autowired
    private TestOrderRepository testOrderRepository;


    @Test
    void shouldSuccessWhenSaveTestOrder() {
        String name = "testName";
        int status = 23;
        CreateTestOrderCommand command = new CreateTestOrderCommand(name, status);
        TestOrder testOrder = TestOrderFactory.create(command);
        TestOrder result = testOrderRepository.save(testOrder);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(name, result.getName());
        Assertions.assertEquals(status, result.getStatus());

    }
}