package com.mercaso.template.test;

import org.junit.jupiter.api.Test;

public class ConcurrentUnitTest {

    @Test
    public void test1() {
        System.out.println(Thread.currentThread().getName() + "=> ConcurrentUnitTest1");
    }


    @Test
    public void test2() {
        System.out.println(Thread.currentThread().getName() + "=> ConcurrentUnitTest2");
    }

    @Test
    public void test3() {
        System.out.println(Thread.currentThread().getName() + "=> ConcurrentUnitTest3");
    }

    @Test
    public void test4() {
        System.out.println(Thread.currentThread().getName() + "=> ConcurrentUnitTest4");
    }

}