import com.mercaso.template.application.command.CreateTestOrderCommand
import com.mercaso.template.application.dto.TestOrderDto
import com.mercaso.template.application.mapper.testorder.TestOrderDtoApplicationMapper
import com.mercaso.template.application.service.TestOrderApplicationService
import com.mercaso.template.application.service.impl.TestOrderOrderApplicationServiceImpl
import com.mercaso.template.domain.businessevent.service.BusinessEventService
import com.mercaso.template.domain.testorder.TestOrder
import com.mercaso.template.domain.testorder.TestOrderFactory
import com.mercaso.template.domain.testorder.TestOrderRepository
import spock.lang.Specification

class TestOrderApplicationServiceTest extends Specification {

    BusinessEventService businessEventService = Mock()
    TestOrderRepository testOrderRepository = Mock()
    TestOrderDtoApplicationMapper testOrderDtoApplicationMapper = Mock()
    TestOrderFactory testOrderFactory = new TestOrderFactory();
    TestOrderApplicationService subject = new TestOrderOrderApplicationServiceImpl(businessEventService, testOrderRepository, testOrderDtoApplicationMapper)

    def "test create TestOrder"() {
        given: "create an test order with CreateTestOrderCommand"
        def command = new CreateTestOrderCommand("GroovyTestName", 22);
        TestOrder savedOrder = testOrderFactory.create(command);
        TestOrderDto testOrderDto = new TestOrderDto(UUID.randomUUID(), "GroovyTestName", 22);
        testOrderRepository.save(_) >> savedOrder
        testOrderDtoApplicationMapper.domainToDto(_) >> testOrderDto

        when: "the method is called"
        def result = subject.create(command)

        then: "the result should be expected"
        1 * testOrderRepository.save(_)
        1 * businessEventService.dispatch(_)
    }
}