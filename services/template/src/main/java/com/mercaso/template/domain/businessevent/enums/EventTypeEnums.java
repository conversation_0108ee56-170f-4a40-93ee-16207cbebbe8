package com.mercaso.template.domain.businessevent.enums;

import com.mercaso.template.application.dto.BaseDto;
import com.mercaso.template.application.dto.event.TestOrderCreatedApplicationEvent;
import com.mercaso.template.application.dto.payload.TestOrderCreatedPayloadDto;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.template.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnums {

    TEST_ORDER_CREATED(TestOrderCreatedPayloadDto.class, TestOrderCreatedApplicationEvent.class),
    ;

    private final Class<? extends BusinessEventPayloadDto<? extends BaseDto>> payloadClass;

    private final Class<? extends BaseApplicationEvent<? extends BusinessEventPayloadDto<? extends BaseDto>>> eventClass;

    public static EventTypeEnums forPayload(Class<?> payloadClass) {
        for (EventTypeEnums eventType : values()) {
            if (eventType.getPayloadClass() == payloadClass) {
                return eventType;
            }
        }
        return null;
    }

}
