package com.mercaso.template.infrastructure.repository.test.jpa;

import com.mercaso.template.application.query.TestOrderQuery;
import com.mercaso.template.domain.testorder.TestOrder;
import com.mercaso.template.domain.testorder.TestOrderRepository;
import com.mercaso.template.infrastructure.repository.test.jpa.dataobject.TestOrderDo;
import com.mercaso.template.infrastructure.repository.test.jpa.mapper.TestOrderDoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class TestOrderRepositoryImpl implements TestOrderRepository {

    private final TestOrderDoMapper testOrderDoMapper;

    private final TestOrderJpaDao testOrderJpaDao;


    @Override
    public List<TestOrder> query(TestOrderQuery testOrderQuery) {
        return List.of();
    }

    @Override
    public TestOrder save(TestOrder domain) {
        TestOrderDo testOrderDo = testOrderDoMapper.domainToDo(domain);
        testOrderDo = testOrderJpaDao.save(testOrderDo);
        return testOrderDoMapper.doToDomain(testOrderDo);
    }

    @Override
    public TestOrder findById(UUID id) {
        TestOrderDo testOrderDo = testOrderJpaDao.findById(id).orElse(null);
        return testOrderDoMapper.doToDomain(testOrderDo);
    }

    @Override
    public TestOrder update(TestOrder domain) {
        return null;
    }

    @Override
    public TestOrder deleteById(UUID id) {
        return null;
    }
}
