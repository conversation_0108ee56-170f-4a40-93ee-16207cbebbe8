package com.mercaso.template.infrastructure.event.kafka;

import com.mercaso.template.domain.businessevent.enums.EventTypeEnums;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

public class EventTopics {

    public static final String TEST_ORDER = "test-order";
    private static final Map<EventTypeEnums, String> TOPICS = new EnumMap<>(EventTypeEnums.class);

    static {
        TOPICS.put(EventTypeEnums.TEST_ORDER_CREATED, TEST_ORDER);
    }

    private EventTopics() {

    }

    public static Optional<String> getTopic(EventTypeEnums type) {
        if (TOPICS.containsKey(type)) {
            return Optional.of(TOPICS.get(type));
        }

        return Optional.empty();
    }
}
