package com.mercaso.template.interfaces.listener;

import com.mercaso.template.application.dto.BusinessEventDto;
import com.mercaso.template.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TestOrderEventListener {


    @KafkaListener(topics = "test-order")
    public void handleTestOrderCreatedEvent(ConsumerRecord<String, String> record) {

        String message = record.value();
        log.debug("[{handleTestOrderCreatedEvent}] start to process TestOrder event, message: {} ", message);
        BusinessEventDto eventDTO = SerializationUtils.deserialize(message, BusinessEventDto.class);
        log.debug("[handleTestOrderCreatedEvent] error, message: {}", message);

    }
}