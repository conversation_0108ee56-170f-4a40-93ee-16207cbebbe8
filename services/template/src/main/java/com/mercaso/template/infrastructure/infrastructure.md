# Infrastructure 目录

在领域驱动设计（DDD）中，`infrastructure`目录主要负责实现应用程序所需的技术细节和支持服务。它处理与数据库、消息队列、外部服务等基础设施的交互，实现与领域模型的持久化和通信。

## 主要功能

1. **持久化**：实现仓储接口（Repositories），将领域对象持久化到数据库。
2. **外部服务集成**：与外部系统或第三方服务进行集成，包括调用外部API和处理外部事件。
3. **消息传递**：处理消息队列的发布和订阅，例如Kafka、RabbitMQ等。
4. **配置管理**：管理应用程序的配置，包括加载和解析配置文件。
5. **基础设施服务**：提供通用的基础设施服务，例如日志记录、邮件发送等。

## 需要添加的内容

### 1. 仓储实现（Repository Implementations）

实现领域层定义的仓储接口，负责将领域对象持久化到数据库。

### 2. 外部服务集成（External Service Integration）

与外部系统或第三方服务进行集成，通常通过API调用或SDK实现。

### 3. 事件传递（Event）

处理消息队列的发布和订阅，实现事件驱动架构。

### 4. 配置管理（Configuration Management）

加载和解析应用程序配置，通常使用Spring配置或其他配置管理框架。


### 5. 基础设施服务（Infrastructure Services）

提供通用的基础设施服务，例如日志记录、邮件发送等。

## 目录结构示例

```
src/
└── main/
    └── java/
        └── com/
            └── example/
                └── orderservice/
                    └── infrastructure/
                        ├── adaptor/
                        │   └── IMSAdaptor.java
                        ├── config/
                        │   └── AppConfig.java    
                        ├── event/
                        │   ├── applicationevent
                        │   └── kafka
                        ├── repository/
                        │   ├── JpaOrderRepository.java
                        │   └── SpringDataOrderRepository.java                    
                        └── logging/
                            └── LoggingService.java
```
