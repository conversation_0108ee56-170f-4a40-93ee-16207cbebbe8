package com.mercaso.template.application.queryservice.impl;

import com.mercaso.template.application.dto.TestOrderDto;
import com.mercaso.template.application.queryservice.TestQueryApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class TestQueryApplicationServiceImpl implements TestQueryApplicationService {

    @Override
    public List<TestOrderDto> findByIds(List<UUID> ids) {
        return new ArrayList<>();
    }

}
