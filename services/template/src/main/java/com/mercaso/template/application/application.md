# Application 目录

在领域驱动设计（DDD）中，`application`目录主要负责协调用户请求和领域层（domain
layer），确保业务逻辑在不同的应用场景下被正确执行。该层的主要职责是处理应用程序的任务流程、与外部系统的交互，并将这些请求传递给领域层以执行核心业务逻辑。

## 主要功能

1. **任务协调**：应用服务负责协调多个领域对象完成一个完整的业务操作。
2. **数据转换**：在接口层（interfaces layer）和领域层（domain layer）之间进行数据转换，通常使用DTO（数据传输对象）。
3. **事务管理**：管理业务操作的事务边界，确保操作的一致性和完整性。
4. **安全和权限检查**：在执行业务操作之前进行安全和权限检查。

## 需要添加的内容

### 1. Application Services

应用服务是`application`层的核心组件，用于定义系统中的业务操作。每个应用服务通常对应一个用例或业务流程。
1. 应用query服务： 负责处理query请求，主要返回为DTO的list
2. 应用search服务： 负责处理search请求，主要返回为分页的listDTO
3. 应用服务： 负责处理Command请求
### 2. Data Transfer Objects (DTOs)

DTO用于在接口层和应用层之间传递数据，确保数据的解耦和结构清晰。
1. command 
2. mapper 
3. dto
4. query

## 目录结构示例

```
src/
└── main/
    └── java/
        └── com/
            └── example/
                └── orderservice/
                    └── application/
                        ├── command/
                        ├── mapper/
                        ├── query/                                                                        
                        ├── dto/
                        │   ├── CreateOrderRequest.java
                        │   └── OrderResponse.java
                        ├── service/
                        │   └── OrderApplicationService.java                        
                        ├── queryservice/
                        │   └── TestQueryApplicationService.java
                        └── searchservice/
                            └── TestSearchApplicationService.java
```