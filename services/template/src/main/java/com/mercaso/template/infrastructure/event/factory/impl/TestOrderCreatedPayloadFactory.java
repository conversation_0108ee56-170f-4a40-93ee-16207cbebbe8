package com.mercaso.template.infrastructure.event.factory.impl;

import com.mercaso.template.application.dto.TestOrderDto;
import com.mercaso.template.application.dto.payload.TestOrderCreatedPayloadDto;
import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.template.infrastructure.event.factory.BusinessEventPayloadFactory;
import com.mercaso.template.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TestOrderCreatedPayloadFactory extends BusinessEventPayloadFactory<TestOrderCreatedPayloadDto> {

    @Override
    protected BusinessEventPayloadDto<TestOrderDto> build(BusinessEvent businessEvent) {

        return SerializationUtils.treeToValue(businessEvent.getPayload(), payloadClass());
    }

    @Override
    public Class<TestOrderCreatedPayloadDto> payloadClass() {
        return TestOrderCreatedPayloadDto.class;
    }

}
