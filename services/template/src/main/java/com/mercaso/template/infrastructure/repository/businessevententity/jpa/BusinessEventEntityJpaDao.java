package com.mercaso.template.infrastructure.repository.businessevententity.jpa;

import com.mercaso.template.infrastructure.repository.businessevententity.jpa.dataobject.BusinessEventEntityDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface BusinessEventEntityJpaDao extends JpaRepository<BusinessEventEntityDo, UUID> {

    List<BusinessEventEntityDo> findByEntityIdIn(List<UUID> entityIds);

    List<BusinessEventEntityDo> findByEntityId(UUID entityId);
}
