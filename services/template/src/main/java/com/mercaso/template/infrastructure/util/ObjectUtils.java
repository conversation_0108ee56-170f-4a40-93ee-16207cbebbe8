package com.mercaso.template.infrastructure.util;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

public class ObjectUtils {

    public static boolean allFieldsIsNotNull(Object object) {
        for (Field f : object.getClass().getDeclaredFields()) {
            try {
                f.setAccessible(true);
                if (f.get(object) == null || StringUtils.isBlank(f.get(object).toString())) {
                    return false;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return true;
    }
}
