package com.mercaso.template.application.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestOrderDto extends BaseDto {

    @Schema(required = true)
    private UUID id;

    @Schema(required = true)
    private String name;

    private Integer status;

}
