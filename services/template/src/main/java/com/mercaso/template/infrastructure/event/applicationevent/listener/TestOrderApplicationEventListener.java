package com.mercaso.template.infrastructure.event.applicationevent.listener;


import com.mercaso.template.application.dto.TestOrderDto;
import com.mercaso.template.application.dto.event.TestOrderCreatedApplicationEvent;
import com.mercaso.template.application.dto.payload.TestOrderCreatedPayloadDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class TestOrderApplicationEventListener {


    @Async
    @TransactionalEventListener
    public void handleTestOrderCreatedApplicationEvent(TestOrderCreatedApplicationEvent testOrderCreatedApplicationEvent) {
        TestOrderCreatedPayloadDto testOrderCreatedPayloadDto = testOrderCreatedApplicationEvent.getPayload();
        TestOrderDto testOrder = testOrderCreatedPayloadDto.getData();
        log.info("handleTestOrderCreatedApplicationEvent for testOrder={}", testOrder);
    }
}
