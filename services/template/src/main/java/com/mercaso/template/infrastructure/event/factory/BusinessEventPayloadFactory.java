package com.mercaso.template.infrastructure.event.factory;

import com.mercaso.template.application.dto.BaseDto;
import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
public abstract class BusinessEventPayloadFactory<P extends BusinessEventPayloadDto> {

    protected abstract BusinessEventPayloadDto<? extends BaseDto> build(BusinessEvent businessEvent);

    public abstract Class<P> payloadClass();

    public Optional<BusinessEventPayloadDto<? extends BaseDto>> buildPayloadDto(BusinessEvent businessEvent) {
        BusinessEventPayloadDto<? extends BaseDto> payloadDto = this.build(businessEvent);

        if (payloadDto != null) {
            return Optional.of(payloadDto);
        }

        return Optional.empty();
    }

}
