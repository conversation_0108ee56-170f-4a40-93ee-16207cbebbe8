package com.mercaso.template.infrastructure.repository.businessevent;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.domain.businessevent.BusinessEventRepository;
import com.mercaso.template.domain.businessevent.enums.EntityEnums;
import com.mercaso.template.infrastructure.repository.businessevent.jpa.BusinessEventJpaDao;
import com.mercaso.template.infrastructure.repository.businessevent.jpa.dataobject.BusinessEventDo;
import com.mercaso.template.infrastructure.repository.businessevent.jpa.mapper.BusinessEventDoMapper;
import com.mercaso.template.infrastructure.repository.businessevententity.jpa.BusinessEventEntityJpaDao;
import com.mercaso.template.infrastructure.repository.businessevententity.jpa.dataobject.BusinessEventEntityDo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class BusinessEventRepositoryImpl implements BusinessEventRepository {

    private static final String SPAN_OPERATION_NAME = "dispatchEvent";

    private final BusinessEventDoMapper businessEventDoMapper;

    private final BusinessEventJpaDao businessEventJpaDao;

    private final BusinessEventEntityJpaDao businessEventEntityJpaDao;

    @Override
    public BusinessEvent save(BusinessEvent domain) {
        BusinessEventDo businessEventDo = businessEventDoMapper.domainToDo(domain);
        businessEventDo.setCorrelationId(getCorrelationId());
        businessEventDo = businessEventJpaDao.save(businessEventDo);

        Map<EntityEnums, List<UUID>> relatedEntities = this.getRelatedEntities(domain);
        for (Map.Entry<EntityEnums, List<UUID>> entity : relatedEntities.entrySet()) {
            for (UUID entityId : entity.getValue()) {
                BusinessEventEntityDo entityDo = BusinessEventEntityDo.builder()
                        .businessEventId(businessEventDo.getId())
                        .entityId(entityId)
                        .entityType(entity.getKey().getValue())
                        .createdAt(businessEventDo.getCreatedAt())
                        .createdBy(businessEventDo.getCreatedBy())
                        .updatedAt(businessEventDo.getUpdatedAt())
                        .updatedBy(businessEventDo.getUpdatedBy())
                        .build();
                businessEventEntityJpaDao.save(entityDo);
            }
        }

        return businessEventDoMapper.doToDomain(businessEventDo);
    }

    @Override
    public BusinessEvent findById(UUID id) {
        BusinessEventDo businessEventDo = businessEventJpaDao.findById(id).orElse(null);
        return businessEventDoMapper.doToDomain(businessEventDo);
    }

    @Override
    public BusinessEvent update(BusinessEvent domain) {
        return null;
    }

    @Override
    public BusinessEvent deleteById(UUID id) {
        log.debug("delete.business.event.{}", id);
        return null;
    }

    public Map<EntityEnums, List<UUID>> getRelatedEntities(BusinessEvent businessEvent) {
        EnumMap<EntityEnums, List<UUID>> ret = new EnumMap<>(EntityEnums.class);
        JsonNode payload = businessEvent.getPayload();

        JsonNode payoutId = payload.get("testOrderId");
        if (payoutId != null && payoutId.isTextual()) {
            ret.put(EntityEnums.TEST_ORDER, List.of(UUID.fromString(payoutId.asText())));
        }

        return ret;
    }

    private String getCorrelationId() {
//        if (!GlobalTracer.isRegistered()) {
//            log.warn("global tracer is not registered");
//            return null;
//        }
//
//        Span span = GlobalTracer.get().activeSpan();
//        if (Objects.isNull(span)) {
//            span = GlobalTracer.get().buildSpan(SPAN_OPERATION_NAME).ignoreActiveSpan().start();
//        }
//        JaegerSpanContext context = (JaegerSpanContext) span.context();
//        return context.getTraceId();
        return UUID.randomUUID().toString();
    }

}
