package com.mercaso.template.infrastructure.config;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Callback;
import okhttp3.Cookie;
import okhttp3.CookieJar;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HttpClient {

    private final OkHttpClient client;

    private final CookieJar cookieJar;

    public HttpClient() {
        cookieJar = new CookieJar() {
            private final Map<String, List<Cookie>> cookies = new HashMap<>();

            @Override
            public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
                this.cookies.put(getDomain(String.valueOf(url)), cookies);
            }

            @Override
            public List<Cookie> loadForRequest( HttpUrl url) {
                List<Cookie> cookies = this.cookies.get(getDomain(String.valueOf(url)));
                if (cookies == null) {
                    cookies = new ArrayList<>();
                }
                return cookies;
            }
        };
        client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .cookieJar(cookieJar)
            .build();
    }

    public List<Cookie> getCookies(String url) {
        return cookieJar.loadForRequest(HttpUrl.get(url));
    }

    public String getDomain(String url){
        try {
            return new URL(url).getHost();
        } catch (MalformedURLException e) {
            return "";
        }
    }

    public Response execute(Request request) throws IOException {
        return client.newCall(request).execute();
    }

    public void executeAsync(Request request, Callback callback) {
        client.newCall(request).enqueue(callback);
    }

    public Response doGet(String url, Map<String, String> headers) throws IOException {
        Request.Builder builder = new Request.Builder();
        headers.forEach(builder::addHeader);

        Request request = builder
            .url(url)
            .build();

        return execute(request);
    }

    public Response doPostForMap(String url, Map<String, String> params) throws IOException {
        FormBody.Builder builder = new FormBody.Builder();
        params.forEach(builder::add);
        FormBody formBody = builder.build();
        Request request = new Request.Builder()
            .url(url)
            .post(formBody)
            .build();

        return execute(request);
    }

    public Response doPostForJson(String url, String json) throws IOException {
        Request request = new Request.Builder()
            .url(url)
            .post(RequestBody.create(json, MediaType.get("application/json")))
            .build();

        return execute(request);
    }

}

