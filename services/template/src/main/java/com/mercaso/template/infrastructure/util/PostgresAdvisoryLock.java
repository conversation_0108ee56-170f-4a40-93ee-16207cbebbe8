package com.mercaso.template.infrastructure.util;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;


@Slf4j
@Component
@RequiredArgsConstructor
public class PostgresAdvisoryLock {

    private final EntityManager entityManager;

    @Transactional(propagation = Propagation.MANDATORY)
    public void tryAcquireTransactionalLevelAdvisoryLock(Integer lockKey, String description) {
        StopWatch stopWatch = new StopWatch(lockKey.toString().concat("StopWatch"));
        stopWatch.start();
        log.debug("try to acquire advisory lock lockKey: {}, methodName: {}", lockKey, description);

        String sql = "SELECT count(*) from pg_advisory_xact_lock(:lockKey)";

        Query q = entityManager.createNativeQuery(sql);
        q.setParameter("lockKey", lockKey);

        q.getSingleResult();

        stopWatch.stop();
        log.debug("acquired advisory lock lockKey: {}, methodName: {}, stopWatch duration: {}",
                lockKey,
                description,
                stopWatch.getTotalTimeMillis());
    }
}
