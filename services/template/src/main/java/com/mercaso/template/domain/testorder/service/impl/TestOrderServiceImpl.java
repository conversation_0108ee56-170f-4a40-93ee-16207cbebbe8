package com.mercaso.template.domain.testorder.service.impl;


import com.mercaso.template.application.query.TestOrderQuery;
import com.mercaso.template.domain.testorder.TestOrder;
import com.mercaso.template.domain.testorder.TestOrderRepository;
import com.mercaso.template.domain.testorder.service.TestOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class TestOrderServiceImpl implements TestOrderService {

    private final TestOrderRepository testOrderRepository;


    @Override
    public List<TestOrder> queryTestList(TestOrderQuery testOrderQuery) {
        return List.of();
    }

    @Override
    public TestOrder findById(UUID id) {
        return testOrderRepository.findById(id);
    }
}
