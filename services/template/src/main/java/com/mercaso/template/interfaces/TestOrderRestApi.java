package com.mercaso.template.interfaces;


import com.mercaso.template.application.command.CreateTestOrderCommand;
import com.mercaso.template.application.dto.TestOrderDto;
import com.mercaso.template.application.service.TestOrderApplicationService;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/public/v1/test-orders",
        produces = {MediaType.APPLICATION_JSON_VALUE}
)
@Tag(name = "Test Controller", description = "Controller for sample Test")
public class TestOrderRestApi {

    private final TestOrderApplicationService testOrderApplicationService;

    private final Tracer tracer;

    @Operation(summary = "Create test-order", description = "Create the test-order with createTestOrderCommand")
    @PostMapping
    public TestOrderDto createTestOrder(@RequestBody
                                        CreateTestOrderCommand createTestOrderCommand) {

        Span span = tracer.spanBuilder("test").startSpan();
        try {
            return testOrderApplicationService.create(createTestOrderCommand);
        } finally {
            span.end();
        }

    }

}
