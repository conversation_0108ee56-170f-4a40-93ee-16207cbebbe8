package com.mercaso.template.domain.businessevent.service;

import com.mercaso.template.application.dto.BaseDto;
import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;

public interface BusinessEventService {

    BusinessEvent dispatch(BusinessEvent businessEvent);

    <T extends BaseDto> BusinessEvent dispatch(BusinessEventPayloadDto<T> payloadDto);

}
