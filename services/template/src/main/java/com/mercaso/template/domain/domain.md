# Domain 目录

在领域驱动设计（DDD）中，`domain`目录是系统的核心，包含所有与业务逻辑相关的内容。这个目录主要定义了系统的业务规则、行为和状态，并确保这些规则在整个系统中得到正确的应用和维护。

## 主要功能

1. **领域模型**：定义业务实体、值对象和聚合根等领域模型。
2. **领域服务**：封装复杂的业务逻辑和操作，这些逻辑通常跨越多个实体和值对象。
3. **领域事件**：捕获和传播领域内发生的重要事件，支持事件驱动架构。
4. **仓储接口**：定义领域对象的持久化接口，但不包含具体实现。
5. **工厂**：用于创建复杂的领域对象和聚合。
6. **规范（Specification）**：用于封装业务规则和查询逻辑。

## 需要添加的内容

### 1. 实体（Entities）

实体是具有唯一标识的业务对象，其状态和行为由领域模型定义。

**示例**：

```java
package com.example.orderservice.domain.model;

public class Order {
    private String orderId;
    private String customerId;
    private OrderStatus status;

    public Order(String orderId, String customerId) {
        this.orderId = orderId;
        this.customerId = customerId;
        this.status = OrderStatus.CREATED;
    }

    // Getters, Setters, and Business Methods
    public void markAsShipped() {
        if (this.status == OrderStatus.CREATED) {
            this.status = OrderStatus.SHIPPED;
        }
    }
}
```

### 2. 值对象（Value Objects）

值对象是不可变的对象，用于描述某个领域概念的属性或属性组合。

**示例**：

```java
package com.example.orderservice.domain.model;

import java.math.BigDecimal;
import java.util.Objects;

public class Money {
    private final BigDecimal amount;

    public Money(BigDecimal amount) {
        this.amount = amount;
    }

    // Getters and business methods
    public Money add(Money other) {
        return new Money(this.amount.add(other.amount));
    }

    // Override equals and hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Money money = (Money) o;
        return amount.equals(money.amount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(amount);
    }
}
```

### 3. 聚合根（Aggregates）

聚合根是一个包含多个实体和值对象的集合，并确保其内的业务规则和不变性。

**示例**：

```java
package com.example.orderservice.domain.model;

public class OrderAggregate {
    private Order order;
    private List<OrderItem> items;

    public OrderAggregate(Order order) {
        this.order = order;
        this.items = new ArrayList<>();
    }

    // Business methods
    public void addItem(OrderItem item) {
        this.items.add(item);
    }
}
```

### 4. 领域服务（Domain Services）

领域服务封装了不属于任何实体或值对象的复杂业务逻辑。

**示例**：

```java
package com.example.orderservice.domain.service;

import com.example.orderservice.domain.model.Order;

public class OrderDomainService {

    public boolean canShipOrder(Order order) {
        // Business logic to determine if the order can be shipped
        return order.getStatus() == OrderStatus.CREATED;
    }
}
```

### 5. 领域事件（Domain Events）

领域事件用于描述领域内发生的重要事件，并通知其他部分进行相应处理。

**示例**：

```java
package com.example.orderservice.domain.event;

public class OrderCreatedEvent {
    private String orderId;
    private String customerId;

    public OrderCreatedEvent(String orderId, String customerId) {
        this.orderId = orderId;
        this.customerId = customerId;
    }

    // Getters
    public String getOrderId() {
        return orderId;
    }

    public String getCustomerId() {
        return customerId;
    }
}
```

### 6. 仓储接口（Repository Interfaces）

仓储接口定义了持久化领域对象的方法，但不包含具体实现。

**示例**：

```java
package com.example.orderservice.domain.repository;

import com.example.orderservice.domain.model.Order;

import java.util.Optional;

public interface OrderRepository {
    void save(Order order);

    Optional<Order> findById(String orderId);
}
```

### 7. 工厂（Factories）

工厂用于创建复杂的领域对象和聚合，封装创建逻辑。

**示例**：

```java
package com.example.orderservice.domain.factory;

import com.example.orderservice.domain.model.Order;

public class OrderFactory {

    public Order createOrder(String customerId) {
        return new Order(generateOrderId(), customerId);
    }

    private String generateOrderId() {
        // Generate a unique order ID
        return UUID.randomUUID().toString();
    }
}
```

### 8. 规范（Specifications）

规范用于封装业务规则和查询逻辑，提供更灵活的查询方式。

**示例**：

```java
package com.example.orderservice.domain.specification;

import com.example.orderservice.domain.model.Order;

public class OrderSpecification {

    public boolean isSatisfiedBy(Order order) {
        // Business rule: order must be in CREATED status
        return order.getStatus() == OrderStatus.CREATED;
    }
}
```

## 目录结构示例

```
src/
└── main/
    └── java/
        └── com/
            └── example/
                └── orderservice/
                    └── domain/
                        └── model/
                            ├── Order.java
                            ├── OrderItem.java
                            ├── OrderStatus.java
                            └── Money.java
                            ├── service/
                            │   └── OrderDomainService.java
                            ├── repository/
                            │   └── OrderRepository.java
                            ├── factory/
                            │   └── OrderFactory.java
                            └── specification/
                                └── OrderSpecification.java
```
