package com.mercaso.template.infrastructure.util;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.*;
import java.time.temporal.ChronoUnit;

public class DateUtils {

    private DateUtils() {
    }

    //2021-03-24T00:00:00Z
    public static Instant getStartOfCurrentDay(Instant instant) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return startOfDay.toInstant(ZoneOffset.UTC);
    }

    public static Instant fromTimestamp(Timestamp date) {
        if (date != null) {
            return date.toInstant();
        }

        return null;
    }

    public static Instant fromStringTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        Timestamp timestamp = Timestamp.valueOf(date);
        return fromTimestamp(timestamp);
    }

    public static Instant getDefaultDate(long amountToSubtract) {
        LocalDateTime now = LocalDateTime.now();
        return now.minus(30, ChronoUnit.DAYS).toInstant(ZoneOffset.UTC);
    }

}
