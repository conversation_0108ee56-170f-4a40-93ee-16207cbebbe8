package com.mercaso.template.infrastructure.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Map;

@Component
@SuppressWarnings("all")
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext context;

    /**
     * Returns the Spring managed bean instance of the given class type if it exists. Returns null otherwise.
     */
    public static <T extends Object> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    public static <T extends Object> T getBean(String beanName) {
        return (T) context.getBean(beanName);
    }

    public static String[] getBeansWithAnnotation(Class<? extends Annotation> annotation) {
        return context.getBeanNamesForAnnotation(annotation);
    }

    public static <T extends Annotation> T findAnnotationOnBean(String beanName, Class<T> annotationType) {
        return context.findAnnotationOnBean(beanName, annotationType);
    }

    public static void autowire(Object classToAutowire) {
        SpringContextUtil.context.getAutowireCapableBeanFactory().autowireBean(classToAutowire);
    }

    public static <T> Map<String, T> getBeansOfType(Class<T> typeClass) {
        return SpringContextUtil.context.getBeansOfType(typeClass);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringContextUtil.context = applicationContext;
    }
}
