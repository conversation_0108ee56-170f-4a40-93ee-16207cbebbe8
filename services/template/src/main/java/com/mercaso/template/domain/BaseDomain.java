package com.mercaso.template.domain;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.Instant;

@Data
@SuperBuilder
public abstract class BaseDomain implements Serializable {

    private Instant createdAt;

    private String createdBy;

    private Instant updatedAt;

    private String updatedBy;

    private Instant deletedAt;

    private String deletedBy;

}
