package com.mercaso.template.infrastructure.event.factory;

import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.template.infrastructure.event.applicationevent.BaseApplicationEvent;
import com.mercaso.template.infrastructure.util.SerializationUtils;
import com.mercaso.template.infrastructure.util.SpringContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationEventFactory {

    @SuppressWarnings({"all"})
    public Optional<? extends BaseApplicationEvent> buildApplicationEvent(BusinessEvent businessEvent) {

        // Need to optimize this part
        Map<String, BusinessEventPayloadFactory> beansOfType = SpringContextUtil.getBeansOfType(BusinessEventPayloadFactory.class);

        Optional<? extends BusinessEventPayloadDto> payloadDto = Optional.empty();

        for (Map.Entry<String, BusinessEventPayloadFactory> entry : beansOfType.entrySet()) {
            if (entry.getValue().payloadClass().equals(businessEvent.getType().getPayloadClass())) {
                payloadDto = entry.getValue().buildPayloadDto(businessEvent);
                break;
            }
        }

        if (payloadDto.isEmpty()) {
            payloadDto = Optional.of(SerializationUtils.treeToValue(businessEvent.getPayload(),
                    businessEvent.getType().getPayloadClass()));
        }
        // --

        BaseApplicationEvent<?> applicationEvent = null;
        try {
            applicationEvent = businessEvent.getType().getEventClass()
                    .getDeclaredConstructor(Object.class, businessEvent.getType().getPayloadClass())
                    .newInstance(this, payloadDto.get());

            return Optional.of(applicationEvent);
        } catch (Exception e) {
            log.error("build application event exception", e);
        }

        return Optional.empty();
    }

}
