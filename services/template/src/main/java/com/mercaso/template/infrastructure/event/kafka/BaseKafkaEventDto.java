package com.mercaso.template.infrastructure.event.kafka;

import com.mercaso.template.application.dto.BaseDto;
import com.mercaso.template.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.Instant;
import java.util.UUID;

@Setter
@Getter
@ToString
public class BaseKafkaEventDto<T extends BusinessEventPayloadDto> extends BaseDto {

    private UUID id;

    private T payload;

    private EventTypeEnums type;

    private Instant completedAt;

    private String correlationId;

    private String publishedBy;

}
