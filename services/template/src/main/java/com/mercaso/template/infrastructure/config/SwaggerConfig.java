package com.mercaso.template.infrastructure.config;

import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

@Configuration
public class SwaggerConfig {

    @Value("${spring.application.name}")
    String appName;

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
            .info(new Info().title(appName + "api")
                .description(appName + "api")
                .termsOfService(appName + "api")
                .version("1.0")
            )
            .externalDocs(new ExternalDocumentation()
                .description(appName + "api")
            );
    }

    @Bean
    public FilterRegistrationBean swaggerHttpRequestWrapperFilter() {
        boolean runningInk8s = System.getenv().keySet().stream().anyMatch(x -> x.startsWith("KUBERNETES_"));

        if (runningInk8s) {
            FilterRegistrationBean filter = new FilterRegistrationBean(new SwaggerHttpRequestWrapperFilter(appName));
            filter.setOrder(Ordered.HIGHEST_PRECEDENCE);
            return filter;
        } else {
            return new FilterRegistrationBean((request, response, chain) -> chain.doFilter(request, response));
        }
    }

    // In order for the swagger-ui to work behind our reverse proxy (currently nginx) it needs
    // to be supplied the `x-forwarded-prefix` header.  While this can be done in k8s with the nginx proxy,
    // the header's value cannot vary by each `path` specified in the ingress and instead only set for the entire
    // ingress config.  Since we utilize multiple paths per ingress we have to use the hack below to make an
    // `x-forwarded-prefix` header appear out of thin air.
    class SwaggerHttpRequestWrapperFilter implements Filter {

        private final String prefix;

        SwaggerHttpRequestWrapperFilter(String prefix) {
            // All of our ingress definitions remove the `-service` from the path.  Remove the suffix if the application
            // name includes it.
            if (prefix.endsWith("-service")) {
                prefix = prefix.replace("-service", "");
            }
            this.prefix = "/" + prefix;
        }

        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
            HttpServletRequest req = (HttpServletRequest) request;
            var wrapper = new HttpServletRequestWrapper(req) {
                @Override
                public String getHeader(String name) {
                    if (name.equalsIgnoreCase("x-forwarded-prefix")) {
                        return prefix;
                    }
                    return super.getHeader(name);
                }

                @Override
                public Enumeration<String> getHeaders(String name) {
                    if (name.equalsIgnoreCase("x-forwarded-prefix")) {
                        return Collections.enumeration(List.of(prefix));
                    }
                    return super.getHeaders(name);
                }

                @Override
                public Enumeration<String> getHeaderNames() {
                    Set<String> names = new HashSet<>();
                    Enumeration<String> iter = super.getHeaderNames();
                    while (iter.hasMoreElements()) {
                        names.add(iter.nextElement());
                    }
                    names.add("x-forwarded-prefix");
                    return Collections.enumeration(names);
                }
            };

            chain.doFilter(wrapper, response);
        }
    }

}
