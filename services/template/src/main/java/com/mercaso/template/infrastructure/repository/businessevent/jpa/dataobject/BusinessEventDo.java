package com.mercaso.template.infrastructure.repository.businessevent.jpa.dataobject;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.template.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.template.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;


@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "business_event")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update business_event set deleted_at = current_timestamp where id = ? and updated_at = ?")
@Where(clause = "deleted_at is null")
public class BusinessEventDo extends BaseDo {

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private EventTypeEnums type;

    @Column(columnDefinition = "jsonb")
    @Type(JsonType.class)
    private JsonNode payload;

    @Column(name = "correlation_id")
    private String correlationId;
}
