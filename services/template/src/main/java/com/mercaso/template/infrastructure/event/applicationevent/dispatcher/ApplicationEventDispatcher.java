package com.mercaso.template.infrastructure.event.applicationevent.dispatcher;

import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.factory.ApplicationEventFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationEventDispatcher {

    private final ApplicationContext applicationContext;

    private final ApplicationEventFactory factory;

    public void publishEvent(BusinessEvent businessEvent) {
        log.debug("publish.application.event.{}", businessEvent.getType());
        factory.buildApplicationEvent(businessEvent).ifPresent(applicationContext::publishEvent);
    }

}
