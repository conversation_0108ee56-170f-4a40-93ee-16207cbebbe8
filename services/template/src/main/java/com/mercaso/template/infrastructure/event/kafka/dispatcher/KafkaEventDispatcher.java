package com.mercaso.template.infrastructure.event.kafka.dispatcher;

import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.factory.KafkaMessageFactory;
import com.mercaso.template.infrastructure.event.kafka.EventTopics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaEventDispatcher {

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final KafkaMessageFactory kafkaMessageFactory;

    public void send(BusinessEvent businessEvent) {
        Optional<String> topic = EventTopics.getTopic(businessEvent.getType());

        log.debug("send.kafka.message.to.{}", topic.isPresent() ? topic.get() : null);
        topic.ifPresent(s -> kafkaMessageFactory.buildMessage(businessEvent)
                .ifPresent(message -> kafkaTemplate.send(s, message)));
    }

}
