# Interfaces 目录

在领域驱动设计（DDD）中，`interfaces`目录主要负责提供系统的用户接口和外部系统接口。它包括用户接口（如Web接口、API接口）和外部系统的适配器，负责接收和响应用户请求，并将这些请求转发给应用层。

## 主要功能

1. **消息处理**：处理用户请求，如HTTP请求、RPC调用、消息队列的消息等。
2. **API接口**：提供RESTful API或GraphQL API，供外部系统或客户端调用。
3. **验证**：对输入数据进行验证，确保其符合业务规则和约束。

## 需要添加的内容

### 1. 消息处理（listener）

控制器处理消息队列的消息，调用应用服务。

### 2. API接口（API Interfaces）

定义系统的API接口，通常使用Swagger等工具生成API文档。

### 3. 验证（Validation）

对输入数据进行验证，确保其符合业务规则和约束。


## 目录结构示例

```
src/
└── main/
    └── java/
        └── com/
            └── example/
                └── orderservice/
                    └── interfaces/
                        ├── listener/
                        │   └── IMSKafkaListener.java
                        ├── rest/
                        │   ├── query/
                        │       └── QueryOrderRestApi.java                  
                        │   └── search/  
                        │       └── SearchOrderRestApi.java 
                        │   └── OrderRestApi.java                                                                     
                        └── validation/
                            └── OrderRequestValidator.java
```
