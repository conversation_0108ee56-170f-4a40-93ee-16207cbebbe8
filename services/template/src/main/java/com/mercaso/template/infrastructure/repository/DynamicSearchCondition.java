package com.mercaso.template.infrastructure.repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public abstract class DynamicSearchCondition<P extends PageQuery> implements SearchConditionResolver<P> {

    private final List<SearchConditionResolver<P>> resolvers = new ArrayList<>();

    public DynamicSearchCondition(List<SearchConditionResolver<P>> rs) {
        resolvers.addAll(rs);
    }

    @Override
    public String generateConditionBlock(P query) {
        return resolvers.stream().map(resolver -> resolver.generateConditionBlock(query)).collect(Collectors.joining());
    }

    public int bindSqlParameter(PreparedStatement ps, P query) throws SQLException {
        return this.bindSqlParameter(ps, query, 1);
    }

    @Override
    public int bindSqlParameter(PreparedStatement ps, P query, int index) throws SQLException {
        int startPosition = index;

        for (SearchConditionResolver<P> resolver : resolvers) {
            startPosition = resolver.bindSqlParameter(ps, query, startPosition);
        }
        return startPosition;
    }
}
