package com.mercaso.template.infrastructure.event.applicationevent;

import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Setter
@Getter
public abstract class BaseApplicationEvent<T extends BusinessEventPayloadDto> extends ApplicationEvent implements Serializable {

    private T payload;

    protected BaseApplicationEvent(Object source, T payload) {
        super(source);
        this.payload = payload;
    }
}
