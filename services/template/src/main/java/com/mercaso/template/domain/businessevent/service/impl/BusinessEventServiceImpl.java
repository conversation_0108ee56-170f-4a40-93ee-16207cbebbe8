package com.mercaso.template.domain.businessevent.service.impl;

import com.mercaso.template.application.dto.BaseDto;
import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.template.domain.businessevent.service.BusinessEventService;
import com.mercaso.template.infrastructure.event.BusinessEventDispatcher;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.template.infrastructure.util.SerializationUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BusinessEventServiceImpl implements BusinessEventService {

    private final BusinessEventDispatcher businessEventDispatcher;

    @Override
    public BusinessEvent dispatch(BusinessEvent businessEvent) {
        return businessEventDispatcher.dispatch(businessEvent);
    }

    @Override
    public <T extends BaseDto> BusinessEvent dispatch(BusinessEventPayloadDto<T> payloadDto) {
        BusinessEvent event = BusinessEvent.builder()
                .type(EventTypeEnums.forPayload(payloadDto.getClass()))
                .payload(SerializationUtils.toTree(payloadDto))
                .build();
        return this.dispatch(event);
    }

}
