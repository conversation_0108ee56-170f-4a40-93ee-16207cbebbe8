package com.mercaso.template.infrastructure.event.factory;

import com.mercaso.template.domain.businessevent.BusinessEvent;
import com.mercaso.template.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.template.infrastructure.event.kafka.BaseKafkaEventDto;
import com.mercaso.template.infrastructure.util.SerializationUtils;
import com.mercaso.template.infrastructure.util.SpringContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaMessageFactory {

    @SuppressWarnings({"all"})
    public Optional<String> buildMessage(BusinessEvent businessEvent) {

        Map<String, BusinessEventPayloadFactory> beansOfType = SpringContextUtil.getBeansOfType(BusinessEventPayloadFactory.class);

        Optional<? extends BusinessEventPayloadDto> payloadDto = Optional.empty();

        for (Map.Entry<String, BusinessEventPayloadFactory> entry : beansOfType.entrySet()) {
            if (entry.getValue().payloadClass().equals(businessEvent.getType().getPayloadClass())) {
                payloadDto = entry.getValue().buildPayloadDto(businessEvent);
                break;
            }
        }

        if (payloadDto.isEmpty()) {
            payloadDto = Optional.of(SerializationUtils.treeToValue(businessEvent.getPayload(),
                    businessEvent.getType().getPayloadClass()));
        }

        BaseKafkaEventDto eventDto = new BaseKafkaEventDto<>();
        eventDto.setId(businessEvent.getId());
        eventDto.setPayload(payloadDto.get());
        eventDto.setType(businessEvent.getType());
        eventDto.setCompletedAt(businessEvent.getCreatedAt());
        eventDto.setCorrelationId(businessEvent.getCorrelationId());
        eventDto.setPublishedBy(businessEvent.getCreatedBy());

        return Optional.of(SerializationUtils.serialize(eventDto));
    }

}
