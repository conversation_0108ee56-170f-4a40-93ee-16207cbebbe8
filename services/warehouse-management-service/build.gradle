plugins {
    id "org.sonarqube"
    id "jacoco"
}

gradle.startParameter.parallelProjectExecutionEnabled = true
gradle.startParameter.maxWorkerCount = Runtime.runtime.availableProcessors()

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml"
        def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.branch.name", branchName
        property "sonar.coverage.exclusions",
                "**/command/**, **/mapper/**, **/dto/**, **/mapper/**, **/enums/**, **/adaptor/**, **/config/**, **/exception/**, **/excel/**, **/businessevent/**, **/external/**, **/event/**, **/Application.java, **/*Do.java, **/BaseDomain.java, **/retryabletransaction/**"
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
        html.required = true
    }
    executionData = files(
            "$buildDir/jacoco/test.exec",
            "$buildDir/jacoco/integrationTest.exec"
    )
}

test {
    finalizedBy jacocoTestReport
    maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
    forkEvery = 500

    minHeapSize = "256m"
    maxHeapSize = "1g"

    timeout = Duration.ofMinutes(10)

    failFast = false
    ignoreFailures = false

    afterTest { TestDescriptor descriptor, TestResult result ->
        println "Test ${descriptor.className}.${descriptor.name} took ${result.endTime - result.startTime} ms"
    }
}


dependencies {
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.mercaso.data:data_client:1.0.19771'
    implementation 'com.mercaso.ims:ims_client:1.0.108883'
    implementation 'com.mercaso.user:user_client:1.0.70644'
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'org.glassfish.jaxb:jaxb-runtime:2.3.1'
    implementation 'com.mercaso.components:mercaso-business-event-starter:1.0.459'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation("commons-validator:commons-validator:1.7")
    implementation 'org.hibernate.orm:hibernate-spatial:6.4.4.Final'
    implementation 'net.postgis:postgis-jdbc:2021.1.0'
    implementation 'org.locationtech.jts:jts-core:1.19.0'
    implementation 'com.google.maps:google-maps-services:2.2.0'
    implementation 'com.slack.api:slack-api-client:1.36.1'
    implementation 'com.slack.api:slack-app-backend:1.36.1'
    // WebClient and reactive support
    implementation 'org.springframework:spring-webflux:6.1.14'
}

configurations.configureEach { cfg ->
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.springframework' && details.requested.name == 'spring-webflux') {
            details.useVersion '6.1.14'
        }
        if (details.requested.group == 'org.springframework' && details.requested.name == 'spring-web') {
            details.useVersion '6.1.14'
        }
    }
}

springBoot {
    mainClass = 'com.mercaso.wms.Application'
}

integrationTest {
    systemProperty 'spring.profiles.active', 'integration'
    maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
    forkEvery = 200

    minHeapSize = "256m"
    maxHeapSize = "1g"

    timeout = Duration.ofMinutes(15)

    failFast = false
    ignoreFailures = false

    afterTest { TestDescriptor descriptor, TestResult result ->
        println "IT ${descriptor.className}.${descriptor.name} took ${result.endTime - result.startTime} ms"
    }
}