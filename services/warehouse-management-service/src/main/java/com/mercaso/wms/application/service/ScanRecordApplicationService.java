package com.mercaso.wms.application.service;

import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand;
import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand.ScanRecordCommand;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.scanrecords.ScanRecord;
import com.mercaso.wms.domain.scanrecords.ScanRecordRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ScanRecordApplicationService {

    private static final String MDC_WAREHOUSE_NAME = "MDC";
    private final ScanRecordRepository scanRecordRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final WarehouseRepository warehouseRepository;

    @Transactional
    public void create(BatchScanRecordCommand command) {
        if (command == null || command.getScanRecords() == null || command.getScanRecords().isEmpty()) {
            log.warn("BatchScanRecordCommand is null or contains no scan records");
            return;
        }

        Warehouse mdcWarehouse = getMdcWarehouse();
        Map<UUID, DeliveryOrder> deliveryOrderMap = buildDeliveryOrderMap(command.getScanRecords());

        List<ScanRecord> scanRecords = command.getScanRecords().stream()
            .map(scanRecordCommand -> {
                try {
                    return createOrUpdateScanRecord(scanRecordCommand, deliveryOrderMap, mdcWarehouse);
                } catch (Exception e) {
                    log.error("Failed to create scan record for deliveryOrderId: {}, SKU: {}",
                        scanRecordCommand.getDeliveryOrderId(),
                        scanRecordCommand.getSkuNumber(), e);
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .toList();

        if (!scanRecords.isEmpty()) {
            scanRecordRepository.saveAll(scanRecords);
            log.info("Successfully created {} scan records", scanRecords.size());
        }
    }

    private Warehouse getMdcWarehouse() {
        Warehouse warehouse = warehouseRepository.findByName(MDC_WAREHOUSE_NAME);
        if (warehouse == null) {
            throw new IllegalStateException(
                String.format("Warehouse %s not found", MDC_WAREHOUSE_NAME));
        }
        return warehouse;
    }

    private Map<UUID, DeliveryOrder> buildDeliveryOrderMap(List<ScanRecordCommand> scanRecordCommands) {
        List<UUID> deliveryOrderIds = scanRecordCommands.stream()
            .map(ScanRecordCommand::getDeliveryOrderId)
            .distinct()
            .toList();

        List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.findByIdIn(deliveryOrderIds);

        return deliveryOrders.stream()
            .collect(Collectors.toMap(
                DeliveryOrder::getId,
                Function.identity(),
                (existing, replacement) -> existing
            ));
    }

    private ScanRecord createOrUpdateScanRecord(
        ScanRecordCommand scanRecordCommand,
        Map<UUID, DeliveryOrder> deliveryOrderMap,
        Warehouse warehouse) {

        DeliveryOrder deliveryOrder = deliveryOrderMap.get(scanRecordCommand.getDeliveryOrderId());
        if (deliveryOrder == null) {
            log.warn("No delivery order found for delivery order id: {}", scanRecordCommand.getDeliveryOrderId());
            return null;
        }

        UUID itemId = findItemIdBySku(deliveryOrder, scanRecordCommand.getSkuNumber());
        if (itemId == null) {
            log.warn("SKU {} not found in delivery order: {}",
                scanRecordCommand.getSkuNumber(),
                scanRecordCommand.getDeliveryOrderId());
            return null;
        }
        ScanRecord scanRecord = scanRecordRepository.findByEntityIdAndScanType(itemId,
            scanRecordCommand.getScanType());
        if (scanRecord != null) {
            scanRecord.setQty(scanRecordCommand.getQty());
            scanRecordRepository.update(scanRecord);
            return null;
        }
        return buildScanRecord(scanRecordCommand, itemId, warehouse);
    }

    private UUID findItemIdBySku(DeliveryOrder deliveryOrder, String skuNumber) {
        List<DeliveryOrderItem> items = deliveryOrder.getDeliveryOrderItems();
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }

        return items.stream()
            .filter(item -> skuNumber.equals(item.getSkuNumber()))
            .findFirst()
            .map(DeliveryOrderItem::getId)
            .orElse(null);
    }

    private ScanRecord buildScanRecord(ScanRecordCommand scanRecordCommand, UUID itemId, Warehouse warehouse) {
        UUID userId = null;
        String loginUserId = SecurityContextUtil.getLoginUserId();

        if (loginUserId != null) {
            try {
                userId = UUID.fromString(loginUserId);
            } catch (IllegalArgumentException e) {
                // Continue with null userId rather than failing the entire batch
            }
        }
        
        String username = SecurityContextUtil.getUsername();

        return ScanRecord.builder().build().create(
            scanRecordCommand.getScanType(),
            scanRecordCommand.getQty(),
            itemId,
            EntityEnums.DELIVERY_ORDER_ITEM,
            userId,
            username,
            warehouse
        );
    }
}