package com.mercaso.wms.interfaces;

import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchAssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchUpdatePickingTaskSourceCommand;
import com.mercaso.wms.application.command.pickingtask.BulkCompletePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkSplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.CancelPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.ReassignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.SplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.searchservice.PickingTaskSearchService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/picking-tasks")
@RequiredArgsConstructor
public class PickingTaskResource {

    private final PickingTaskApplicationService pickingTaskApplicationService;

    private final PickingTaskSearchService pickingTaskSearchService;

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/picking")
    public PickingTaskDto startPicking(@PathVariable UUID pickingTaskId) {
        log.info("Starting picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.startPicking(pickingTaskId);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/complete")
    public PickingTaskDto completePickingTask(@PathVariable UUID pickingTaskId) {
        log.info("Completing picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.completePickingTask(pickingTaskId);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/cancel")
    public PickingTaskDto cancelPickingTask(@PathVariable UUID pickingTaskId) {
        log.info("Cancelling picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.cancelPickingTask(pickingTaskId, null);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/assign")
    public PickingTaskDto assign(
        @PathVariable UUID pickingTaskId,
        @RequestBody AssignPickingTaskCommand assignPickingTaskCommand) {
        log.info("Assigning user to picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.assignPickingTask(pickingTaskId, assignPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/batch/assign")
    public List<PickingTaskDto> batchAssign(
        @RequestBody BatchAssignPickingTaskCommand batchAssignPickingTaskCommand) {
        log.info("Assigning user to picking tasks with ids: {}", batchAssignPickingTaskCommand.getPickingTaskIds());
        return pickingTaskApplicationService.batchAssignPickingTask(batchAssignPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/batch/update-source")
    public List<PickingTaskDto> batchUpdateSource(
        @RequestBody BatchUpdatePickingTaskSourceCommand batchUpdatePickingTaskSourceCommand) {
        log.info("Updating source for picking tasks with ids: {}, new source: {}",
            batchUpdatePickingTaskSourceCommand.getPickingTaskIds(),
            batchUpdatePickingTaskSourceCommand.getSource());
        return pickingTaskApplicationService.batchUpdatePickingTaskSource(batchUpdatePickingTaskSourceCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/reassign")
    public PickingTaskDto reassign(
        @PathVariable UUID pickingTaskId,
        @RequestBody ReassignPickingTaskCommand reassignPickingTaskCommand) {
        log.info("Reassigning user to picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.reassignPickingTask(pickingTaskId, reassignPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}")
    public PickingTaskDto updatePickingTask(
        @PathVariable UUID pickingTaskId,
        @RequestBody UpdatePickingTaskCommand updatePickingTaskCommand) {
        log.info("Updating picking task with id: {}, updatePickingTaskCommand: {}", pickingTaskId, updatePickingTaskCommand);
        return pickingTaskApplicationService.updatePickingTask(pickingTaskId, updatePickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/{pickingTaskId}/split-task")
    public PickingTaskDto splitPickingTask(
        @PathVariable UUID pickingTaskId,
        @RequestBody SplitPickingTaskCommand splitPickingTaskCommand) {
        log.info("Splitting picking task with id: {}", pickingTaskId);
        return pickingTaskApplicationService.splitPickingTask(pickingTaskId, splitPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/batch/cancel")
    public List<PickingTaskDto> cancelPickingTask(@RequestBody CancelPickingTaskCommand cancelPickingTaskCommand) {
        return pickingTaskApplicationService.batchCancelPickingTask(cancelPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/bulk-split")
    public PickingTaskDto bulkSplitPickingTask(@RequestBody BulkSplitPickingTaskCommand bulkSplitPickingTaskCommand) {
        return pickingTaskApplicationService.bulkSplitPickingTask(bulkSplitPickingTaskCommand);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/bulk-complete")
    public List<UUID> bulkCompletePickingTask(@RequestBody BulkCompletePickingTaskCommand command) {
        return pickingTaskApplicationService.bulkCompletePickingTask(command);
    }

    @PreAuthorize("hasAuthority('wms:write:picking-tasks')")
    @PutMapping("/batch/unassign")
    public List<PickingTaskDto> batchUnassign(@RequestBody List<UUID> pickingTaskIds) {
        log.info("Batch unassign picking tasks with ids: {}", pickingTaskIds);
        return pickingTaskApplicationService.batchUnassignPickingTasks(pickingTaskIds);
    }

    @PreAuthorize("hasAuthority('wms:read:picking-tasks')")
    @GetMapping("/{deliveryDate}/picked-items-export")
    public void pickedItemsExport(@PathVariable(value = "deliveryDate") String deliveryDate, HttpServletResponse response)
        throws IOException {
        String fileName = URLEncoder.encode(deliveryDate + "-picked-items-export", StandardCharsets.UTF_8);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.getOutputStream().write(pickingTaskSearchService.pickedItemsExport(deliveryDate).toByteArray());
    }

}
