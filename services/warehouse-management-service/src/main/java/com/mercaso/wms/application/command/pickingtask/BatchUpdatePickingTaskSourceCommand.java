package com.mercaso.wms.application.command.pickingtask;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.batch.enums.SourceEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class BatchUpdatePickingTaskSourceCommand extends BaseCommand {

    @NotEmpty(message = "Picking task IDs cannot be empty")
    private List<UUID> pickingTaskIds;

    @NotNull(message = "Source cannot be null")
    private SourceEnum source;

}