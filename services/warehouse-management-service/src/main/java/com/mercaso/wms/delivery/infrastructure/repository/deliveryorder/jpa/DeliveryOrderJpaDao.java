package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa;

import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface DeliveryOrderJpaDao extends JpaRepository<DeliveryOrderDo, UUID> {

    DeliveryOrderDo findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId);

    List<DeliveryOrderDo> findAllByDeliveryTaskIdAndStatusIn(UUID deliveryTaskId, Collection<DeliveryOrderStatus> statuses);

    List<DeliveryOrderDo> findAllByOrderNumberIn(Collection<String> orderNumbers);

    List<DeliveryOrderDo> findAllByDeliveryTaskIdIn(Collection<UUID> taskIds);

    List<DeliveryOrderDo> findAllByDeliveryTaskId(UUID id);

    DeliveryOrderDo findByOrderNumber(String orderNumber);

    List<DeliveryOrderDo> findByDeliveryDate(String deliveryDate);

    List<DeliveryOrderDo> findByIdIn(List<UUID> deliveryOrderIds);
}