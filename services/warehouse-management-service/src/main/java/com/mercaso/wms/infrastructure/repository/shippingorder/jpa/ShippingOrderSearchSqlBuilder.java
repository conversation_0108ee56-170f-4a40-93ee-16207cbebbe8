package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.application.query.SortType;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Data
@Builder
public class ShippingOrderSearchSqlBuilder {

    private ShippingOrderQuery query;
    private Pageable pageable;
    private MapSqlParameterSource params;

    public String buildSelectSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct ");
        sql.append("so.id, ");
        sql.append("so.order_number, ");
        sql.append("so.shopify_order_id, ");
        sql.append("so.order_date, ");
        sql.append("so.delivery_date, ");
        sql.append("so.shipped_date, ");
        sql.append("so.status, ");
        sql.append("l.name, ");
        sql.append("so.pallet_count, ");
        sql.append("so.created_at, ");
        sql.append("so.updated_at, ");
        sql.append("so.driver_user_name, ");
        sql.append("so.truck_number, ");
        sql.append("CASE WHEN EXISTS (");
        sql.append("SELECT 1 FROM shipping_order_items soi_check ");
        sql.append("WHERE soi_check.shipping_order_id = so.id ");
        sql.append("AND soi_check.high_value_item = true ");
        sql.append("AND soi_check.deleted_at IS NULL");
        sql.append(") THEN true ELSE false END as has_high_value_items, ");
        sql.append("COALESCE(SUM(COALESCE(soi.original_qty, 0)) OVER (PARTITION BY so.id), 0) as original_total_qty, ");
        sql.append("COALESCE(SUM(COALESCE(soi.qty, 0)) OVER (PARTITION BY so.id), 0) as current_total_qty, ");
        sql.append("COALESCE(SUM(COALESCE(soi.picked_qty, 0)) OVER (PARTITION BY so.id), 0) as picked_total_qty, ");
        sql.append("COALESCE(SUM(COALESCE(soi.fulfilled_qty, 0)) OVER (PARTITION BY so.id), 0) as fulfilled_total_qty, ");
        sql.append("COALESCE(SUM(COALESCE(soi.validated_qty, 0)) OVER (PARTITION BY so.id), 0) as validated_total_qty, ");
        sql.append("COALESCE(SUM(COALESCE(soi.delivered_qty, 0)) OVER (PARTITION BY so.id), 0) as delivered_total_qty ");
        sql.append("FROM shipping_order so ");
        sql.append("LEFT JOIN location l ON l.id = so.breakdown_location_id ");
        sql.append("LEFT JOIN shipping_order_items soi ON soi.shipping_order_id = so.id AND soi.deleted_at IS NULL ");
        sql.append("WHERE so.deleted_at IS NULL ");

        buildWhereClause(sql);
        buildOrderByClause(sql);
        buildPaginationClause(sql);

        return sql.toString();
    }

    public String buildCountSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT so.id) ");
        sql.append("FROM shipping_order so ");
        sql.append("LEFT JOIN location l ON l.id = so.breakdown_location_id ");
        sql.append("WHERE so.deleted_at IS NULL ");

        buildWhereClause(sql);

        return sql.toString();
    }

    private void buildWhereClause(StringBuilder sql) {
        // Order number conditions
        if (StringUtils.hasText(query.getOrderNumber())) {
            sql.append("AND so.order_number = :orderNumber ");
            params.addValue("orderNumber", query.getOrderNumber());
        }

        // Status conditions
        if (!CollectionUtils.isEmpty(query.getStatuses())) {
            sql.append("AND so.status IN (:statuses) ");
            params.addValue("statuses", query.getStatuses());
        }

        // Delivery date conditions
        if (StringUtils.hasText(query.getDeliveryDate())) {
            sql.append("AND so.delivery_date = :deliveryDate ");
            params.addValue("deliveryDate", query.getDeliveryDate());
        }

        // Breakdown name condition
        if (StringUtils.hasText(query.getBreakdownName())) {
            sql.append("AND l.name ILIKE :breakdownName ");
            params.addValue("breakdownName", "%" + query.getBreakdownName() + "%");
        }
        // Driver user ID condition
        if (query.getDriverUserId() != null) {
            sql.append("AND so.driver_user_id = :driverUserId ");
            params.addValue("driverUserId", query.getDriverUserId());
        }
        // Truck number condition
        if (StringUtils.hasText(query.getTruckNumber())) {
            sql.append("AND so.truck_number ILIKE :truckNumber ");
            params.addValue("truckNumber", "%" + query.getTruckNumber() + "%");
        }
        // High value items condition - check if any shipping order item has high_value_item = true
        if (query.getHasHighValueItems() != null) {
            if (Boolean.TRUE.equals(query.getHasHighValueItems())) {
                sql.append("AND EXISTS (");
                sql.append("SELECT 1 FROM shipping_order_items soi_hv ");
                sql.append("WHERE soi_hv.shipping_order_id = so.id ");
                sql.append("AND soi_hv.high_value_item = true ");
                sql.append("AND soi_hv.deleted_at IS NULL");
                sql.append(") ");
            } else {
                // If not filtering for high value items, exclude orders that have high value items
                sql.append("AND NOT EXISTS (");
                sql.append("SELECT 1 FROM shipping_order_items soi_hv ");
                sql.append("WHERE soi_hv.shipping_order_id = so.id ");
                sql.append("AND soi_hv.high_value_item = true ");
                sql.append("AND soi_hv.deleted_at IS NULL");
                sql.append(") ");
            }
        }
        // Needs check order condition - check if any shipping order item has fulfilled_qty != picked_qty
        if (query.getQaRequired() != null) {
            if (Boolean.TRUE.equals(query.getQaRequired())) {
                sql.append("AND EXISTS (");
                sql.append("SELECT 1 FROM shipping_order_items soi_check ");
                sql.append("WHERE soi_check.shipping_order_id = so.id ");
                sql.append("AND COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0) ");
                sql.append("AND soi_check.deleted_at IS NULL");
                sql.append(") ");
            } else {
                // If not filtering for QA required, exclude orders that need QA
                sql.append("AND NOT EXISTS (");
                sql.append("SELECT 1 FROM shipping_order_items soi_check ");
                sql.append("WHERE soi_check.shipping_order_id = so.id ");
                sql.append("AND COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0) ");
                sql.append("AND soi_check.deleted_at IS NULL");
                sql.append(") ");
            }
        }

        // Picked not delivered condition - check if any shipping order item has delivered_qty < picked_qty
        if (query.getPickedNotDelivered() != null && query.getPickedNotDelivered()) {
            sql.append("AND EXISTS (");
            sql.append("SELECT 1 FROM shipping_order_items soi_pnd ");
            sql.append("WHERE soi_pnd.shipping_order_id = so.id ");
            sql.append("AND COALESCE(soi_pnd.delivered_qty, 0) < COALESCE(soi_pnd.picked_qty, 0) ");
            sql.append("AND soi_pnd.deleted_at IS NULL");
            sql.append(") ");
        }
    }

    private void buildOrderByClause(StringBuilder sql) {
        SortType sortType = query.getSort() != null ? query.getSort() : SortType.CREATED_AT_DESC;

        sql.append("ORDER BY ");
        String orderByClause = switch (sortType) {
            case CREATED_AT_DESC -> "so.created_at DESC";
            case CREATED_AT_ASC -> "so.created_at ASC";
            case UPDATED_AT_DESC -> "so.updated_at DESC";
            case UPDATED_AT_ASC -> "so.updated_at ASC";
            case BREAKDOWN_NAME_DESC -> "l.name DESC";
            case BREAKDOWN_NAME_ASC -> "l.name ASC";
            default -> "so.created_at DESC";
        };

        sql.append(orderByClause);
    }

    private void buildPaginationClause(StringBuilder sql) {
        if (pageable != null) {
            sql.append(" LIMIT :limit OFFSET :offset");
            params.addValue("limit", pageable.getPageSize());
            params.addValue("offset", pageable.getOffset());
        }
    }
} 