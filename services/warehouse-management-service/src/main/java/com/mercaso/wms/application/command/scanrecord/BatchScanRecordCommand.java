package com.mercaso.wms.application.command.scanrecord;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BatchScanRecordCommand extends BaseCommand {

    @NotEmpty
    private List<ScanRecordCommand> scanRecords;

    @Getter
    @Builder
    public static class ScanRecordCommand {

        private UUID deliveryOrderId;

        private String skuNumber;

        private ScanType scanType;

        private Integer qty;
    }

}
