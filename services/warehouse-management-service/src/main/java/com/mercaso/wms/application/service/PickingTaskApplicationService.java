package com.mercaso.wms.application.service;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;
import static com.mercaso.wms.infrastructure.contant.FeatureFlagKeys.TRANSFER_LOST_FOUND_INVENTORY;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchAssignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BatchUpdatePickingTaskSourceCommand;
import com.mercaso.wms.application.command.pickingtask.BulkCompletePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkSplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.BulkSplitPickingTaskCommand.ItemSuggestLocation;
import com.mercaso.wms.application.command.pickingtask.CancelPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.ReassignPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.SplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.event.PickingTaskAssignedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskCanceledPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskFailedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskPartiallyCompletedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskPickedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskReassignedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskStartedPayloadDto;
import com.mercaso.wms.application.dto.event.PickingTaskUnassignedPayloadDto;
import com.mercaso.wms.application.mapper.pickingtask.PickingTaskDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.PickingTaskItemQueryService;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntity;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleEntityTypeEnum;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentDto.ShipmentItem;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import jakarta.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.ResourceNotFoundException;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class PickingTaskApplicationService {

    private final PickingTaskRepository pickingTaskRepository;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final PickingTaskDtoApplicationMapper pickingTaskDtoApplicationMapper;

    private final ApplicationEventDispatcher applicationEventDispatcher;

    private final BatchItemRepository batchItemRepository;

    private final PickingTaskItemRepository pickingTaskItemRepository;

    private final ImsAdaptor imsAdaptor;

    private final PickingTaskItemQueryService pickingTaskItemQueryService;

    private final LocationCache locationCache;

    private final LocationRepository locationRepository;

    private final FinaleConfigProperties finaleConfigProperties;

    private final FinaleProductService finaleProductService;

    private final BatchRepository batchRepository;

    private final CrossDockTaskItemService crossDockTaskItemService;

    private final FeatureFlagsManager featureFlagsManager;

    private final WarehouseRepository warehouseRepository;

    private static final String FACILITY_URL_PREFIX = "/api/facility/";

    private static final String PRODUCT_URL_PREFIX = "/api/product/";

    private static final String TRANSFER_SHIPMENT_URL_PREFIX = "https://app.finaleinventory.com/mercaso/sc2/?shipment/detail/";

    private static final String TRANSFER_SHIPMENT_ID_PREFIX = "/mercaso/api/shipment/";

    @RetryableTransaction
    public PickingTaskDto assignPickingTask(UUID pickingTaskId, AssignPickingTaskCommand assignPickingTaskCommand) {
        log.info("Assigning picking task with id: {}", pickingTaskId);

        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        pickingTask.assignTask(assignPickingTaskCommand.getPickerUserId(), assignPickingTaskCommand.getPickerUserName());
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(
            pickingTask));

        businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskAssignedPayloadDto.builder()
            .pickingTaskId(pickingTaskDto.getId())
            .data(pickingTaskDto)
            .build()));
        return pickingTaskDto;
    }

    @RetryableTransaction
    public List<PickingTaskDto> batchAssignPickingTask(BatchAssignPickingTaskCommand command) {
        log.info("[batchAssignPickingTask] Assigning picking tasks with ids: {}", command.getPickingTaskIds());
        List<PickingTask> pickingTasks = pickingTaskRepository.findByIds(command.getPickingTaskIds());
        if (pickingTasks.isEmpty()) {
            throw new ResourceNotFoundException("Picking tasks not found.");
        }

        List<UUID> reassignedTaskIds = new LinkedList<>();
        pickingTasks.forEach(task -> {
            if (task.getPickerUserId() != null) {
                task.reassignTask(command.getPickerUserId(), command.getPickerUserName());
                reassignedTaskIds.add(task.getId());
            } else {
                task.assignTask(command.getPickerUserId(), command.getPickerUserName());
            }
        });

        pickingTasks = pickingTaskRepository.saveAll(pickingTasks);
        List<PickingTaskDto> pickingTaskDtos = pickingTaskDtoApplicationMapper.domainToDtos(pickingTasks);

        pickingTaskDtos.forEach(dto -> {
            if (reassignedTaskIds.contains(dto.getId())) {
                businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskReassignedPayloadDto.builder()
                    .pickingTaskId(dto.getId())
                    .data(dto)
                    .build()));
            } else {
                businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskAssignedPayloadDto.builder()
                    .pickingTaskId(dto.getId())
                    .data(dto)
                    .build()));
            }
        });
        return pickingTaskDtos;
    }

    @RetryableTransaction
    public PickingTaskDto reassignPickingTask(UUID pickingTaskId, ReassignPickingTaskCommand reassignPickingTaskCommand) {
        log.info("Reassigning picking task with id: {}", pickingTaskId);
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        pickingTask.reassignTask(reassignPickingTaskCommand.getPickerUserId(), reassignPickingTaskCommand.getPickerUserName());
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(
            pickingTask));
        businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskReassignedPayloadDto.builder()
            .pickingTaskId(pickingTaskDto.getId())
            .data(pickingTaskDto)
            .build()));
        return pickingTaskDto;
    }

    @RetryableTransaction
    public PickingTaskDto startPicking(UUID pickingTaskId) {
        log.info("Starting picking task with id: {}", pickingTaskId);
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        if (SecurityContextUtil.getLoginUserId() == null || !SecurityContextUtil.getLoginUserId()
            .equals(pickingTask.getPickerUserId().toString())) {
            throw new WmsBusinessException(ErrorCodeEnums.PICKING_TASK_START_ERROR.getCode(),
                ErrorCodeEnums.PICKING_TASK_START_ERROR.getMessage());
        }
        pickingTask.startPicking();
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(
            pickingTask));
        businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskStartedPayloadDto.builder()
            .pickingTaskId(pickingTaskDto.getId())
            .data(pickingTaskDto)
            .build()));

        return pickingTaskDto;
    }

    @RetryableTransaction
    public List<UUID> bulkCompletePickingTask(BulkCompletePickingTaskCommand command) {
        log.info("[bulkCompletePickingTask] Completing picking tasks with ids: {}", command.getPickingTaskIds());
        List<UUID> updateFailedPickingTaskIds = new ArrayList<>();
        List<PickingTaskDto> completedTasks = new ArrayList<>();
        command.getPickingTaskIds().forEach(pickingTaskId -> {
            try {
                PickingTaskDto completedTask = saveCompletePickingTask(pickingTaskId);
                if (completedTask != null) {
                    completedTasks.add(completedTask);
                }
            } catch (Exception e) {
                log.warn("[bulkCompletePickingTask] Error while completing picking task with id: {}", pickingTaskId, e);
                updateFailedPickingTaskIds.add(pickingTaskId);
            }
        });
        completedTasks.forEach(this::dispatchPickingTaskEvents);
        return updateFailedPickingTaskIds;
    }

    @RetryableTransaction
    public PickingTaskDto completePickingTask(UUID pickingTaskId) {
        log.info("[completePickingTask] Completing picking task with id: {}", pickingTaskId);
        PickingTaskDto completedTask = saveCompletePickingTask(pickingTaskId);
        if (completedTask != null) {
            dispatchPickingTaskEvents(completedTask);
        }
        return completedTask;
    }

    public void deletePickingTaskItems(List<UUID> pickingTaskItemIds) {
        log.info("[deletePickingTaskItems] Deleting picking task items with ids: {}", pickingTaskItemIds);

        List<PickingTaskItem> pickingTaskItems = pickingTaskItemRepository.findByIds(pickingTaskItemIds);
        if (CollectionUtils.isEmpty(pickingTaskItems)) {
            log.warn("[deletePickingTaskItems] No picking tasks found for the provided item IDs: {}", pickingTaskItemIds);
            return;
        }

        List<UUID> pickingTaskIds = pickingTaskItems.stream()
            .map(PickingTaskItem::getPickingTaskId)
            .distinct()
            .toList();

        if (CollectionUtils.isEmpty(pickingTaskIds)) {
            log.warn("[deletePickingTaskItems] No picking tasks found for the provided item IDs: {}", pickingTaskItemIds);
            return;
        }

        List<PickingTask> pickingTasks = pickingTaskRepository.findByIds(pickingTaskIds);
        if (pickingTasks.stream().anyMatch(this::isTaskCompletedOrPartiallyCompleted)) {
            throw new WmsBusinessException("Cannot delete items from completed or partially completed picking tasks.");
        }

        pickingTasks.forEach(task -> task.deleteItemsByIds(pickingTaskItemIds));
        pickingTaskRepository.saveAll(pickingTasks);
        pickingTaskItemRepository.deleteByIds(pickingTaskItemIds);
        crossDockTaskItemService.handlePickingTaskItemsDelete(pickingTaskItemIds);
    }

    private boolean isTaskCompletedOrPartiallyCompleted(PickingTask task) {
        return task.getStatus() == PickingTaskStatus.COMPLETED
            || task.getStatus() == PickingTaskStatus.PARTIALLY_COMPLETED;
    }

    private PickingTaskDto saveCompletePickingTask(UUID pickingTaskId) {
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        if (pickingTask == null) {
            log.error("[completePickingTaskInternal] Picking task not found with id: {}", pickingTaskId);
            return null;
        }
        if (pickingTask.getStatus().equals(PickingTaskStatus.PICKED)) {
            pickingTask.completePicking();
        } else if (pickingTask.getStatus().equals(PickingTaskStatus.FAILED)) {
            pickingTask.partiallyCompletePicking();
        }
        return pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(pickingTask));
    }

    private void dispatchPickingTaskEvents(PickingTaskDto pickingTaskDto) {
        if (PickingTaskStatus.COMPLETED.equals(pickingTaskDto.getStatus())) {
            DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                PickingTaskCompletedPayloadDto.builder()
                    .pickingTaskId(pickingTaskDto.getId())
                    .data(pickingTaskDto)
                    .build()));
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        } else if (PickingTaskStatus.PARTIALLY_COMPLETED.equals(pickingTaskDto.getStatus())) {
            DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                PickingTaskPartiallyCompletedPayloadDto.builder()
                    .pickingTaskId(pickingTaskDto.getId())
                    .data(pickingTaskDto)
                    .build()));
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        }
    }

    public List<PickingTaskDto> batchCancelPickingTask(CancelPickingTaskCommand command) {
        log.info("[batchCancelPickingTask] Cancelling picking tasks with ids: {}", command.getPickingTaskIds());
        List<PickingTaskDto> pickingTaskDtos = new ArrayList<>();
        command.getPickingTaskIds()
            .forEach(pickingTaskId -> pickingTaskDtos.add(cancelPickingTask(pickingTaskId, command.getReason())));
        return pickingTaskDtos;
    }

    public PickingTaskDto cancelPickingTask(UUID pickingTaskId, String reason) {
        log.info("Cancelling picking task with id: {}", pickingTaskId);
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);

        pickingTask.cancelTask();
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(
            pickingTask));

        businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskCanceledPayloadDto.builder()
            .pickingTaskId(pickingTaskDto.getId())
            .data(pickingTaskDto)
            .reason(reason)
            .build()));

        return pickingTaskDto;
    }

    @RetryableTransaction
    public PickingTaskDto updatePickingTask(UUID pickingTaskId, UpdatePickingTaskCommand updatePickingTaskCommand) {
        log.info("Updating picking task with id: {}", pickingTaskId);
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        PickingTaskStatus originStatus = pickingTask.getStatus();
        Map<UUID, Location> locationMap = locationCache.getLocationMap();
        Map<UUID, Integer> beforeMap = pickingTask.getPickingTaskItems().stream()
            .collect(Collectors.toMap(PickingTaskItem::getId, i -> i.getPickedQty() == null ? 0 : i.getPickedQty()));
        pickingTask.update(updatePickingTaskCommand, locationMap);
        PickingTask updated = pickingTaskRepository.update(pickingTask);
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(updated);
        dispatchEvents(pickingTaskId, pickingTask.getStatus(), originStatus, pickingTaskDto);
        if (Objects.equals(pickingTask.getType(), PickingTaskType.BATCH)) {
            try {
                List<PickingTaskItem> afterItems = pickingTask.getPickingTaskItems();
                Map<UUID, Integer> qtyChangeMap = afterItems.stream()
                    .filter(item -> !Objects.equals(beforeMap.getOrDefault(item.getId(), 0), item.getPickedQty()))
                    .collect(Collectors.toMap(PickingTaskItem::getId,
                        item -> item.getPickedQty() == null ? 0 : item.getPickedQty()));
                crossDockTaskItemService.handlePickingTaskItemsQtyChange(qtyChangeMap, pickingTask);
            } catch (Exception e) {
                log.error("[updatePickingTask] Error while handling task item quantity change for picking task id: {}",
                    pickingTaskId,
                    e);
            }
        }
        return pickingTaskDto;
    }

    private void dispatchEvents(UUID pickingTaskId,
        PickingTaskStatus newStatus,
        PickingTaskStatus originStatus,
        PickingTaskDto pickingTaskDto) {
        if (originStatus == newStatus) {
            return;
        }
        if (newStatus == PickingTaskStatus.ASSIGNED) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskAssignedPayloadDto.builder()
                .pickingTaskId(pickingTaskId)
                .data(pickingTaskDto)
                .build()));
        } else if (newStatus == PickingTaskStatus.FAILED) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskFailedPayloadDto.builder()
                .pickingTaskId(pickingTaskId)
                .data(pickingTaskDto)
                .build()));
        } else if (newStatus == PickingTaskStatus.COMPLETED) {
            DispatchResponseDto responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                PickingTaskCompletedPayloadDto.builder()
                    .pickingTaskId(pickingTaskId)
                    .data(pickingTaskDto)
                    .build()));
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        } else if (newStatus == PickingTaskStatus.PICKING) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskStartedPayloadDto.builder()
                .pickingTaskId(pickingTaskId)
                .data(pickingTaskDto)
                .build()));
        } else if (newStatus == PickingTaskStatus.PICKED) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(
                PickingTaskPickedPayloadDto.builder()
                    .pickingTaskId(pickingTaskId)
                    .data(pickingTaskDto)
                    .build()));
        }
    }

    @RetryableTransaction
    public PickingTaskDto splitPickingTask(UUID pickingTaskId, SplitPickingTaskCommand splitPickingTaskCommand) {
        PickingTask pickingTask = getPickingTaskById(pickingTaskId);
        if (pickingTask.getStatus() != PickingTaskStatus.FAILED) {
            throw new WmsBusinessException("Only failed picking task can be split.");
        }

        splitPickingTaskCommand.getSplitItemIds().forEach(itemId -> {
            if (pickingTask.getPickingTaskItems().stream().noneMatch(item -> item.getId().equals(itemId))) {
                throw new WmsBusinessException("Picking task item not found.");
            }
        });

        if (pickingTask.getPickingTaskItems().stream()
            .filter(item -> splitPickingTaskCommand.getSplitItemIds().contains(item.getId()))
            .anyMatch(item -> StringUtils.isEmpty(item.getErrorInfo()))) {
            throw new WmsBusinessException("Only failed picking task item can be split.");
        }

        List<PickingTaskItem> needSplitItems = pickingTask.getPickingTaskItems()
            .stream()
            .filter(item -> splitPickingTaskCommand.getSplitItemIds().contains(item.getId()))
            .toList();
        List<PickingTaskItem> splitPickingTaskItems = splitPickingTaskBySelectedItems(pickingTask.getId(), needSplitItems,
            splitPickingTaskCommand.getWarehouseName(), null);
        populateAisleInfoFromIMS(splitPickingTaskCommand.getWarehouseName(), splitPickingTaskItems);
        return saveSplitPickingTask(splitPickingTaskItems, pickingTask.getBatchId(), splitPickingTaskCommand);
    }

    private PickingTaskDto saveSplitPickingTask(List<PickingTaskItem> splitPickingTaskItems,
        UUID batchId,
        SplitPickingTaskCommand splitPickingTaskCommand) {
        PickingTaskDto pickingTaskDto = pickingTaskDtoApplicationMapper.domainToDto(pickingTaskRepository.save(PickingTask.builder()
            .build()
            .createSplitTask(batchId, splitPickingTaskItems, splitPickingTaskCommand)));
        dispatchEvents(pickingTaskDto.getId(), pickingTaskDto.getStatus(), PickingTaskStatus.FAILED, pickingTaskDto);
        return pickingTaskDto;
    }

    public PickingTask getPickingTaskById(UUID pickingTaskId) {
        PickingTask pickingTask = pickingTaskRepository.findById(pickingTaskId);
        if (pickingTask == null) {
            throw new ResourceNotFoundException("Picking task not found.");
        }
        return pickingTask;
    }

    @RetryableTransaction
    public PickingTaskDto bulkSplitPickingTask(BulkSplitPickingTaskCommand command) {
        List<UUID> pickingTaskItemIds = command.getPickingTaskItemIds();
        if (!CollectionUtils.isEmpty(command.getItemSuggestLocations())) {
            pickingTaskItemIds = command.getItemSuggestLocations().stream()
                .map(ItemSuggestLocation::getPickingTaskItemId)
                .filter(Objects::nonNull)
                .toList();
        }
        if (CollectionUtils.isEmpty(pickingTaskItemIds)) {
            throw new WmsBusinessException("Picking task item IDs cannot be empty.");
        }
        final String warehouseName = getWarehouseName(command);
        log.info("[bulkSplitPickingTask] Splitting picking tasks with ids: {}", pickingTaskItemIds);
        List<PickingTaskItem> pickingTaskItems = pickingTaskItemRepository.findByIds(pickingTaskItemIds);
        if (pickingTaskItems.isEmpty()) {
            throw new WmsBusinessException("Picking task items not found.");
        }
        PickingTask firstPickingTask = pickingTaskRepository.findById(pickingTaskItems.getFirst().getPickingTaskId());
        List<PickingTaskItem> splitPickingTaskItems = new LinkedList<>();
        pickingTaskItems.stream().collect(Collectors.groupingBy(PickingTaskItem::getPickingTaskId))
            .forEach((pickingTaskId, needSplitItems) -> splitPickingTaskItems.addAll(splitPickingTaskBySelectedItems(pickingTaskId,
                needSplitItems, warehouseName, command.getItemSuggestLocations())));

        if (CollectionUtils.isEmpty(command.getItemSuggestLocations())) {
            populateAisleInfoFromIMS(warehouseName, splitPickingTaskItems);
        }

        return saveSplitPickingTask(splitPickingTaskItems,
            firstPickingTask.getBatchId(),
            SplitPickingTaskCommand.builder().pickerUserId(command.getPickerUserId()).pickerUserName(
                command.getPickerUserName()).warehouseName(warehouseName).build());
    }

    private String getWarehouseName(BulkSplitPickingTaskCommand command) {
        if (command.getWarehouseId() != null) {
            Warehouse warehouse = warehouseRepository.findById(command.getWarehouseId());
            if (warehouse != null) {
                return warehouse.getName();
            } else {
                throw new WmsBusinessException("Warehouse not found for the provided ID: " + command.getWarehouseId());
            }
        } else {
            return command.getWarehouseName();
        }
    }

    public ByteArrayOutputStream failedItemExport(String deliveryDate) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            List<FailedPickingTaskItemDto> failedPickingTaskItemDtoList = new ArrayList<>(pickingTaskItemQueryService.searchBy(
                    deliveryDate,
                    null,
                    null,
                    Pageable.ofSize(500))
                .getContent());
            return writeTemplate(outputStream, failedPickingTaskItemDtoList);
        } catch (Exception e) {
            log.error("[failedItemExport] Error while exporting failed picking task items.", e);
        }
        return writeTemplate(new ByteArrayOutputStream(), List.of());
    }

    public ByteArrayOutputStream writeTemplate(ByteArrayOutputStream outputStream,
        List<FailedPickingTaskItemDto> failedPickingTaskItemDtoList) {
        Resource resource = new ClassPathResource("template/failed-item-list.xlsx");
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).withTemplate(resource.getInputStream()).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            if (CollectionUtils.isEmpty(failedPickingTaskItemDtoList)) {
                failedPickingTaskItemDtoList = Lists.newArrayList();
            }
            WriteSheet writeSheet = writerSheet(BatchConstants.FAILED_ITEM_LIST_SHEET_NAME).build();
            excelWriter.fill(failedPickingTaskItemDtoList, fillConfig, writeSheet);
            excelWriter.finish();
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to write replenishment report.", e);
        }
        return outputStream;
    }

    public void bulkInventoryTransfer(Batch batch) {
        List<PickingTask> batchPickingTasks = pickingTaskRepository.findByBatchId(batch.getId());
        List<PickingTask> eligibleTasks = filterEligiblePickingTasks(batchPickingTasks);

        if (areAllTasksCompletedOrPartiallyCompleted(eligibleTasks)) {

            processCompletedTasks(batch, eligibleTasks);

            if (featureFlagsManager.isFeatureOn(TRANSFER_LOST_FOUND_INVENTORY)) {
                List<PickingTask> partiallyCompletedTasks = filterPartiallyCompletedTasks(eligibleTasks);
                if (!partiallyCompletedTasks.isEmpty()) {
                    transferUnpickedItemsToLostFound(batch, partiallyCompletedTasks);
                } else {
                    log.info("[bulkInventoryTransfer] No picking tasks to transfer to lost and found.");
                }
            }
            batchRepository.update(batch);
        } else {
            long incompleteCount = countIncompleteTasks(eligibleTasks);
            log.info("[bulkInventoryTransfer] still has {} eligible picking task(s) not completed", incompleteCount);
        }
    }

    private List<PickingTask> filterEligiblePickingTasks(List<PickingTask> tasks) {
        return tasks.stream()
            .filter(task -> task.getStatus() != PickingTaskStatus.CANCELED)
            .filter(task -> task.getSource() == SourceEnum.MDC || task.getSource() == SourceEnum.MFC)
            .toList();
    }

    private boolean areAllTasksCompletedOrPartiallyCompleted(List<PickingTask> tasks) {
        return tasks.stream().allMatch(this::isCompletedOrPartiallyCompleted);
    }

    private List<PickingTask> filterPartiallyCompletedTasks(List<PickingTask> tasks) {
        return tasks.stream()
            .filter(task -> task.getStatus() == PickingTaskStatus.PARTIALLY_COMPLETED)
            .toList();
    }

    private long countIncompleteTasks(List<PickingTask> tasks) {
        return tasks.stream().filter(task -> !isCompletedOrPartiallyCompleted(task)).count();
    }

    private boolean isCompletedOrPartiallyCompleted(PickingTask task) {
        return task.getStatus() == PickingTaskStatus.COMPLETED
            || task.getStatus() == PickingTaskStatus.PARTIALLY_COMPLETED;
    }

    private void transferUnpickedItemsToLostFound(Batch batch, List<PickingTask> partiallyCompletedTasks) {
        Location lostFoundLocation = locationRepository.findByName(finaleConfigProperties.getSubLocations().getLostFound());
        Map<UUID, Location> locationMap = locationCache.getLocationMap();

        log.info(
            "[transferUnpickedItemsToLostFound] Transferring unpicked items to lost and found for batch: {}, lostFoundLocation: {}, partiallyCompletedTasks: {}",
            batch.getId(),
            lostFoundLocation,
            partiallyCompletedTasks);

        if (lostFoundLocation == null) {
            log.warn("[transferUnpickedItemsToLostFound] Lost and found location not found, skipping transfer.");
            return;
        }

        if (CollectionUtils.isEmpty(partiallyCompletedTasks)) {
            log.warn("[transferUnpickedItemsToLostFound] No partially completed tasks to transfer to lost and found.");
            return;
        }

        if (CollectionUtils.isEmpty(locationMap)) {
            log.warn("[transferUnpickedItemsToLostFound] Location map is empty, cannot transfer items to lost and found.");
            return;
        }

        // Build shipment data - facilityUrl in ShipmentItem already contains original location info
        Map<String, List<ShipmentItem>> shipmentItemsBySku = buildLostFoundShipmentData(
            partiallyCompletedTasks, locationMap, lostFoundLocation
        );

        String deliveryDate = batch.getTag();
        log.info("[transferUnpickedItemsToLostFound] Delivery date: {}, shipmentItemsBySku: {}",
            deliveryDate,
            shipmentItemsBySku);
        // Create single transfer shipment for all lost found items
        FinaleTransferShipmentResponse response = processFinaleTransfers(shipmentItemsBySku,
            deliveryDate,
            FinaleEntityTypeEnum.TRANSFER_TO_LOST_FOUND);

        if (response != null && StringUtils.isNotEmpty(response.getShipmentIdUser())) {
            updateFinaleEntities(batch,
                response.getShipmentIdUser(),
                response.getShipmentId(),
                FinaleEntityTypeEnum.TRANSFER_TO_LOST_FOUND);
        } else {
            log.warn("[transferUnpickedItemsToLostFound] No final transfer shipments found for lost found items");
        }
    }

    /**
     * Build shipment data for lost found transfer ShipmentItem.facilityUrl already contains original location info, so no need to
     * group by location
     */
    private Map<String, List<ShipmentItem>> buildLostFoundShipmentData(
        List<PickingTask> partiallyCompletedTasks,
        Map<UUID, Location> locationMap,
        Location lostFoundLocation
    ) {
        Map<String, List<ShipmentItem>> shipmentItemsBySku = new HashMap<>();

        partiallyCompletedTasks.forEach(task ->
            task.getPickingTaskItems().forEach(item ->
                processUnpickedItemForLostFound(item, locationMap, lostFoundLocation, shipmentItemsBySku)
            )
        );

        return shipmentItemsBySku;
    }

    /**
     * Process unpicked item for lost found transfer
     */
    private void processUnpickedItemForLostFound(
        PickingTaskItem item,
        Map<UUID, Location> locationMap,
        Location lostFoundLocation,
        Map<String, List<ShipmentItem>> shipmentItemsBySku
    ) {
        Location originLocation = locationMap.get(item.getLocationId());
        if (originLocation == null) {
            log.warn("[transferUnpickedItemsToLostFound] Location not found for item: {}", item);
            return;
        }

        if (originLocation.getFinaleId() == null) {
            log.warn("[transferUnpickedItemsToLostFound] Origin location {} does not have a Finale ID, skipping item: {}",
                originLocation.getName(), item);
            return;
        }

        int pickedQty = defaultIfNull(item.getPickedQty());
        int expectedQty = defaultIfNull(item.getExpectQty());

        if (pickedQty == expectedQty) {
            log.info("[transferUnpickedItemsToLostFound] Item {} has been fully picked, skipping transfer to lost and found.",
                item.getSkuNumber());
            return;
        }

        int unpickedQty = expectedQty - pickedQty;

        // Find existing shipment item with same SKU and same original location (facilityUrl)
        List<ShipmentItem> existingItems = shipmentItemsBySku.computeIfAbsent(item.getSkuNumber(), k -> new ArrayList<>());
        log.info("[transferUnpickedItemsToLostFound] Processing task: {}, item: {}, unpickedQty: {}, originLocation: {}",
            item.getPickingTaskId(), item.getSkuNumber(), unpickedQty, originLocation.getFinaleId());

        ShipmentItem existingItem = existingItems.stream()
            .filter(dto -> Objects.equals(dto.getOriginFinaleLocationId(), originLocation.getFinaleId()))
            .findFirst()
            .orElse(null);

        if (existingItem != null) {
            // Accumulate quantity for existing item with same SKU and same original location
            existingItem.setQuantity(existingItem.getQuantity() + unpickedQty);
        } else {
            // Create new shipment item (facilityUrl will contain original location info)
            ShipmentItem newShipmentItem = buildShipmentItem(item, originLocation, lostFoundLocation);
            newShipmentItem.setQuantity(unpickedQty);
            existingItems.add(newShipmentItem);
        }
    }

    private int defaultIfNull(Integer value) {
        return value != null ? value : 0;
    }

    private void processCompletedTasks(Batch batch, List<PickingTask> pickingTaskList) {
        Location destinationLocation = locationRepository.findByName(finaleConfigProperties.getShipSb());
        Map<UUID, Location> locationMap = locationCache.getLocationMap();
        Map<String, List<ShipmentItem>> shipmentItemMap = new HashMap<>();

        pickingTaskList.forEach(task -> task.getPickingTaskItems().forEach(item -> {
            Location originLocation = locationMap.get(item.getLocationId());
            if (originLocation == null) {
                log.warn("[bulkInventoryTransfer] Location not found for item: {}", item);
                return;
            }
            if (item.getPickedQty() == null || item.getPickedQty() <= 0) {
                log.warn("[bulkInventoryTransfer] Invalid picked quantity for item: {}", item);
                return;
            }
            shipmentItemMap.computeIfAbsent(item.getSkuNumber(), k -> new ArrayList<>())
                .stream()
                .filter(dto -> StringUtils.contains(dto.getFacilityUrl(), originLocation.getFinaleId()))
                .findFirst()
                .ifPresentOrElse(
                    dto -> dto.setQuantity(dto.getQuantity() + item.getPickedQty()),
                    () -> shipmentItemMap.get(item.getSkuNumber())
                        .add(buildShipmentItem(item, originLocation, destinationLocation))
                );
        }));

        FinaleTransferShipmentResponse response = processFinaleTransfers(shipmentItemMap,
            batch.getTag(),
            FinaleEntityTypeEnum.TRANSFER_SHIPMENT);

        if (response != null && StringUtils.isNotEmpty(response.getShipmentIdUser())) {
            batch.setFinaleTransferShipmentNumber(response.getShipmentIdUser());
            updateFinaleEntities(batch,
                response.getShipmentIdUser(),
                response.getShipmentId(),
                FinaleEntityTypeEnum.TRANSFER_SHIPMENT);
        } else {
            log.warn("[bulkInventoryTransfer] No final transfer shipments found.");
        }
    }

    private void updateFinaleEntities(Batch batch, String shipmentIdUser, String shipmentId,
        FinaleEntityTypeEnum finaleEntityType) {
        JsonNode finaleEntities = batch.getFinaleEntities();
        List<FinaleEntity> finaleEntityList = Optional.ofNullable(finaleEntities)
            .filter(node -> !node.isNull())
            .map(node -> new ObjectMapper().convertValue(node, new TypeReference<List<FinaleEntity>>() {
            }))
            .orElseGet(ArrayList::new);

        // Check for existing entity of the same type to prevent duplicates
        boolean entityExists = finaleEntityList.stream()
            .anyMatch(entity -> entity.getEntityType() == finaleEntityType);

        if (entityExists) {
            log.warn("[updateFinaleEntities] FinaleEntity of type {} already exists for batch {}, skipping duplicate creation",
                finaleEntityType, batch.getId());
            return;
        }

        FinaleEntity finaleEntity = new FinaleEntity();
        finaleEntity.setEntityId(shipmentIdUser);
        String transferShipmentId = TRANSFER_SHIPMENT_ID_PREFIX + shipmentId;
        finaleEntity.setEntityUrl(
            TRANSFER_SHIPMENT_URL_PREFIX + Base64.getEncoder().encodeToString(transferShipmentId.getBytes()));
        finaleEntity.setEntityType(finaleEntityType);
        finaleEntityList.add(finaleEntity);

        batch.setFinaleEntities(SerializationUtils.toTree(finaleEntityList));
    }

    private FinaleTransferShipmentResponse processFinaleTransfers(Map<String, List<ShipmentItem>> shipmentItemMap,
        String deliveryDate, FinaleEntityTypeEnum finaleEntityType) {
        if (CollectionUtils.isEmpty(shipmentItemMap)) {
            log.warn("[bulkInventoryTransfer] Type is {}, no shipment items found", finaleEntityType);
            return null;
        }

        List<FinaleTransferShipmentDto.ShipmentItem> shipmentItems = shipmentItemMap.values().stream()
            .flatMap(List::stream)
            .toList();

        String mfcId = finaleConfigProperties.getMfcFacilityUrl()
            .substring(finaleConfigProperties.getMfcFacilityUrl().lastIndexOf("/") + 1);

        FinaleTransferShipmentDto transferDto = new FinaleTransferShipmentDto();
        transferDto.setReceiveDateEstimated(LocalDate.now().toString());
        transferDto.setShipDateEstimated(LocalDate.now().toString());
        transferDto.setOriginFacilityUrl(finaleConfigProperties.getMfcFacilityUrl());
        transferDto.setOriginLocationId(mfcId);
        transferDto.setDestinationFacilityUrl(finaleConfigProperties.getMfcFacilityUrl());
        transferDto.setDestinationLocationId(mfcId);
        transferDto.setPrivateNotes("Create transfer [" + finaleEntityType + "] task for delivery date: " + deliveryDate);

        transferDto.setShipmentItemList(shipmentItems);
        transferDto.setFinaleEntityType(finaleEntityType);

        log.info("[bulkInventoryTransfer] Type is {}, creating transfer shipment with transferDto: {}",
            finaleEntityType,
            transferDto.getShipmentTypeId());
        FinaleTransferShipmentResponse response = finaleProductService.createTransferShipment(transferDto);

        if (response != null) {
            log.info("[bulkInventoryTransfer] Type is {}, shipment created: {}", finaleEntityType, response.getShipmentIdUser());
            if (StringUtils.isNotEmpty(response.getShipmentId())) {
                handleShipment(response.getShipmentId(), finaleEntityType);
            }
        } else {
            log.warn("[bulkInventoryTransfer]  Type is {},failed to create transfer shipment, response is null",
                finaleEntityType);
        }
        return response;
    }

    private void handleShipment(String shipmentId, FinaleEntityTypeEnum finaleEntityType) {
        FinaleTransferShipmentResponse shippedShipment = finaleProductService.shipTransferShipment(shipmentId);
        if (shippedShipment != null && StringUtils.isNotEmpty(shippedShipment.getShipDate())) {
            FinaleTransferShipmentResponse receivedShipment = finaleProductService.receiveTransferShipment(shipmentId);
            if (receivedShipment != null && StringUtils.isNotEmpty(receivedShipment.getReceiveDate())) {
                log.info("[bulkInventoryTransfer] Type is {}, successfully shipped and received shipment with id: {}",
                    finaleEntityType,
                    shipmentId);
            } else {
                log.error("[bulkInventoryTransfer]  Type is {}, Error receiving shipment with id: {}",
                    finaleEntityType,
                    shipmentId);
            }
        } else {
            log.error("[bulkInventoryTransfer]  Type is {}, Error shipping shipment with id: {}", finaleEntityType, shipmentId);
        }
    }


    private ShipmentItem buildShipmentItem(PickingTaskItem pickingTaskItem, Location originLocation,
        Location destinationLocation) {
        ShipmentItem shipmentItem = new ShipmentItem();
        shipmentItem.setOriginFinaleLocationId(originLocation.getFinaleId());
        shipmentItem.setProductUrl(
            "/" + finaleConfigProperties.getDomain() + PRODUCT_URL_PREFIX + pickingTaskItem.getSkuNumber());
        shipmentItem.setFacilityUrl(
            "/" + finaleConfigProperties.getDomain() + FACILITY_URL_PREFIX + originLocation.getFinaleId());
        shipmentItem.setQuantity(pickingTaskItem.getPickedQty());
        shipmentItem.setFacilityUrlReceive(
            "/" + finaleConfigProperties.getDomain() + FACILITY_URL_PREFIX + destinationLocation.getFinaleId());
        log.info("[bulkInventoryTransfer] Shipment item: {}", shipmentItem);
        return shipmentItem;
    }

    private void populateAisleInfoFromIMS(String warehouseName, List<PickingTaskItem> splitPickingTaskItems) {
        try {
            Set<UUID> itemIds = splitPickingTaskItems.stream().map(PickingTaskItem::getItemId).collect(Collectors.toSet());
            List<ItemCategoryDto> items = imsAdaptor.getItemsByIds(itemIds.stream().toList());
            splitPickingTaskItems.forEach(item -> {
                ItemCategoryDto itemCategoryDto = items.stream()
                    .filter(i -> Objects.equals(i.getId(), item.getItemId()))
                    .findFirst()
                    .orElse(null);
                item.setLocationName(getAisleInfoBy(itemCategoryDto, warehouseName));
                item.setAisleNumber(getAisleInfoBy(itemCategoryDto, warehouseName));
            });
            splitPickingTaskItems.sort(Comparator.comparing(PickingTaskItem::getLocationName,
                    Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(PickingTaskItem::getDepartment, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(PickingTaskItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));
        } catch (Exception e) {
            log.warn("[bulkInventoryTransfer] Error while getting aisle info from IMS.", e);
        }
    }

    private String getAisleInfoBy(ItemCategoryDto dto, String warehouseName) {
        if (dto == null) {
            return "N/A";
        }
        if (CollectionUtils.isEmpty(dto.getVendorItemDtos()) || dto.getBackupVendorId() == null) {
            return dto.getDepartment();
        }
        for (VendorItemDto vendorItemDto : dto.getVendorItemDtos()) {
            if (Objects.equals(dto.getBackupVendorId(), vendorItemDto.getVendorId())
                && SourceEnum.fromVendorName(vendorItemDto.getVendorName()) != null &&
                Objects.equals(Objects.requireNonNull(SourceEnum.fromVendorName(vendorItemDto.getVendorName())).name(),
                    warehouseName)) {
                return vendorItemDto.getAisle();
            }
        }
        return dto.getDepartment() != null ? dto.getDepartment() : "N/A";
    }

    private List<PickingTaskItem> splitPickingTaskBySelectedItems(UUID pickingTaskId,
        List<PickingTaskItem> needSplitItems,
        String warehouseName,
        List<ItemSuggestLocation> itemSuggestLocations) {

        List<PickingTaskItem> splitPickingTaskItems = new LinkedList<>();
        PickingTask pickingTask = pickingTaskRepository.findById(pickingTaskId);
        if (pickingTask.getStatus() != PickingTaskStatus.FAILED
            && PickingTaskStatus.PARTIALLY_COMPLETED != pickingTask.getStatus()) {
            throw new WmsBusinessException("The picking task %s status is not correct, can not split.", pickingTask.getNumber());
        }
        List<PickingTaskItem> needRemoveItems = new LinkedList<>();
        List<PickingTaskItem> needUpdateItems = new LinkedList<>();

        needSplitItems.forEach(item -> {
            if (item.getPickedQty() == 0) {
                PickingTaskItem newPickingTaskItem = PickingTaskItem.builder().build();
                BeanUtils.copyProperties(item, newPickingTaskItem, "id", "pickingTaskId", "errorInfo");
                populateAisleInfoFromLocation(itemSuggestLocations, newPickingTaskItem, item.getId());
                splitPickingTaskItems.add(newPickingTaskItem);
                needRemoveItems.add(item);
            } else if (item.getExpectQty() > item.getPickedQty()) {
                BatchItem savedNewBatchItem = updateAndCreateNewBatchItem(item, warehouseName);

                PickingTaskItem newPickingTaskItem = PickingTaskItem.builder().build();
                BeanUtils.copyProperties(item, newPickingTaskItem, "id", "pickingTaskId", "errorInfo");
                newPickingTaskItem.setExpectQty(item.getExpectQty() - item.getPickedQty());
                newPickingTaskItem.setPickedQty(0);
                newPickingTaskItem.setBatchItemId(savedNewBatchItem.getId());
                populateAisleInfoFromLocation(itemSuggestLocations, newPickingTaskItem, item.getId());
                splitPickingTaskItems.add(newPickingTaskItem);
                item.setExpectQty(item.getPickedQty());
                item.setErrorInfo(null);
                needUpdateItems.add(item);
            }
        });

        pickingTask.removePickingTaskItems(needRemoveItems);
        pickingTask.updatePickingTaskItems(needUpdateItems);
        pickingTask.update(true);
        pickingTask = pickingTaskRepository.update(pickingTask);
        dispatchEvents(pickingTaskId,
            pickingTask.getStatus(),
            PickingTaskStatus.FAILED,
            pickingTaskDtoApplicationMapper.domainToDto(pickingTask));
        return splitPickingTaskItems;
    }

    private void populateAisleInfoFromLocation(List<ItemSuggestLocation> itemSuggestLocations,
        PickingTaskItem splitPickingTaskItem,
        UUID targetPickingTaskItemId) {
        if (CollectionUtils.isEmpty(itemSuggestLocations)) {
            return;
        }

        itemSuggestLocations.stream()
            .filter(itemLocation -> targetPickingTaskItemId.equals(itemLocation.getPickingTaskItemId()))
            .findFirst()
            .ifPresent(matchedLocation -> setLocationInfo(splitPickingTaskItem, matchedLocation.getLocationId()));
    }

    private void setLocationInfo(PickingTaskItem pickingTaskItem, UUID locationId) {
        Location location = locationCache.getLocationMap().get(locationId);
        if (location == null) {
            throw new WmsBusinessException("Location not found for id: " + locationId);
        }
        pickingTaskItem.setLocationId(location.getId());
        pickingTaskItem.setLocationName(location.getName());
        pickingTaskItem.setAisleNumber(location.getAisleNumber());
    }

    private BatchItem updateAndCreateNewBatchItem(PickingTaskItem item, String warehouseName) {
        BatchItem batchItem = batchItemRepository.findById(item.getBatchItemId());
        batchItem.setExpectQty(item.getPickedQty());
        batchItemRepository.update(batchItem);
        BatchItem newBatchItem = BatchItem.builder().build();
        BeanUtils.copyProperties(batchItem, newBatchItem, "id", "expectQty");
        newBatchItem.setExpectQty(item.getExpectQty() - item.getPickedQty());
        newBatchItem.setSource(warehouseName);
        return batchItemRepository.save(newBatchItem);
    }

    @RetryableTransaction
    public List<PickingTaskDto> batchUnassignPickingTasks(List<UUID> pickingTaskIds) {
        List<PickingTask> pickingTasks = pickingTaskRepository.findByIds(pickingTaskIds);
        if (CollectionUtils.isEmpty(pickingTasks)) {
            throw new ResourceNotFoundException("Picking tasks not found.");
        }

        List<PickingTask> assignedTasks = pickingTasks.stream()
            .filter(task -> PickingTaskStatus.ASSIGNED.equals(task.getStatus()))
            .toList();

        if (CollectionUtils.isEmpty(assignedTasks)) {
            log.info("[batchUnassignPickingTasks] No assigned picking tasks found to unassign.");
            return Collections.emptyList();
        }
        assignedTasks.forEach(PickingTask::unassignTask);
        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(assignedTasks);

        List<PickingTaskDto> pickingTaskDtos = pickingTaskDtoApplicationMapper.domainToDtos(savedTasks);
        pickingTaskDtos.forEach(dto -> businessEventDispatcher.dispatch(BusinessEventFactory.build(PickingTaskUnassignedPayloadDto.builder()
            .pickingTaskId(dto.getId())
            .data(dto)
            .build())));
        return pickingTaskDtos;
    }

    @Transactional
    public List<PickingTaskDto> batchUpdatePickingTaskSource(BatchUpdatePickingTaskSourceCommand command) {
        log.info("[batchUpdatePickingTaskSource] Updating source for picking tasks with ids: {}, new source: {}",
            command.getPickingTaskIds(), command.getSource());

        List<PickingTask> pickingTasks = pickingTaskRepository.findByIds(command.getPickingTaskIds());
        if (pickingTasks.isEmpty()) {
            throw new ResourceNotFoundException("Picking tasks not found.");
        }

        // Validate that all tasks are in CREATED or ASSIGNED status
        List<PickingTask> invalidStatusTasks = pickingTasks.stream()
            .filter(task -> !List.of(PickingTaskStatus.CREATED, PickingTaskStatus.ASSIGNED).contains(task.getStatus()))
            .toList();

        if (!invalidStatusTasks.isEmpty()) {
            String invalidTaskNumbers = invalidStatusTasks.stream()
                .map(PickingTask::getNumber)
                .collect(Collectors.joining(", "));
            throw new WmsBusinessException("Cannot update source for picking tasks: " + invalidTaskNumbers +
                ". Only tasks with CREATED or ASSIGNED status can be updated.");
        }

        // Validate that the new source is in PICKING_VENDOR_SET
        if (!SourceEnum.pickingVendors().contains(command.getSource())) {
            throw new WmsBusinessException("Invalid source: " + command.getSource() +
                ". Only sources from PICKING_VENDOR_SET are allowed: " + SourceEnum.pickingVendors());
        }

        // Update source for all valid tasks
        pickingTasks.forEach(task -> task.updateSource(command.getSource()));

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(pickingTasks);
        List<PickingTaskDto> pickingTaskDtos = pickingTaskDtoApplicationMapper.domainToDtos(savedTasks);

        log.info("[batchUpdatePickingTaskSource] Successfully updated source for {} picking tasks", savedTasks.size());
        return pickingTaskDtos;
    }
}
