package com.mercaso.wms.application.queryservice;

import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.mapper.inventorystock.InventoryStockDtoApplicationMapper;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.location.enums.LocationPriority;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryStockQueryService {

    private final InventoryStockRepository inventoryStockRepository;
    private final InventoryStockDtoApplicationMapper inventoryStockDtoApplicationMapper;
    private final PickingTaskItemRepository pickingTaskItemRepository;

    public InventoryStockDto findById(UUID id) {
        return inventoryStockDtoApplicationMapper.domainToDto(inventoryStockRepository.findById(id));
    }

    public Map<String, List<InventoryStockDto>> findByWarehouseIdAndSkuNumbers(UUID warehouseId, List<String> skuNumbers) {
        List<InventoryStock> inventoryStocks = inventoryStockRepository.findByWarehouseIdAndSkuNumbers(warehouseId, skuNumbers);
        return inventoryStocks.stream()
            .map(inventoryStockDtoApplicationMapper::domainToDto)
            .collect(Collectors.groupingBy(
                InventoryStockDto::getSkuNumber,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> list.stream()
                        .sorted(Comparator.comparing(stock -> {
                            if (stock.getLocation() == null || stock.getLocation().getName() == null) {
                                return Integer.MAX_VALUE; // Put null locations at the end
                            }
                            return LocationPriority.getPriority(stock.getLocation().getName());
                        }))
                        .toList()
                )
            ));
    }

    public Map<String, List<InventoryStockDto>> findByWarehouseIdAndPickingTaskItemIds(UUID warehouseId,
        List<UUID> pickingTaskItemIds) {
        // Query picking task items to get SKU numbers and unpicked quantities
        List<PickingTaskItem> pickingTaskItems = pickingTaskItemRepository.findByIds(pickingTaskItemIds);

        // Return empty map if no picking task items found
        if (pickingTaskItems == null || pickingTaskItems.isEmpty()) {
            return new HashMap<>();
        }

        // Group by SKU number and calculate total unpicked quantity
        Map<String, Integer> unpickedQtyMap = pickingTaskItems.stream()
            .collect(Collectors.groupingBy(
                PickingTaskItem::getSkuNumber,
                Collectors.summingInt(item -> item.getExpectQty() - item.getPickedQty())
            ));

        List<String> skuNumbers = unpickedQtyMap.keySet().stream().toList();

        // Query inventory stocks using existing logic
        List<InventoryStock> inventoryStocks = inventoryStockRepository.findByWarehouseIdAndSkuNumbers(warehouseId, skuNumbers);

        // Return empty map if no inventory stocks found
        if (inventoryStocks == null || inventoryStocks.isEmpty()) {
            return new HashMap<>();
        }

        // Convert to DTOs and group by SKU number
        Map<String, List<InventoryStockDto>> result = inventoryStocks.stream()
            .map(inventoryStockDtoApplicationMapper::domainToDto)
            .collect(Collectors.groupingBy(
                InventoryStockDto::getSkuNumber,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> list.stream()
                        .sorted(Comparator.comparing(stock -> {
                            if (stock.getLocation() == null || stock.getLocation().getName() == null) {
                                return Integer.MAX_VALUE; // Put null locations at the end
                            }
                            return LocationPriority.getPriority(stock.getLocation().getName());
                        }))
                        .toList()
                )
            ));

        // Calculate location type, detail, and QOH for each SKU
        result.replaceAll((skuNumber, stocks) -> {
            int totalUnpickedQty = unpickedQtyMap.getOrDefault(skuNumber, 0);
            return calculateLocationTypeAndQoh(stocks, totalUnpickedQty);
        });

        return result;
    }

    private List<InventoryStockDto> calculateLocationTypeAndQoh(List<InventoryStockDto> stocks, int totalUnpickedQty) {
        // Filter stocks with valid locations and non-negative quantities
        List<InventoryStockDto> validStocks = stocks.stream()
            .filter(stock -> stock.getLocation() != null && stock.getLocation().getType() != null)
            .filter(stock -> stock.getQty().intValue() >= 0) // Filter out negative quantities
            .toList();

        // Handle case when no valid stocks found
        if (validStocks.isEmpty()) {
            // Set default values for all stocks
            stocks.forEach(stock -> {
                stock.setLocationType("NONE");
                stock.setDetail(List.of("NONE:0"));
                stock.setQoh(0);
            });
            return stocks;
        }

        // Group by location type and sum quantities
        Map<LocationType, Integer> typeQtyMap = validStocks.stream()
            .collect(Collectors.groupingBy(
                stock -> stock.getLocation().getType(),
                Collectors.summingInt(stock -> stock.getQty().intValue())
            ));

        int binQty = typeQtyMap.getOrDefault(LocationType.BIN, 0);
        int stockQty = typeQtyMap.getOrDefault(LocationType.STOCK, 0);

        String locationType;
        List<String> detail;
        int qoh;

        if (binQty > 0 && stockQty == 0) {
            // Only BIN
            locationType = "BIN";
            detail = buildLocationDetailList(validStocks, LocationType.BIN);
            qoh = binQty;
        } else if (binQty == 0 && stockQty > 0) {
            // Only STOCK
            locationType = "STOCK";
            detail = buildLocationDetailList(validStocks, LocationType.STOCK);
            qoh = stockQty;
        } else if (binQty > 0 && stockQty > 0) {
            // Both BIN and STOCK
            if (binQty >= totalUnpickedQty) {
                locationType = "BIN";
                detail = buildLocationDetailList(validStocks, LocationType.BIN);
                qoh = binQty;
            } else {
                locationType = "BIN+STOCK";
                List<String> binDetails = buildLocationDetailList(validStocks, LocationType.BIN);
                List<String> stockDetails = buildLocationDetailList(validStocks, LocationType.STOCK);
                detail = new ArrayList<>();
                detail.addAll(binDetails);
                detail.addAll(stockDetails);
                qoh = binQty + stockQty;
            }
        } else {
            // No inventory
            locationType = "NONE";
            detail = List.of("NONE:0");
            qoh = 0;
        }

        // Set calculated fields for all stocks
        stocks.forEach(stock -> {
            stock.setLocationType(locationType);
            stock.setDetail(detail);
            stock.setQoh(qoh);
        });

        // If BIN can cover the unpicked qty and we set locationType to BIN, exclude STOCK entries from the returned list
        if ("BIN".equals(locationType)) {
            return stocks.stream()
                .filter(s -> s.getLocation() != null && s.getLocation().getType() == LocationType.BIN)
                .toList();
        }

        return stocks;
    }

    private List<String> buildLocationDetailList(List<InventoryStockDto> validStocks, LocationType locationType) {
        return validStocks.stream()
            .filter(stock -> stock.getLocation().getType() == locationType)
            .map(stock -> "[" + stock.getLocation().getType() + "]" + stock.getLocation().getName() + "(" + stock.getQty()
                .intValue() + ")")
            .toList();
    }
}
