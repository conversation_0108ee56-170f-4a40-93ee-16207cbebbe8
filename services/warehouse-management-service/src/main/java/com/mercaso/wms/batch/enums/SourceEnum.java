package com.mercaso.wms.batch.enums;

import java.util.Collections;
import java.util.EnumSet;
import java.util.Set;
import lombok.Getter;

@Getter
public enum SourceEnum {

    COSTCO("Costco"),
    MISSION("Mission"),
    DOWNEY("Downey Wholesale"),
    MFC(null),
    MD<PERSON>(null),
    VERNON("Vernon Sales"),
    EXOTIC("Exotic Blvd"),
    SEVEN_STARS("7 Star Savings"),
    <PERSON><PERSON><PERSON>("Jetro"),
    SMART_AND_FINAL("Smart & Final"),
    CORE_MARK("Core Mark"),
    DOLLAR_EMPIRE("Dollar Empire"),
    ;

    private final String vendorName;

    private static final Set<SourceEnum> ONLINE_VENDOR_SET = EnumSet.of(
        MISSION,
        VERNON,
        EXOTIC,
        SEVEN_STARS,
        SMART_AND_FINAL,
        CORE_MARK,
        DOLLAR_EMPIRE
    );

    private static final Set<SourceEnum> PICKING_VENDOR_SET = EnumSet.of(
        COSTCO,
        DOWNEY
    );

    public static Set<SourceEnum> onlineVendors() {
        return Collections.unmodifiableSet(ONLINE_VENDOR_SET);
    }

    public static Set<SourceEnum> pickingVendors() {
        return Collections.unmodifiableSet(PICKING_VENDOR_SET);
    }

    SourceEnum(String vendorName) {
        this.vendorName = vendorName;
    }

    public static SourceEnum fromName(String value) {
        for (SourceEnum sourceEnum : SourceEnum.values()) {
            if (sourceEnum.name().equals(value)) {
                return sourceEnum;
            }
        }
        throw new IllegalArgumentException("Invalid source: " + value);
    }

    public static SourceEnum fromVendorName(String vendorName) {
        for (SourceEnum sourceEnum : SourceEnum.values()) {
            if (sourceEnum.vendorName != null && sourceEnum.vendorName.equalsIgnoreCase(vendorName)) {
                return sourceEnum;
            }
        }
        return null;
    }

}
