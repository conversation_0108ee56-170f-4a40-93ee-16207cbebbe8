package com.mercaso.wms.infrastructure.slackalert;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.NoteAttributeDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShippingAddressDto;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class FraudOrderDetectionServiceTest {

    @Mock
    private CustomerAddressRepository customerAddressRepository;

    @Mock
    private ShippingOrderRepository shippingOrderRepository;

    @InjectMocks
    private FraudOrderDetectionService fraudOrderDetectionService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(fraudOrderDetectionService, "slackWebhook", "https://hooks.slack.com/test");
        ReflectionTestUtils.setField(fraudOrderDetectionService, "enable", true);
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudIps", Arrays.asList("***********", "********"));
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudDeviceIds", Arrays.asList("device123", "device456"));
        ReflectionTestUtils.setField(fraudOrderDetectionService,
            "fraudPhoneNumbers",
            Arrays.asList("+1234567890", "+0987654321"));
    }

    @Test
    void when_detectFraudOrder_and_noteAttributesEmpty_then_noAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = ShopifyOrderDto.builder()
            .name("ORDER-001")
            .noteAttributes(Collections.emptyList())
            .build();

        // When

        fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

        // Then
        verify(customerAddressRepository, never()).findByLatitudeAndLongitude(any(), any());
    }

    @Test
    void when_detectFraudOrder_and_highRiskIP_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "safe_device");

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When

            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                eq("https://hooks.slack.com/test"), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_highRiskDeviceId_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("*************", "device123");

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When

            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_newUser_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("*************", "safe_device");

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_newAddress_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("*************", "safe_device");

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_achPayment_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoWithPayment();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When

            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_slackDisabled_then_noAlert() {
        // Given
        ReflectionTestUtils.setField(fraudOrderDetectionService, "enable", false);
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "safe_device");

        // Mock repositories to prevent any alert detection (even though slack is disabled)
        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment(anyString(), eq("Tobacco")))
            .thenReturn(Collections.singletonList(createTestShippingOrderWithTobacco()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_nullShippingAddress_then_noException() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .shippingAddress(null)
            .noteAttributes(Arrays.asList(
                new NoteAttributeDto("ip", "***********"),
                new NoteAttributeDto("device_id", "safe_device"),
                new NoteAttributeDto("payment_type", "CREDIT_CARD")
            ))
            .build();

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When

            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not throw exception and should send alert for high risk IP
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_fraudListsEmpty_then_noHighRiskAlert() {
        // Given
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudIps", Collections.emptyList());
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudDeviceIds", Collections.emptyList());
        ReflectionTestUtils.setField(fraudOrderDetectionService, "fraudPhoneNumbers", Collections.emptyList());

        ShippingOrder shippingOrder = createTestShippingOrder();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDto("***********", "device123");

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        // Mock shipping order repository to return existing tobacco order to prevent first tobacco alert
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment(anyString(), eq("Tobacco")))
            .thenReturn(Collections.singletonList(createTestShippingOrderWithTobacco()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send high risk alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_highSkuQuantity_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrderWithHighQuantity();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighQuantityTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_highValueOrder_then_sendAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrderWithHighValue();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighValueTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_highSkuQuantityAndHighValue_then_sendHighSkuQuantityAlert() {
        // Given - Order with both high quantity and high value, should trigger high quantity alert first
        ShippingOrder shippingOrder = createTestShippingOrderWithHighQuantityAndHighValue();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighQuantityTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should send alert for high SKU quantity (higher priority than high value)
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_normalQuantityAndValue_then_noAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrderWithNormalQuantityAndValue();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighQuantityTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        // Mock shipping order repository to return existing tobacco order to prevent first tobacco alert
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment(anyString(), eq("Tobacco")))
            .thenReturn(Collections.singletonList(createTestShippingOrderWithTobacco()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send any alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_nullQuantity_then_noHighSkuQuantityAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrderWithNullQuantity();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighQuantityTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        // Mock shipping order repository to return existing tobacco order to prevent first tobacco alert
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment(anyString(), eq("Tobacco")))
            .thenReturn(Collections.singletonList(createTestShippingOrderWithTobacco()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send high SKU quantity alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_nullTotalPrice_then_noHighValueAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrderWithNullTotalPrice();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForHighValueTest();

        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        // Mock shipping order repository to return existing tobacco order to prevent first tobacco alert
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment(anyString(), eq("Tobacco")))
            .thenReturn(Collections.singletonList(createTestShippingOrderWithTobacco()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send high value alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    private ShippingOrder createTestShippingOrder() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .build();
    }

    private CustomerAddress createTestCustomerAddress() {
        return CustomerAddress.builder()
            .id(UUID.randomUUID())
            .firstName("John")
            .lastName("Doe")
            .email("<EMAIL>")
            .phone("+1555123456")
            .city("New York")
            .postalCode("10001")
            .addressOne("123 Main St")
            .latitude(new BigDecimal("40.7128"))
            .longitude(new BigDecimal("-74.0060"))
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDto(String ip, String deviceId) {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", ip),
            new NoteAttributeDto("device_id", deviceId),
            new NoteAttributeDto("payment_type", "CREDIT_CARD")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1555123456");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDtoWithPayment() {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", "*************"),
            new NoteAttributeDto("device_id", "safe_device"),
            new NoteAttributeDto("payment_type", "ACH_DIRECT_DEBIT")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1555123456");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDtoForHighQuantityTest() {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", "*************"),
            new NoteAttributeDto("device_id", "safe_device"),
            new NoteAttributeDto("payment_type", "CREDIT_CARD")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1555123456");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDtoForHighValueTest() {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", "*************"),
            new NoteAttributeDto("device_id", "safe_device"),
            new NoteAttributeDto("payment_type", "CREDIT_CARD")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1555123456");

        return ShopifyOrderDto.builder()
            .name("ORDER-001")
            .id("12345")
            .note("Test order")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }

    private ShippingOrder createTestShippingOrderWithHighQuantity() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(100)
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithHighValue() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(1)
            .price(new BigDecimal("10000.00"))
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .totalPrice(new BigDecimal("10000.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithHighQuantityAndHighValue() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(100)
            .price(new BigDecimal("10000.00"))
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .totalPrice(new BigDecimal("10000.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithNormalQuantityAndValue() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(1)
            .price(new BigDecimal("100.00"))
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .totalPrice(new BigDecimal("100.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithNullQuantity() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithNullTotalPrice() {
        CustomerAddress address = createTestCustomerAddress();
        ShippingOrderItem item = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(1)
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(item))
            .build();
    }

    @Test
    void when_detectFraudOrder_and_firstTobaccoOrderForNewUser_then_sendAlert() {
        // Given - New user ordering tobacco for the first time
        ShippingOrder shippingOrder = createTestShippingOrderWithTobacco();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Mock: No historical tobacco orders for this email (new user)
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment("<EMAIL>", "Tobacco"))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should send first tobacco order alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_firstTobaccoOrderForExistingUser_then_sendAlert() {
        // Given - Existing user with historical orders but no tobacco items
        ShippingOrder shippingOrder = createTestShippingOrderWithTobacco();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Mock: Historical orders exist but none contain tobacco
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment("<EMAIL>", "Tobacco"))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should send first tobacco order alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_notFirstTobaccoOrder_then_noFirstTobaccoAlert() {
        // Given - User with historical tobacco orders
        ShippingOrder shippingOrder = createTestShippingOrderWithTobacco();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Create historical order with tobacco
        ShippingOrder baseOrder = createTestShippingOrderWithTobacco();
        ShippingOrder historicalTobaccoOrder = ShippingOrder.builder()
            .id(UUID.randomUUID()) // Different ID from current order
            .orderNumber("HISTORICAL-TOBACCO-001")
            .customerAddress(baseOrder.getCustomerAddress())
            .shippingOrderItems(baseOrder.getShippingOrderItems())
            .totalPrice(baseOrder.getTotalPrice())
            .build();

        // Mock: Historical orders exist with tobacco items
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment("<EMAIL>", "Tobacco"))
            .thenReturn(Collections.singletonList(historicalTobaccoOrder));

        // Mock customer address repository to prevent new user/address detection
        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send first tobacco order alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_noTobaccoInOrder_then_noFirstTobaccoAlert() {
        // Given - Order without tobacco products
        ShippingOrder shippingOrder = createTestShippingOrderWithoutTobacco();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Mock customer address repository to prevent new user/address detection
        when(customerAddressRepository.findByEmail(anyString()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send first tobacco order alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    @Test
    void when_detectFraudOrder_and_multipleAlertsTriggered_then_sendHighestPriorityAlert() {
        // Given - Order that would trigger both first tobacco and high quantity alerts
        ShippingOrder shippingOrder = createTestShippingOrderWithFirstTobaccoAndHighQuantity();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Mock: New user (would trigger first tobacco alert)
        when(shippingOrderRepository.findByCustomerAddressEmailAndDepartment("<EMAIL>", "Tobacco"))
            .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should send alert (priority: first tobacco comes before high quantity)
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), times(1));
        }
    }

    @Test
    void when_detectFraudOrder_and_nullEmail_then_noFirstTobaccoAlert() {
        // Given - Order with null email in customer address
        ShippingOrder shippingOrder = createTestShippingOrderWithTobaccoNullEmail();
        ShopifyOrderDto shopifyOrderDto = createTestShopifyOrderDtoForFirstTobaccoTest();

        // Mock customer address repository to prevent new address detection
        when(customerAddressRepository.findByLatitudeAndLongitude(any(), any()))
            .thenReturn(Collections.singletonList(createTestCustomerAddress()));

        try (MockedStatic<HttpClientUtils> mockedHttpClient = Mockito.mockStatic(HttpClientUtils.class)) {
            // When
            fraudOrderDetectionService.detectFraudOrder(shippingOrder, shopifyOrderDto);

            // Then - should not send first tobacco order alert
            mockedHttpClient.verify(() -> HttpClientUtils.executePostRequest(
                anyString(), any(), any(), eq(String.class)), never());
        }
    }

    // Helper methods for first tobacco order tests

    private ShippingOrder createTestShippingOrderWithTobacco() {
        CustomerAddress address = createTestCustomerAddressForFirstTobacco();
        ShippingOrderItem tobaccoItem = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(1)
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-TOBACCO-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(tobaccoItem))
            .totalPrice(new BigDecimal("50.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithoutTobacco() {
        CustomerAddress address = createTestCustomerAddressForFirstTobacco();
        ShippingOrderItem nonTobaccoItem = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Electronics")
            .qty(1)
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-ELECTRONICS-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(nonTobaccoItem))
            .totalPrice(new BigDecimal("50.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithFirstTobaccoAndHighQuantity() {
        CustomerAddress address = createTestCustomerAddressForFirstTobacco();
        ShippingOrderItem tobaccoItem = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(50) // High quantity
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-TOBACCO-HIGH-QTY-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(tobaccoItem))
            .totalPrice(new BigDecimal("500.00"))
            .build();
    }

    private ShippingOrder createTestShippingOrderWithTobaccoNullEmail() {
        CustomerAddress address = CustomerAddress.builder()
            .id(UUID.randomUUID())
            .firstName("John")
            .lastName("Doe")
            .email(null) // Null email
            .city("New York")
            .addressOne("123 Main St")
            .latitude(new BigDecimal("40.7128"))
            .longitude(new BigDecimal("-74.0060"))
            .build();

        ShippingOrderItem tobaccoItem = ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .department("Tobacco")
            .qty(1)
            .build();

        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER-TOBACCO-NULL-EMAIL-001")
            .customerAddress(address)
            .shippingOrderItems(Collections.singletonList(tobaccoItem))
            .build();
    }

    private CustomerAddress createTestCustomerAddressForFirstTobacco() {
        return CustomerAddress.builder()
            .id(UUID.randomUUID())
            .firstName("Jane")
            .lastName("Smith")
            .email("<EMAIL>")
            .phone("+1555987654")
            .city("New York")
            .postalCode("10001")
            .addressOne("789 Tobacco St")
            .latitude(new BigDecimal("40.7128"))
            .longitude(new BigDecimal("-74.0060"))
            .build();
    }

    private ShopifyOrderDto createTestShopifyOrderDtoForFirstTobaccoTest() {
        List<NoteAttributeDto> noteAttributes = Arrays.asList(
            new NoteAttributeDto("ip", "*************"),
            new NoteAttributeDto("device_id", "safe_device"),
            new NoteAttributeDto("payment_type", "CREDIT_CARD")
        );

        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setPhone("+1555987654");

        return ShopifyOrderDto.builder()
            .name("ORDER-TOBACCO-001")
            .id("67890")
            .note("First tobacco order test")
            .shippingAddress(shippingAddress)
            .noteAttributes(noteAttributes)
            .build();
    }
}