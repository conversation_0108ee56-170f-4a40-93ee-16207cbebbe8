package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.CustomerDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShippingAddressDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShopifyLineItemDto;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShippingOrderRepositoryIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    ShippingOrderRepository shippingOrderRepository;
    private static final String TEST_EMAIL_PREFIX = "test.email.";
    private static final String TEST_EMAIL_SUFFIX = "@integration.test.com";

    @Test
    void test_find_rescheduled_shipping_orders() throws Exception {
        LocalDate futureDate = LocalDate.now().plusDays(365);
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(futureDate + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        updateShippingOrder(shopifyOrderDto);

        List<ShippingOrder> rescheduledShippingOrders = shippingOrderRepository.findRescheduledShippingOrders(futureDate.minusDays(
            1).toString());
        assertTrue(rescheduledShippingOrders.stream()
            .anyMatch(order -> order.getOrderNumber().equals(shopifyOrderDto.getName())));
    }

    @Test
    void test_findByCustomerAddressEmailAndContainsTobacco_when_noTobaccoOrders_then_returnEmpty() throws Exception {
        // Given
        String testEmail = generateUniqueTestEmail();
        createOrderWithLineItems(testEmail, createNonTobaccoLineItems());

        // When
        List<ShippingOrder> tobaccoOrders = shippingOrderRepository.findByCustomerAddressEmailAndDepartment(testEmail, "Tobacco");

        // Then
        assertEquals(0, tobaccoOrders.size());
    }

    @Test
    void test_findByCustomerAddressEmailAndContainsTobacco_when_emailNotExists_then_returnEmpty() {
        // Given
        String nonExistentEmail = "<EMAIL>";

        // When
        List<ShippingOrder> tobaccoOrders = shippingOrderRepository.findByCustomerAddressEmailAndDepartment(nonExistentEmail,
            "Tobacco");

        // Then
        assertEquals(0, tobaccoOrders.size());
    }

    private String generateUniqueTestEmail() {
        return TEST_EMAIL_PREFIX + "tobacco_empty" + "." + System.currentTimeMillis() + TEST_EMAIL_SUFFIX;
    }

    private void createOrderWithLineItems(String email, List<ShopifyLineItemDto> lineItems) throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDtoWithCustomer(email);
        shopifyOrderDto.setLineItems(lineItems);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);
    }

    private void updateShippingOrder(ShopifyOrderDto shopifyOrderDto) {
        Location location = locationRepository.findByName("00-01-A-1");
        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.setBreakdownLocation(location);
        shippingOrder.setStatus(ShippingOrderStatus.IN_PROGRESS);
        shippingOrderRepository.save(shippingOrder);
    }

    private List<ShopifyLineItemDto> createNonTobaccoLineItems() {
        return createLineItems(BigDecimal.valueOf(99.99));
    }

    private List<ShopifyLineItemDto> createLineItems(BigDecimal price) {
        ShopifyLineItemDto lineItem = new ShopifyLineItemDto();
        lineItem.setId(UUID.randomUUID().toString());
        lineItem.setSku("ELECTRONICS-" + RandomStringUtils.randomAlphabetic(5));
        lineItem.setTitle("Electronic Product");
        lineItem.setCurrentQuantity(2);
        lineItem.setQuantity(2);
        lineItem.setPrice(price);
        lineItem.setFulfillableQuantity(2);
        return Collections.singletonList(lineItem);
    }

    private ShopifyOrderDto buildShopifyOrderDtoWithCustomer(String email) {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setCustomer(CustomerDto.builder()
            .id(UUID.randomUUID().toString())
            .firstName("Test")
            .lastName("Customer")
            .email(email)
            .build());
        ensureShippingAddress(shopifyOrderDto);
        return shopifyOrderDto;
    }

    private void ensureShippingAddress(ShopifyOrderDto shopifyOrderDto) {
        if (shopifyOrderDto.getShippingAddress() == null) {
            shopifyOrderDto.setShippingAddress(new ShippingAddressDto());
        }
        ShippingAddressDto shippingAddress = shopifyOrderDto.getShippingAddress();
        shippingAddress.setFirstName("Test");
        shippingAddress.setLastName("Customer");
        shippingAddress.setAddress1("123 Test Street");
        shippingAddress.setCity("Test City");
        shippingAddress.setProvince("Test State");
        shippingAddress.setCountry("US");
        shippingAddress.setZip("12345");
        shippingAddress.setPhone("+1234567890");
    }

}