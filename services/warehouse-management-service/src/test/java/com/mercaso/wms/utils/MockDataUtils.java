package com.mercaso.wms.utils;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.VendorDto;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShippingAddressDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShopifyLineItemDto;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.Item;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.transfertaskitem.TransferTaskItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;

public class MockDataUtils {

    public static Warehouse buildWarehouse(UUID warehouseId) {
        return Warehouse.builder()
            .id(warehouseId)
            .name(RandomStringUtils.randomAlphabetic(10))
            .status(WarehouseStatus.ACTIVE)
            .type(WarehouseType.INTERNAL)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .updatedBy("test")
            .build();
    }

    public static WarehouseDto buildWarehouseDto(UUID warehouseId, String warehouseName, WarehouseType type) {
        return WarehouseDto.builder()
            .id(warehouseId)
            .name(warehouseName)
            .status(WarehouseStatus.ACTIVE)
            .type(type)
            .build();
    }

    public static List<ItemCategoryDto> buildItemCategoryDtoList(String skuNumber) {
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
        itemCategoryDto.setId(UUID.randomUUID());
        itemCategoryDto.setSkuNumber(skuNumber);
        itemCategoryDto.setCategory(RandomStringUtils.randomAlphabetic(10));
        return List.of(itemCategoryDto);
    }

    public static List<BatchItem> buildBatchItems(UUID batchId, int size) {
        List<BatchItem> batchItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            batchItems.add(BatchItem.builder()
                .batchId(batchId)
                .orderNumber(RandomStringUtils.randomAlphabetic(10))
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .expectQty(20)
                .allocatedQty(0)
                .line(i)
                .locationId(UUID.randomUUID())
                .locationName(RandomStringUtils.randomAlphabetic(10))
                .breakdownName("10")
                .source("MDC")
                .department(BatchConstants.BEVERAGE)
                .prep(BatchConstants.BEVERAGE.toUpperCase(Locale.ROOT))
                .category(RandomStringUtils.randomAlphabetic(10))
                .subCategory(RandomStringUtils.randomAlphabetic(10))
                .clazz(RandomStringUtils.randomAlphabetic(10))
                .pickingAppCovered(true)
                .bigOrder(true)
                .build());
        }
        return batchItems;
    }

    public static List<PickingTask> buildPickingTask(UUID batchId, int size, SourceEnum source, PickingTaskStatus status, PickingTaskType type) {
        List<PickingTask> pickingTasks = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            pickingTasks.add(PickingTask.builder()
                    .batchId(batchId)
                    .number(RandomStringUtils.randomNumeric(10))
                    .status(status)
                    .source(source)
                .createdAt(Instant.now())
                .createdBy("test")
                .updatedAt(Instant.now())
                .updatedBy("test")
                    .pickingTaskItems(buildPickingTaskItems(2))
                    .type(type)
                    .build());
        }
        return pickingTasks;
    }

    public static List<ReceivingTask> buildReceivingTask(UUID batchId,
        int size,
        SourceEnum source,
        ReceivingTaskStatus status,
        ReceivingTaskType type) {
        List<ReceivingTask> receivingTasks = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            receivingTasks.add(ReceivingTask.builder()
                .batchId(batchId)
                .status(status)
                .vendor(source.name())
                .createdAt(Instant.now())
                .createdBy("test")
                .updatedAt(Instant.now())
                .updatedBy("test")
                .receivingTaskItems(buildReceivingTaskItems(2))
                .type(type)
                .build());
        }
        return receivingTasks;
    }

    public static List<ReceivingTaskItem> buildReceivingTaskItems(int size) {
        List<ReceivingTaskItem> receivingTaskItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            ReceivingTaskItem build = ReceivingTaskItem.builder()
                .batchItemId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .locationName(RandomStringUtils.randomAlphabetic(10))
                .expectQty(10)
                .receivedQty(0)
                .orderNumber(RandomStringUtils.randomAlphabetic(10))
                .breakdownName(RandomStringUtils.randomAlphabetic(10))
                .shippingOrderId(UUID.randomUUID())
                .shippingOrderItemId(UUID.randomUUID())
                .department("Baby")
                .category("Baby feeding")
                .build();
            receivingTaskItems.add(build);
        }
        return receivingTaskItems;
    }

    public static List<PickingTask> buildPickingTask(UUID batchId, int size) {
        return buildPickingTask(batchId, size, SourceEnum.MFC, PickingTaskStatus.CREATED, PickingTaskType.ORDER);
    }

    public static List<PickingTask> buildBatchLevelPickingTask(UUID batchId, int size) {
        return buildPickingTask(batchId, size, SourceEnum.MFC, PickingTaskStatus.CREATED, PickingTaskType.BATCH);
    }

    public static List<PickingTask> buildPickingTask(UUID batchId, int size, PickingTaskType type) {
        return buildPickingTask(batchId, size, SourceEnum.MFC, PickingTaskStatus.CREATED, type);
    }

    public static PickingTask buildPickingTask(UUID id, PickingTaskType type) {
        return PickingTask.builder()
            .batchId(UUID.randomUUID())
            .number(RandomStringUtils.randomNumeric(10))
            .status(PickingTaskStatus.PICKED)
            .source(SourceEnum.MDC)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .updatedBy("test")
            .pickingTaskItems(buildPickingTaskItems(2))
            .type(type)
            .build();
    }

    public static Batch buildBatch(UUID batchId) {
        return Batch.builder()
            .id(batchId)
            .tag(LocalDate.now().plusDays(10).toString())
            .status(BatchStatus.IN_PROGRESS)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .updatedBy("test")
            .build();
    }

    public static List<VendorDto> buildVendorDtoList(int size, SourceEnum source) {
        List<VendorDto> vendorDtos = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            VendorDto vendor = new VendorDto();
            vendor.setId(UUID.randomUUID());
            vendor.setVendorName(source.name());
            vendor.setFinaleId("123456");
            vendorDtos.add(vendor);
        }
        return vendorDtos;
    }

    public static List<PickingTaskItem> buildPickingTaskItems(int size) {
        List<PickingTaskItem> pickingTaskItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            PickingTaskItem build = PickingTaskItem.builder()
                .batchItemId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .locationName(RandomStringUtils.randomAlphabetic(10))
                .prep(RandomStringUtils.randomAlphabetic(10))
                .expectQty(10)
                .pickedQty(0)
                .breakdownName(RandomStringUtils.randomAlphabetic(10))
                .shippingOrderId(UUID.randomUUID())
                .shippingOrderItemId(UUID.randomUUID())
                .department("Candy & Snacks")
                .category("Candy")
                .build();
            pickingTaskItems.add(build);
        }
        return pickingTaskItems;
    }

    public static Batch createBatch() {
        Batch batch = Batch.builder().build();
        batch.setLastModifiedBy("test");
        batch.setTag(DateUtils.getNextDeliveryDate().toString());
        DocumentResponse originalDocumentResponse = new DocumentResponse();
        originalDocumentResponse.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        originalDocumentResponse.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setOriginal(SerializationUtils.toTree(originalDocumentResponse));

        DocumentResponse generated = new DocumentResponse();
        generated.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        generated.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setGenerated(SerializationUtils.toTree(generated));
        batch.setStatus(BatchStatus.IN_PROGRESS);
        return batch;
    }

    public static Batch createBatchForCancellation(UUID batchId) {
        Batch batch = Batch.builder()
            .id(batchId)
            .tag(LocalDate.now().plusDays(10).toString())
            .status(BatchStatus.CREATED)
            .createdAt(Instant.now())
            .createdBy("test")
            .updatedAt(Instant.now())
            .updatedBy("test")
            .build();
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        return batch;
    }

    public static ShopifyOrderDto buildShopifyOrderDto() {
        return ShopifyOrderDto.builder()
            .id(UUID.randomUUID().toString())
            .name(RandomStringUtils.randomAlphabetic(10))
            .tags(LocalDate.now() + ", SELLER_Mercaso")
            .lineItems(buildShopifyLineItemDto(10))
            .shippingAddress(buildShippingAddressDto())
            .build();
    }

    public static List<ShopifyLineItemDto> buildShopifyLineItemDto(int size) {
        List<ShopifyLineItemDto> shopifyLineItemDtos = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            ShopifyLineItemDto shopifyLineItemDto = new ShopifyLineItemDto();
            shopifyLineItemDto.setId(UUID.randomUUID().toString());
            shopifyLineItemDto.setSku(RandomStringUtils.randomAlphabetic(10));
            shopifyLineItemDto.setCurrentQuantity(10);
            shopifyLineItemDto.setQuantity(10);
            shopifyLineItemDtos.add(shopifyLineItemDto);
        }
        return shopifyLineItemDtos;
    }

    public static ShippingOrder buildShippingOrder(ShopifyOrderDto shopifyOrderDto) {
        return ShippingOrder.builder().build().create(shopifyOrderDto);
    }

    public static ShippingAddressDto buildShippingAddressDto() {
        ShippingAddressDto shippingAddressDto = new ShippingAddressDto();
        shippingAddressDto.setName(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setFirstName(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setLastName(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setAddress1(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setAddress2(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setPhone(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setCity(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setZip(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setProvince(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setCountry(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setCompany(RandomStringUtils.randomAlphabetic(10));
        shippingAddressDto.setLatitude(null);
        return shippingAddressDto;
    }

    public static List<ShippingOrder> buildShippingOrders(int size, ShippingOrderStatus status) {
        List<ShippingOrder> shippingOrders = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            ShippingOrder shippingOrder = ShippingOrder.builder()
                .orderNumber(RandomStringUtils.randomAlphabetic(10))
                .deliveryDate(LocalDate.now().plusDays(10))
                .status(status)
                .build();
            shippingOrder.setShippingOrderItems(buildShippingOrderItems(2));
            shippingOrders.add(shippingOrder);
        }
        return shippingOrders;
    }

    public static List<ShippingOrder> buildShippingOrdersWithId(int size, ShippingOrderStatus status) {
        List<ShippingOrder> shippingOrders = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            ShippingOrder shippingOrder = ShippingOrder.builder()
                .id(UUID.randomUUID())
                .orderNumber(RandomStringUtils.randomAlphabetic(10))
                .deliveryDate(LocalDate.now().plusDays(10))
                .status(status)
                .build();
            shippingOrder.setShippingOrderItems(buildShippingOrderItemsWithId(2));
            shippingOrders.add(shippingOrder);
        }
        return shippingOrders;
    }

    private static List<ShippingOrderItem> buildShippingOrderItems(int size) {
        List<ShippingOrderItem> shippingOrderItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            shippingOrderItems.add(ShippingOrderItem.builder()
                .shippingOrderId(UUID.randomUUID())
                .shopifyOrderItemId(RandomStringUtils.randomAlphabetic(10))
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .originalQty(10)
                .qty(10)
                .line(i)
                .department("Candy & Snacks")
                .category("Candy")
                .picked(false)
                .createdAt(Instant.now())
                .createdBy("test")
                .updatedAt(Instant.now())
                .updatedBy("test")
                .build());
        }
        return shippingOrderItems;
    }

    private static List<ShippingOrderItem> buildShippingOrderItemsWithId(int size) {
        List<ShippingOrderItem> shippingOrderItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            shippingOrderItems.add(ShippingOrderItem.builder()
                .id(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .qty(10)
                .line(i + 1)
                .shopifyOrderItemId(UUID.randomUUID().toString())
                .department("department")
                .category("category")
                .subCategory("subCategory")
                .build());
        }
        return shippingOrderItems;
    }

    public static List<InventoryStock> buildInventoryStocks(int size) {
        List<InventoryStock> inventoryStocks = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            inventoryStocks.add(InventoryStock.builder()
                .item(Item.builder()
                    .skuNumber(RandomStringUtils.randomAlphabetic(10))
                    .id(UUID.randomUUID())
                    .title(RandomStringUtils.randomAlphabetic(10))
                    .build())
                .location(Location.builder().name(RandomStringUtils.randomAlphabetic(10)).type(LocationType.BIN).build())
                .expirationDate(LocalDate.now().plusDays(10))
                .status(InventoryStockStatus.AVAILABLE)
                .qty(BigDecimal.TEN)
                .warehouse(Warehouse.builder().name("MFC").build())
                .build());
        }
        return inventoryStocks;
    }

    public static AccountPreference buildAccountPreference(UUID userId, UUID warehouseId) {
        return AccountPreference.builder()
            .userId(userId)
            .userName(RandomStringUtils.randomAlphabetic(10))
            .email(RandomStringUtils.randomAlphabetic(10))
            .workWarehouse(Warehouse.builder().id(warehouseId).name(RandomStringUtils.randomAlphabetic(10)).build())
            .isFullTime(true)
            .dailyWorkHours(8)
            .secretKey(RandomStringUtils.randomAlphabetic(10))
            .externalWarehouse(Warehouse.builder().id(warehouseId).name(RandomStringUtils.randomAlphabetic(10)).build())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment(RandomStringUtils.randomAlphabetic(10))
            .status(AccountPreferenceStatus.ACTIVE)
            .build();
    }

    public static List<TransferTask> buildTransferTask(int count, TransferTaskStatus status) {
        List<TransferTask> transferTasks = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            TransferTask transferTask = TransferTask.builder()
                .status(status)
                .originWarehouse(buildWarehouse(UUID.randomUUID()))
                .destinationWarehouse(buildWarehouse(UUID.randomUUID()))
                .deliveryDate(LocalDate.now().toString())
                .loaderUserId(UUID.randomUUID())
                .loaderUserName(RandomStringUtils.randomAlphabetic(10))
                .receiverUserId(UUID.randomUUID())
                .receiverUserName(RandomStringUtils.randomAlphabetic(10))
                .loadingAt(Instant.now())
                .receivedAt(Instant.now())
                .transferTaskItems(buildTransferTaskItems(count))
                .build();
            transferTasks.add(transferTask);
        }
        return transferTasks;
    }

    public static List<TransferTaskItem> buildTransferTaskItems(int size) {
        List<TransferTaskItem> transferTaskItems = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            TransferTaskItem build = TransferTaskItem.builder()
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .transferQty(10)
                .pickingTaskItemId(UUID.randomUUID())
                .originLocationId(UUID.randomUUID())
                .destinationLocationId(UUID.randomUUID())
                .title(RandomStringUtils.randomAlphabetic(10))
                .transferQty(0)
                .build();
            transferTaskItems.add(build);
        }
        return transferTaskItems;
    }

    public static AccountPreference buildAccountPreference(UUID userId, Warehouse warehouseId, Warehouse externalWarehouseId) {
        return AccountPreference.builder()
            .userId(userId)
            .userName(RandomStringUtils.randomAlphabetic(10))
            .email(RandomStringUtils.randomAlphabetic(10))
            .workWarehouse(warehouseId)
            .isFullTime(true)
            .dailyWorkHours(8)
            .secretKey(RandomStringUtils.randomAlphabetic(10))
            .externalWarehouse(externalWarehouseId)
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment(RandomStringUtils.randomAlphabetic(10))
            .status(AccountPreferenceStatus.ACTIVE)
            .build();
    }

    public static List<CrossDockTask> buildCrossDockTasks(int count) {
        List<CrossDockTask> crossDockTasks = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            CrossDockTask crossDockTask = CrossDockTask.builder()
                .pickerUserId(UUID.randomUUID())
                .number(RandomStringUtils.randomAlphabetic(10))
                .pickerUserName(RandomStringUtils.randomAlphabetic(10))
                .warehouseId(UUID.randomUUID())
                .totalQty(0)
                .crossDockTaskItems(buildCrossTaskItems(count))
                .build();
            crossDockTasks.add(crossDockTask);
        }
        return crossDockTasks;
    }

    public static List<CrossDockTaskItem> buildCrossTaskItems(int size) {
        List<CrossDockTaskItem> crossDockTaskItemList = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            CrossDockTaskItem crossDockTaskItem = CrossDockTaskItem.builder()
                .pickedQty(1)
                .crossDockedQty(1)
                .batchId(UUID.randomUUID())
                .itemId(UUID.randomUUID())
                .taskItemId(UUID.randomUUID())
                .shippingOrderId(UUID.randomUUID())
                .shippingOrderItemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .taskType(CrossDockItemSourceEnum.PICKING_TASK)
                .taskNumber(RandomStringUtils.randomAlphabetic(10))
                .department("Beverage")
                .category("Soda")
                .source(SourceEnum.MFC)
                .orderNumber(RandomStringUtils.randomAlphabetic(10))
                .build();
            crossDockTaskItemList.add(crossDockTaskItem);
        }
        return crossDockTaskItemList;
    }

}
