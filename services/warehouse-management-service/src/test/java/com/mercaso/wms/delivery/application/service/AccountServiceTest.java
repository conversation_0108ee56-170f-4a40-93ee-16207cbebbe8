package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.client.dto.CreateUserRequest;
import com.mercaso.user.client.dto.IdentityDto;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.delivery.application.command.account.CreateAccountCommand;
import com.mercaso.wms.delivery.application.command.account.UpdateAccountCommand;
import com.mercaso.wms.delivery.application.command.account.UpdateLoginTimeCommand;
import com.mercaso.wms.delivery.application.dto.account.AccountDto;
import com.mercaso.wms.delivery.application.mapper.account.AccountDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class AccountServiceTest {

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private AccountDtoApplicationMapper accountDtoApplicationMapper;

    @Mock
    private UmsAdaptor umsAdaptor;

    @Mock
    private RouteManagerService routeManagerService;

    @InjectMocks
    private AccountService accountService;

    private UUID testUserId;
    private UUID testAccountId;
    private UUID testWarehouseId;
    private final String testEmail = "<EMAIL>";
    private final String testUserName = "Test User";
    private final String testEncryptedSecretKey = "encryptedSecretKey";

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testAccountId = UUID.randomUUID();
        testWarehouseId = UUID.randomUUID();

        ReflectionTestUtils.setField(accountService, "publicKey", "test-public-key");
    }

    @Test
    @DisplayName("When creating account with valid data Then return created account")
    void when_createAccount_with_valid_data_then_return_created_account() {
        // Arrange
        CreateAccountCommand command = createTestCommand();

        UserDto userDto = new UserDto();
        userDto.setId(testUserId);
        userDto.setEmail(testEmail);
        userDto.setName(testUserName);

        Account account = createTestAccount();
        Account accountWithDriver = createTestAccount(); // Same account after driver is added

        AccountDto accountDto = new AccountDto();
        accountDto.setId(testAccountId);
        accountDto.setEmail(testEmail);
        accountDto.setUserName(testUserName);

        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString())).thenReturn(userDto);
        when(accountRepository.save(any(Account.class))).thenReturn(account);
        when(routeManagerService.addDriver(any(Account.class))).thenReturn(accountWithDriver);
        when(accountDtoApplicationMapper.domainToDto(accountWithDriver)).thenReturn(accountDto);

        // Act
        AccountDto result = accountService.createAccount(command);

        // Assert
        assertNotNull(result);
        assertEquals(testAccountId, result.getId());
        assertEquals(testEmail, result.getEmail());
        assertEquals(testUserName, result.getUserName());
        verify(umsAdaptor, times(1)).getUserByEmailAndConnectionIsMobileConnection(testEmail);
        verify(umsAdaptor, times(1)).createUser(any(CreateUserRequest.class), anyString());
    }

    @Test
    @DisplayName("When creating account with existing email in UMS Then use existing user")
    void when_createAccount_with_existing_email_in_ums_then_use_existing_user() {
        // Arrange
        CreateAccountCommand command = createTestCommand();

        UserDto existingUserDto = new UserDto();
        existingUserDto.setId(testUserId);
        existingUserDto.setEmail(testEmail);
        existingUserDto.setName(testUserName);

        IdentityDto identityDto = new IdentityDto();
        identityDto.setConnection("Mobile-connection");
        List<IdentityDto> identities = new ArrayList<>();
        identities.add(identityDto);
        existingUserDto.setIdentities(identities);

        Account account = createTestAccount();
        Account accountWithDriver = createTestAccount(); // Same account after driver is added

        AccountDto accountDto = new AccountDto();
        accountDto.setId(testAccountId);
        accountDto.setEmail(testEmail);
        accountDto.setUserName(testUserName);

        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(existingUserDto);
        when(accountRepository.save(any(Account.class))).thenReturn(account);
        when(routeManagerService.addDriver(any(Account.class))).thenReturn(accountWithDriver);
        when(accountDtoApplicationMapper.domainToDto(accountWithDriver)).thenReturn(accountDto);

        // Act
        AccountDto result = accountService.createAccount(command);

        // Assert
        assertNotNull(result);
        assertEquals(testAccountId, result.getId());
        assertEquals(testEmail, result.getEmail());
        assertEquals(testUserName, result.getUserName());
        verify(umsAdaptor, times(1)).getUserByEmailAndConnectionIsMobileConnection(testEmail);
        verify(umsAdaptor, never()).createUser(any(CreateUserRequest.class), anyString());
    }

    @Test
    @DisplayName("When creating account with existing email Then throw exception")
    void when_createAccount_with_existing_email_then_throw_exception() {
        // Arrange
        CreateAccountCommand command = createTestCommand();

        Account existingAccount = createTestAccount();

        when(accountRepository.findByEmail(testEmail)).thenReturn(Optional.ofNullable(existingAccount));

        // Act & Assert
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class,
            () -> accountService.createAccount(command));

        assertEquals("Email " + testEmail + " already exists", exception.getMessage());
    }

    @Test
    @DisplayName("When UMS user creation fails Then throw exception")
    void when_ums_user_creation_fails_then_throw_exception() {
        // Arrange
        CreateAccountCommand command = createTestCommand();

        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString()))
            .thenThrow(new RuntimeException("UMS service unavailable for creation"));

        // Act & Assert
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class,
            () -> accountService.createAccount(command));

        assertEquals("Failed to create user: UMS service unavailable for creation", exception.getMessage());
    }

    @Test
    @DisplayName("When UMS createUser returns null Then throw exception")
    void when_ums_createUser_returns_null_then_throw_exception() {
        // Arrange
        CreateAccountCommand command = createTestCommand();

        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString())).thenReturn(null);

        // Act & Assert
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class,
            () -> accountService.createAccount(command));

        assertEquals("Failed to create user: Failed to create user " + command.getUserName(), exception.getMessage());
    }

    @Test
    @DisplayName("When updating account with valid data Then return updated account")
    void when_updateAccount_with_valid_data_then_return_updated_account() {
        // Arrange
        UUID accountId = testAccountId;
        UpdateAccountCommand command = UpdateAccountCommand.builder()
            .status(AccountStatus.ACTIVE)
            .userName("Updated User")
            .warehouseId(UUID.randomUUID())
            .build();

        Account existingAccount = createTestAccount();

        AccountDto accountDto = new AccountDto();
        accountDto.setId(accountId);
        accountDto.setEmail(testEmail);
        accountDto.setUserName(command.getUserName());
        accountDto.setWarehouseId(command.getWarehouseId());
        accountDto.setStatus(AccountStatus.ACTIVE);

        when(accountRepository.findById(accountId)).thenReturn(existingAccount);
        when(accountRepository.save(any(Account.class))).thenReturn(existingAccount);
        when(accountDtoApplicationMapper.domainToDto(existingAccount)).thenReturn(accountDto);

        // Act
        AccountDto result = accountService.updateAccount(accountId, command);

        // Assert
        assertNotNull(result);
        assertEquals(accountId, result.getId());
        assertEquals(AccountStatus.ACTIVE, result.getStatus());
        assertEquals(command.getUserName(), result.getUserName());
        assertEquals(command.getWarehouseId(), result.getWarehouseId());
    }

    @Test
    @DisplayName("When updating non-existent account Then throw exception")
    void when_updateAccount_with_non_existent_id_then_throw_exception() {
        // Arrange
        UUID accountId = UUID.randomUUID();
        UpdateAccountCommand command = UpdateAccountCommand.builder()
            .status(AccountStatus.ACTIVE)
            .build();

        when(accountRepository.findById(accountId)).thenReturn(null);

        // Act & Assert
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class,
            () -> accountService.updateAccount(accountId, command));

        assertEquals("Account not found, ID: " + accountId, exception.getMessage());
    }

    @Test
    @DisplayName("When batch updating login time with specific user IDs Then update those accounts")
    void when_batchUpdateLoginTime_with_userIds_then_update_specific_accounts() {
        // Arrange
        List<UUID> userIds = List.of(UUID.randomUUID(), UUID.randomUUID());
        Instant loginTime = Instant.now();

        UpdateLoginTimeCommand command = UpdateLoginTimeCommand.builder()
            .userIds(userIds)
            .loginTime(loginTime)
            .build();

        Account account1 = Account.builder()
            .id(UUID.randomUUID())
            .userId(userIds.get(0))
            .email("<EMAIL>")
            .userName("Test User 1")
            .status(AccountStatus.ACTIVE)
            .build();

        Account account2 = Account.builder()
            .id(UUID.randomUUID())
            .userId(userIds.get(1))
            .email("<EMAIL>")
            .userName("Test User 2")
            .status(AccountStatus.ACTIVE)
            .build();

        List<Account> accounts = List.of(account1, account2);

        when(accountRepository.findBy(userIds)).thenReturn(accounts);
        when(accountRepository.save(any(Account.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(command);

        // Assert
        assertEquals(2, updatedCount);
        verify(accountRepository, times(1)).findBy(userIds);
        verify(accountRepository, times(2)).save(any(Account.class));
    }

    @Test
    @DisplayName("When batch updating login time with null command Then update all accounts")
    void when_batchUpdateLoginTime_with_null_command_then_update_all_accounts() {
        // Arrange
        Account account1 = Account.builder()
            .id(UUID.randomUUID())
            .userId(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User 1")
            .status(AccountStatus.ACTIVE)
            .build();

        Account account2 = Account.builder()
            .id(UUID.randomUUID())
            .userId(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User 2")
            .status(AccountStatus.ACTIVE)
            .build();

        List<Account> accounts = List.of(account1, account2);

        when(accountRepository.findAll()).thenReturn(accounts);
        when(accountRepository.save(any(Account.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(null);

        // Assert
        assertEquals(2, updatedCount);
        verify(accountRepository, times(1)).findAll();
        verify(accountRepository, times(2)).save(any(Account.class));
    }

    @Test
    @DisplayName("When batch updating login time with empty user IDs Then update all accounts")
    void when_batchUpdateLoginTime_with_empty_userIds_then_update_all_accounts() {
        // Arrange
        List<UUID> emptyUserIds = new ArrayList<>();
        UpdateLoginTimeCommand command = UpdateLoginTimeCommand.builder()
            .userIds(emptyUserIds)
            .loginTime(Instant.now())
            .build();

        Account account1 = Account.builder()
            .id(UUID.randomUUID())
            .userId(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User 1")
            .status(AccountStatus.ACTIVE)
            .build();

        Account account2 = Account.builder()
            .id(UUID.randomUUID())
            .userId(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User 2")
            .status(AccountStatus.ACTIVE)
            .build();

        List<Account> accounts = List.of(account1, account2);

        when(accountRepository.findAll()).thenReturn(accounts);
        when(accountRepository.save(any(Account.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(command);

        // Assert
        assertEquals(2, updatedCount);
        verify(accountRepository).findAll();
        verify(accountRepository, times(2)).save(any(Account.class));
    }

    @Test
    @DisplayName("When batch updating login time with no accounts found Then return zero")
    void when_batchUpdateLoginTime_with_no_accounts_found_then_return_zero() {
        // Arrange
        List<UUID> userIds = List.of(UUID.randomUUID());
        UpdateLoginTimeCommand command = UpdateLoginTimeCommand.builder()
            .userIds(userIds)
            .loginTime(Instant.now())
            .build();

        when(accountRepository.findBy(userIds)).thenReturn(new ArrayList<>());

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(command);

        // Assert
        assertEquals(0, updatedCount);
        verify(accountRepository, times(1)).findBy(userIds);
        verify(accountRepository, never()).save(any(Account.class));
    }

    @Test
    @DisplayName("When batch updating login time with null login time Then use Los Angeles time")
    void when_batchUpdateLoginTime_with_null_loginTime_then_use_los_angeles_time() {
        // Arrange
        List<UUID> userIds = List.of(UUID.randomUUID());
        UpdateLoginTimeCommand command = UpdateLoginTimeCommand.builder()
            .userIds(userIds)
            .loginTime(null)
            .build();

        Account account = Account.builder()
            .id(UUID.randomUUID())
            .userId(userIds.get(0))
            .email("<EMAIL>")
            .userName("Test User")
            .status(AccountStatus.ACTIVE)
            .build();

        List<Account> accounts = List.of(account);

        when(accountRepository.findBy(userIds)).thenReturn(accounts);
        when(accountRepository.save(any(Account.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(command);

        // Assert
        assertEquals(1, updatedCount);
        verify(accountRepository, times(1)).findBy(userIds);
        verify(accountRepository, times(1)).save(any(Account.class));
    }

    @Test
    @DisplayName("When batch updating login time encounters exception Then continue processing")
    void when_batchUpdateLoginTime_encounters_exception_then_continue_processing() {
        // Arrange
        List<UUID> userIds = List.of(UUID.randomUUID(), UUID.randomUUID());
        Instant loginTime = Instant.now();

        UpdateLoginTimeCommand command = UpdateLoginTimeCommand.builder()
            .userIds(userIds)
            .loginTime(loginTime)
            .build();

        Account account1 = Account.builder()
            .id(UUID.randomUUID())
            .userId(userIds.get(0))
            .email("<EMAIL>")
            .userName("Test User 1")
            .status(AccountStatus.ACTIVE)
            .build();

        Account account2 = Account.builder()
            .id(UUID.randomUUID())
            .userId(userIds.get(1))
            .email("<EMAIL>")
            .userName("Test User 2")
            .status(AccountStatus.ACTIVE)
            .build();

        List<Account> accounts = List.of(account1, account2);

        when(accountRepository.findBy(userIds)).thenReturn(accounts);
        when(accountRepository.save(account1)).thenReturn(account1);
        when(accountRepository.save(account2)).thenThrow(new RuntimeException("Test exception"));

        // Act
        int updatedCount = accountService.batchUpdateLoginTime(command);

        // Assert
        assertEquals(1, updatedCount);
        verify(accountRepository, times(1)).findBy(userIds);
        verify(accountRepository, times(2)).save(any(Account.class));
    }

    private CreateAccountCommand createTestCommand() {
        return CreateAccountCommand.builder()
            .email(testEmail)
            .userName(testUserName)
            .warehouseId(testWarehouseId)
            .build();
    }

    private Account createTestAccount() {
        return Account.builder()
            .id(testAccountId)
            .userId(testUserId)
            .email(testEmail)
            .userName(testUserName)
            .secretKey(testEncryptedSecretKey)
            .warehouseId(testWarehouseId)
            .status(AccountStatus.ACTIVE)
            .build();
    }
}