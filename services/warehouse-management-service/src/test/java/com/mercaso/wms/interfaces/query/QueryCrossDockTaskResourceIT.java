package com.mercaso.wms.interfaces.query;

import static com.mercaso.wms.utils.MockDataUtils.buildCrossDockTasks;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryCrossDockTaskResourceIT extends AbstractIT {

    @Autowired
    CrossDockTaskResourceApi crossDockTaskResourceApi;
    @Autowired
    CrossDockTaskRepository crossDockTaskRepository;

    @Test
    void when_find_cross_dock_task_by_id_then_return_cross_dock_task() {
        // given
        List<CrossDockTask> crossDockTasks = buildCrossDockTasks(2);
        List<CrossDockTask> savedTasks = crossDockTaskRepository.saveAll(crossDockTasks);

        // when
        CrossDockTaskDto crossDockTaskDto = crossDockTaskResourceApi.getCrossDockTask(savedTasks.getFirst().getId());
        // then
        assertNotNull(crossDockTaskDto);
        assertEquals(savedTasks.getFirst().getId(), crossDockTaskDto.getId());
    }
} 