package com.mercaso.wms.delivery.application.service;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryOrder;
import static java.util.Base64.getEncoder;
import static java.util.Base64.getUrlDecoder;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.security.auth0.utils.CipherUtil;
import com.mercaso.wms.delivery.application.command.DeliveryUploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.mapper.document.DeliveryDocumentDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.external.document.DeliveryDocumentAdaptor;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.config.EncryptConfig;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DeliveryDocumentApplicationServiceTest {

    private final DeliveryDocumentRepository deliveryDocumentRepository = mock(DeliveryDocumentRepository.class);
    private final DeliveryDocumentAdaptor deliveryDocumentAdaptor = mock(DeliveryDocumentAdaptor.class);
    private final DeliveryDocumentDtoApplicationMapper deliveryDocumentDtoApplicationMapper = mock(
        DeliveryDocumentDtoApplicationMapper.class);
    private final DeliveryOrderRepository deliveryOrderRepository = mock(DeliveryOrderRepository.class);
    private final EncryptConfig encryptConfig = mock(EncryptConfig.class);
    private final DeliveryDocumentApplicationService deliveryDocumentApplicationService = new DeliveryDocumentApplicationService(
        deliveryDocumentRepository,
        deliveryDocumentAdaptor, deliveryOrderRepository, deliveryDocumentDtoApplicationMapper, encryptConfig);

    private DeliveryUploadDocumentCommand command;
    private MockMultipartFile file;
    private DocumentResponse documentResponse;
    private DeliveryDocument deliveryDocument;
    private DeliveryDocumentDto deliveryDocumentDto;
    private MockHttpServletResponse response;
    private Map<String, String> decryptedData;
    private static final String TEST_CIPHER_KEY = "8qWW8HbvJyyDxkO/Po6zqGbGntzyCrL4";
    private static final String TEST_DOWNLOAD_URL = "http://test.com/download/";
    private static final String TEST_FILE_NAME = "test-invoice.pdf";

    @BeforeEach
    void setUp() {
        file = new MockMultipartFile(
            "file",
            "test.pdf",
            "application/pdf",
            "test content".getBytes()
        );

        command = DeliveryUploadDocumentCommand.builder()
            .entityId(UUID.randomUUID())
            .entityName(EntityEnums.DELIVERY_ORDER)
            .documentType(DeliveryDocumentType.POD)
            .build();

        documentResponse = DocumentResponse.builder()
            .name("test-document.pdf")
            .signedUrl("https://example.com/test-document.pdf")
            .build();

        deliveryDocument = DeliveryDocument.builder().build().createDeliveryOrderDocument(command, documentResponse.getName());
        deliveryDocumentDto = DeliveryDocumentDto.builder()
            .id(UUID.randomUUID())
            .fileName(documentResponse.getName())
            .build();

        response = new MockHttpServletResponse();
        decryptedData = new HashMap<>();
        decryptedData.put("documentName", "test-document.pdf");
        decryptedData.put("createdAt", Instant.now().toString());

        ReflectionTestUtils.setField(deliveryDocumentApplicationService, "downloadSignatureUrl", TEST_DOWNLOAD_URL);
    }

    @Test
    void uploadDocument_Success_ShouldUploadAndSaveDocument() {
        // Given
        when(deliveryDocumentAdaptor.uploadToS3(any())).thenReturn(documentResponse);
        when(deliveryDocumentRepository.save(any(DeliveryDocument.class))).thenReturn(deliveryDocument);
        when(deliveryOrderRepository.findById(any())).thenReturn(buildDeliveryOrder());
        when(deliveryDocumentDtoApplicationMapper.domainToDto(any(DeliveryDocument.class))).thenReturn(deliveryDocumentDto);

        // When
        List<DeliveryDocumentDto> result = deliveryDocumentApplicationService.uploadDocuments(command,
            List.of(file).toArray(new MultipartFile[0]));

        // Then
        assertNotNull(result);
        assertEquals(documentResponse.getName(), result.getFirst().getFileName());
        assertEquals(documentResponse.getSignedUrl(), result.getFirst().getFileUrl());

        verify(deliveryDocumentAdaptor).uploadToS3(any());
        verify(deliveryDocumentRepository).save(any(DeliveryDocument.class));
        verify(deliveryDocumentDtoApplicationMapper).domainToDto(any(DeliveryDocument.class));
    }

    @Test
    void uploadDocuments_Success_ShouldUploadAndSaveDocument() {
        // Given
        when(deliveryDocumentAdaptor.uploadToS3(any())).thenReturn(documentResponse);
        when(deliveryDocumentRepository.save(any(DeliveryDocument.class))).thenReturn(deliveryDocument);
        when(deliveryOrderRepository.findById(any())).thenReturn(buildDeliveryOrder());
        when(deliveryDocumentDtoApplicationMapper.domainToDto(any(DeliveryDocument.class))).thenReturn(deliveryDocumentDto);

        command.setClientFileIds(List.of(UUID.randomUUID().toString()));

        // When
        List<DeliveryDocumentDto> result = deliveryDocumentApplicationService.uploadDocuments(List.of(command),
            Map.of(command.getClientFileIds().getFirst(), file));

        // Then
        assertNotNull(result);
        assertEquals(documentResponse.getName(), result.getFirst().getFileName());
        assertEquals(documentResponse.getSignedUrl(), result.getFirst().getFileUrl());

        verify(deliveryDocumentAdaptor).uploadToS3(any());
        verify(deliveryDocumentRepository).save(any(DeliveryDocument.class));
        verify(deliveryDocumentDtoApplicationMapper).domainToDto(any(DeliveryDocument.class));
    }

    @Test
    void download_WhenDocumentExists_ShouldReturnDocument() throws IOException {
        // Arrange
        String documentName = "test.pdf";
        byte[] documentContent = "PDF content".getBytes();
        when(deliveryDocumentAdaptor.downloadDocument(any())).thenReturn(documentContent);

        // Act
        deliveryDocumentApplicationService.download(documentName, response);

        // Assert
        assertAll(
            () -> assertEquals(HttpServletResponse.SC_OK, response.getStatus()),
            () -> assertEquals("application/pdf", response.getContentType()),
            () -> assertEquals("attachment; filename=test.pdf",
                response.getHeader("Content-Disposition")),
            () -> assertArrayEquals(documentContent, response.getContentAsByteArray())
        );
    }

    @Test
    void download_WhenDocumentNotFound_ShouldReturn404() throws IOException {
        // Arrange
        String documentName = "nonexistent.pdf";
        when(deliveryDocumentAdaptor.downloadDocument(documentName)).thenReturn(null);

        // Act
        deliveryDocumentApplicationService.download(documentName, response);

        // Assert
        assertEquals(HttpServletResponse.SC_NOT_FOUND, response.getStatus());
    }

    @Test
    void download_WhenEmptyDocument_ShouldReturn404() throws IOException {
        // Arrange
        String documentName = "empty.pdf";
        when(deliveryDocumentAdaptor.downloadDocument(documentName)).thenReturn(new byte[0]);

        // Act
        deliveryDocumentApplicationService.download(documentName, response);

        // Assert
        assertEquals(HttpServletResponse.SC_NOT_FOUND, response.getStatus());
    }

    @Test
    void downloadInvoice_WhenValidToken_ShouldReturnDocument() throws IOException {
        // Arrange
        Map<String, String> documentNameAndCreateAt = Map.of("documentName",
            "test-document.pdf",
            "createdAt",
            Instant.now().toString());
        String cipherKey = "8qWW8HbvJyyDxkO/Po6zqGbGntzyCrL4";
        String signature = new CipherUtil(cipherKey).encrypt(SerializationUtils.serialize(documentNameAndCreateAt));
        byte[] documentContent = "Invoice content".getBytes();

        when(encryptConfig.getCipherKey()).thenReturn(cipherKey);
        when(deliveryDocumentAdaptor.downloadDocument(any())).thenReturn(documentContent);

        // Act
        deliveryDocumentApplicationService.downloadInvoice(getEncoder().encodeToString(signature.getBytes()), response);

        // Assert
        assertAll(
            () -> assertEquals(HttpServletResponse.SC_OK, response.getStatus()),
            () -> assertEquals("application/pdf", response.getContentType()),
            () -> assertEquals("attachment; filename=test-document.pdf",
                response.getHeader("Content-Disposition")),
            () -> assertArrayEquals(documentContent, response.getContentAsByteArray())
        );
    }

    @Test
    void generateInvoiceSignatureWithExpiration_ShouldGenerateValidSignature() throws IOException {
        // When
        when(encryptConfig.getCipherKey()).thenReturn(TEST_CIPHER_KEY);
        String signature = deliveryDocumentApplicationService.generateInvoiceSignatureWithExpiration(TEST_FILE_NAME);

        // Then
        assertNotNull(signature);
        assertTrue(signature.startsWith(TEST_DOWNLOAD_URL));

        // Verify the encrypted content can be decrypted and contains correct data
        String encryptedPart = signature.substring(TEST_DOWNLOAD_URL.length());
        CipherUtil cipherUtil = new CipherUtil(TEST_CIPHER_KEY);
        String decrypted = cipherUtil.decrypt(new String(getUrlDecoder().decode(encryptedPart.getBytes())));

        decryptedData = SerializationUtils.readValue(decrypted, new TypeReference<Map<String, String>>() {
        });

        assertEquals(TEST_FILE_NAME, decryptedData.get("documentName"));
        assertNotNull(decryptedData.get("createdAt"));

        // Verify the createdAt is a valid Instant
        Instant createdAt = Instant.parse(decryptedData.get("createdAt"));
        assertTrue(createdAt.isBefore(Instant.now().plusSeconds(1)));
        assertTrue(createdAt.isAfter(Instant.now().minusSeconds(10)));
    }

    @Test
    void generateInvoiceSignatureWithExpiration_WithNullFileName_ShouldThrowException() {
        // When/Then
        assertThrows(NullPointerException.class, () ->
            deliveryDocumentApplicationService.generateInvoiceSignatureWithExpiration(null)
        );
    }

}