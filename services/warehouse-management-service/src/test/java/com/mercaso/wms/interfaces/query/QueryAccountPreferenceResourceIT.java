package com.mercaso.wms.interfaces.query;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.AccountPreferenceResourceApi;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryAccountPreferenceResourceIT extends AbstractIT {

    @Autowired
    AccountPreferenceResourceApi accountPreferenceResourceApi;

    @Test
    void when_findById_then_accountPreferenceIsFound() throws Exception {
        // given
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        // given
        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName("Test User")
            .userId(UUID.randomUUID())
            .status(AccountPreferenceStatus.ACTIVE)
            .workWarehouseId(warehouses.getFirst().getId())
            .isFullTime(true)
            .dailyWorkHours(8)
            .email(RandomStringUtils.randomAlphabetic(10))
            .externalWarehouseId(warehouses.getLast().getId())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment("Test Department")
            .build();

        doNothing().when(umsAdaptor).assignPickerRolesToUser(command.getUserId());

        // when
        AccountPreferenceDto createdAccountPreference = accountPreferenceResourceApi.createAccountPreference(command);

        AccountPreferenceDto accountPreferenceDto = accountPreferenceResourceApi.findById(createdAccountPreference.getId());

        assertNotNull(accountPreferenceDto);
        assertNotNull(accountPreferenceDto.getId());
        assertEquals(command.getUserName(), accountPreferenceDto.getUserName());
        assertEquals(command.getStatus().toString(), accountPreferenceDto.getStatus());
        assertEquals(command.getWorkWarehouseId(), accountPreferenceDto.getWorkWarehouse().getId());
        assertEquals(command.getIsFullTime(), accountPreferenceDto.getIsFullTime());
        assertEquals(command.getDailyWorkHours(), accountPreferenceDto.getDailyWorkHours());
        assertEquals(command.getExternalWarehouseId(), accountPreferenceDto.getExternalWarehouse().getId());
        assertEquals(command.getTaskType(), accountPreferenceDto.getTaskType());
        assertEquals(command.getPreferredDepartment(), accountPreferenceDto.getPreferredDepartment());
    }

    @Test
    void when_find_by_ids_then_return_list() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<UUID> ids = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            // given
            CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
                .userName(RandomStringUtils.randomAlphabetic(10))
                .userId(UUID.randomUUID())
                .status(AccountPreferenceStatus.ACTIVE)
                .workWarehouseId(warehouses.getFirst().getId())
                .isFullTime(true)
                .dailyWorkHours(8)
                .email(RandomStringUtils.randomAlphabetic(10))
                .externalWarehouseId(warehouses.getLast().getId())
                .taskType(PickingTaskType.ORDER)
                .preferredDepartment("Test Department")
                .build();

            doNothing().when(umsAdaptor).assignPickerRolesToUser(command.getUserId());

            // when
            AccountPreferenceDto createdAccountPreference = accountPreferenceResourceApi.createAccountPreference(command);
            ids.add(createdAccountPreference.getId());
        }
        List<AccountPreferenceDto> preferenceDtoList = accountPreferenceResourceApi.findBy(ids);

        assertEquals(5, preferenceDtoList.size());
    }

    @Test
    void when_find_by_userName_or_email_then_return_list() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);

        // given
        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName(RandomStringUtils.randomAlphabetic(10))
            .userId(UUID.randomUUID())
            .status(AccountPreferenceStatus.ACTIVE)
            .workWarehouseId(warehouses.getFirst().getId())
            .isFullTime(true)
            .dailyWorkHours(8)
            .email(RandomStringUtils.randomAlphabetic(10))
            .externalWarehouseId(warehouses.getLast().getId())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment("Test Department")
            .build();

        doNothing().when(umsAdaptor).assignPickerRolesToUser(command.getUserId());

        // when
        AccountPreferenceDto createdAccountPreference = accountPreferenceResourceApi.createAccountPreference(command);
        Boolean accountExist = accountPreferenceResourceApi.findBy(createdAccountPreference.getUserName(), null);

        assertTrue(accountExist);

        accountExist = accountPreferenceResourceApi.findBy(null, createdAccountPreference.getEmail());

        assertTrue(accountExist);
    }
}