package com.mercaso.wms;

import com.mercaso.document.operations.operations.impl.DocumentsOperationsImpl;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.delivery.infrastructure.external.onesignal.OnesignalAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.DeliveryOrderJpaDao;
import com.mercaso.wms.delivery.utils.DeliveryTaskResourceApi;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.infrastructure.external.delivery.DeliveryAdaptor;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import com.mercaso.wms.infrastructure.repository.inventorystockhistory.jpa.InventoryStockHistoryJpaDao;
import com.mercaso.wms.infrastructure.repository.location.LocationRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.scanrecords.jpa.ScanRecordJpaDao;
import com.mercaso.wms.infrastructure.repository.warehouse.WarehouseRepositoryImpl;
import com.mercaso.wms.utils.AccountPreferenceResourceApi;
import com.mercaso.wms.utils.TransferTaskResourceApi;
import com.mercaso.wms.utils.WarehouseResourceApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;


@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration()
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public abstract class AbstractIT {

    @Autowired
    protected WarehouseRepositoryImpl warehouseRepository;

    @Autowired
    protected LocationRepositoryImpl locationRepository;

    @Autowired
    protected BatchRepository batchRepository;

    @Autowired
    protected BatchItemRepository batchItemRepository;

    @Autowired
    protected ShippingOrderRepository shippingOrderRepository;

    @Autowired
    protected ReceivingTaskRepository receivingTaskRepository;

    @MockBean
    protected ImsAdaptor imsAdaptor;

    @MockBean
    protected FeatureFlagsManager featureFlagsManager;

    @MockBean
    protected ShopifyAdaptor shopifyAdaptor;

    @MockBean
    protected PickingTaskAssignmentConfig pickingTaskAssignmentConfig;

    @MockBean
    protected UmsAdaptor umsAdaptor;

    @Autowired
    protected DeliveryOrderJpaDao deliveryOrderJpaDao;

    @MockBean
    protected DocumentsOperationsImpl documentOperations;

    @Autowired
    protected InventoryStockHistoryJpaDao inventoryStockHistoryJpaDao;

    @MockBean
    protected RouteManagerAdaptor routeManagerAdaptor;

    @MockBean
    protected OnesignalAdaptor onesignalAdaptor;
    @MockBean
    protected DeliveryAdaptor deliveryAdaptor;

    @Autowired
    protected WarehouseResourceApi warehouseResourceApi;
    @Autowired
    protected TransferTaskResourceApi transferTaskResourceApi;
    @Autowired
    protected AccountPreferenceResourceApi accountPreferenceResourceApi;
    @Autowired
    protected DeliveryTaskResourceApi deliveryTaskResourceApi;
    @Autowired
    protected ScanRecordJpaDao scanRecordJpaDao;

}
