package com.mercaso.wms.delivery.domain.customer;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildCustomer;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyCustomerDto;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class CustomerServiceTest {

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private final CustomerRepository customerRepository = mock(CustomerRepository.class);

    private final CustomerService customerService = new CustomerService(pgAdvisoryLock, customerRepository);

    @Test
    void findOrSaveCustomer_NullCustomerDto_ShouldReturnNull() {
        // When
        Customer result = customerService.findOrSaveCustomer(null);

        // Then
        assertNull(result);
    }

    @Test
    void findOrSaveCustomer_NullEmail_ShouldReturnNull() {
        // Given
        ShopifyCustomerDto shopifyCustomerDto = new ShopifyCustomerDto();
        shopifyCustomerDto.setId(null);

        // When
        Customer result = customerService.findOrSaveCustomer(shopifyCustomerDto);

        // Then
        assertNull(result);
    }

    @Test
    void findOrSaveCustomer_ExistingEmail_ShouldReturnCustomer() {
        ShopifyCustomerDto shopifyCustomerDto = new ShopifyCustomerDto();
        shopifyCustomerDto.setId(RandomStringUtils.randomAlphabetic(10));
        shopifyCustomerDto.setEmail(RandomStringUtils.randomAlphabetic(10));

        when(customerRepository.findByExternalId(shopifyCustomerDto.getId())).thenReturn(null);
        when(customerRepository.findByEmail(shopifyCustomerDto.getEmail())).thenReturn(buildCustomer());
        when(customerRepository.update(any())).thenReturn(buildCustomer());

        Customer result = customerService.findOrSaveCustomer(shopifyCustomerDto);

        // Then
        assertNotNull(result);
    }

    @Test
    void findOrSaveCustomer_NewEmail_ShouldReturnCustomer() {
        // Given
        ShopifyCustomerDto shopifyCustomerDto = new ShopifyCustomerDto();
        shopifyCustomerDto.setId(RandomStringUtils.randomAlphabetic(10));
        shopifyCustomerDto.setEmail(RandomStringUtils.randomAlphabetic(10));

        when(customerRepository.findByExternalId(shopifyCustomerDto.getId())).thenReturn(null);
        when(customerRepository.findByEmail(shopifyCustomerDto.getEmail())).thenReturn(null);
        when(customerRepository.save(any())).thenReturn(buildCustomer());

        Customer result = customerService.findOrSaveCustomer(shopifyCustomerDto);

        // Then
        assertNotNull(result);
    }

}