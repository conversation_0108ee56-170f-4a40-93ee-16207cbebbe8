package com.mercaso.wms.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import java.io.IOException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DocumentResourceTest {

    @Mock
    private DocumentOperations documentOperations;

    @InjectMocks
    private DocumentResource documentResource;

    @Test
    void whenUploadDocumentsWithValidFileThenReturnDocumentResponse() throws IOException {
        MultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", "test content".getBytes());
        DocumentResponse documentResponse = DocumentResponse.builder()
            .name("test.txt20250115120000")
            .signedUrl("http://test-url.com/test.txt")
            .build();
        when(documentOperations.uploadDocument(any(UploadDocumentRequest.class))).thenReturn(documentResponse);

        DocumentResponse response = documentResource.uploadDocuments(file);

        assertEquals(documentResponse, response);
    }

    @Test
    void whenUploadDocumentsWithNullFileThenThrowIOException() {
        MultipartFile file = null;

        assertThrows(NullPointerException.class, () -> documentResource.uploadDocuments(file));
    }

    @Test
    void whenGetDocumentWithValidDocumentNameThenReturnSignedUrl() {
        String documentName = "test-document";
        String signedUrl = "http://signed.url";
        when(documentOperations.getSignedUrl(documentName)).thenReturn(signedUrl);

        String result = documentResource.getDocument(documentName);

        assertEquals(signedUrl, result);
    }

    @Test
    void whenGetDocumentWithInvalidDocumentNameThenReturnNull() {
        String documentName = "invalid-document";
        when(documentOperations.getSignedUrl(documentName)).thenReturn(null);

        String result = documentResource.getDocument(documentName);

        assertNull(result);
    }

}