package com.mercaso.wms.infrastructure.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

class RSAEncryptorTest {

    private static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDzxRZdNlmOE20viI0hKz56FEPu6AePRVFEFyEw2mGYIxezYYYIVDXYeSWVlAi5redc9FmiTR06qsP9CI7iRXwqgH0ZJ9XdfiYObJpiJH05Cr9j1m8b/1CgJFUhIEvcxlfLns+ZfIZFP/Es22ElJ10Rb/j1UokDrOfthsJD7PYN0wIDAQAB";
    private static final String PRIVATE_KEY = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAPPFFl02WY4TbS+IjSErPnoUQ+7oB49FUUQXITDaYZgjF7NhhghUNdh5JZWUCLmt51z0WaJNHTqqw/0IjuJFfCqAfRkn1d1+Jg5smmIkfTkKv2PWbxv/UKAkVSEgS9zGV8uez5l8hkU/8SzbYSUnXRFv+PVSiQOs5+2GwkPs9g3TAgMBAAECgYABhAa1Eqrw4rcJliBGhgXP7YNUn3W4ktZEBZ7Ltnfn4cP6dg9GyvtAutQcf3ykZuvq3IwYIcGeRUSNtLlqt8LjX6jF4YxK+W5mVHWQXF5zIFAvCmR28/lmles9ats+Vc9a8uGAesk0komw8DSSNkDFD25MGv3IC6E/aMNH8jbE6QJBAPYipMlgySDbOj9AnONKyKxfqnknPs1zppbtavVjdq9Txi9avZZS9tYzsnYd9JIPmNjboGDeHmjrr8eDYG3FPDkCQQD9iiyM1iAo6jubq41inJx+N+yEOiPodAY651ffu/0kt4ekknkWdQtaz7YkPFTQ/r8jnIYT+gc+xoD1yAYNWPJrAkEAwAjvgJ6ezZ+9pjQHM97ZCoPpQXU5LpqY0xCRbrsXkltOOQwW9v3rz9ut1TIx6vN4UVmpm2oxV7LrBkKeLYbBIQJBAM0ufiE3xGz4KmcvrUrprSfkQC8+EgMzqjsPL6krrJCDSm2f8AiBjj/equumO4mrL6o1KLiP9j8STBkb1/IDUqMCQQDleUQN8KWl5CY+jklCFMV9qgbd5DX1NcEOEmGTyTTrBXPiHWcy89lLyjS8F1VHuvaCwjtNpOKVjbDbUDGXJ2D5";

    @Test
    void shouldEncryptAndDecryptSuccessfully() throws Exception {
        // Given
        String originalText = "Hello, World!";

        // When
        String encrypted = RSAEncryptor.encrypt(originalText, PUBLIC_KEY);
        String decrypted = RSAEncryptor.decrypt(encrypted, PRIVATE_KEY);

        // Then
        assertNotEquals(originalText, encrypted);
        assertEquals(originalText, decrypted);
    }

    @Test
    void shouldThrowExceptionForInvalidPublicKey() {
        // Given
        String invalidPublicKey = "invalid-key";
        String text = "Hello, World!";

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            RSAEncryptor.encrypt(text, invalidPublicKey);
        });
        assertNotNull(exception);
    }

    @Test
    void shouldThrowExceptionForInvalidPrivateKey() {
        // Given
        String validEncryptedData = "some-valid-encrypted-data";
        String invalidPrivateKey = "invalid-key";

        // When & Then
        Exception exception = assertThrows(Exception.class, () -> {
            RSAEncryptor.decrypt(validEncryptedData, invalidPrivateKey);
        });
        assertNotNull(exception);
    }

    @Test
    void testEncrypt() {
        String encryptedData = "<EMAIL>";
        String secretKey = "MerCaso@666";
        String encrypt = RSAEncryptor.encrypt(encryptedData, secretKey, PUBLIC_KEY);
        assertNotNull(encrypt);
    }
}