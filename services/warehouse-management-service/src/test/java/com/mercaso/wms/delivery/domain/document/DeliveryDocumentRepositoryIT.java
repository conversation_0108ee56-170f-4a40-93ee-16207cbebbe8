package com.mercaso.wms.delivery.domain.document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.utils.DeliveryMockDataUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DeliveryDocumentRepositoryIT extends AbstractIT {

    @Autowired
    private DeliveryDocumentRepository deliveryDocumentRepository;

    @Test
    void when_create_document_then_success() {
        DeliveryDocument deliveryDocument = DeliveryMockDataUtils.buildDocument();

        DeliveryDocument saved = deliveryDocumentRepository.save(deliveryDocument);

        assertNotNull(saved);
        assertNotNull(saved.getId());
        assertEquals(deliveryDocument.getEntityId(), saved.getEntityId());
        assertEquals(deliveryDocument.getEntityName(), saved.getEntityName());
        assertEquals(deliveryDocument.getDocumentType(), saved.getDocumentType());
        assertEquals(deliveryDocument.getFileName(), saved.getFileName());

        DeliveryDocument deliveryDocument1 = deliveryDocumentRepository.findById(saved.getId());

        assertNotNull(deliveryDocument1);
        assertEquals(saved.getId(), deliveryDocument1.getId());
        assertEquals(saved.getEntityId(), deliveryDocument1.getEntityId());
        assertEquals(saved.getEntityName(), deliveryDocument1.getEntityName());
        assertEquals(saved.getDocumentType(), deliveryDocument1.getDocumentType());
        assertEquals(saved.getFileName(), deliveryDocument1.getFileName());
    }

}