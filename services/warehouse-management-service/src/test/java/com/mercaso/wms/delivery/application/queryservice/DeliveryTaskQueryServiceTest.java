package com.mercaso.wms.delivery.application.queryservice;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.mapper.deliverytask.DeliveryTaskDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("DeliveryTaskQueryService Tests")
class DeliveryTaskQueryServiceTest {

    @Mock
    private DeliveryTaskRepository deliveryTaskRepository;

    @Mock
    private DeliveryTaskDtoApplicationMapper deliveryTaskDtoApplicationMapper;

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    @Mock
    private DeliveryOrderDtoApplicationMapper deliveryOrderDtoApplicationMapper;

    @InjectMocks
    private DeliveryTaskQueryService deliveryTaskQueryService;

    private UUID taskId;
    private DeliveryTask mockDeliveryTask;
    private DeliveryTaskDto mockDeliveryTaskDto;

    @BeforeEach
    void setUp() {
        taskId = UUID.randomUUID();
        mockDeliveryTask = DeliveryTask.builder().id(taskId).build();
        mockDeliveryTaskDto = new DeliveryTaskDto();
        mockDeliveryTaskDto.setId(taskId);
    }

    @Nested
    @DisplayName("FindById Tests")
    class FindByIdTests {

        @Test
        @DisplayName("Should return null when task doesn't exist")
        void shouldReturnNullWhenTaskDoesNotExist() {
            // Given
            when(deliveryTaskRepository.findById(taskId)).thenReturn(null);

            // When
            DeliveryTaskDto result = deliveryTaskQueryService.findById(taskId);

            // Then
            assertThat(result).isNull();
            verify(deliveryTaskRepository).findById(taskId);
        }

        @Test
        @DisplayName("Should return task without orders when no orders are found")
        void shouldReturnTaskWithoutOrdersWhenNoOrdersFound() {
            // Given
            setupBasicTaskMocks();
            when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(Collections.emptyList());

            // When
            DeliveryTaskDto result = deliveryTaskQueryService.findById(taskId);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(taskId);
            assertThat(result.getDeliveryOrders()).isNull();

            // Verify interactions
            verifyBasicTaskInteractions();
        }

        @ParameterizedTest(name = "Sequence order: {0}")
        @MethodSource("com.mercaso.wms.delivery.application.queryservice.DeliveryTaskQueryServiceTest#provideSequenceTestCases")
        @DisplayName("Should correctly sort orders by sequence")
        void shouldSortOrdersBySequence(String testName, List<Integer> inputSequences, List<Integer> expectedOrder) {
            // Given
            setupBasicTaskMocks();

            // Create orders with provided sequences
            List<DeliveryOrder> unsortedOrders = createOrdersWithSequences(inputSequences);
            when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(unsortedOrders);

            // Setup DTO mapping
            setupOrderDtoMapping();

            // When
            DeliveryTaskDto result = deliveryTaskQueryService.findById(taskId);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getDeliveryOrders()).hasSize(inputSequences.size());

            // Extract and verify sequence order
            List<Integer> actualSequence = result.getDeliveryOrders().stream()
                .map(DeliveryOrderDto::getSequence)
                .toList();

            assertThat(actualSequence).isEqualTo(expectedOrder);

            // Verify interactions
            verifyBasicTaskInteractions();
        }

        private void setupBasicTaskMocks() {
            when(deliveryTaskRepository.findById(taskId)).thenReturn(mockDeliveryTask);
            when(deliveryTaskDtoApplicationMapper.domainToDto(mockDeliveryTask)).thenReturn(mockDeliveryTaskDto);
        }

        private void verifyBasicTaskInteractions() {
            verify(deliveryTaskRepository).findById(taskId);
            verify(deliveryTaskDtoApplicationMapper).domainToDto(mockDeliveryTask);
            verify(deliveryOrderRepository).findAllByDeliveryTaskId(taskId);
        }

        private void setupOrderDtoMapping() {
            when(deliveryOrderDtoApplicationMapper.domainToDto(any())).thenAnswer(invocation -> {
                DeliveryOrder order = invocation.getArgument(0);
                DeliveryOrderDto dto = new DeliveryOrderDto();
                dto.setSequence(order.getSequence());
                return dto;
            });
        }

        private List<DeliveryOrder> createOrdersWithSequences(List<Integer> sequences) {
            return sequences.stream()
                .map(seq -> createDeliveryOrder(seq))
                .toList();
        }
    }

    @Test
    @DisplayName("Should fetch and map complete delivery task with orders")
    void shouldFetchAndMapCompleteDeliveryTaskWithOrders() {
        // Prepare test data
        UUID taskId = UUID.randomUUID();
        DeliveryTask deliveryTask = createTaskWithDetails(taskId);
        DeliveryTaskDto deliveryTaskDto = createTaskDtoWithDetails(taskId);

        List<DeliveryOrder> deliveryOrders = List.of(mock(DeliveryOrder.class), mock(DeliveryOrder.class));
        List<DeliveryOrderDto> deliveryOrderDtos = List.of(mock(DeliveryOrderDto.class), mock(DeliveryOrderDto.class));

        // Configure mock behavior
        when(deliveryTaskRepository.findById(taskId)).thenReturn(deliveryTask);
        when(deliveryTaskDtoApplicationMapper.domainToDto(deliveryTask)).thenReturn(deliveryTaskDto);
        when(deliveryOrderRepository.findAllByDeliveryTaskId(taskId)).thenReturn(deliveryOrders);

        for (int i = 0; i < deliveryOrders.size(); i++) {
            when(deliveryOrderDtoApplicationMapper.domainToDto(deliveryOrders.get(i))).thenReturn(deliveryOrderDtos.get(i));
        }

        // When
        DeliveryTaskDto result = deliveryTaskQueryService.findById(taskId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(taskId);
        assertThat(result.getNumber()).isEqualTo("TD-001");
        assertThat(result.getDeliveryOrders()).hasSize(2);

        // Verify method calls
        verify(deliveryTaskRepository).findById(taskId);
        verify(deliveryTaskDtoApplicationMapper).domainToDto(deliveryTask);
        verify(deliveryOrderRepository).findAllByDeliveryTaskId(taskId);
    }

    /**
     * 提供用于参数化测试的序列测试用例
     */
    static Stream<Arguments> provideSequenceTestCases() {
        return Stream.of(
            // testName, input sequences, expected order
            Arguments.of("Mixed values with null", Arrays.asList(5, 2, null, 1), Arrays.asList(1, 2, 5, null)),
            Arguments.of("All nulls", Arrays.asList(null, null, null), Arrays.asList(null, null, null)),
            Arguments.of("Already sorted", Arrays.asList(1, 2, 3, 4), Arrays.asList(1, 2, 3, 4)),
            Arguments.of("Reverse order", Arrays.asList(4, 3, 2, 1), Arrays.asList(1, 2, 3, 4)),
            Arguments.of("Multiple nulls mixed", Arrays.asList(null, 3, null, 1), Arrays.asList(1, 3, null, null))
        );
    }

    private DeliveryTask createTaskWithDetails(UUID id) {
        return DeliveryTask.builder()
            .id(id)
            .number("TD-001")
            .deliveryDate(LocalDate.now().toString())
            .status(DeliveryTaskStatus.CREATED)
            .truckNumber("TRUCK-001")
            .driverUserName("TestDriver")
            .build();
    }

    private DeliveryTaskDto createTaskDtoWithDetails(UUID id) {
        return DeliveryTaskDto.builder()
            .id(id)
            .number("TD-001")
            .deliveryDate(LocalDate.now().toString())
            .status(DeliveryTaskStatus.CREATED)
            .truckNumber("TRUCK-001")
            .driverUserName("TestDriver")
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }

    private DeliveryOrder createDeliveryOrder(Integer sequence) {
        return DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .sequence(sequence)
            .build();
    }
} 