package com.mercaso.wms.interfaces.query;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand;
import com.mercaso.wms.application.dto.scanrecord.ScanRecordDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import com.mercaso.wms.utils.ScanRecordResourceApi;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryScanRecordResourceIT extends AbstractIT {

    @Autowired
    private ScanRecordResourceApi scanRecordResourceApi;
    @Autowired
    DeliveryOrderWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void when_querying_scan_records_by_delivery_order_id_then_success() throws Exception {
        scanRecordJpaDao.deleteAll();
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumber(shopifyOrderDto.getName());

        BatchScanRecordCommand command = BatchScanRecordCommand.builder()
            .scanRecords(List.of(
                BatchScanRecordCommand.ScanRecordCommand.builder()
                    .deliveryOrderId(deliveryOrderDo.getId())
                    .skuNumber(shopifyOrderDto.getLineItems().getFirst().getSku())
                    .scanType(ScanType.DELIVERY_SCAN_REMOVED)
                    .qty(-1)
                    .build(),
                BatchScanRecordCommand.ScanRecordCommand.builder()
                    .deliveryOrderId(deliveryOrderDo.getId())
                    .skuNumber(shopifyOrderDto.getLineItems().getLast().getSku())
                    .scanType(ScanType.DELIVERY_SCAN_REMOVED)
                    .qty(-2)
                    .build()
            ))
            .build();

        scanRecordResourceApi.create(command);

        List<ScanRecordDto> scanRecordDtos = scanRecordResourceApi.findByDeliveryOrderId(deliveryOrderDo.getId());

        assertEquals(2, scanRecordDtos.size());
        scanRecordDtos.forEach(scanRecordDto -> {
            assertEquals(ScanType.DELIVERY_SCAN_REMOVED.name(), scanRecordDto.getScanType());
        });
    }

}