package com.mercaso.wms.infrastructure.repository.consolidationlocation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

class ConsolidationLocationSyncLogJpaDaoIT extends AbstractIT {

    @Autowired
    private ConsolidationLocationSyncLogJpaDao consolidationLocationSyncLogJpaDao;

    @Test
    void when_search_by_delivery_date_then_return_page() {
        // given
        ConsolidationLocationSyncLog consolidationLocationSyncLog = new ConsolidationLocationSyncLog();
        consolidationLocationSyncLog.setDeliveryDate(RandomStringUtils.randomAlphabetic(10));
        consolidationLocationSyncLog.setCreatedUserName(RandomStringUtils.randomAlphabetic(10));
        consolidationLocationSyncLogJpaDao.save(consolidationLocationSyncLog);
        // when
        Page<ConsolidationLocationSyncLog> consolidationLocationSyncLogs = consolidationLocationSyncLogJpaDao.searchBy(
            consolidationLocationSyncLog.getDeliveryDate(),
            null,
            PageRequest.of(0, 10));
        // then
        assertNotNull(consolidationLocationSyncLogs.getContent());
        assertEquals(1, consolidationLocationSyncLogs.getContent().size());

        Page<ConsolidationLocationSyncLog> byCreatedUserNameLogs = consolidationLocationSyncLogJpaDao.searchBy(
            null,
            consolidationLocationSyncLog.getCreatedUserName(),
            PageRequest.of(0, 10));
        // then
        assertNotNull(byCreatedUserNameLogs.getContent());
        assertEquals(1, byCreatedUserNameLogs.getContent().size());
    }

}