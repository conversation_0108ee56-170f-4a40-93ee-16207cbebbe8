package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class SevenStarsTotalSheetWriterTest extends Writer {

    private final SevenStarsTotalSheetWriter sevenStarsTotalSheetWriter = new SevenStarsTotalSheetWriter();

    @Test
    void when_write_seven_stars_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> sevenStars = Lists.newArrayList();
        ExcelBatchDto sevenStarsExcelBatchDto = new ExcelBatchDto();
        sevenStarsExcelBatchDto.setItemNumber("sevenStars");
        sevenStarsExcelBatchDto.setFrom("Location2");
        sevenStarsExcelBatchDto.setQuantity(1);
        sevenStarsExcelBatchDto.setSource(SourceEnum.SEVEN_STARS.name());
        sevenStarsExcelBatchDto.setFrom("121");
        sevenStarsExcelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto sevenStarsExcelBatchDto1 = new ExcelBatchDto();
        sevenStarsExcelBatchDto1.setItemNumber("sevenStars");
        sevenStarsExcelBatchDto1.setFrom("Location2");
        sevenStarsExcelBatchDto1.setQuantity(1);
        sevenStarsExcelBatchDto1.setSource(SourceEnum.SEVEN_STARS.name());
        sevenStarsExcelBatchDto1.setFrom("10B");
        sevenStarsExcelBatchDto1.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto sevenStarsExcelBatchDto2 = new ExcelBatchDto();
        sevenStarsExcelBatchDto2.setItemNumber("sevenStars");
        sevenStarsExcelBatchDto2.setFrom("Location2");
        sevenStarsExcelBatchDto2.setQuantity(1);
        sevenStarsExcelBatchDto2.setSource(SourceEnum.SEVEN_STARS.name());
        sevenStarsExcelBatchDto2.setFrom("10A");
        sevenStarsExcelBatchDto2.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        sevenStars.add(sevenStarsExcelBatchDto);
        sevenStars.add(sevenStarsExcelBatchDto1);
        sevenStars.add(sevenStarsExcelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.SEVEN_STARS.name(), sevenStars));

        writeBatchTemplate(condition, sevenStarsTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> sevenStarsTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SEVEN_STARS_TOTALS.getValue()).doReadSync();

        assertEquals(4, sevenStarsTotals.size());
        assertEquals("3", sevenStarsTotals.get(3).get(6));
    }

    @Test
    void when_write_empty_seven_start_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> batchDtos = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.SEVEN_STARS.name(), batchDtos));

        writeBatchTemplate(condition, sevenStarsTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> exoticTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SEVEN_STARS_TOTALS.getValue()).doReadSync();

        assertEquals(3, exoticTotals.size());
    }

}