package com.mercaso.wms.delivery.interfaces;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildAccount;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildApprovedRoutesResponse;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildCustomer;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryOrder;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildOrder;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildStep;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildVehicleSettingMap;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.deliverytask.BuildDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.command.deliverytask.ReassignDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.command.deliverytask.UpdateDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PostCheckDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.PreCheckDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.customer.CustomerRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoutesResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.CurrentRoutes;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Order;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Route;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Vehicle;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.VehicleSetting;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DeliveryTaskResourceIT extends AbstractIT {

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private DeliveryTaskJpaDao deliveryTaskJpaDao;
    @Autowired
    private RmRouteRepository rmRouteRepository;

    @Test
    void testBuildTasks() {

        LocalDate deliveryDate = LocalDate.now().plusDays(1);

        DeliveryTaskDo taskDo1 = buildDeliveryTaskDo("TD-001", deliveryDate, "TRUCK-001", "Driver1");
        DeliveryTaskDo taskDo2 = buildDeliveryTaskDo("TD-002", deliveryDate.minusDays(1), "TRUCK-002", "Driver2");
        DeliveryTaskDo savedTask1 = deliveryTaskJpaDao.save(taskDo1);
        DeliveryTaskDo savedTask2 = deliveryTaskJpaDao.save(taskDo2);

        ApprovedRoutesResponse approvedRoutesResponse = buildApprovedRoutesResponse(2, deliveryDate, true);
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(approvedRoutesResponse);

        Customer customer1 = buildCustomer();
        Customer customer2 = buildCustomer();
        Customer savedCustom1 = customerRepository.save(customer1);
        Customer savedCustom2 = customerRepository.save(customer2);

        ApprovedRoute approvedRoute1 = approvedRoutesResponse.getApprovedRoutes().get(0);
        ApprovedRoute approvedRoute2 = approvedRoutesResponse.getApprovedRoutes().get(1);

        Account account1 = buildAccount(approvedRoute1.getDriver().getEmail());
        Account account2 = buildAccount(approvedRoute2.getDriver().getEmail());
        Account savedDriver1 = accountRepository.save(account1);
        Account savedDriver2 = accountRepository.save(account2);

        Order order1_1 = buildOrder(UUID.randomUUID());
        Order order1_2 = buildOrder(UUID.randomUUID());
        Order order1_3 = buildOrder(UUID.randomUUID());
        Map<UUID, Order> approvedRoute1_orders = new HashMap<>();
        approvedRoute1_orders.put(UUID.fromString(order1_1.getId()), order1_1);
        approvedRoute1_orders.put(UUID.fromString(order1_2.getId()), order1_2);
        approvedRoute1_orders.put(UUID.fromString(order1_3.getId()), order1_3);
        approvedRoute1.setOrders(approvedRoute1_orders);

        Order order2_1 = buildOrder(UUID.randomUUID());
        Order order2_2 = buildOrder(UUID.randomUUID());
        Order order2_3 = buildOrder(UUID.randomUUID());
        Map<UUID, Order> approvedRoute2_orders = new HashMap<>();
        approvedRoute2_orders.put(UUID.fromString(order2_1.getId()), order2_1);
        approvedRoute2_orders.put(UUID.fromString(order2_2.getId()), order2_2);
        approvedRoute2_orders.put(UUID.fromString(order2_3.getId()), order2_3);
        approvedRoute2.setOrders(approvedRoute2_orders);

        Step step1_1 = buildStep(1, order1_1.getId(), false);
        Step step1_2 = buildStep(2, order1_2.getId(), true);
        Step step1_3 = buildStep(3, order1_2.getId(), false);
        Step step1_4 = buildStep(3, order1_3.getId(), false);
        approvedRoute1.getRoute().setSteps(List.of(step1_1, step1_2, step1_3, step1_4));

        Step step2_1 = buildStep(1, order2_1.getId(), false);
        Step step2_2 = buildStep(2, order2_2.getId(), false);
        Step step2_3 = buildStep(3, order2_3.getId(), false);
        Step step2_4 = buildStep(3, order2_3.getId(), true);
        approvedRoute2.getRoute().setSteps(List.of(step2_1, step2_2, step2_3, step2_4));

        DeliveryOrder deliveryOrder1_1 = buildDeliveryOrder();
        deliveryOrder1_1.setShopifyOrderId(order1_1.getId());
        deliveryOrder1_1.setCustomer(savedCustom1);
        deliveryOrder1_1.setOrderNumber(order1_1.getName());
        DeliveryOrder deliveryOrder1_2 = buildDeliveryOrder();
        deliveryOrder1_2.setShopifyOrderId(order1_2.getId());
        deliveryOrder1_2.setCustomer(savedCustom1);
        deliveryOrder1_2.setOrderNumber(order1_2.getName());
        DeliveryOrder deliveryOrder1_3 = buildDeliveryOrder();
        deliveryOrder1_3.setShopifyOrderId(order1_3.getId());
        deliveryOrder1_3.setCustomer(savedCustom1);
        deliveryOrder1_3.setOrderNumber(order1_3.getName());
        DeliveryOrder deliveryOrder2_1 = buildDeliveryOrder();
        deliveryOrder2_1.setShopifyOrderId(order2_1.getId());
        deliveryOrder2_1.setCustomer(savedCustom2);
        deliveryOrder2_1.setOrderNumber(order2_1.getName());
        DeliveryOrder deliveryOrder2_2 = buildDeliveryOrder();
        deliveryOrder2_2.setShopifyOrderId(order2_2.getId());
        deliveryOrder2_2.setCustomer(savedCustom2);
        deliveryOrder2_2.setOrderNumber(order2_2.getName());
        DeliveryOrder deliveryOrder2_3 = buildDeliveryOrder();
        deliveryOrder2_3.setShopifyOrderId(order2_3.getId());
        deliveryOrder2_3.setCustomer(savedCustom2);
        deliveryOrder2_3.setOrderNumber(order2_3.getName());

        deliveryOrderRepository.saveAll(List.of(deliveryOrder1_1,
            deliveryOrder1_2,
            deliveryOrder1_3,
            deliveryOrder2_1,
            deliveryOrder2_2,
            deliveryOrder2_3));

        DeliveryOrder revertDeliveryOrder1 = buildDeliveryOrder();
        revertDeliveryOrder1.setCustomer(savedCustom1);
        revertDeliveryOrder1.setStatus(DeliveryOrderStatus.ASSIGNED);
        revertDeliveryOrder1.setDeliveryTaskId(savedTask1.getId());
        DeliveryOrder revertDeliveryOrder2 = buildDeliveryOrder();
        revertDeliveryOrder2.setCustomer(savedCustom2);
        revertDeliveryOrder2.setStatus(DeliveryOrderStatus.ASSIGNED);
        revertDeliveryOrder2.setDeliveryTaskId(savedTask1.getId());

        DeliveryOrder remainDeliveryOrder = buildDeliveryOrder();
        remainDeliveryOrder.setCustomer(savedCustom1);
        remainDeliveryOrder.setStatus(DeliveryOrderStatus.ASSIGNED);
        remainDeliveryOrder.setDeliveryTaskId(savedTask2.getId());
        remainDeliveryOrder.setSequence(1);

        deliveryTaskResourceApi.buildDeliveryTasks(deliveryDate);

        // assert
        List<DeliveryTaskDo> tasks = deliveryTaskJpaDao.findByDeliveryDate(deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER));
        assert tasks.stream().anyMatch(task -> task.getTruckNumber().equals(approvedRoute1.getVehicle().getExternalId()));
        assert tasks.stream().anyMatch(task -> task.getTruckNumber().equals(approvedRoute2.getVehicle().getExternalId()));
        assert tasks.stream().anyMatch(task -> task.getDriverUserId().equals(savedDriver1.getUserId()));
        assert tasks.stream()
            .anyMatch(task -> task.getDriverUserId().equals(savedDriver2.getUserId()) && task.getBreakStartAt() != null);

        deliveryOrderRepository.findAllByOrderNumberIn(List.of(order1_1.getName(), order1_2.getName(), order1_3.getName()))
            .forEach(deliveryOrder -> {
                assert deliveryOrder.getDeliveryTaskId() != null;
                assert deliveryOrder.getStatus().equals(DeliveryOrderStatus.ASSIGNED);
                assert tasks.stream().anyMatch(t -> t.getId().equals(deliveryOrder.getDeliveryTaskId()));
                assert deliveryOrder.getPlanArriveAt() != null;
                assert deliveryOrder.getPlanDeliveryAt() != null;
            });

        deliveryOrderRepository.findAllByOrderNumberIn(List.of(revertDeliveryOrder1.getOrderNumber())).forEach(deliveryOrder -> {
            assert deliveryOrder.getDeliveryTaskId() == null;
            assert deliveryOrder.getStatus().equals(DeliveryOrderStatus.CREATED);
            assert deliveryOrder.getPlanArriveAt() == null;
            assert deliveryOrder.getPlanDeliveryAt() == null;
            assert deliveryOrder.getSequence() == null;
        });

        deliveryOrderRepository.findAllByOrderNumberIn(List.of(remainDeliveryOrder.getOrderNumber())).forEach(deliveryOrder -> {
            assert deliveryOrder.getDeliveryTaskId().equals(savedTask2.getId());
            assert deliveryOrder.getStatus().equals(DeliveryOrderStatus.ASSIGNED);
            assert deliveryOrder.getPlanArriveAt() != null;
            assert deliveryOrder.getPlanDeliveryAt() != null;
            assert deliveryOrder.getSequence() != null;
        });

    }

    @Test
    void testRebuildTask() {
        LocalDate deliveryDate = LocalDate.now().plusDays(3);

        // Prepare test data
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", deliveryDate, "TRUCK-001", "Driver1");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        UUID taskId = savedTask.getId();

        String routeId = "RT-" + RandomStringUtils.randomNumeric(6);
        RmRoute rmRoute = buildRmRoute(UUID.randomUUID(), taskId);
        rmRoute.setRouteId(routeId);
        rmRouteRepository.save(rmRoute);

        Customer customer = buildCustomer();
        Customer savedCustomer = customerRepository.save(customer);

        Account driver = buildAccount("<EMAIL>");
        Account savedDriver = accountRepository.save(driver);

        // Create test order data
        UUID orderId1 = UUID.randomUUID();
        UUID orderId2 = UUID.randomUUID();
        UUID orderId3 = UUID.randomUUID();

        Order order1 = buildOrder(orderId1);
        Order order2 = buildOrder(orderId2);
        Order order3 = buildOrder(orderId3);

        // Create and save test delivery orders
        DeliveryOrder deliveryOrder1 = buildDeliveryOrder();
        deliveryOrder1.setShopifyOrderId(orderId1.toString());
        deliveryOrder1.setOrderNumber(order1.getName());
        deliveryOrder1.setCustomer(savedCustomer);
        deliveryOrder1.setDeliveryTaskId(savedTask.getId());
        deliveryOrder1.setStatus(DeliveryOrderStatus.ASSIGNED);
        deliveryOrder1.setSequence(1);

        DeliveryOrder deliveryOrder2 = buildDeliveryOrder();
        deliveryOrder2.setShopifyOrderId(orderId2.toString());
        deliveryOrder2.setOrderNumber(order2.getName());
        deliveryOrder2.setCustomer(savedCustomer);
        deliveryOrder2.setDeliveryTaskId(savedTask.getId());
        deliveryOrder2.setStatus(DeliveryOrderStatus.ASSIGNED);
        deliveryOrder2.setSequence(2);

        DeliveryOrder deliveryOrder3 = buildDeliveryOrder();
        deliveryOrder3.setShopifyOrderId(orderId3.toString());
        deliveryOrder3.setOrderNumber(order3.getName());
        deliveryOrder3.setCustomer(savedCustomer);
        deliveryOrder3.setStatus(DeliveryOrderStatus.CREATED);

        deliveryOrderRepository.saveAll(List.of(deliveryOrder1, deliveryOrder2, deliveryOrder3));

        // Prepare mock route response using CurrentRoutes instead of ApprovedRoute
        CurrentRoutes currentRoutes = new CurrentRoutes();

        // Create route object
        Route routeObj = new Route();
        routeObj.setId(routeId);
        routeObj.setDate(deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER));
        routeObj.setDriverId(savedDriver.getUserId().toString());
        routeObj.setVehicleId("TRUCK-001");

        // Setup route steps
        Step step1 = buildStep(1, orderId1.toString(), false);
        Step step3 = buildStep(2, orderId3.toString(), false);
        routeObj.setSteps(List.of(step1, step3));

        // Add route to routes map
        Map<String, Route> routesMap = new HashMap<>();
        routesMap.put(routeId, routeObj);
        currentRoutes.setRoutes(routesMap);

        // Define updated order list - remove order2, keep order1, add order3
        Map<UUID, Order> updatedOrders = new HashMap<>();
        updatedOrders.put(orderId1, order1);
        updatedOrders.put(orderId3, order3);
        currentRoutes.setOrders(updatedOrders);

        // Add driver
        Map<UUID, Driver> driversMap = new HashMap<>();
        Driver driver1 = new Driver();
        driver1.setId(savedDriver.getUserId().toString());
        driver1.setName(savedDriver.getUserName());
        driver1.setEmail(savedDriver.getEmail());
        driversMap.put(savedDriver.getUserId(), driver1);
        currentRoutes.setDrivers(driversMap);

        // Add vehicle
        Map<UUID, Vehicle> vehiclesMap = new HashMap<>();
        Vehicle vehicle = new Vehicle();
        vehicle.setId("TRUCK-001");
        vehicle.setExternalId("TRUCK-001");
        Map<String, VehicleSetting> vehicleSettingMap = buildVehicleSettingMap(routeObj.getOriginalDate());
        vehicle.setSettings(vehicleSettingMap);
        vehiclesMap.put(UUID.randomUUID(), vehicle);
        currentRoutes.setVehicles(vehiclesMap);

        // Mock getCurrentRoute instead of getApprovedRoute
        when(routeManagerAdaptor.getCurrentRoute(routeId)).thenReturn(currentRoutes);

        // Execute test
        deliveryTaskResourceApi.rebuildTask(taskId);

        // Verify order1 is still assigned to the task with updated information
        deliveryOrderRepository.findAllByOrderNumberIn(List.of(order1.getName()))
            .forEach(order -> {
                assert order.getDeliveryTaskId() != null;
                assert order.getDeliveryTaskId().equals(taskId);
                assert order.getStatus().equals(DeliveryOrderStatus.ASSIGNED);
                assert order.getSequence() != null;
                assert order.getPlanArriveAt() != null;
                assert order.getPlanDeliveryAt() != null;
            });

        // Verify order2 has been removed from the task
        deliveryOrderRepository.findAllByOrderNumberIn(List.of(order2.getName()))
            .forEach(order -> {
                assert order.getDeliveryTaskId() == null;
                assert order.getStatus().equals(DeliveryOrderStatus.CREATED);
                assert order.getPlanArriveAt() == null;
                assert order.getPlanDeliveryAt() == null;
                assert order.getSequence() == null;
            });

        // Verify order3 has been added to the task
        deliveryOrderRepository.findAllByOrderNumberIn(List.of(order3.getName()))
            .forEach(order -> {
                assert order.getDeliveryTaskId() != null;
                assert order.getDeliveryTaskId().equals(taskId);
                assert order.getStatus().equals(DeliveryOrderStatus.ASSIGNED);
                assert order.getSequence() != null;
                assert order.getPlanArriveAt() != null;
                assert order.getPlanDeliveryAt() != null;
            });

        // Verify route record was updated
        List<RmRoute> updatedRmRoutes = rmRouteRepository.findByDeliveryTaskId(taskId);
        assert !updatedRmRoutes.isEmpty();
        assert updatedRmRoutes.getFirst().getRouteId().equals(routeId);
    }

    @Test
    void testCompleteTask() {
        LocalDate deliveryDate = LocalDate.now().plusDays(2);

        // Prepare test data - create task
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-COMP-001", deliveryDate, "TRUCK-COMP-001", "Driver-Comp");
        taskDo.setStatus(DeliveryTaskStatus.IN_PROGRESS);
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        UUID taskId = savedTask.getId();

        // Create test customer
        Customer customer = buildCustomer();
        Customer savedCustomer = customerRepository.save(customer);

        // Create and save test delivery orders with DELIVERED status
        DeliveryOrder deliveryOrder1 = buildDeliveryOrder();
        deliveryOrder1.setOrderNumber("COMP-ORDER-001");
        deliveryOrder1.setCustomer(savedCustomer);
        deliveryOrder1.setDeliveryTaskId(savedTask.getId());
        deliveryOrder1.setStatus(DeliveryOrderStatus.DELIVERED);

        DeliveryOrder deliveryOrder2 = buildDeliveryOrder();
        deliveryOrder2.setOrderNumber("COMP-ORDER-002");
        deliveryOrder2.setCustomer(savedCustomer);
        deliveryOrder2.setDeliveryTaskId(savedTask.getId());
        deliveryOrder2.setStatus(DeliveryOrderStatus.CANCELED);

        deliveryOrderRepository.saveAll(List.of(deliveryOrder1, deliveryOrder2));

        // Execute test
        deliveryTaskResourceApi.completeTask(taskId);

        // Verify task is completed
        DeliveryTaskDo completedTask = deliveryTaskJpaDao.findById(taskId).orElse(null);
        assert completedTask != null;
        assert DeliveryTaskStatus.COMPLETED.equals(completedTask.getStatus());
    }

    @Test
    void testBatchCompleteTasks() {
        LocalDate deliveryDate = LocalDate.now().plusDays(2);

        // Prepare test data - create task
        DeliveryTaskDo taskDo1 = buildDeliveryTaskDo("TD-BATCH-001", deliveryDate, "TRUCK-BATCH-001", "Driver-Batch-1");
        taskDo1.setStatus(DeliveryTaskStatus.IN_PROGRESS);
        DeliveryTaskDo savedTask1 = deliveryTaskJpaDao.save(taskDo1);
        UUID taskId1 = savedTask1.getId();

        DeliveryTaskDo taskDo2 = buildDeliveryTaskDo("TD-BATCH-002", deliveryDate, "TRUCK-BATCH-002", "Driver-Batch-2");
        taskDo2.setStatus(DeliveryTaskStatus.IN_PROGRESS);
        DeliveryTaskDo savedTask2 = deliveryTaskJpaDao.save(taskDo2);
        UUID taskId2 = savedTask2.getId();

        // Create test customer
        Customer customer = buildCustomer();
        Customer savedCustomer = customerRepository.save(customer);

        // Create and save test delivery orders for task 1
        DeliveryOrder order1_1 = buildDeliveryOrder();
        order1_1.setOrderNumber("BATCH-ORDER-001");
        order1_1.setCustomer(savedCustomer);
        order1_1.setDeliveryTaskId(savedTask1.getId());
        order1_1.setStatus(DeliveryOrderStatus.DELIVERED);

        DeliveryOrder order1_2 = buildDeliveryOrder();
        order1_2.setOrderNumber("BATCH-ORDER-002");
        order1_2.setCustomer(savedCustomer);
        order1_2.setDeliveryTaskId(savedTask1.getId());
        order1_2.setStatus(DeliveryOrderStatus.DELIVERED);

        // Create and save test delivery orders for task 2
        DeliveryOrder order2_1 = buildDeliveryOrder();
        order2_1.setOrderNumber("BATCH-ORDER-003");
        order2_1.setCustomer(savedCustomer);
        order2_1.setDeliveryTaskId(savedTask2.getId());
        order2_1.setStatus(DeliveryOrderStatus.DELIVERED);

        DeliveryOrder order2_2 = buildDeliveryOrder();
        order2_2.setOrderNumber("BATCH-ORDER-004");
        order2_2.setCustomer(savedCustomer);
        order2_2.setDeliveryTaskId(savedTask2.getId());
        order2_2.setStatus(DeliveryOrderStatus.CANCELED);

        deliveryOrderRepository.saveAll(List.of(order1_1, order1_2, order2_1, order2_2));

        // Execute test
        deliveryTaskResourceApi.batchCompleteTasks(List.of(taskId1, taskId2));

        // Verify tasks are completed
        List<DeliveryTaskDo> completedTasks = deliveryTaskJpaDao.findAllById(List.of(taskId1, taskId2));
        assert completedTasks.size() == 2;
        assert completedTasks.stream().allMatch(task -> DeliveryTaskStatus.COMPLETED.equals(task.getStatus()));
    }

    private RmRoute buildRmRoute(UUID id, UUID taskId) {

        return RmRoute.builder()
            .id(id)
            .routeId(UUID.randomUUID().toString())
            .vehicleId("TRUCK-001")
            .driverId("Driver1")
            .deliveryTaskId(taskId)
            .createdBy("system")
            .createdAt(Instant.now())
            .updatedBy("system")
            .updatedAt(Instant.now())
            .build();
    }

    @Test
    void when_reassign_task_should_success() throws JsonProcessingException {
        // Prepare test data - create task
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-REASSIGN-001", LocalDate.now(), "TRUCK-REASSIGN-001", "Driver-Reassign");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        UUID taskId = savedTask.getId();

        Account driver = buildAccount(RandomStringUtils.randomAlphabetic(10) + "@mercaso.com");
        Account savedDriver = accountRepository.save(driver);

        // Prepare reassign command
        ReassignDeliveryTaskCommand command = new ReassignDeliveryTaskCommand();
        command.setPreviousUserId(taskDo.getDriverUserId());
        command.setCurrentUserId(savedDriver.getUserId());

        // Execute reassign task
        DeliveryTaskDto reassignedTask = deliveryTaskResourceApi.reassignTask(taskId, command);

        // Verify task is reassigned
        assertEquals(taskId, reassignedTask.getId());
        assertEquals(savedDriver.getUserId(), reassignedTask.getDriverUserId());
        assertEquals(savedDriver.getUserName(), reassignedTask.getDriverUserName());
    }

    @Test
    void when_update_task_should_success() throws JsonProcessingException {
        // Prepare test data - create task
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-UPDATE-001", LocalDate.now(), "TRUCK-UPDATE-001", "Driver-Update");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        UUID taskId = savedTask.getId();

        // Prepare update command
        UpdateDeliveryTaskCommand command = new UpdateDeliveryTaskCommand();
        command.setPreCheck(PreCheckDto.builder().dollyLoaded(true).electricPalletJackLoaded(true).build());
        command.setMoneyBagNumber(RandomStringUtils.randomAlphabetic(10));

        // Execute update task
        DeliveryTaskDto updatedTask = deliveryTaskResourceApi.update(taskId, command);

        // Verify task is updated
        assertEquals(taskId, updatedTask.getId());
        assertTrue(updatedTask.getPreCheck().isDollyLoaded());
        assertTrue(updatedTask.getPreCheck().isElectricPalletJackLoaded());
        assertFalse(updatedTask.getPreCheck().isLiftgateOperational());
        assertEquals(command.getMoneyBagNumber(), updatedTask.getMoneyBagNumber());

        UpdateDeliveryTaskCommand postCheckCommand = new UpdateDeliveryTaskCommand();
        postCheckCommand.setPostCheck(PostCheckDto.builder().notes(RandomStringUtils.randomAlphabetic(10)).build());

        // Execute update task
        updatedTask = deliveryTaskResourceApi.update(taskId, postCheckCommand);

        // Verify task is updated with post check
        assertEquals(taskId, updatedTask.getId());
        assertEquals(postCheckCommand.getPostCheck().getNotes(), updatedTask.getPostCheck().getNotes());
    }

    @Test
    void testSyncNewRoutesWithNoExistingTasks() {
        // Given - no existing tasks for the delivery date
        LocalDate deliveryDate = LocalDate.now().plusDays(5);

        // Clean up any existing data for this date first
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        List<DeliveryTaskDo> existingTasksToClean = deliveryTaskJpaDao.findByDeliveryDate(formattedDate);
        if (!existingTasksToClean.isEmpty()) {
            deliveryTaskJpaDao.deleteAll(existingTasksToClean);
        }

        // Setup approved routes response
        ApprovedRoutesResponse approvedRoutesResponse = buildApprovedRoutesResponse(2, deliveryDate, true);
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(approvedRoutesResponse);

        // Setup test data - customers, drivers, orders
        Customer customer1 = buildCustomer();
        Customer customer2 = buildCustomer();
        Customer savedCustomer1 = customerRepository.save(customer1);
        Customer savedCustomer2 = customerRepository.save(customer2);

        ApprovedRoute route1 = approvedRoutesResponse.getApprovedRoutes().get(0);
        ApprovedRoute route2 = approvedRoutesResponse.getApprovedRoutes().get(1);

        Account driver1 = buildAccount(route1.getDriver().getEmail());
        Account driver2 = buildAccount(route2.getDriver().getEmail());
        accountRepository.save(driver1);
        accountRepository.save(driver2);

        // Create orders for the routes
        createOrdersForRoute(route1, savedCustomer1);
        createOrdersForRoute(route2, savedCustomer2);

        // When - sync new routes
        BuildDeliveryTaskCommand command = BuildDeliveryTaskCommand.builder()
            .deliveryDate(deliveryDate)
            .build();
        deliveryTaskResourceApi.syncNewRoutes(command);

        // Then - verify tasks were created (should behave like buildTasks)
        List<DeliveryTaskDo> tasks = deliveryTaskJpaDao.findByDeliveryDate(deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER));
        assertEquals(2, tasks.size());

        // Verify route records were created
        List<RmRoute> rmRoutes = rmRouteRepository.findByDeliveryTaskIdIn(
            tasks.stream().map(DeliveryTaskDo::getId).toList()
        );
        assertEquals(2, rmRoutes.size());
    }

    @Test
    void testSyncNewRoutesWithExistingTasksAndNewRoutes() {
        // Given - existing tasks and some new routes
        LocalDate deliveryDate = LocalDate.now().plusDays(6);

        // Clean up any existing data for this date first
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        List<DeliveryTaskDo> existingTasksToClean = deliveryTaskJpaDao.findByDeliveryDate(formattedDate);
        if (!existingTasksToClean.isEmpty()) {
            rmRouteRepository.deleteAllByDeliveryTaskIdIn(
                existingTasksToClean.stream().map(DeliveryTaskDo::getId).collect(java.util.stream.Collectors.toSet())
            );
            deliveryTaskJpaDao.deleteAll(existingTasksToClean);
        }

        // Create existing task
        DeliveryTaskDo existingTask = buildDeliveryTaskDo("TD-EXISTING", deliveryDate, "TRUCK-EXISTING", "Driver-Existing");
        DeliveryTaskDo savedExistingTask = deliveryTaskJpaDao.save(existingTask);

        // Create RmRoute for existing task
        RmRoute existingRmRoute = RmRoute.builder()
            .routeId("existing-route-id")
            .deliveryTaskId(savedExistingTask.getId())
            .createdBy("test")
            .createdAt(Instant.now())
            .updatedBy("test")
            .updatedAt(Instant.now())
            .build();
        rmRouteRepository.save(existingRmRoute);

        // Setup approved routes - one existing, one new
        ApprovedRoutesResponse response = new ApprovedRoutesResponse();
        
        // Existing route
        ApprovedRoute existingRoute = new ApprovedRoute();
        Route existingRouteObj = new Route();
        existingRouteObj.setId("existing-route-id");
        existingRouteObj.setDate(deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER));
        existingRoute.setRoute(existingRouteObj);
        
        // New route
        ApprovedRoute newRoute = new ApprovedRoute();
        Route newRouteObj = new Route();
        newRouteObj.setId("new-route-id");
        newRouteObj.setDate(deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER));
        newRoute.setRoute(newRouteObj);
        
        // Set up drivers
        Driver existingDriver = new Driver();
        existingDriver.setId(UUID.randomUUID().toString());
        existingDriver.setEmail("<EMAIL>");
        existingDriver.setName("Existing Driver");
        existingRoute.setDriver(existingDriver);
        
        Driver newDriver = new Driver();
        newDriver.setId(UUID.randomUUID().toString());
        newDriver.setEmail("<EMAIL>");
        newDriver.setName("New Driver");
        newRoute.setDriver(newDriver);
        
        // Set up vehicles
        Vehicle existingVehicle = new Vehicle();
        existingVehicle.setId("TRUCK-EXISTING");
        existingVehicle.setExternalId("TRUCK-EXISTING");
        existingVehicle.setSettings(buildVehicleSettingMap(existingRouteObj.getOriginalDate()));
        existingRoute.setVehicle(existingVehicle);
        
        Vehicle newVehicle = new Vehicle();
        newVehicle.setId("TRUCK-NEW");
        newVehicle.setExternalId("TRUCK-NEW");
        newVehicle.setSettings(buildVehicleSettingMap(newRouteObj.getOriginalDate()));
        newRoute.setVehicle(newVehicle);

        // Set up orders for routes
        Map<UUID, Order> existingOrders = new HashMap<>();
        Order existingOrder = new Order();
        UUID existingOrderId = UUID.randomUUID();
        existingOrder.setId(existingOrderId.toString());
        existingOrder.setName("O-EXISTING");
        existingOrders.put(existingOrderId, existingOrder);
        existingRoute.setOrders(existingOrders);

        Map<UUID, Order> newOrders = new HashMap<>();
        Order newOrder = new Order();
        UUID newOrderId = UUID.randomUUID();
        newOrder.setId(newOrderId.toString());
        newOrder.setName("O-NEW");
        newOrders.put(newOrderId, newOrder);
        newRoute.setOrders(newOrders);

        // Set up steps for routes
        Step existingStep = new Step();
        existingStep.setType("delivery");
        existingStep.setOrderId(existingOrderId.toString());
        existingStep.setDisplayLabel("1.1");
        existingRouteObj.setSteps(List.of(existingStep));

        Step newStep = new Step();
        newStep.setType("delivery");
        newStep.setOrderId(newOrderId.toString());
        newStep.setDisplayLabel("1.1");
        newRouteObj.setSteps(List.of(newStep));

        response.setApprovedRoutes(List.of(existingRoute, newRoute));
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(response);

        // Create customers and delivery orders
        Customer existingCustomer = buildCustomer();
        Customer newCustomer = buildCustomer();
        Customer savedExistingCustomer = customerRepository.save(existingCustomer);
        Customer savedNewCustomer = customerRepository.save(newCustomer);
        
        // Create delivery orders in database
        createOrdersForRoute(existingRoute, savedExistingCustomer);
        createOrdersForRoute(newRoute, savedNewCustomer);

        // Create accounts for drivers
        Account existingDriverAccount = buildAccount(existingDriver.getEmail());
        Account newDriverAccount = buildAccount(newDriver.getEmail());
        accountRepository.save(existingDriverAccount);
        accountRepository.save(newDriverAccount);

        // When - sync new routes
        BuildDeliveryTaskCommand command = BuildDeliveryTaskCommand.builder()
            .deliveryDate(deliveryDate)
            .build();
        deliveryTaskResourceApi.syncNewRoutes(command);

        // Then - verify only new task was created
        List<DeliveryTaskDo> allTasks = deliveryTaskJpaDao.findByDeliveryDate(deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER));
        assertEquals(2, allTasks.size()); // existing + new

        // Verify new route record was created
        List<RmRoute> allRmRoutes = rmRouteRepository.findByDeliveryTaskIdIn(
            allTasks.stream().map(DeliveryTaskDo::getId).toList()
        );
        assertEquals(2, allRmRoutes.size());
        assertTrue(allRmRoutes.stream().anyMatch(r -> "existing-route-id".equals(r.getRouteId())));
        assertTrue(allRmRoutes.stream().anyMatch(r -> "new-route-id".equals(r.getRouteId())));
    }

    @Test
    void testSyncNewRoutesWithNoNewRoutes() {
        // Given - existing tasks that cover all approved routes
        LocalDate deliveryDate = LocalDate.now().plusDays(7);

        // Clean up any existing data for this date first
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        List<DeliveryTaskDo> existingTasksToClean = deliveryTaskJpaDao.findByDeliveryDate(formattedDate);
        if (!existingTasksToClean.isEmpty()) {
            deliveryTaskJpaDao.deleteAll(existingTasksToClean);
        }

        // Create existing task
        DeliveryTaskDo existingTask = buildDeliveryTaskDo("TD-NO-NEW", deliveryDate, "TRUCK-NO-NEW", "Driver-No-New");
        DeliveryTaskDo savedExistingTask = deliveryTaskJpaDao.save(existingTask);

        String routeId = "no-new-route-id";
        
        // Create RmRoute for existing task
        RmRoute existingRmRoute = RmRoute.builder()
            .routeId(routeId)
            .deliveryTaskId(savedExistingTask.getId())
            .createdBy("test")
            .createdAt(Instant.now())
            .updatedBy("test")
            .updatedAt(Instant.now())
            .build();
        rmRouteRepository.save(existingRmRoute);

        // Setup approved routes - only the existing route
        ApprovedRoutesResponse response = new ApprovedRoutesResponse();
        ApprovedRoute existingRoute = new ApprovedRoute();
        Route routeObj = new Route();
        routeObj.setId(routeId);
        routeObj.setDate(deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER));
        existingRoute.setRoute(routeObj);
        
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail("<EMAIL>");
        driver.setName("No New Driver");
        existingRoute.setDriver(driver);
        
        Vehicle vehicle = new Vehicle();
        vehicle.setId("TRUCK-NO-NEW");
        vehicle.setExternalId("TRUCK-NO-NEW");
        vehicle.setSettings(buildVehicleSettingMap(routeObj.getOriginalDate()));
        existingRoute.setVehicle(vehicle);

        response.setApprovedRoutes(List.of(existingRoute));
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(response);

        // When - sync new routes
        BuildDeliveryTaskCommand command = BuildDeliveryTaskCommand.builder()
            .deliveryDate(deliveryDate)
            .build();
        deliveryTaskResourceApi.syncNewRoutes(command);

        // Then - verify no new tasks were created
        List<DeliveryTaskDo> allTasks = deliveryTaskJpaDao.findByDeliveryDate(deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER));
        assertEquals(1, allTasks.size()); // only the existing task

        // Verify existing route record is still there
        List<RmRoute> allRmRoutes = rmRouteRepository.findByDeliveryTaskIdIn(
            allTasks.stream().map(DeliveryTaskDo::getId).toList()
        );
        assertEquals(1, allRmRoutes.size());
        assertEquals(routeId, allRmRoutes.get(0).getRouteId());
    }

    @Test
    void testSyncNewRoutesWithNoApprovedRoutes() {
        // Given - no approved routes available
        LocalDate deliveryDate = LocalDate.now().plusDays(8);

        // Clean up any existing data for this date first
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        List<DeliveryTaskDo> existingTasksToClean = deliveryTaskJpaDao.findByDeliveryDate(formattedDate);
        if (!existingTasksToClean.isEmpty()) {
            deliveryTaskJpaDao.deleteAll(existingTasksToClean);
        }

        // Create existing task (should remain unchanged)
        DeliveryTaskDo existingTask = buildDeliveryTaskDo("TD-NO-APPROVED", deliveryDate, "TRUCK-NO-APPROVED", "Driver-No-Approved");
        DeliveryTaskDo savedExistingTask = deliveryTaskJpaDao.save(existingTask);

        // Setup empty approved routes response
        ApprovedRoutesResponse response = new ApprovedRoutesResponse();
        response.setApprovedRoutes(List.of());
        when(routeManagerAdaptor.getApprovedRoutesV2(anyList())).thenReturn(response);

        // When - sync new routes
        BuildDeliveryTaskCommand command = BuildDeliveryTaskCommand.builder()
            .deliveryDate(deliveryDate)
            .build();
        deliveryTaskResourceApi.syncNewRoutes(command);

        // Then - verify no changes were made
        List<DeliveryTaskDo> allTasks = deliveryTaskJpaDao.findByDeliveryDate(deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER));
        assertEquals(1, allTasks.size()); // only the existing task
        assertEquals(savedExistingTask.getId(), allTasks.get(0).getId());
    }

    private void createOrdersForRoute(ApprovedRoute route, Customer customer) {
        if (route.getOrders() == null) {
            return;
        }
        
        List<DeliveryOrder> ordersToSave = route.getOrders().values().stream()
            .map(order -> {
                DeliveryOrder deliveryOrder = buildDeliveryOrder();
                deliveryOrder.setShopifyOrderId(order.getId());
                deliveryOrder.setOrderNumber(order.getName());
                deliveryOrder.setCustomer(customer);
                deliveryOrder.setStatus(DeliveryOrderStatus.CREATED);
                return deliveryOrder;
            })
            .toList();
        
        deliveryOrderRepository.saveAll(ordersToSave);
    }

}