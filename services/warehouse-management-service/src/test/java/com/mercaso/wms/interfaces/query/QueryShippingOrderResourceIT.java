package com.mercaso.wms.interfaces.query;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import com.mercaso.wms.utils.ShippingOrderResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

class QueryShippingOrderResourceIT extends AbstractIT {

    @Autowired
    ShippingOrderResourceApi shippingOrderResourceApi;
    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void when_find_shipping_order_should_return_shipping_order() throws Exception {

        shippingOrderRepository.deleteAll();
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(RandomStringUtils.randomAlphabetic(10));
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Page<ShippingOrder> shippingOrderList = shippingOrderRepository.findShippingOrderList(new ShippingOrderSearchCriteria(),
            Pageable.ofSize(10));

        ShippingOrder first = shippingOrderList.getContent().getFirst();
        ShippingOrderDto shippingOrder = shippingOrderResourceApi.getShippingOrder(first.getId());


        assertNotNull(shippingOrder);
        assertEquals(shopifyOrderDto.getName(), shippingOrder.getOrderNumber());
    }

    @Test
    void when_find_high_value_shipping_order_items_by_driver_user_id_and_delivery_date_should_return_items() throws Exception {

        shippingOrderRepository.deleteAll();
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(RandomStringUtils.randomAlphabetic(10));
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.getShippingOrderItems().getFirst().setHighValueItem(true);
        shippingOrder.setDriverUserId(UUID.randomUUID());
        shippingOrder.setDriverUserName(RandomStringUtils.randomAlphabetic(10));
        shippingOrder.setTruckNumber(RandomStringUtils.randomAlphabetic(10));

        shippingOrderRepository.save(shippingOrder);

        shippingOrderResourceApi.findHighValueItemsBy(shippingOrder.getDriverUserId(), shippingOrder.getDeliveryDate().toString())
            .forEach(item -> {
                assertNotNull(item);
                assertTrue(item.isHighValueItem());
                assertEquals(shippingOrder.getId(), item.getShippingOrderId());
                assertEquals(shippingOrder.getOrderNumber(), item.getOrderNumber());
            });
    }

}