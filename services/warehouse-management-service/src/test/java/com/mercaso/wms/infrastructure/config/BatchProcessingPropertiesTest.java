package com.mercaso.wms.infrastructure.config;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class BatchProcessingPropertiesTest {

    @Test
    void should_Return_Default_Values() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        
        assertEquals(3, properties.getDaysBefore());
        assertEquals(3, properties.getDaysAfter());
    }

    @Test
    void should_Calculate_Total_Days_Correctly() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        
        // Default: 3 + 3 + 1 = 7 days
        assertEquals(7, properties.getTotalDays());
    }

    @Test
    void should_Calculate_Total_Days_With_Custom_Values() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        properties.setDaysBefore(5);
        properties.setDaysAfter(2);
        
        // 5 + 2 + 1 = 8 days
        assertEquals(8, properties.getTotalDays());
    }

    @Test
    void should_Set_And_Get_Days_Before() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        properties.setDaysBefore(7);
        
        assertEquals(7, properties.getDaysBefore());
    }

    @Test
    void should_Set_And_Get_Days_After() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        properties.setDaysAfter(5);
        
        assertEquals(5, properties.getDaysAfter());
    }

    @Test
    void should_Handle_Zero_Values() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        properties.setDaysBefore(0);
        properties.setDaysAfter(0);
        
        assertEquals(0, properties.getDaysBefore());
        assertEquals(0, properties.getDaysAfter());
        assertEquals(1, properties.getTotalDays()); // Only the delivery date itself
    }

    @Test
    void should_Handle_Asymmetric_Values() {
        BatchProcessingProperties properties = new BatchProcessingProperties();
        properties.setDaysBefore(1);
        properties.setDaysAfter(10);
        
        assertEquals(1, properties.getDaysBefore());
        assertEquals(10, properties.getDaysAfter());
        assertEquals(12, properties.getTotalDays()); // 1 + 10 + 1 = 12
    }
}
