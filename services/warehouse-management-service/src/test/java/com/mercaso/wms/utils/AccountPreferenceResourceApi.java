package com.mercaso.wms.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.dto.view.AccountPreferenceView;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class AccountPreferenceResourceApi extends IntegrationTestRestUtil {

    public static final String FIND_BY_ID_URL = "/query/account-preferences/%s";

    public static final String FIND_BY_USER_ID_URL = "/query/account-preferences?ids=%s";

    public static final String CREATE_ACCOUNT_PREFERENCE_URL = "/account-preferences";

    public static final String UPDATE_ACCOUNT_PREFERENCE_URL = "/account-preferences/%s";

    public static final String SEARCH_ACCOUNT_PREFERENCES_URL = "/search/account-preferences";

    public static final String FIND_BY_USER_NAME_AND_EMAIL_URL = "/query/account-preferences/validate";

    public AccountPreferenceResourceApi(Environment environment) {
        super(environment);
    }

    public AccountPreferenceDto findById(UUID id) {
        return getEntity(String.format(FIND_BY_ID_URL, id), AccountPreferenceDto.class).getBody();
    }

    public AccountPreferenceDto createAccountPreference(CreateAccountPreferenceCommand command) throws Exception {
        return createEntity(CREATE_ACCOUNT_PREFERENCE_URL, SerializationUtils.serialize(command), AccountPreferenceDto.class);
    }

    public AccountPreferenceDto updateAccountPreference(UUID id, UpdateAccountPreferenceCommand command) throws Exception {
        return updateEntity(String.format(UPDATE_ACCOUNT_PREFERENCE_URL, id),
            SerializationUtils.serialize(command),
            AccountPreferenceDto.class);
    }

    public List<AccountPreferenceDto> findBy(List<UUID> ids) throws Exception {
        return getEntityList(String.format(FIND_BY_USER_ID_URL,
            ids.stream().map(UUID::toString).collect(Collectors.joining(","))), AccountPreferenceDto.class);
    }

    public Result<AccountPreferenceView> search(String userName,
        String email,
        AccountPreferenceStatus status,
        UUID workWarehouseId,
        UUID externalWarehouseId,
        PickingTaskType taskType) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_ACCOUNT_PREFERENCES_URL + "?page=1&pageSize=20");
        if (userName != null) {
            url.append("&userName=").append(userName);
        }
        if (email != null) {
            url.append("&email=").append(email);
        }
        if (status != null) {
            url.append("&status=").append(status);
        }
        if (workWarehouseId != null) {
            url.append("&workWarehouseId=").append(workWarehouseId);
        }
        if (externalWarehouseId != null) {
            url.append("&externalWarehouseId=").append(externalWarehouseId);
        }
        if (taskType != null) {
            url.append("&taskType=").append(taskType);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<AccountPreferenceView>>() {
        });
    }

    public Boolean findBy(String userName, String email) {
        if (StringUtils.hasText(userName)) {
            return getEntity(FIND_BY_USER_NAME_AND_EMAIL_URL + "?userName=" + userName, Boolean.class).getBody();
        } else {
            return getEntity(FIND_BY_USER_NAME_AND_EMAIL_URL + "?email=" + email, Boolean.class).getBody();
        }
    }
}
