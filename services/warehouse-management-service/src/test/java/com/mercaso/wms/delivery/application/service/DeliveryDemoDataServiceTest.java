package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.config.DemoDataRefreshConfig;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.gps.GpsRawRepository;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeliveryDemoDataServiceTest {

    @Mock
    private DeliveryTaskRepository deliveryTaskRepository;

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    @Mock
    private DemoDataRefreshConfig demoDataRefreshConfig;

    @Mock
    private GpsRawRepository gpsRawRepository;

    @Mock
    private DeliveryDocumentRepository deliveryDocumentRepository;

    @InjectMocks
    private DeliveryDemoDataService deliveryDemoDataService;

    private List<String> taskNumbers;
    private List<String> orderNumbers;
    private List<DeliveryTask> deliveryTasks;
    private List<DeliveryOrder> deliveryOrders;

    @BeforeEach
    void setUp() {
        taskNumbers = Arrays.asList("TASK001", "TASK002");
        orderNumbers = Arrays.asList("ORDER001", "ORDER002");

        // Create test delivery tasks
        DeliveryTask task1 = DeliveryTask.builder()
            .id(UUID.randomUUID())
            .number("TASK001")
            .deliveryDate("2024-01-01")
            .moneyBagNumber("BAG001")
            .dispatchAt(Instant.now())
            .breakStartAt(Instant.now())
            .breakEndAt(Instant.now())
            .completedAt(Instant.now())
            .preCheck("Pre-check data")
            .postCheck("Post-check data")
            .build();
        task1.setState(DeliveryTaskStatus.IN_PROGRESS);

        DeliveryTask task2 = DeliveryTask.builder()
            .id(UUID.randomUUID())
            .number("TASK002")
            .deliveryDate("2024-01-01")
            .moneyBagNumber("BAG002")
            .dispatchAt(Instant.now())
            .breakStartAt(Instant.now())
            .breakEndAt(Instant.now())
            .completedAt(Instant.now())
            .preCheck("Pre-check data 2")
            .postCheck("Post-check data 2")
            .build();
        task2.setState(DeliveryTaskStatus.COMPLETED);

        deliveryTasks = Arrays.asList(task1, task2);

        // Create test delivery orders
        DeliveryOrderItem orderItem1 = DeliveryOrderItem.builder()
            .id(UUID.randomUUID())
            .build();

        DeliveryOrderItem orderItem2 = DeliveryOrderItem.builder()
            .id(UUID.randomUUID())
            .build();

        DeliveryOrder order1 = DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER001")
            .deliveryDate("2024-01-01")
            .tobaccoTotalPrice(java.math.BigDecimal.valueOf(100.00))
            .paymentType(List.of(PaymentType.CASH))
            .planArriveAt(Instant.now())
            .planDeliveryAt(Instant.now())
            .inTransitAt(Instant.now())
            .arrivedAt(Instant.now())
            .unloadedAt(Instant.now())
            .deliveredAt(Instant.now())
            .notes("Test notes")
            .nonTobaccoTotalPrice(java.math.BigDecimal.valueOf(50.00))
            .adjustedPrice("100.00")
            .adjustedPriceInfo("Adjusted price info")
            .rescheduleType(com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType.LATER)
            .rescheduledAt(Instant.now())
            .deliveryOrderItems(Collections.singletonList(orderItem1))
            .build();
        order1.setState(DeliveryOrderStatus.DELIVERED);

        DeliveryOrder order2 = DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORDER002")
            .deliveryDate("2024-01-01")
            .tobaccoTotalPrice(java.math.BigDecimal.valueOf(200.00))
            .paymentType(List.of(PaymentType.CREDIT))
            .planArriveAt(Instant.now())
            .planDeliveryAt(Instant.now())
            .inTransitAt(Instant.now())
            .arrivedAt(Instant.now())
            .unloadedAt(Instant.now())
            .deliveredAt(Instant.now())
            .notes("Test notes 2")
            .nonTobaccoTotalPrice(java.math.BigDecimal.valueOf(75.00))
            .adjustedPrice("200.00")
            .adjustedPriceInfo("Adjusted price info 2")
            .rescheduleType(com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType.NEXT_DAY)
            .rescheduledAt(Instant.now())
            .deliveryOrderItems(Collections.singletonList(orderItem2))
            .build();
        order2.setState(DeliveryOrderStatus.DELIVERED);

        deliveryOrders = Arrays.asList(order1, order2);
    }

    @Test
    void refreshDeliveryDemoData_WhenConfigDisabled_ShouldReturnEarly() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(false);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        verify(demoDataRefreshConfig, times(1)).isEnabled();
        verify(deliveryTaskRepository, never()).findByTaskNumberIn(any());
        verify(deliveryOrderRepository, never()).findAllByOrderNumberIn(any());
        verify(gpsRawRepository, never()).deleteByDeliveryTaskIds(any());
        verify(deliveryDocumentRepository, never()).deleteByEntityIdIn(any());
    }

    @Test
    void refreshDeliveryDemoData_WhenNoDeliveryOrdersFound_ShouldReturnEarly() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(Collections.emptyList());

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        verify(demoDataRefreshConfig, times(1)).isEnabled();
        verify(deliveryTaskRepository, times(1)).findByTaskNumberIn(taskNumbers);
        verify(deliveryOrderRepository, times(1)).findAllByOrderNumberIn(orderNumbers);
        verify(gpsRawRepository, never()).deleteByDeliveryTaskIds(any());
        verify(deliveryDocumentRepository, never()).deleteByEntityIdIn(any());
    }

    @Test
    void refreshDeliveryDemoData_WhenSuccessful_ShouldResetAllData() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(deliveryOrders);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        verify(demoDataRefreshConfig, times(1)).isEnabled();
        verify(deliveryTaskRepository, times(1)).findByTaskNumberIn(taskNumbers);
        verify(deliveryOrderRepository, times(1)).findAllByOrderNumberIn(orderNumbers);
        verify(deliveryTaskRepository, times(1)).updateAll(deliveryTasks);
        verify(deliveryOrderRepository, times(1)).updateAll(deliveryOrders);
        verify(gpsRawRepository, times(1)).deleteByDeliveryTaskIds(any());
        verify(deliveryDocumentRepository, times(3)).deleteByEntityIdIn(any());

        // Verify delivery tasks are reset
        for (DeliveryTask task : deliveryTasks) {
            assertEquals(DeliveryTaskStatus.CREATED, task.getState());
            assertEquals(DateUtils.getCurrentDeliveryDate(), task.getDeliveryDate());
            assertNull(task.getMoneyBagNumber());
            assertNull(task.getDispatchAt());
            assertNull(task.getBreakStartAt());
            assertNull(task.getBreakEndAt());
            assertNull(task.getCompletedAt());
            assertNull(task.getPreCheck());
            assertNull(task.getPostCheck());
        }

        // Verify delivery orders are reset
        for (DeliveryOrder order : deliveryOrders) {
            assertEquals(DeliveryOrderStatus.ASSIGNED, order.getState());
            assertEquals(DateUtils.getCurrentDeliveryDate(), order.getDeliveryDate());
            assertNull(order.getTobaccoTotalPrice());
            assertNull(order.getPaymentType());
            assertNull(order.getInTransitAt());
            assertNull(order.getArrivedAt());
            assertNull(order.getUnloadedAt());
            assertNull(order.getDeliveredAt());
            assertNull(order.getNotes());
            assertNull(order.getNonTobaccoTotalPrice());
            assertNull(order.getAdjustedPrice());
            assertNull(order.getAdjustedPriceInfo());
            assertNull(order.getRescheduleType());
            assertNull(order.getRescheduledAt());
        }
    }

    @Test
    void refreshDeliveryDemoData_WhenSuccessful_ShouldCallDeleteGpsData() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(deliveryOrders);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        verify(gpsRawRepository, times(1)).deleteByDeliveryTaskIds(any());
    }

    @Test
    void refreshDeliveryDemoData_WhenSuccessful_ShouldCallDeleteRelatedDocuments() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(deliveryOrders);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        // Should be called 3 times: once for tasks, once for orders, once for order items
        verify(deliveryDocumentRepository, times(3)).deleteByEntityIdIn(any());
    }

    @Test
    void refreshDeliveryDemoData_WhenSuccessful_ShouldUpdatePlanArriveAtAndPlanDeliveryAt() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(deliveryOrders);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        for (DeliveryOrder order : deliveryOrders) {
            if (order.getPlanArriveAt() != null) {
                assertTrue(order.getPlanArriveAt().isAfter(Instant.now().minusSeconds(1)));
            }
            if (order.getPlanDeliveryAt() != null) {
                assertTrue(order.getPlanDeliveryAt().isAfter(Instant.now().minusSeconds(1)));
            }
        }
    }

    @Test
    void refreshDeliveryDemoData_WhenOrderItemsExist_ShouldDeleteDocumentsForOrderItems() {
        // Given
        when(demoDataRefreshConfig.isEnabled()).thenReturn(true);
        when(demoDataRefreshConfig.getTaskNumbers()).thenReturn(taskNumbers);
        when(demoDataRefreshConfig.getOrderNumbers()).thenReturn(orderNumbers);
        when(deliveryTaskRepository.findByTaskNumberIn(taskNumbers)).thenReturn(deliveryTasks);
        when(deliveryOrderRepository.findAllByOrderNumberIn(orderNumbers)).thenReturn(deliveryOrders);

        // When
        deliveryDemoDataService.refreshDeliveryDemoData();

        // Then
        // Verify that deleteByEntityIdIn is called for tasks, orders, and order items
        verify(deliveryDocumentRepository, times(3)).deleteByEntityIdIn(any());
    }
}
