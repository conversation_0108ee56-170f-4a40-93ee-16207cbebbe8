package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class CoreMarkTotalSheetWriterTest extends Writer {

    private final CoreMarkTotalSheetWriter coreMarkTotalSheetWriter = new CoreMarkTotalSheetWriter();

    @Test
    void when_write_core_mark_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> coreMarkList = Lists.newArrayList();
        ExcelBatchDto excelBatchDto = new ExcelBatchDto();
        excelBatchDto.setItemNumber("test");
        excelBatchDto.setFrom("Location2");
        excelBatchDto.setQuantity(1);
        excelBatchDto.setSource(SourceEnum.CORE_MARK.name());

        ExcelBatchDto excelBatchDto1 = new ExcelBatchDto();
        excelBatchDto1.setItemNumber("test1");
        excelBatchDto1.setFrom("Location2");
        excelBatchDto1.setQuantity(1);
        excelBatchDto1.setSource(SourceEnum.CORE_MARK.name());

        ExcelBatchDto excelBatchDto2 = new ExcelBatchDto();
        excelBatchDto2.setItemNumber("test1");
        excelBatchDto2.setFrom("Location2");
        excelBatchDto2.setQuantity(1);
        excelBatchDto2.setSource(SourceEnum.CORE_MARK.name());

        coreMarkList.add(excelBatchDto);
        coreMarkList.add(excelBatchDto1);
        coreMarkList.add(excelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.CORE_MARK.name(), coreMarkList));

        writeBatchTemplate(condition, coreMarkTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> totals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.CORE_MARK_TOTALS.getValue()).doReadSync();

        assertEquals(5, totals.size());
        assertEquals("2", totals.get(4).get(6));
        assertEquals("1", totals.get(3).get(6));
    }

    @Test
    void when_write_empty_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> coreMark = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.CORE_MARK.name(), coreMark));

        writeBatchTemplate(condition, coreMarkTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> totals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.CORE_MARK_TOTALS.getValue()).doReadSync();

        assertEquals(3, totals.size());
    }

}