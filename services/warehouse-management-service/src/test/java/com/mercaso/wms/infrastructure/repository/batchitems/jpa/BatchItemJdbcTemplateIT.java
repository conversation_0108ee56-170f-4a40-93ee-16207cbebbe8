package com.mercaso.wms.infrastructure.repository.batchitems.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrders;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class BatchItemJdbcTemplateIT extends AbstractIT {

    @Autowired
    private BatchItemJdbcTemplate batchItemJdbcTemplate;

    @Test
    void when_findTobaccoAndRefrigeratedBatchItemsBy_then_returnExcelBatchDto() {
        shippingOrderRepository.deleteAll();
        LocalDate deliveryDate = LocalDate.now().plusDays(100);
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        shippingOrders.forEach(shippingOrder -> {
            shippingOrder.setDeliveryDate(deliveryDate);
            shippingOrder.setWarehouse(warehouses.getFirst());
        });
        List<ShippingOrder> shippingOrderList = shippingOrderRepository.saveAll(shippingOrders);
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItems.forEach(batchItem -> {
            batchItem.setShippingOrderId(shippingOrderList.getFirst().getId());
            batchItem.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        });
        batchItemRepository.saveAll(batchItems);

        // when
        var result = batchItemJdbcTemplate.findTobaccoAndRefrigeratedBatchItemsBy(deliveryDate.toString());

        // then
        assertNotNull(result);
        assertEquals(2, result.size());
    }

}