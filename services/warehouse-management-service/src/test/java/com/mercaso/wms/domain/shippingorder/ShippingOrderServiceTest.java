package com.mercaso.wms.domain.shippingorder;

import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrdersWithId;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.repository.districs.CongressionalDistrictsDo;
import com.mercaso.wms.infrastructure.repository.districs.CongressionalDistrictsRepository;
import com.mercaso.wms.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.wms.infrastructure.utils.SpringContextUtil;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ShippingOrderServiceTest {

    private final ShippingOrderRepository shippingOrderRepository = mock(ShippingOrderRepository.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper = mock(ShippingOrderDtoApplicationMapper.class);

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private final CustomerAddressRepository customerAddressRepository = mock(CustomerAddressRepository.class);

    private final CongressionalDistrictsRepository congressionalDistrictsRepository = mock(CongressionalDistrictsRepository.class);

    private final ApplicationEventDispatcher applicationEventDispatcher = mock(ApplicationEventDispatcher.class);

    private final PickingTaskItemRepository pickingTaskItemRepository = mock(PickingTaskItemRepository.class);

    private final ShippingOrderService shippingOrderService = new ShippingOrderService(
        shippingOrderRepository,
        pgAdvisoryLock,
        businessEventDispatcher,
        shippingOrderDtoApplicationMapper,
        customerAddressRepository,
        congressionalDistrictsRepository,
        applicationEventDispatcher,
        pickingTaskItemRepository
    );

    private UUID shippingOrderId;
    private UUID shippingOrderItemId;
    private ShippingOrder mockShippingOrder;

    @BeforeEach
    void setUp() {
        shippingOrderId = UUID.randomUUID();
        shippingOrderItemId = UUID.randomUUID();
        mockShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.OPEN).getFirst();
        // Note: Using buildShippingOrdersWithId to ensure ShippingOrderItem has id set
    }

    @Test
    void setCustomerAddress_Success() {
        // Arrange
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.OPEN).getFirst();
        CongressionalDistrictsDo districtsDo = mock(CongressionalDistrictsDo.class);
        CustomerAddress savedAddress = CustomerAddress.builder().addressOne(RandomStringUtils.randomAlphabetic(10)).build();
        shippingOrder.setCustomerAddress(savedAddress);

        when(customerAddressRepository.findBySha1hex("test-sha1")).thenReturn(null);
        when(congressionalDistrictsRepository.findByZipCode("12345")).thenReturn(districtsDo);
        when(customerAddressRepository.save(any())).thenReturn(savedAddress);

        // Act
        shippingOrderService.setCustomerAddress(shippingOrder);

        // Assert
        verify(customerAddressRepository, times(1)).save(any());
    }

    @Test
    void setCustomerAddress_AddressExists() {
        // Arrange
        ShippingOrder shippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.OPEN).getFirst();
        CustomerAddress savedAddress = shippingOrder.getCustomerAddress();

        when(customerAddressRepository.findBySha1hex(anyString())).thenReturn(savedAddress);

        // Act
        shippingOrderService.setCustomerAddress(shippingOrder);

        // Assert
        verify(customerAddressRepository, times(0)).save(any());
    }

    @Test
    void setCustomerAddress_NullAddress() {
        // Arrange
        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getCustomerAddress()).thenReturn(null);

        // Act
        shippingOrderService.setCustomerAddress(shippingOrder);

        // Assert
        verify(customerAddressRepository, never()).findBySha1hex(any());
        verify(customerAddressRepository, never()).save(any());
    }

    // Tests for updateFulfilledQty method
    @Test
    void testUpdateFulfilledQty_WhenValidParameters_ShouldUpdateSuccessfully() {
        // Given
        Integer additionalFulfilledQty = 2;
        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(mockShippingOrder);
        when(shippingOrderRepository.save(any(ShippingOrder.class))).thenReturn(mockShippingOrder);

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, shippingOrderItemId, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository).findById(shippingOrderId);
        verify(shippingOrderRepository).save(mockShippingOrder);
    }

    @Test
    void testUpdateFulfilledQty_WhenShippingOrderNotFound_ShouldLogWarningAndReturn() {
        // Given
        Integer additionalFulfilledQty = 2;
        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(null);

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, shippingOrderItemId, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository).findById(shippingOrderId);
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    @Test
    void testUpdateFulfilledQty_WhenShippingOrderIdIsNull_ShouldLogWarningAndReturn() {
        // Given
        Integer additionalFulfilledQty = 2;

        // When
        shippingOrderService.updateFulfilledQty(null, shippingOrderItemId, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository, never()).findById(any());
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    @Test
    void testUpdateFulfilledQty_WhenShippingOrderItemIdIsNull_ShouldLogWarningAndReturn() {
        // Given
        Integer additionalFulfilledQty = 2;

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, null, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository, never()).findById(any());
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    @Test
    void testUpdateFulfilledQty_WhenAdditionalFulfilledQtyIsNull_ShouldLogWarningAndReturn() {
        // Given

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, shippingOrderItemId, null);

        // Then
        verify(shippingOrderRepository, never()).findById(any());
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    @Test
    void testUpdateFulfilledQty_WhenAdditionalFulfilledQtyIsZero_ShouldLogWarningAndReturn() {
        // Given
        Integer additionalFulfilledQty = 0;

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, shippingOrderItemId, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository, never()).findById(any());
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    @Test
    void testUpdateFulfilledQty_WhenAdditionalFulfilledQtyIsNegative_ShouldLogWarningAndReturn() {
        // Given
        Integer additionalFulfilledQty = -1;

        // When
        shippingOrderService.updateFulfilledQty(shippingOrderId, shippingOrderItemId, additionalFulfilledQty);

        // Then
        verify(shippingOrderRepository, never()).findById(any());
        verify(shippingOrderRepository, never()).save(any(ShippingOrder.class));
    }

    // Tests for updateSingleOrderWithRetry method
    @Test
    void updateSingleOrderWithRetry_WhenOrderNotFound_ShouldLogErrorAndReturn() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        List<PickingTaskItem> pickedPickingTaskItems = List.of();
        PickingTaskType type = PickingTaskType.ORDER;

        when(shippingOrderRepository.findById(orderId)).thenReturn(null);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrderRepository, never()).update(any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void updateSingleOrderWithRetry_WhenOrderAlreadyPicked_ShouldStillUpdateQuantities() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        UUID shippingOrderItemId = UUID.randomUUID();

        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .shippingOrderItemId(shippingOrderItemId)
            .pickedQty(5)
            .build();
        List<PickingTaskItem> pickedPickingTaskItems = List.of(pickingTaskItem);
        PickingTaskType type = PickingTaskType.ORDER;

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.PICKED);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId)).thenReturn(0);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class)); // Now it should be called
        verify(shippingOrderRepository).update(shippingOrder); // Now it should be called
        verify(businessEventDispatcher, never()).dispatch(any()); // But no event should be dispatched
    }

    @Test
    void updateSingleOrderWithRetry_WhenOrderLevelPickingTask_ShouldUpdateOrderAndFulfilledQty() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        UUID shippingOrderItemId = UUID.randomUUID();
        PickingTaskType type = PickingTaskType.ORDER;

        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId)
            .pickedQty(5)
            .build();
        List<PickingTaskItem> pickedPickingTaskItems = List.of(pickingTaskItem);

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.IN_PROGRESS);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder()
            .id(orderId)
            .status(ShippingOrderStatus.PICKED)
            .build();

        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId)).thenReturn(0);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class));
        verify(shippingOrder).updateFulfilledQty(shippingOrderItemId, 5);
        verify(shippingOrderRepository).update(shippingOrder);
        verify(shippingOrderDtoApplicationMapper).domainToDto(shippingOrder);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void updateSingleOrderWithRetry_WhenBatchLevelPickingTask_ShouldUpdateOrderWithoutFulfilledQty() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        UUID shippingOrderItemId = UUID.randomUUID();
        PickingTaskType type = PickingTaskType.BATCH;

        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId)
            .pickedQty(5)
            .build();
        List<PickingTaskItem> pickedPickingTaskItems = List.of(pickingTaskItem);

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.IN_PROGRESS);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder()
            .id(orderId)
            .status(ShippingOrderStatus.PICKED)
            .build();

        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId)).thenReturn(0);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class));
        verify(shippingOrder, never()).updateFulfilledQty(any(), any());
        verify(shippingOrderRepository).update(shippingOrder);
        verify(shippingOrderDtoApplicationMapper).domainToDto(shippingOrder);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void updateSingleOrderWithRetry_WhenMultiplePickingTaskItems_ShouldUpdateAllItems() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        UUID shippingOrderItemId1 = UUID.randomUUID();
        UUID shippingOrderItemId2 = UUID.randomUUID();
        PickingTaskType type = PickingTaskType.ORDER;

        PickingTaskItem pickingTaskItem1 = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId1)
            .pickedQty(3)
            .build();
        PickingTaskItem pickingTaskItem2 = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId2)
            .pickedQty(7)
            .build();
        List<PickingTaskItem> pickedPickingTaskItems = List.of(pickingTaskItem1, pickingTaskItem2);

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.IN_PROGRESS);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder()
            .id(orderId)
            .status(ShippingOrderStatus.PICKED)
            .build();

        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId1)).thenReturn(0);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId2)).thenReturn(0);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class));
        verify(shippingOrder).updateFulfilledQty(shippingOrderItemId1, 3);
        verify(shippingOrder).updateFulfilledQty(shippingOrderItemId2, 7);
        verify(shippingOrderRepository).update(shippingOrder);
        verify(shippingOrderDtoApplicationMapper).domainToDto(shippingOrder);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void updateSingleOrderWithRetry_WhenEmptyPickingTaskItems_ShouldStillUpdateOrder() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        List<PickingTaskItem> pickedPickingTaskItems = List.of();
        PickingTaskType type = PickingTaskType.ORDER;

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.IN_PROGRESS);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder()
            .id(orderId)
            .status(ShippingOrderStatus.PICKED)
            .build();

        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class));
        verify(shippingOrder, never()).updateFulfilledQty(any(), any());
        verify(shippingOrderRepository).update(shippingOrder);
        verify(shippingOrderDtoApplicationMapper).domainToDto(shippingOrder);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void updateSingleOrderWithRetry_WhenPickingTaskItemHasZeroPickedQty_ShouldStillUpdateFulfilledQty() {
        // Given
        UUID orderId = UUID.randomUUID();
        UUID pickingTaskId = UUID.randomUUID();
        UUID shippingOrderItemId = UUID.randomUUID();
        PickingTaskType type = PickingTaskType.ORDER;

        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId)
            .pickedQty(0)
            .build();
        List<PickingTaskItem> pickedPickingTaskItems = List.of(pickingTaskItem);

        ShippingOrder shippingOrder = mock(ShippingOrder.class);
        when(shippingOrder.getStatus()).thenReturn(ShippingOrderStatus.IN_PROGRESS);
        when(shippingOrder.getOrderNumber()).thenReturn("TEST-ORDER-001");
        ShippingOrderDto shippingOrderDto = ShippingOrderDto.builder()
            .id(orderId)
            .status(ShippingOrderStatus.PICKED)
            .build();

        when(shippingOrderRepository.findById(orderId)).thenReturn(shippingOrder);
        when(shippingOrderRepository.update(shippingOrder)).thenReturn(shippingOrder);
        when(shippingOrderDtoApplicationMapper.domainToDto(shippingOrder)).thenReturn(shippingOrderDto);
        when(pickingTaskItemRepository.getFailedQtyByShippingOrderItemId(shippingOrderItemId)).thenReturn(0);

        // When
        shippingOrderService.updateSingleOrderWithRetry(orderId, pickedPickingTaskItems, pickingTaskId, type);

        // Then
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(orderId.hashCode(),
            "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        verify(shippingOrderRepository).findById(orderId);
        verify(shippingOrder).picked(eq(pickedPickingTaskItems), any(Map.class));
        verify(shippingOrder).updateFulfilledQty(shippingOrderItemId, 0);
        verify(shippingOrderRepository).update(shippingOrder);
        verify(shippingOrderDtoApplicationMapper).domainToDto(shippingOrder);
        verify(businessEventDispatcher).dispatch(any());
    }


    // Tests for revertPickedStatusForSplitItems method
    @Test
    void testRevertPickedStatusForSplitItems_WhenValidItemIds_ShouldRevertPickedStatusAndRecalculateOrderStatus() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();
        UUID shippingOrderItemId1 = UUID.randomUUID();
        UUID shippingOrderItemId2 = UUID.randomUUID();
        Set<UUID> shippingOrderItemIds = Set.of(shippingOrderItemId1, shippingOrderItemId2);

        ShippingOrder mockShippingOrder = mock(ShippingOrder.class);
        ShippingOrderItem mockItem1 = mock(ShippingOrderItem.class);
        ShippingOrderItem mockItem2 = mock(ShippingOrderItem.class);
        List<ShippingOrderItem> mockItems = List.of(mockItem1, mockItem2);

        when(mockShippingOrder.getId()).thenReturn(shippingOrderId);
        when(mockShippingOrder.getShippingOrderItems()).thenReturn(mockItems);
        when(mockItem1.getId()).thenReturn(shippingOrderItemId1);
        when(mockItem1.isPicked()).thenReturn(true);
        when(mockItem2.getId()).thenReturn(shippingOrderItemId2);
        when(mockItem2.isPicked()).thenReturn(true);

        when(shippingOrderRepository.findByShippingOrderItemId(shippingOrderItemId1)).thenReturn(mockShippingOrder);
        when(shippingOrderRepository.findByShippingOrderItemId(shippingOrderItemId2)).thenReturn(mockShippingOrder);
        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(mockShippingOrder);
        when(mockShippingOrder.getStatus()).thenReturn(ShippingOrderStatus.PICKED);

        // When
        shippingOrderService.revertPickedStatusForSplitItems(shippingOrderItemIds);

        // Then
        verify(shippingOrderRepository, times(2)).findByShippingOrderItemId(any());
        verify(mockItem1).setPicked(false);
        verify(mockItem2).setPicked(false);
        verify(shippingOrderRepository, times(2)).update(mockShippingOrder);
        verify(shippingOrderRepository).findById(shippingOrderId);
    }

    @Test
    void testRevertPickedStatusForSplitItems_WhenEmptyItemIds_ShouldReturnEarly() {
        // Given
        Set<UUID> emptyItemIds = Set.of();

        // When
        shippingOrderService.revertPickedStatusForSplitItems(emptyItemIds);

        // Then
        verify(shippingOrderRepository, never()).findByShippingOrderItemId(any());
        verify(shippingOrderRepository, never()).update(any());
    }

    // Tests for domain entity business logic with SpringContextUtil mocking
    @Test
    void testShippingOrderDomainLogic_WhenPickedCalled_ShouldHandleStateTransition() {
        try (MockedStatic<SpringContextUtil> mockedStatic = mockStatic(SpringContextUtil.class)) {
            // Given
            StateMachineProcessor<ShippingOrder, ShippingOrderStatus, ?> stateMachineProcessor = mock(StateMachineProcessor.class);
            mockedStatic.when(() -> SpringContextUtil.getBean(StateMachineProcessor.class)).thenReturn(stateMachineProcessor);

            ShippingOrder realShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
            List<ShippingOrderItem> items = realShippingOrder.getShippingOrderItems();

            // Create picking task items for ALL shipping order items to trigger state transition
            List<PickingTaskItem> pickingTaskItems = items.stream()
                .map(item -> PickingTaskItem.builder()
                    .id(UUID.randomUUID())
                    .shippingOrderItemId(item.getId())
                    .pickedQty(10) // Pick full quantity
                    .build())
                .collect(Collectors.toList());

            // When
            realShippingOrder.picked(pickingTaskItems, Map.of());

            // Then
            verify(stateMachineProcessor).processEvent(eq(realShippingOrder), any());
        }
    }

    @Test
    void testShippingOrderDomainLogic_WhenItemFullyPickedWithoutFailed_ShouldSetPicked() {
        try (MockedStatic<SpringContextUtil> mockedStatic = mockStatic(SpringContextUtil.class)) {
            // Given
            StateMachineProcessor<ShippingOrder, ShippingOrderStatus, ?> stateMachineProcessor = mock(StateMachineProcessor.class);
            mockedStatic.when(() -> SpringContextUtil.getBean(StateMachineProcessor.class)).thenReturn(stateMachineProcessor);

            ShippingOrder realShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
            List<ShippingOrderItem> items = realShippingOrder.getShippingOrderItems();

            // Create picking task items for ALL shipping order items to trigger state transition
            List<PickingTaskItem> pickingTaskItems = items.stream()
                .map(item -> PickingTaskItem.builder()
                    .id(UUID.randomUUID())
                    .shippingOrderItemId(item.getId())
                    .pickedQty(10) // Pick exactly the order quantity
                    .build())
                .collect(Collectors.toList());

            // When
            realShippingOrder.picked(pickingTaskItems, Map.of());

            // Then
            ShippingOrderItem firstItem = realShippingOrder.getShippingOrderItems().getFirst();
            assertEquals(10, firstItem.getPickedQty());
            assertTrue(firstItem.isPicked());
            // Verify all items are picked
            assertTrue(items.stream().allMatch(ShippingOrderItem::isPicked));
            verify(stateMachineProcessor).processEvent(eq(realShippingOrder), any());
        }
    }

    @Test
    void testShippingOrderDomainLogic_WhenPartialPickedWithFailedQtyReachesTotal_ShouldSetPicked() {
        try (MockedStatic<SpringContextUtil> mockedStatic = mockStatic(SpringContextUtil.class)) {
            // Given
            StateMachineProcessor<ShippingOrder, ShippingOrderStatus, ?> stateMachineProcessor = mock(StateMachineProcessor.class);
            mockedStatic.when(() -> SpringContextUtil.getBean(StateMachineProcessor.class)).thenReturn(stateMachineProcessor);

            ShippingOrder realShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
            List<ShippingOrderItem> items = realShippingOrder.getShippingOrderItems();

            // Create picking task items for ALL shipping order items to trigger state transition
            List<PickingTaskItem> pickingTaskItems = items.stream()
                .map(item -> PickingTaskItem.builder()
                    .id(UUID.randomUUID())
                    .shippingOrderItemId(item.getId())
                    .pickedQty(6) // Pick 6 out of 10
                    .build())
                .collect(Collectors.toList());

            // Create failed quantity map for ALL items
            Map<UUID, Integer> failedQtyMap = items.stream()
                .collect(Collectors.toMap(
                    ShippingOrderItem::getId,
                    item -> 4 // Failed 4, total = 6+4=10
                ));

            // When
            realShippingOrder.picked(pickingTaskItems, failedQtyMap);

            // Then
            ShippingOrderItem firstItem = realShippingOrder.getShippingOrderItems().getFirst();
            assertEquals(6, firstItem.getPickedQty());
            assertTrue(firstItem.isPicked()); // 6 (picked) + 4 (failed) = 10 >= 10 (total)
            // Verify all items are picked
            assertTrue(items.stream().allMatch(ShippingOrderItem::isPicked));
            verify(stateMachineProcessor).processEvent(eq(realShippingOrder), any());
        }
    }

    @Test
    void testShippingOrderDomainLogic_WhenInsufficientQuantity_ShouldNotSetPicked() {
        try (MockedStatic<SpringContextUtil> mockedStatic = mockStatic(SpringContextUtil.class)) {
            // Given
            StateMachineProcessor<ShippingOrder, ShippingOrderStatus, ?> stateMachineProcessor = mock(StateMachineProcessor.class);
            mockedStatic.when(() -> SpringContextUtil.getBean(StateMachineProcessor.class)).thenReturn(stateMachineProcessor);

            ShippingOrder realShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.IN_PROGRESS).getFirst();
            UUID shippingOrderItemId = realShippingOrder.getShippingOrderItems().getFirst().getId();

            List<PickingTaskItem> pickingTaskItems = List.of(
                PickingTaskItem.builder()
                    .id(UUID.randomUUID())
                    .shippingOrderItemId(shippingOrderItemId)
                    .pickedQty(3) // Pick only 3 out of 10
                    .build()
            );

            Map<UUID, Integer> failedQtyMap = Map.of(shippingOrderItemId, 1); // Failed 1, total = 3+1=4 < 10

            // When
            realShippingOrder.picked(pickingTaskItems, failedQtyMap);

            // Then
            ShippingOrderItem item = realShippingOrder.getShippingOrderItems().getFirst();
            assertEquals(3, item.getPickedQty());
            assertFalse(item.isPicked()); // 3 (picked) + 1 (failed) = 4 < 10 (total)
            verify(stateMachineProcessor, never()).processEvent(any(), any());
        }
    }

    @Test
    void testShippingOrderDomainLogic_WhenCanceledOrder_ShouldSkipStateTransition() {
        // Given
        ShippingOrder realShippingOrder = buildShippingOrdersWithId(1, ShippingOrderStatus.CANCELED).getFirst();
        UUID shippingOrderItemId = realShippingOrder.getShippingOrderItems().getFirst().getId();

        List<PickingTaskItem> pickingTaskItems = List.of(
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .shippingOrderItemId(shippingOrderItemId)
                .pickedQty(10)
                .build()
        );

        try (MockedStatic<SpringContextUtil> mockedStatic = mockStatic(SpringContextUtil.class)) {
            StateMachineProcessor<ShippingOrder, ShippingOrderStatus, ?> stateMachineProcessor = mock(StateMachineProcessor.class);
            mockedStatic.when(() -> SpringContextUtil.getBean(StateMachineProcessor.class)).thenReturn(stateMachineProcessor);

            // When
            realShippingOrder.picked(pickingTaskItems, Map.of());

            // Then - Should update quantities but not trigger state transition
            assertEquals(10, realShippingOrder.getShippingOrderItems().getFirst().getPickedQty());
            verify(stateMachineProcessor, never()).processEvent(any(), any());
        }
    }
}