package com.mercaso.wms.delivery.application.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.domain.gps.GpsRaw;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCache;
import com.mercaso.wms.delivery.domain.latestgps.LatestGpsCacheRepository;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class LatestGpsCacheApplicationServiceTest {

    private final LatestGpsCacheRepository latestGpsCacheRepository = Mockito.mock(LatestGpsCacheRepository.class);

    private final LatestGpsCacheApplicationService latestGpsCacheApplicationService = new LatestGpsCacheApplicationService(
        latestGpsCacheRepository);

    @Test
    void testCreateOrUpdate() {
        // Given
        GpsRaw gpsRaw = GpsRaw.builder().userId(UUID.randomUUID()).build();

        when(latestGpsCacheRepository.findByUserId(any())).thenReturn(Optional.empty());

        // When
        latestGpsCacheApplicationService.createOrUpdate(gpsRaw);

        // Then
        Mockito.verify(latestGpsCacheRepository).save(any());
    }

    @Test
    void testCreateOrUpdateWithExistingUserId() {
        // Given
        GpsRaw gpsRaw = GpsRaw.builder().userId(UUID.randomUUID()).userName(RandomStringUtils.randomAlphabetic(10)).build();

        LatestGpsCache latestGpsCache = LatestGpsCache.builder().userId(gpsRaw.getUserId()).build();
        when(latestGpsCacheRepository.findByUserId(any())).thenReturn(Optional.of(latestGpsCache));

        // When
        latestGpsCacheApplicationService.createOrUpdate(gpsRaw);

        // Then
        Mockito.verify(latestGpsCacheRepository).update(any());
    }

}