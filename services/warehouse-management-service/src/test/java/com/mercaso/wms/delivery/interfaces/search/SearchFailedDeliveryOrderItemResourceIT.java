package com.mercaso.wms.delivery.interfaces.search;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderItemCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto.ShopifyLineItemDto;
import com.mercaso.wms.delivery.application.dto.view.SearchFailedDeliveryOrderItemView;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchFailedDeliveryOrderItemResourceIT extends AbstractIT {

    @Autowired
    DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;
    @Autowired
    DeliveryTaskJpaDao deliveryTaskJpaDao;

    @Test
    void when_search_failed_delivery_order_items_then_return_search_failed_delivery_order_item_view() throws Exception {
        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        Optional<DeliveryOrderDo> deliveryOrderDoOptional = deliveryOrderJpaDao.findById(deliveryOrderDo.getId());
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", LocalDate.now(), "TRUCK-001", "Driver1");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        deliveryOrderDoOptional.ifPresent(orderDo -> {
            orderDo.setStatus(DeliveryOrderStatus.ASSIGNED);
            orderDo.setDeliveryTaskId(savedTask.getId());
            deliveryOrderJpaDao.saveAndFlush(orderDo);
        });

        deliveryOrderResourceApi.updateToInTransit(deliveryOrderDo.getId());
        DeliveryOrderDto arrived = deliveryOrderResourceApi.updateToArrived(deliveryOrderDo.getId());

        List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos = Lists.newArrayList();
        arrived.getDeliveryOrderItems().forEach(item -> {
            updateDeliveryOrderItemDtos.add(UpdateDeliveryOrderItemCommand.builder()
                .id(item.getId())
                .deliveredQty(item.getQty().subtract(BigDecimal.ONE))
                .reasonCode("{\"MISSING\":1}")
                .build());
        });
        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(updateDeliveryOrderItemDtos)
            .build();

        deliveryOrderResourceApi.updateToUnloaded(arrived.getId(), command);

        deliveryOrderResourceApi.updateToDelivered(deliveryOrderDo.getId(),
            DeliveryOrderDeliveredCommand.builder()
                .paymentType(List.of(PaymentType.CASH, PaymentType.OTHER))
                .notes("Test notes")
                .build());

        Result<SearchFailedDeliveryOrderItemView> searchByOrderNumbers = deliveryOrderResourceApi
            .search(
                List.of(deliveryOrderDo.getOrderNumber()),
                null,
                null,
                null,
                List.of(SortType.UPDATED_AT_DESC)
            );

        assertEquals(10, searchByOrderNumbers.getData().size());

        Result<SearchFailedDeliveryOrderItemView> searchByDeliveryDate = deliveryOrderResourceApi.search(
            null,
            LocalDate.now(),
            null,
            null,
            List.of(SortType.UPDATED_AT_DESC)
        );

        assertEquals(10, searchByDeliveryDate.getData().size());

        Result<SearchFailedDeliveryOrderItemView> searchBySkuNumbers = deliveryOrderResourceApi.search(
            null,
            null,
            shopifyOrderDto.getLineItems().stream().map(ShopifyLineItemDto::getSku).toList(),
            null,
            List.of(SortType.UPDATED_AT_DESC)
        );

        assertEquals(10, searchBySkuNumbers.getData().size());

        Result<SearchFailedDeliveryOrderItemView> searchByReasonCodes = deliveryOrderResourceApi.search(
            null,
            null,
            null,
            List.of("MISSING"),
            List.of(SortType.UPDATED_AT_DESC)
        );

        assertEquals(10, searchByReasonCodes.getData().size());
    }

}