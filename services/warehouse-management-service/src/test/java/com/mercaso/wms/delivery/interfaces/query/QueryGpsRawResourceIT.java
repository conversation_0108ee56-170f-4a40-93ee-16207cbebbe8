package com.mercaso.wms.delivery.interfaces.query;

import static com.mercaso.wms.infrastructure.utils.DateUtils.getCurrentDeliveryDate;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.command.GpsRawCommand.GpsReportDto;
import com.mercaso.wms.delivery.application.dto.gps.GpsRawDto;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.infrastructure.repository.latestgps.jpa.LatestGpsCacheJpaDao;
import com.mercaso.wms.delivery.utils.GpsRawResourceApi;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryGpsRawResourceIT extends AbstractIT {

    @Autowired
    private GpsRawResourceApi gpsRawResourceApi;
    @Autowired
    private DeliveryTaskRepository deliveryTaskRepository;
    @Autowired
    private LatestGpsCacheJpaDao latestGpsCacheJpaDao;

    @Test
    void shouldRetrieveGpsRawDataByDeliveryTaskId() throws Exception {
        latestGpsCacheJpaDao.deleteAll();
        // Arrange
        DeliveryTask deliveryTask = DeliveryTask.builder()
            .driverUserId(UUID.randomUUID())
            .status(DeliveryTaskStatus.IN_PROGRESS)
            .number(RandomStringUtils.randomAlphanumeric(8))
            .deliveryDate(getCurrentDeliveryDate())
            .driverUserName("TestDriver")
            .build();
        deliveryTask = deliveryTaskRepository.save(deliveryTask);

        GpsRawCommand command = GpsRawCommand.builder()
            .userId(deliveryTask.getDriverUserId())
            .gpsReportDtos(List.of(GpsReportDto.builder().latitude(31.2304).longitude(121.4737).reportAt(Instant.now()).build()))
            .build();

        gpsRawResourceApi.report(command);

        // Act
        List<GpsRawDto> gpsRawDtos = gpsRawResourceApi.getGpsByDeliveryTaskId(deliveryTask.getId());

        // Assert
        Assertions.assertNotNull(gpsRawDtos);
        Assertions.assertEquals(1, gpsRawDtos.size());

        List<LatestGpsCacheDto> latestGpsCache = gpsRawResourceApi.findLatestGpsCache();

        Assertions.assertNotNull(latestGpsCache);
        Assertions.assertEquals(1, latestGpsCache.size());
        Assertions.assertEquals(deliveryTask.getDriverUserId(), latestGpsCache.getFirst().getUserId());
    }
}