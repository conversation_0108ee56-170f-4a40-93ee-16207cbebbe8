package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildCrossDockTasks;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchCrossDockTaskResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskResourceApi crossDockTaskResourceApi;

    @Autowired
    private CrossDockTaskRepository crossDockTaskRepository;

    @BeforeEach
    void setUp() {
        crossDockTaskRepository.deleteAll();
    }

    @Test
    void searchCrossDockTasks_withValidParameters_returnsResult() throws Exception {
        List<CrossDockTask> crossDockTasks = buildCrossDockTasks(5);
        crossDockTaskRepository.saveAll(crossDockTasks);

        Result<SearchCrossDockTaskView> crossDockTaskViewResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null,
            null,
            null
        );

        assertNotNull(crossDockTaskViewResult);
        assertNotNull(crossDockTaskViewResult.getData());
        assertNotNull(crossDockTaskViewResult.getTotalCount());
        assertEquals(5, crossDockTaskViewResult.getData().size());
        assertEquals(5, crossDockTaskViewResult.getTotalCount());

        // Verify that orderNumbers and skuNumbers fields are present and populated
        SearchCrossDockTaskView firstTask = crossDockTaskViewResult.getData().getFirst();
        assertNotNull(firstTask.getOrderNumbers(), "orderNumbers should not be null");
        assertNotNull(firstTask.getSkuNumbers(), "skuNumbers should not be null");
        assertFalse(firstTask.getOrderNumbers().isEmpty(), "orderNumbers should not be empty");
        assertFalse(firstTask.getSkuNumbers().isEmpty(), "skuNumbers should not be empty");
    }

    @Test
    void searchCrossDockTasks_withOrderNumberAndSkuNumber_returnsFilteredResult() throws Exception {
        // Create test data with specific orderNumber and skuNumber
        List<CrossDockTask> crossDockTasks = buildCrossDockTasks(3);

        // Set specific values for the first task's items
        String targetOrderNumber = "TEST_ORDER_123";
        String targetSkuNumber = "TEST_SKU_456";
        String otherOrderNumber = "OTHER_ORDER_789";
        String otherSkuNumber = "OTHER_SKU_999";

        // First task: has both target and other orders/skus (should be returned when searching for target)
        crossDockTasks.getFirst().getCrossDockTaskItems().getFirst().setOrderNumber(targetOrderNumber);
        crossDockTasks.getFirst().getCrossDockTaskItems().getFirst().setSkuNumber(targetSkuNumber);
        if (crossDockTasks.getFirst().getCrossDockTaskItems().size() > 1) {
            crossDockTasks.getFirst().getCrossDockTaskItems().get(1).setOrderNumber(otherOrderNumber);
            crossDockTasks.getFirst().getCrossDockTaskItems().get(1).setSkuNumber(otherSkuNumber);
        }

        // Second task: only has other orders/skus (should NOT be returned when searching for target)
        crossDockTasks.get(1).getCrossDockTaskItems().forEach(item -> {
            item.setOrderNumber(otherOrderNumber);
            item.setSkuNumber(otherSkuNumber);
        });

        // Save all tasks
        crossDockTaskRepository.saveAll(crossDockTasks);

        // Test search by orderNumber only
        Result<SearchCrossDockTaskView> orderResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null, null, null, targetOrderNumber, null
        );

        assertNotNull(orderResult);
        assertEquals(1, orderResult.getTotalCount());
        assertEquals(1, orderResult.getData().size());

        // Verify that the returned task contains ALL orderNumbers and skuNumbers, not just the filtered ones
        SearchCrossDockTaskView returnedTask = orderResult.getData().getFirst();
        assertTrue(returnedTask.getOrderNumbers().contains(targetOrderNumber), "Should contain target orderNumber");
        assertTrue(returnedTask.getSkuNumbers().contains(targetSkuNumber), "Should contain target skuNumber");
        if (crossDockTasks.getFirst().getCrossDockTaskItems().size() > 1) {
            assertTrue(returnedTask.getOrderNumbers().contains(otherOrderNumber), "Should contain other orderNumber too");
            assertTrue(returnedTask.getSkuNumbers().contains(otherSkuNumber), "Should contain other skuNumber too");
        }

        // Test search by skuNumber only
        Result<SearchCrossDockTaskView> skuResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null, null, null, null, targetSkuNumber
        );

        assertNotNull(skuResult);
        assertEquals(1, skuResult.getTotalCount());
        assertEquals(1, skuResult.getData().size());

        // Verify that the returned task contains ALL orderNumbers and skuNumbers, not just the filtered ones
        SearchCrossDockTaskView skuReturnedTask = skuResult.getData().getFirst();
        assertTrue(skuReturnedTask.getOrderNumbers().contains(targetOrderNumber), "Should contain target orderNumber");
        assertTrue(skuReturnedTask.getSkuNumbers().contains(targetSkuNumber), "Should contain target skuNumber");
        if (crossDockTasks.getFirst().getCrossDockTaskItems().size() > 1) {
            assertTrue(skuReturnedTask.getOrderNumbers().contains(otherOrderNumber), "Should contain other orderNumber too");
            assertTrue(skuReturnedTask.getSkuNumbers().contains(otherSkuNumber), "Should contain other skuNumber too");
        }

        // Test search by both orderNumber and skuNumber
        Result<SearchCrossDockTaskView> combinedResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null, null, null, targetOrderNumber, targetSkuNumber
        );

        assertNotNull(combinedResult);
        assertEquals(1, combinedResult.getTotalCount());
        assertEquals(1, combinedResult.getData().size());

        // Test search with non-existing orderNumber
        Result<SearchCrossDockTaskView> noMatchResult = crossDockTaskResourceApi.searchCrossDockTasks(
            null, null, null, "NON_EXISTING_ORDER", null
        );

        assertNotNull(noMatchResult);
        assertEquals(0, noMatchResult.getTotalCount());
        assertEquals(0, noMatchResult.getData().size());
    }

}