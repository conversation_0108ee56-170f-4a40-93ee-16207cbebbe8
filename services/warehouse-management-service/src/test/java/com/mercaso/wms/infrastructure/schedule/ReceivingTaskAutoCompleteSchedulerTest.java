package com.mercaso.wms.infrastructure.schedule;

import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.service.ReceivingTaskApplicationService;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class ReceivingTaskAutoCompleteSchedulerTest {

    @Mock
    private ReceivingTaskRepository receivingTaskRepository;

    @Mock
    private ReceivingTaskApplicationService receivingTaskApplicationService;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @InjectMocks
    private ReceivingTaskAutoCompleteScheduler scheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void autoCompleteReceivingTasks_Does_Nothing_When_No_Lock() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        scheduler.autoCompleteReceivingTasks();

        verify(pgAdvisoryLock).tryLockWithSessionLevel(anyInt());
        verifyNoInteractions(receivingTaskRepository, receivingTaskApplicationService, batchRepository);
    }

    @Test
    void autoCompleteReceivingTasks_Does_Nothing_When_No_Tasks_Found() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(receivingTaskRepository.findByDeliveryDateAndStatus(anyString(), eq(ReceivingTaskStatus.RECEIVING)))
            .thenReturn(Collections.emptyList());

        scheduler.autoCompleteReceivingTasks();

        verify(receivingTaskRepository).findByDeliveryDateAndStatus(anyString(), eq(ReceivingTaskStatus.RECEIVING));
        verifyNoInteractions(receivingTaskApplicationService);
        verify(pgAdvisoryLock).unLock(anyInt());
    }

    @Test
    void autoCompleteReceivingTasks_Processes_All_Tasks() {
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();
        
        ReceivingTask task1 = ReceivingTask.builder().id(taskId1).status(ReceivingTaskStatus.RECEIVING).build();
        ReceivingTask task2 = ReceivingTask.builder().id(taskId2).status(ReceivingTaskStatus.RECEIVING).build();
        
        ReceivingTaskDto taskDto1 = new ReceivingTaskDto();
        taskDto1.setId(taskId1);
        taskDto1.setStatus(ReceivingTaskStatus.RECEIVED);
        
        ReceivingTaskDto taskDto2 = new ReceivingTaskDto();
        taskDto2.setId(taskId2);
        taskDto2.setStatus(ReceivingTaskStatus.RECEIVED);
        
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(receivingTaskRepository.findByDeliveryDateAndStatus(anyString(), eq(ReceivingTaskStatus.RECEIVING)))
            .thenReturn(List.of(task1, task2));
        when(receivingTaskApplicationService.receive(taskId1)).thenReturn(taskDto1);
        when(receivingTaskApplicationService.receive(taskId2)).thenReturn(taskDto2);

        scheduler.autoCompleteReceivingTasks();

        verify(receivingTaskApplicationService).receive(taskId1);
        verify(receivingTaskApplicationService).receive(taskId2);

        ReceivingTaskDto result1 = receivingTaskApplicationService.receive(taskId1);
        ReceivingTaskDto result2 = receivingTaskApplicationService.receive(taskId2);
        
        assertNotNull(result1);
        assertEquals(taskId1, result1.getId());
        assertEquals(ReceivingTaskStatus.RECEIVED, result1.getStatus());
        
        assertNotNull(result2);
        assertEquals(taskId2, result2.getId());
        assertEquals(ReceivingTaskStatus.RECEIVED, result2.getStatus());
    }

    @Test
    void autoCompleteReceivingTasks_Continues_Processing_When_Task_Fails() {
        UUID taskId1 = UUID.randomUUID();
        UUID taskId2 = UUID.randomUUID();
        
        ReceivingTask task1 = ReceivingTask.builder().id(taskId1).status(ReceivingTaskStatus.RECEIVING).build();
        ReceivingTask task2 = ReceivingTask.builder().id(taskId2).status(ReceivingTaskStatus.RECEIVING).build();
        
        ReceivingTaskDto taskDto2 = new ReceivingTaskDto();
        taskDto2.setId(taskId2);
        taskDto2.setStatus(ReceivingTaskStatus.RECEIVED);
        
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(receivingTaskRepository.findByDeliveryDateAndStatus(anyString(), eq(ReceivingTaskStatus.RECEIVING)))
            .thenReturn(List.of(task1, task2));
        
        doThrow(new WmsBusinessException("Task already received"))
            .when(receivingTaskApplicationService).receive(taskId1);
        when(receivingTaskApplicationService.receive(taskId2)).thenReturn(taskDto2);

        scheduler.autoCompleteReceivingTasks();

        verify(receivingTaskApplicationService).receive(taskId1);
        verify(receivingTaskApplicationService).receive(taskId2);

        ReceivingTaskDto result2 = receivingTaskApplicationService.receive(taskId2);
        assertNotNull(result2);
        assertEquals(taskId2, result2.getId());
        assertEquals(ReceivingTaskStatus.RECEIVED, result2.getStatus());
    }

    @Test
    void autoCompleteReceivingTasks_Always_Releases_Lock() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(receivingTaskRepository.findByDeliveryDateAndStatus(anyString(), eq(ReceivingTaskStatus.RECEIVING)))
            .thenThrow(new RuntimeException("Unexpected error"));

        scheduler.autoCompleteReceivingTasks();

        verify(pgAdvisoryLock).unLock(anyInt());
    }
} 