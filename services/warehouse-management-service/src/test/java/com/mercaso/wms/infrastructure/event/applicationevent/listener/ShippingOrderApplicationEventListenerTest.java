package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedApplicationEvent;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedPayloadDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ShippingOrderApplicationEventListenerTest {

    private final ShopifyAdaptor shopifyAdaptor = mock(ShopifyAdaptor.class);

    private final ShippingOrderRepository shippingOrderRepository = mock(ShippingOrderRepository.class);

    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);

    private final ShippingOrderApplicationEventListener listener = new ShippingOrderApplicationEventListener(
        shopifyAdaptor,
        shippingOrderRepository,
        businessEventDispatcher
    );

    @Test
    void shouldHandleShippingOrderValidatedSuccessfully() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();
        String shopifyOrderId = "12345";
        String orderNumber = "SO-001";

        ShippingOrderValidatedPayloadDto payloadDto = ShippingOrderValidatedPayloadDto.builder()
            .shippingOrderId(shippingOrderId)
            .data(ShippingOrderDto.builder().build())
            .build();

        ShippingOrderValidatedApplicationEvent event = new ShippingOrderValidatedApplicationEvent(
            this, payloadDto
        );

        // Create shipping order items with different quantities
        ShippingOrderItem item1 = createShippingOrderItem("item1", 5, 3);
        ShippingOrderItem item2 = createShippingOrderItem("item2", 10, 8);

        ShippingOrder shippingOrder = createShippingOrder(shippingOrderId,
            shopifyOrderId,
            orderNumber,
            Arrays.asList(item1, item2));

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(shippingOrder);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder().build());

        // When
        listener.handleShippingOrderValidated(event);

        // Then
        verify(shopifyAdaptor, times(1)).updateOrderItemsQuantity(anyString(), any());
        verify(businessEventDispatcher, times(1)).dispatch(any());
    }

    @Test
    void shouldHandleShippingOrderValidatedWhenNoLineItemsNeedUpdate() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();
        String shopifyOrderId = "12345";
        String orderNumber = "SO-001";

        ShippingOrderValidatedPayloadDto payloadDto = ShippingOrderValidatedPayloadDto.builder()
            .shippingOrderId(shippingOrderId)
            .data(ShippingOrderDto.builder().build())
            .build();

        ShippingOrderValidatedApplicationEvent event = new ShippingOrderValidatedApplicationEvent(
            this, payloadDto
        );

        // Create shipping order items with same quantities (no update needed)
        ShippingOrderItem item1 = createShippingOrderItem("item1", 5, 5);
        ShippingOrderItem item2 = createShippingOrderItem("item2", 10, 10);

        ShippingOrder shippingOrder = createShippingOrder(shippingOrderId,
            shopifyOrderId,
            orderNumber,
            Arrays.asList(item1, item2));

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(shippingOrder);

        // When
        listener.handleShippingOrderValidated(event);

        // Then
        verify(shopifyAdaptor, never()).updateOrderItemsQuantity(anyString(), any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void shouldHandleShippingOrderValidatedWhenShippingOrderNotFound() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();

        ShippingOrderValidatedPayloadDto payloadDto = ShippingOrderValidatedPayloadDto.builder()
            .shippingOrderId(shippingOrderId)
            .data(ShippingOrderDto.builder().build())
            .build();

        ShippingOrderValidatedApplicationEvent event = new ShippingOrderValidatedApplicationEvent(
            this, payloadDto
        );

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(null);

        // When
        listener.handleShippingOrderValidated(event);

        // Then
        verify(shopifyAdaptor, never()).updateOrderItemsQuantity(anyString(), any());
        verify(businessEventDispatcher, never()).dispatch(any());
    }

    @Test
    void shouldHandleShippingOrderValidatedWhenShopifyUpdateFails() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();
        String shopifyOrderId = "12345";
        String orderNumber = "SO-001";

        ShippingOrderValidatedPayloadDto payloadDto = ShippingOrderValidatedPayloadDto.builder()
            .shippingOrderId(shippingOrderId)
            .data(ShippingOrderDto.builder().build())
            .build();

        ShippingOrderValidatedApplicationEvent event = new ShippingOrderValidatedApplicationEvent(
            this, payloadDto
        );

        ShippingOrderItem item1 = createShippingOrderItem("item1", 5, 3);
        ShippingOrder shippingOrder = createShippingOrder(shippingOrderId, shopifyOrderId, orderNumber,
            Collections.singletonList(item1));

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(shippingOrder);
        doThrow(new RuntimeException("Shopify API error")).when(shopifyAdaptor).updateOrderItemsQuantity(anyString(), any());
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder().build());

        // When
        listener.handleShippingOrderValidated(event);

        // Then
        verify(shopifyAdaptor, times(1)).updateOrderItemsQuantity(anyString(), any());
        verify(businessEventDispatcher, times(1)).dispatch(any());
    }

    @Test
    void shouldHandleShippingOrderValidatedWhenShopifyOrderItemIdIsNull() {
        // Given
        UUID shippingOrderId = UUID.randomUUID();
        String shopifyOrderId = "12345";
        String orderNumber = "SO-001";

        ShippingOrderValidatedPayloadDto payloadDto = ShippingOrderValidatedPayloadDto.builder()
            .shippingOrderId(shippingOrderId)
            .data(ShippingOrderDto.builder().build())
            .build();

        ShippingOrderValidatedApplicationEvent event = new ShippingOrderValidatedApplicationEvent(
            this, payloadDto
        );

        // Create shipping order items with null shopifyOrderItemId
        ShippingOrderItem item1 = createShippingOrderItem(null, 5, 3);
        ShippingOrderItem item2 = createShippingOrderItem("item2", 10, 8);

        ShippingOrder shippingOrder = createShippingOrder(shippingOrderId,
            shopifyOrderId,
            orderNumber,
            Arrays.asList(item1, item2));

        when(shippingOrderRepository.findById(shippingOrderId)).thenReturn(shippingOrder);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder().build());

        // When
        listener.handleShippingOrderValidated(event);

        // Then
        // Should only update item2 since item1 has null shopifyOrderItemId
        verify(shopifyAdaptor, times(1)).updateOrderItemsQuantity(anyString(), any());
        verify(businessEventDispatcher, times(1)).dispatch(any());
    }

    private ShippingOrderItem createShippingOrderItem(String shopifyOrderItemId, Integer qty, Integer validatedQty) {
        return ShippingOrderItem.builder()
            .id(UUID.randomUUID())
            .shopifyOrderItemId(shopifyOrderItemId)
            .qty(qty)
            .validatedQty(validatedQty)
            .build();
    }

    private ShippingOrder createShippingOrder(UUID id, String shopifyOrderId, String orderNumber, List<ShippingOrderItem> items) {
        return ShippingOrder.builder()
            .id(id)
            .shopifyOrderId(shopifyOrderId)
            .orderNumber(orderNumber)
            .shippingOrderItems(items != null ? items : List.of())
            .build();
    }
} 