package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class BreakdownSheetsWriterTest extends Writer {

    private final BreakdownSheetsWriter breakdownSheetsWriter = new BreakdownSheetsWriter();

    @Test
    void when_write_mfc_big_order_breakdown_then_success() {
        BreakdownDto breakdownDto = new BreakdownDto();
        breakdownDto.setOriginalBreakdown("OriginalBreakdown");
        breakdownDto.setBreakdown("Breakdown");
        breakdownDto.setTotal(25);
        breakdownDto.setOrderNumber(RandomStringUtils.randomAlphabetic(6));

        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<BreakdownDto> list = Lists.newArrayList();
        list.add(breakdownDto);
        condition.setBigBreakdownDtos(list);
        condition.setSmallBreakdownDtos(Lists.newArrayList());
        writeBatchTemplate(condition, breakdownSheetsWriter);

        List<LinkedHashMap<Integer, String>> bigOrders =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.FULL_BREAKDOWN.getValue()).doReadSync();

        assertEquals(2, bigOrders.size());
    }

    @Test
    void when_write_mfc_small_order_breakdown_then_success() {
        BreakdownDto breakdownDto = new BreakdownDto();
        breakdownDto.setOriginalBreakdown("OriginalBreakdown");
        breakdownDto.setBreakdown("Breakdown");
        breakdownDto.setTotal(25);
        breakdownDto.setOrderNumber(RandomStringUtils.randomAlphabetic(6));

        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<BreakdownDto> list = Lists.newArrayList();
        list.add(breakdownDto);
        condition.setBigBreakdownDtos(Lists.newArrayList());
        condition.setSmallBreakdownDtos(list);
        writeBatchTemplate(condition, breakdownSheetsWriter);

        List<LinkedHashMap<Integer, String>> smallOrders =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.FULL_BREAKDOWN_SMALL.getValue()).doReadSync();

        assertEquals(2, smallOrders.size());
    }

    @Test
    void when_write_mfc_full_order_breakdown_then_success() {
        BreakdownDto breakdownDto = new BreakdownDto();
        breakdownDto.setOriginalBreakdown("OriginalBreakdown");
        breakdownDto.setBreakdown("Breakdown");
        breakdownDto.setTotal(25);
        breakdownDto.setOrderNumber("123");

        BreakdownDto breakdownDto1 = new BreakdownDto();
        breakdownDto1.setOriginalBreakdown("OriginalBreakdown");
        breakdownDto1.setBreakdown("Breakdown");
        breakdownDto1.setTotal(5);
        breakdownDto1.setOrderNumber("123");

        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<BreakdownDto> list = Lists.newArrayList();
        list.add(breakdownDto);
        List<BreakdownDto> smallList = Lists.newArrayList();
        smallList.add(breakdownDto);
        condition.setBigBreakdownDtos(list);
        condition.setSmallBreakdownDtos(smallList);
        writeBatchTemplate(condition, breakdownSheetsWriter);

        List<LinkedHashMap<Integer, String>> smallOrders =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.FULL_BREAKDOWN_ALL.getValue()).doReadSync();

        assertEquals(2, smallOrders.size());
    }

}