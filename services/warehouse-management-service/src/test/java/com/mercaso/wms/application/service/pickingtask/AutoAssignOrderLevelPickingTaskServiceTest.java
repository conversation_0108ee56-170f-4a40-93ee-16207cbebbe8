package com.mercaso.wms.application.service.pickingtask;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import com.mercaso.wms.application.command.pickingtask.AssignPickingTaskCommand;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties.Picker;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class AutoAssignOrderLevelPickingTaskServiceTest {

    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);

    private final AutoAssignOrderLevelPickingTaskService service = new AutoAssignOrderLevelPickingTaskService(
        pickingTaskApplicationService);

    @Test
    void assignPickingTask_WhenConfigIsNull_ShouldReturn() {
        service.assignPickingTask(Collections.emptyList(), null);
        verifyNoInteractions(pickingTaskApplicationService);
    }

    @Test
    void assignPickingTask_WhenAutoAssignmentIsFalse_ShouldReturn() {
        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);

        service.assignPickingTask(Collections.emptyList(), config);
        verifyNoInteractions(pickingTaskApplicationService);
    }

    @Test
    void assignPickingTask_WhenNoAvailablePickers_ShouldReturn() {
        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(true);
        config.setPickers(Collections.emptyList());

        service.assignPickingTask(Collections.emptyList(), config);
        verifyNoInteractions(pickingTaskApplicationService);
    }

    @Test
    void assignPickingTask_WithMDCAdaptation() {
        List<PickingTask> tasks = createPickingTasks(300); // Create more than 250 tasks
        PickingTaskAssignmentProperties config = createConfig(2, 100, 20); // 2 pickers, target 100, tolerance 20

        // Act
        service.assignPickingTask(tasks, config);

        // Assert
        verify(pickingTaskApplicationService, atMost(250)).assignPickingTask(any(), any());
    }

    @Test
    void assignPickingTask_WithoutMDCAdaptation() {
        List<PickingTask> tasks = createPickingTasks(300);
        PickingTaskAssignmentProperties config = createConfig(2, 100, 20);

        // Act
        service.assignPickingTask(tasks, config);

        // Assert
        verify(pickingTaskApplicationService, atMost(250)).assignPickingTask(any(), any());
    }

    @Test
    void assignPickingTask_ShouldRespectMaxQuantity() {
        // Arrange
        List<PickingTask> tasks = createPickingTasks(5);
        PickingTaskAssignmentProperties config = createConfig(1, 10, 0); // 1 picker, max 10 items

        // Act
        service.assignPickingTask(tasks, config);

        // Assert
        // Verify that assignments stop when quantity limit is reached
        verify(pickingTaskApplicationService, atMost(2)).assignPickingTask(any(), any());
    }

    @Test
    void assignPickingTask_ShouldNotDistributeTasksEvenly() {
        // Arrange
        List<PickingTask> tasks = createPickingTasks(4);
        PickingTaskAssignmentProperties config = createConfig(2, 20, 0); // 2 pickers, max 20 items

        // Act
        service.assignPickingTask(tasks, config);

        // Assert
        // Verify tasks are distributed between pickers
        ArgumentCaptor<AssignPickingTaskCommand> commandCaptor =
            ArgumentCaptor.forClass(AssignPickingTaskCommand.class);
        verify(pickingTaskApplicationService, times(0))
            .assignPickingTask(any(), commandCaptor.capture());

        List<AssignPickingTaskCommand> commands = commandCaptor.getAllValues();
        assertEquals(0, commands.stream()
            .map(AssignPickingTaskCommand::getPickerUserId)
            .distinct()
            .count());
    }

    // Helper methods
    private List<PickingTask> createPickingTasks(int count) {
        return IntStream.rangeClosed(1, count)
            .mapToObj(i -> {
                PickingTask task = PickingTask.builder().id(UUID.randomUUID()).build();
                PickingTaskItem item = PickingTaskItem.builder().build();
                item.setBreakdownName(String.valueOf(i));
                item.setExpectQty(5); // Each task has 5 items
                task.setPickingTaskItems(Collections.singletonList(item));
                return task;
            })
            .collect(Collectors.toList());
    }

    private PickingTaskAssignmentProperties createConfig(int pickerCount, int targetQty, int tolerance) {
        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(true);
        config.setTargetQty(targetQty);
        config.setTolerance(tolerance);

        List<Picker> pickers = IntStream.range(0, pickerCount)
            .mapToObj(i -> {
                Picker picker = new Picker();
                picker.setUserId(UUID.randomUUID());
                picker.setUserName("Picker-" + (i + 1));
                return picker;
            })
            .toList();
        config.setPickers(pickers);

        return config;
    }

    @Test
    void assignPickingTask_WhenPickerReachesMaxQty_ShouldBeRemoved() {
        // Arrange
        List<PickingTask> tasks = createLargeQuantityTasks(3); // Each task has 40 items
        PickingTaskAssignmentProperties config = createConfig(2, 60, 0); // Max 50 items per picker

        // Act
        service.assignPickingTask(tasks, config);

        // Assert
        // Verify first picker is removed after reaching limit
        ArgumentCaptor<AssignPickingTaskCommand> commandCaptor =
            ArgumentCaptor.forClass(AssignPickingTaskCommand.class);
        verify(pickingTaskApplicationService, times(2))
            .assignPickingTask(any(), commandCaptor.capture());

        List<UUID> assignedPickerIds = commandCaptor.getAllValues().stream()
            .map(AssignPickingTaskCommand::getPickerUserId)
            .toList();

        // First task should go to first picker, second task to second picker
        assertNotEquals(assignedPickerIds.get(0), assignedPickerIds.get(1));
    }

    private List<PickingTask> createLargeQuantityTasks(int count) {
        return IntStream.rangeClosed(1, count)
            .mapToObj(i -> {
                PickingTask task = PickingTask.builder().id(UUID.randomUUID()).build();
                PickingTaskItem item = PickingTaskItem.builder().build();
                item.setBreakdownName(String.valueOf(i));
                item.setExpectQty(60); // Each task has 40 items
                task.setPickingTaskItems(Collections.singletonList(item));
                return task;
            })
            .collect(Collectors.toList());
    }


}