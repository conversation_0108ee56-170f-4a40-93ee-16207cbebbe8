package com.mercaso.wms.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.infrastructure.external.finale.FinaleAdaptor;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.UpdateInventoryVarianceRequest;
import com.mercaso.wms.infrastructure.external.finale.enums.VarianceReasonType;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InventoryStockApplicationServiceTest {

    @Mock(lenient = true)
    private LocationRepository locationRepository;


    @Mock
    private FinaleAdaptor finaleAdaptor;

    @Mock
    private FinaleConfigProperties finaleConfigProperties;

    @InjectMocks
    private InventoryStockApplicationService service;

    @Test
    void cleanGhostInventory_shouldSkipWhenDeliveryOrderIsNull() {
        service.cleanGhostInventory(null);

        verifyNoInteractions(locationRepository, finaleAdaptor);
    }

    @Test
    void cleanGhostInventory_shouldSkipWhenNoItems() {
        DeliveryOrderDto deliveryOrder = DeliveryOrderDto.builder()
            .orderNumber("SO-123")
            .deliveryOrderItems(null)
            .build();

        service.cleanGhostInventory(deliveryOrder);

        verifyNoInteractions(locationRepository, finaleAdaptor);
    }

    @Test
    void cleanGhostInventory_shouldSkipWhenLocationNotFound() {
        DeliveryOrderDto deliveryOrder = createDeliveryOrderWithUndeliveredItems();
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(locationRepository.findByName("SHIP-SB")).thenReturn(null);

        service.cleanGhostInventory(deliveryOrder);

        verify(locationRepository).findByName("SHIP-SB");
        verifyNoInteractions(finaleAdaptor);
    }

    @Test
    void cleanGhostInventory_shouldUpdateInventoryVarianceForUndeliveredItems() {
        // Given
        DeliveryOrderDto deliveryOrder = createDeliveryOrderWithUndeliveredItems();
        Location location = createMockLocation();

        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(locationRepository.findByName("SHIP-SB")).thenReturn(location);
        when(finaleConfigProperties.getDomain()).thenReturn("testdomain");

        // When
        service.cleanGhostInventory(deliveryOrder);

        // Then
        ArgumentCaptor<UpdateInventoryVarianceRequest> requestCaptor =
            ArgumentCaptor.forClass(UpdateInventoryVarianceRequest.class);
        verify(finaleAdaptor).updateInventoryVariance(requestCaptor.capture());

        UpdateInventoryVarianceRequest request = requestCaptor.getValue();
        assertEquals("PHSCL_INV_COMMITTED", request.getStatusId());
        assertEquals("FACILITY", request.getPhysicalInventoryTypeId());
        assertEquals("/testdomain/api/facility/12345", request.getFacilityUrl());
        assertEquals("Auto cleanup after delivery; order=SO-123;", request.getGeneralComments());
        assertEquals(1, request.getInventoryItemVarianceList().size());

        var varianceItem = request.getInventoryItemVarianceList().get(0);
        assertEquals("/testdomain/api/product/SKU001", varianceItem.getProductUrl());
        assertEquals(-2, varianceItem.getQuantityOnHandVar()); // 3 - 5 = -2
        assertEquals(VarianceReasonType.VAR_DAMAGED.getCode(), varianceItem.getVarianceReasonId());
    }

    @Test
    void cleanGhostInventory_shouldSkipItemsWithFullDelivery() {
        // Given
        DeliveryOrderItemDto fullyDeliveredItem = DeliveryOrderItemDto.builder()
            .skuNumber("SKU002")
            .currentQty(BigDecimal.valueOf(5))
            .deliveredQty(BigDecimal.valueOf(5)) // Fully delivered
            .reasonCode("{\"DAMAGED\":1}")
            .build();

        DeliveryOrderDto deliveryOrder = DeliveryOrderDto.builder()
            .orderNumber("SO-123")
            .deliveryOrderItems(List.of(fullyDeliveredItem))
            .build();

        Location location = createMockLocation();
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(locationRepository.findByName("SHIP-SB")).thenReturn(location);

        // When
        service.cleanGhostInventory(deliveryOrder);

        // Then
        verifyNoInteractions(finaleAdaptor);
    }

    @Test
    void mapReasonCodeToVarianceReasonId_shouldMapCorrectly() {
        assertEquals(VarianceReasonType.VAR_DAMAGED.getCode(),
            service.mapReasonCodeToVarianceReasonId("{\"DAMAGED\":1}"));
        assertEquals(VarianceReasonType.VAR_EXPIRED.getCode(),
            service.mapReasonCodeToVarianceReasonId("{\"EXPIRED\":1}"));
        assertEquals(VarianceReasonType.VAR_LOST.getCode(),
            service.mapReasonCodeToVarianceReasonId("{\"MISSING\":1}"));
        assertEquals(VarianceReasonType.VAR_RTN_CUSTOMER.getCode(),
            service.mapReasonCodeToVarianceReasonId("{\"RETURNS\":1}"));
        assertEquals(VarianceReasonType.VAR_SAMPLE.getCode(),
            service.mapReasonCodeToVarianceReasonId("UNKNOWN"));
        assertEquals(VarianceReasonType.VAR_SAMPLE.getCode(),
            service.mapReasonCodeToVarianceReasonId(null));
    }

    private DeliveryOrderDto createDeliveryOrderWithUndeliveredItems() {
        DeliveryOrderItemDto undeliveredItem = DeliveryOrderItemDto.builder()
            .skuNumber("SKU001")
            .currentQty(BigDecimal.valueOf(5))
            .deliveredQty(BigDecimal.valueOf(3))
            .reasonCode("{\"DAMAGED\":1}")
            .build();

        return DeliveryOrderDto.builder()
            .orderNumber("SO-123")
            .deliveryOrderItems(List.of(undeliveredItem))
            .build();
    }

    private Location createMockLocation() {
        Location location = mock(Location.class);
        when(location.getFinaleId()).thenReturn("12345");
        return location;
    }
}