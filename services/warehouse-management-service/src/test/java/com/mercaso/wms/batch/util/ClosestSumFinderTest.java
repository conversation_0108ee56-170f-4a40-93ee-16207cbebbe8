package com.mercaso.wms.batch.util;

import static com.mercaso.wms.batch.util.ClosestSumFinder.findClosestTask;
import static com.mercaso.wms.batch.util.ClosestSumFinder.splitMapWith;
import static com.mercaso.wms.batch.util.ClosestSumFinder.splitTasks;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ClosestSumFinderTest {

    @Test
    void testBasicCase() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 50),
            "B", new LongSummaryStatistics(1, 0, 0, 30),
            "C", new LongSummaryStatistics(1, 0, 0, 20),
            "D", new LongSummaryStatistics(1, 0, 0, 10)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 2);

        assertEquals(2, result.size());
        // 验证每个子列表的结果
        assertTrue(result.get(0).containsAll(List.of("A", "D")));
        assertTrue(result.get(1).containsAll(List.of("B", "C")));
    }

    @Test
    void test_portion_result() {
        Map<String, LongSummaryStatistics> orderQtyMap = new HashMap<>();
        orderQtyMap.put("Order0", new LongSummaryStatistics(1, 0, 0, 50));
        orderQtyMap.put("Order1", new LongSummaryStatistics(1, 0, 0, 90));
        orderQtyMap.put("Order2", new LongSummaryStatistics(1, 0, 0, 70));
        orderQtyMap.put("Order3", new LongSummaryStatistics(1, 0, 0, 30));
        orderQtyMap.put("Order4", new LongSummaryStatistics(1, 0, 0, 200));
        orderQtyMap.put("Order5", new LongSummaryStatistics(1, 0, 0, 80));
        orderQtyMap.put("Order6", new LongSummaryStatistics(1, 0, 0, 150));

        long target = 200;
        long tolerance = 50;
        int splitCount = 2;

        List<List<String>> result = findClosestTask(orderQtyMap, target, tolerance, splitCount);
        assertTrue(result.get(0).containsAll(List.of("Order0", "Order4")));
        assertTrue(result.get(1).containsAll(List.of("Order6", "Order1")));
    }

    @Test
    void testNoFeasibleGroup() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 100),
            "B", new LongSummaryStatistics(1, 0, 0, 90),
            "C", new LongSummaryStatistics(1, 0, 0, 80)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 2);

        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("A"));
        assertTrue(result.get(1).contains("B"));
    }

    @Test
    void testFewerOrdersThanGroups() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 30),
            "B", new LongSummaryStatistics(1, 0, 0, 20)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 4);

        assertEquals(4, result.size());
        assertTrue(result.get(0).contains("A"));
        assertTrue(result.get(0).contains("B"));
    }

    @Test
    void testMoreOrdersThanGroups() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 40),
            "B", new LongSummaryStatistics(1, 0, 0, 30),
            "C", new LongSummaryStatistics(1, 0, 0, 20),
            "D", new LongSummaryStatistics(1, 0, 0, 10)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 2);

        assertEquals(2, result.size());
        assertTrue(result.stream().flatMap(List::stream).anyMatch(order -> order.equals("A")));
        assertTrue(result.stream().flatMap(List::stream).anyMatch(order -> order.equals("B")));
    }

    @Test
    void testSingleGroup() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 25),
            "B", new LongSummaryStatistics(1, 0, 0, 25)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 5, 1);

        assertEquals(1, result.size());
        assertTrue(result.get(0).containsAll(List.of("A", "B")));
    }

    @Test
    void testEmptyInput() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of();

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 2);

        assertEquals(2, result.size());
        assertTrue(result.get(0).isEmpty());
        assertTrue(result.get(1).isEmpty());
    }

    @Test
    void testExactTarget() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 50),
            "B", new LongSummaryStatistics(1, 0, 0, 50)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 0, 2);

        assertEquals(2, result.size());
    }

    @Test
    void testScatteredOrders() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 15),
            "B", new LongSummaryStatistics(1, 0, 0, 15),
            "C", new LongSummaryStatistics(1, 0, 0, 15),
            "D", new LongSummaryStatistics(1, 0, 0, 15)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 50, 10, 3);

        assertEquals(3, result.size());
        assertEquals(4, result.stream().mapToInt(List::size).sum());
    }

    @Test
    void testExtremeTolerance() {
        Map<String, LongSummaryStatistics> orderQtyMap = Map.of(
            "A", new LongSummaryStatistics(1, 0, 0, 100),
            "B", new LongSummaryStatistics(1, 0, 0, 200),
            "C", new LongSummaryStatistics(1, 0, 0, 300)
        );

        List<List<String>> result = findClosestTask(orderQtyMap, 100, 1000, 3);

        assertEquals(3, result.size());
        assertEquals(3, result.stream().mapToInt(List::size).sum());
    }

    @Test
    void testSplitTask() {
        Map<UUID, LongSummaryStatistics> orderQtyMap = Map.of(
            UUID.randomUUID(), new LongSummaryStatistics(1, 0, 0, 50),
            UUID.randomUUID(), new LongSummaryStatistics(1, 0, 0, 30),
            UUID.randomUUID(), new LongSummaryStatistics(1, 0, 0, 20),
            UUID.randomUUID(), new LongSummaryStatistics(1, 0, 0, 10)
        );

        List<List<UUID>> result = splitMapWith(orderQtyMap);

        assertEquals(2, result.size());
    }

    @Test
    void splitTasks_EmptyList_ReturnsEmptyResult() {
        List<List<PickingTask>> result = splitTasks(Collections.emptyList(), 3);
        assertTrue(result.isEmpty());
    }

    @Test
    void splitTasks_ZeroSplitCount_ReturnsEmptyResult() {
        List<PickingTask> tasks = createMockTasks(5, 10);
        List<List<PickingTask>> result = splitTasks(tasks, 0);
        assertTrue(result.isEmpty());
    }

    @Test
    void splitTasks_SingleTask_ReturnsSingleGroup() {
        List<PickingTask> tasks = createMockTasks(1, 10);
        List<List<PickingTask>> result = splitTasks(tasks, 1);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).size());
    }

    @Test
    void splitTasks_EvenDistribution() {
        // Create 6 tasks with 10 qty each
        List<PickingTask> tasks = createMockTasks(6, 10);
        List<List<PickingTask>> result = splitTasks(tasks, 2);

        assertEquals(2, result.size());
        assertEquals(3, result.get(0).size());
        assertEquals(3, result.get(1).size());

        // Verify total quantity in each group
        assertEquals(30, calculateGroupQty(result.get(0)));
        assertEquals(30, calculateGroupQty(result.get(1)));
    }

    @Test
    void splitTasks_UnevenDistribution() {
        // Create tasks with different quantities
        List<PickingTask> tasks = Arrays.asList(
            createMockTask(20),  // Group 1: 20
            createMockTask(10),  // Group 1: 30
            createMockTask(15),  // Group 1: 15
            createMockTask(25),  // Group 2: 40
            createMockTask(10)   // Group 2: 50
        );

        List<List<PickingTask>> result = splitTasks(tasks, 2);

        assertEquals(2, result.size());
        assertEquals(3, result.get(0).size());
        assertEquals(2, result.get(1).size());
    }

    @Test
    void splitTasks_Uneven_Distribution() {
        // Create tasks with different quantities
        List<PickingTask> tasks = Arrays.asList(
            createMockTask(100),  // Group 1: 100
            createMockTask(10),  // Group 1: 10
            createMockTask(98),  // Group 2: 98
            createMockTask(1),  // Group 2: 1
            createMockTask(11), // Group 2: 11
            createMockTask(95), // Group 3: 95
            createMockTask(5) // Group 3: 5
        );

        List<List<PickingTask>> result = splitTasks(tasks, 3);

        assertEquals(3, result.size());
        assertEquals(2, result.get(0).size());
        assertEquals(3, result.get(1).size());
        assertEquals(2, result.get(2).size());
    }

    @Test
    void splitTasks_MoreGroupsThanTasks() {
        List<PickingTask> tasks = createMockTasks(2, 10);
        List<List<PickingTask>> result = splitTasks(tasks, 3);

        assertEquals(2, result.size());
        assertEquals(1, result.get(0).size());
        assertEquals(1, result.get(1).size());
    }

    private List<PickingTask> createMockTasks(int count, int qtyPerTask) {
        List<PickingTask> tasks = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            tasks.add(createMockTask(qtyPerTask));
        }
        return tasks;
    }

    private PickingTask createMockTask(int qty) {
        PickingTask task = mock(PickingTask.class);
        List<PickingTaskItem> items = new ArrayList<>();
        PickingTaskItem item = mock(PickingTaskItem.class);
        when(item.getExpectQty()).thenReturn(qty);
        items.add(item);
        when(task.getPickingTaskItems()).thenReturn(items);
        return task;
    }

    private long calculateGroupQty(List<PickingTask> group) {
        return group.stream()
            .mapToLong(task -> task.getPickingTaskItems().stream()
                .mapToLong(PickingTaskItem::getExpectQty)
                .sum())
            .sum();
    }

}