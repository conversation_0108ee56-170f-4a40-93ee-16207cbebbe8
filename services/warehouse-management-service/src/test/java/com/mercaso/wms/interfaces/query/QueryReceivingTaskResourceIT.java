package com.mercaso.wms.interfaces.query;

import static com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus.CREATED;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.utils.ReceivingTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryReceivingTaskResourceIT extends AbstractIT {

    @Autowired
    ReceivingTaskResourceApi receivingTaskResourceApi;

    @Test
    void findById_withValidParameters_returnsReceivingTaskDto() {
        receivingTaskRepository.deleteAll();
        List<ReceivingTask> receivingTasks = buildReceivingTask(UUID.randomUUID(),
            1,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        ReceivingTask result = receivingTaskRepository.save(receivingTasks.getFirst());

        ReceivingTaskDto receivingTask = receivingTaskResourceApi.getReceivingTask(result.getId());

        assertNotNull(receivingTask);
        assertEquals(result.getId(), receivingTask.getId());
    }
}