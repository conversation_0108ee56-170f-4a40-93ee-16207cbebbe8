package com.mercaso.wms.interfaces.search;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doNothing;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.AccountPreferenceView;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.AccountPreferenceResourceApi;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchAccountPreferenceResourceIT extends AbstractIT {

    @Autowired
    AccountPreferenceResourceApi accountPreferenceResourceApi;

    @Test
    void when_search_account_preference_then_success() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        // given
        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName(RandomStringUtils.randomAlphabetic(10))
            .userId(UUID.randomUUID())
            .status(AccountPreferenceStatus.ACTIVE)
            .workWarehouseId(warehouses.getFirst().getId())
            .isFullTime(true)
            .email(RandomStringUtils.randomAlphabetic(10))
            .dailyWorkHours(8)
            .externalWarehouseId(warehouses.getLast().getId())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment("Test Department")
            .build();

        doNothing().when(umsAdaptor).assignPickerRolesToUser(command.getUserId());

        // when
        accountPreferenceResourceApi.createAccountPreference(command);

        Result<AccountPreferenceView> userNameSearchResult = accountPreferenceResourceApi.search(command.getUserName(),
            null,
            null,
            null,
            null,
            null);
        assertNotNull(userNameSearchResult.getData());
        assertEquals(command.getUserName(), userNameSearchResult.getData().getFirst().getUserName());

        Result<AccountPreferenceView> emailSearchResult = accountPreferenceResourceApi.search(null,
            command.getEmail(),
            null,
            null,
            null,
            null);
        assertNotNull(emailSearchResult.getData());
        assertEquals(command.getEmail(), emailSearchResult.getData().getFirst().getEmail());

        Result<AccountPreferenceView> statusSearchResult = accountPreferenceResourceApi.search(null,
            null,
            command.getStatus(),
            null,
            null,
            null);
        assertNotNull(statusSearchResult.getData());
        assertEquals(command.getStatus().toString(), statusSearchResult.getData().getFirst().getStatus());

        Result<AccountPreferenceView> workWarehouseIdSearchResult = accountPreferenceResourceApi.search(null,
            null,
            null,
            command.getWorkWarehouseId(),
            null,
            null);
        assertNotNull(workWarehouseIdSearchResult.getData());
        assertEquals(command.getWorkWarehouseId(), workWarehouseIdSearchResult.getData().getFirst().getWorkWarehouse().getId());

        Result<AccountPreferenceView> externalWarehouseIdSearchResult = accountPreferenceResourceApi.search(null,
            null,
            null,
            null,
            command.getExternalWarehouseId(),
            null);
        assertNotNull(externalWarehouseIdSearchResult.getData());
        assertEquals(command.getExternalWarehouseId(),
            externalWarehouseIdSearchResult.getData().getFirst().getExternalWarehouse().getId());

        Result<AccountPreferenceView> taskTypeSearchResult = accountPreferenceResourceApi.search(null,
            null,
            null,
            null,
            null,
            command.getTaskType());
        assertNotNull(taskTypeSearchResult.getData());
        assertEquals(command.getTaskType().toString(), taskTypeSearchResult.getData().getFirst().getTaskType());

    }

}