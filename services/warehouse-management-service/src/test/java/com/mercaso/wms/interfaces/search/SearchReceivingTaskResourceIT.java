package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus.CREATED;
import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.utils.ReceivingTaskResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchReceivingTaskResourceIT extends AbstractIT {

    @Autowired
    private ReceivingTaskResourceApi receivingTaskResourceApi;

    @Test
    void searchPickingTasks_withValidParameters_returnsResult() throws Exception {
        receivingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(buildBatch(null));
        List<ReceivingTask> receivingTasks = buildReceivingTask(saved.getId(),
            5,
            SourceEnum.VERNON,
            CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);

        List<ReceivingTask> result = receivingTaskRepository.saveAll(receivingTasks);

        Result<ReceivingTaskDto> receivingTaskDtoResult = receivingTaskResourceApi.search(
            null,
            SourceEnum.VERNON,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(receivingTaskDtoResult);
        assertNotNull(receivingTaskDtoResult.getData());
        assertNotNull(receivingTaskDtoResult.getTotalCount());
        assertEquals(5, receivingTaskDtoResult.getData().size());
        assertEquals(5, receivingTaskDtoResult.getTotalCount());

        receivingTaskDtoResult.getData().forEach(receivingTaskDto -> {
            result.forEach(receivingTask -> {
                if (receivingTask.getId().equals(receivingTaskDto.getId())) {
                    assertEquals(receivingTask.getId(), receivingTaskDto.getId());
                    assertEquals(receivingTask.getVendor(), receivingTaskDto.getVendor());
                    assertEquals(receivingTask.getStatus(), receivingTaskDto.getStatus());
                }
            });
        });

        Result<ReceivingTaskDto> receivingTaskDtoResultByNumber = receivingTaskResourceApi.search(
            null,
            SourceEnum.VERNON,
            null,
            null,
            ReceivingTaskType.ONLINE_RECEIVING,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(receivingTaskDtoResultByNumber);
        assertNotNull(receivingTaskDtoResultByNumber.getData());
        assertNotNull(receivingTaskDtoResultByNumber.getTotalCount());
        assertEquals(5, receivingTaskDtoResultByNumber.getData().size());
        assertEquals(5, receivingTaskDtoResultByNumber.getTotalCount());

        Result<ReceivingTaskDto> receivingTaskDtoResultByDeliveryDate = receivingTaskResourceApi.search(
            null,
            SourceEnum.VERNON,
            null,
            LocalDate.parse(saved.getTag()),
            ReceivingTaskType.ONLINE_RECEIVING,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(receivingTaskDtoResultByDeliveryDate);
        assertNotNull(receivingTaskDtoResultByDeliveryDate.getData());
        assertNotNull(receivingTaskDtoResultByDeliveryDate.getTotalCount());
        assertEquals(5, receivingTaskDtoResultByDeliveryDate.getData().size());
        assertEquals(5, receivingTaskDtoResultByDeliveryDate.getTotalCount());

    }

}