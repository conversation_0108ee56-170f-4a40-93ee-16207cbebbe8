package com.mercaso.wms.batch.strategy.impl;

import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

class FullBreakdownPopulateStrategyTest {

    private final FullBreakdownPopulateStrategy fullBreakdownPopulateStrategy = new FullBreakdownPopulateStrategy();

    @Test
    void when_populate_breakdown_template_with_more_than_15_then_populate() {
        List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();
        String orderNumber = "M-".concat(RandomStringUtils.randomAlphabetic(5));
        for (int i = 0; i < 17; i++) {
            String itemNumber = "JC".concat(RandomStringUtils.randomNumeric(5));
            excelBatchDtos.add(ExcelBatchDto.builder()
                .orderNumber(orderNumber)
                .itemNumber(itemNumber)
                .bigOrder(true)
                .pos("[AA]")
                .quantity(1)
                .build());
        }

        List<BreakdownDto> breakdownDtos = fullBreakdownPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);

        assertEquals(1, breakdownDtos.size());
    }

    @Test
    void when_populate_breakdown_template_with_more_than_15_then_populate_and_sorted() {
        List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();
        String orderNumber = "M-".concat(RandomStringUtils.randomAlphabetic(5));
        for (int i = 0; i < 17; i++) {
            String itemNumber = "JC".concat(RandomStringUtils.randomNumeric(5));
            excelBatchDtos.add(ExcelBatchDto.builder()
                .orderNumber(orderNumber)
                .source(SourceEnum.MFC.name())
                .itemNumber(itemNumber)
                .quantity(1)
                .bigOrder(true)
                .pos("010")
                .build());
        }

        String orderNumber1 = "M-".concat(RandomStringUtils.randomAlphabetic(5));
        for (int i = 0; i < 17; i++) {
            String itemNumber = "JC".concat(RandomStringUtils.randomNumeric(5));
            excelBatchDtos.add(ExcelBatchDto.builder()
                .orderNumber(orderNumber1)
                .source(SourceEnum.MFC.name())
                .itemNumber(itemNumber)
                .quantity(1)
                .pos("001")
                .bigOrder(true)
                .build());
        }

        List<BreakdownDto> breakdownDtos = fullBreakdownPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);

        assertEquals(2, breakdownDtos.size());
        assertEquals("001", breakdownDtos.get(0).getBreakdown());
        assertEquals("010", breakdownDtos.get(1).getBreakdown());
    }

}