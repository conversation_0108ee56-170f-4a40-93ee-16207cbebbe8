package com.mercaso.wms.batch.writer.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class VernonTotalSheetWriterTest extends Writer {

    private final VernonTotalSheetWriter vernonTotalSheetWriter = new VernonTotalSheetWriter();

    @Test
    void when_write_vernon_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> vernons = Lists.newArrayList();
        ExcelBatchDto vernonExcelBatchDto = new ExcelBatchDto();
        vernonExcelBatchDto.setItemNumber("vernon");
        vernonExcelBatchDto.setFrom("Location2");
        vernonExcelBatchDto.setQuantity(1);
        vernonExcelBatchDto.setSource(SourceEnum.VERNON.name());

        ExcelBatchDto vernonExcelBatchDto1 = new ExcelBatchDto();
        vernonExcelBatchDto1.setItemNumber("vernon1");
        vernonExcelBatchDto1.setFrom("Location2");
        vernonExcelBatchDto1.setQuantity(1);
        vernonExcelBatchDto1.setSource(SourceEnum.VERNON.name());

        ExcelBatchDto vernonExcelBatchDto2 = new ExcelBatchDto();
        vernonExcelBatchDto2.setItemNumber("vernon1");
        vernonExcelBatchDto2.setFrom("Location2");
        vernonExcelBatchDto2.setQuantity(1);
        vernonExcelBatchDto2.setSource(SourceEnum.VERNON.name());

        vernons.add(vernonExcelBatchDto);
        vernons.add(vernonExcelBatchDto1);
        vernons.add(vernonExcelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.VERNON.getValue(), vernons));

        writeBatchTemplate(condition, vernonTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> vernonTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.VERNON_TOTALS.getValue()).doReadSync();

        assertEquals(5, vernonTotals.size());
        assertEquals("1", vernonTotals.get(4).get(6));
        assertEquals("2", vernonTotals.get(3).get(6));
    }

    @Test
    void when_write_empty_tobacco_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> vernons = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.VERNON.getValue(), vernons));

        writeBatchTemplate(condition, vernonTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> vernonTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.VERNON_TOTALS.getValue()).doReadSync();

        assertEquals(3, vernonTotals.size());
    }
}