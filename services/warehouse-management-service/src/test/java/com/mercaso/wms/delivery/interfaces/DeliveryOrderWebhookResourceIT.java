package com.mercaso.wms.delivery.interfaces;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderAdminForDeliveryDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DeliveryOrderWebhookResourceIT extends AbstractIT {

    @Autowired
    DeliveryOrderWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    DeliveryOrderRepository deliveryOrderRepository;

    @Test
    void when_webhook_then_return_void() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        DeliveryOrder deliveryOrder = deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertEquals(shopifyOrderDto.getId(), deliveryOrder.getShopifyOrderId());
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
        assertNotNull(deliveryOrder.getCurrentTotalDiscounts());
        assertNotNull(deliveryOrder.getDiscountApplications());
    }

    @Test
    void when_webhook_with_invalid_payload_then_return_use_admin_api() {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();
        shopifyOrderDto.setId(RandomStringUtils.randomNumeric(11));
        String serialize = SerializationUtils.serialize(shopifyOrderDto);

        when(shopifyAdaptor.getShopifyOrderForDeliveryByAdminApi(any())).thenReturn(new ShopifyOrderAdminForDeliveryDto(
            shopifyOrderDto));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        shopifyWebhookResourceApi.webhook(serialize.substring(0, serialize.length() - 10));

        DeliveryOrder deliveryOrder = deliveryOrderRepository.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        assertEquals(shopifyOrderDto.getName(), deliveryOrder.getOrderNumber());
        assertEquals(shopifyOrderDto.getId(), deliveryOrder.getShopifyOrderId());
        assertEquals(shopifyOrderDto.getLineItems().size(), deliveryOrder.getDeliveryOrderItems().size());
    }

}