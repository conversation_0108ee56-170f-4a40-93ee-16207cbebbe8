package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.ColourMarkingEnum;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class LabelPrintingSheetsWriterTest extends Writer {

    private final LabelPrintingSheetsWriter labelPrintingSheetsWriter = new LabelPrintingSheetsWriter();

    @Test
    void write_validCondition_writesToMfcRelatedExcel() {
        ExcelBatchDto bigExcelBatchDto = new ExcelBatchDto();
        bigExcelBatchDto.setItemNumber("Item2");
        bigExcelBatchDto.setFrom("Location1");
        bigExcelBatchDto.setQuantity(5);
        bigExcelBatchDto.setSource(SourceEnum.MFC.name());
        bigExcelBatchDto.setColourMarking(ColourMarkingEnum.YELLOW.name());

        ExcelBatchDto smallExcelBatchDto = new ExcelBatchDto();
        smallExcelBatchDto.setItemNumber("Item1");
        smallExcelBatchDto.setFrom("Location2");
        smallExcelBatchDto.setQuantity(10);
        smallExcelBatchDto.setSource(SourceEnum.MFC.name());
        smallExcelBatchDto.setColourMarking(null);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .mfcBeverageBigOrder(Collections.singletonList(bigExcelBatchDto))
            .mfcBeverageSmallOrder(Collections.singletonList(smallExcelBatchDto))
            .sourceAndListMap(new HashMap<>())
            .build();
        writeBatchTemplate(condition, labelPrintingSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> bigOrders =
            EasyExcel.read(fileName)
                .sheet(GeneratedDocNameEnum.MFC_BEVERAGES_BIG_ORDER_LABELS_WMS_PICKING.getValue())
                .doReadSync();

        List<LinkedHashMap<Integer, String>> smallOrders =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_BEVERAGES_SMALL_ORDER_LABELS.getValue()).doReadSync();

        // Verify the data
        assertEquals(9, bigOrders.size());
        assertEquals("Item2", bigOrders.get(5).get(1));
        assertEquals("5", bigOrders.get(5).get(5));
        assertEquals("Location1", bigOrders.get(5).get(8));

        assertEquals(14, smallOrders.size());
        assertEquals("Item1", smallOrders.get(5).get(1));
        assertEquals("10", smallOrders.get(5).get(5));
        assertEquals("Location2", smallOrders.get(5).get(8));

    }

    @Test
    void write_validCondition_writesToMfcCandyExcel() {
        ExcelBatchDto batch1 = new ExcelBatchDto();
        batch1.setItemNumber("Item1");
        batch1.setFrom("Location1");
        batch1.setQuantity(10);
        batch1.setSource(SourceEnum.MFC.name());
        batch1.setColourMarking(ColourMarkingEnum.YELLOW.name());

        ExcelBatchDto batch2 = new ExcelBatchDto();
        batch2.setItemNumber("Item2");
        batch2.setFrom("Location2");
        batch2.setQuantity(5);
        batch2.setSource(SourceEnum.MFC.name());
        batch2.setColourMarking(null);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .mfcCandyOrderItems(Arrays.asList(batch1, batch2))
            .sourceAndListMap(new HashMap<>())
            .build();
        writeBatchTemplate(condition, labelPrintingSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> candyOrders =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_CANDY_LABELS.getValue()).doReadSync();

        assertEquals(19, candyOrders.size());
        assertEquals("Item1", candyOrders.get(5).get(1));
        assertEquals("10", candyOrders.get(5).get(5));
        assertEquals("Location1", candyOrders.get(5).get(8));

    }

    @Test
    void write_validCondition_writesToVendorExcel() {
        ExcelBatchDto threePlExcelBatchDto = new ExcelBatchDto();
        threePlExcelBatchDto.setItemNumber("downey");
        threePlExcelBatchDto.setFrom("Location1");
        threePlExcelBatchDto.setQuantity(3);
        threePlExcelBatchDto.setSource(SourceEnum.MFC.name());
        threePlExcelBatchDto.setColourMarking(ColourMarkingEnum.YELLOW.name());

        ExcelBatchDto missionExcelBatchDto = new ExcelBatchDto();
        missionExcelBatchDto.setItemNumber("mission");
        missionExcelBatchDto.setFrom("Location2");
        missionExcelBatchDto.setQuantity(2);
        missionExcelBatchDto.setSource(SourceEnum.MFC.name());
        missionExcelBatchDto.setColourMarking(null);

        ExcelBatchDto costcoExcelBatchDto = new ExcelBatchDto();
        costcoExcelBatchDto.setItemNumber("costco");
        costcoExcelBatchDto.setFrom("Location2");
        costcoExcelBatchDto.setQuantity(1);
        costcoExcelBatchDto.setSource(SourceEnum.MFC.name());
        costcoExcelBatchDto.setColourMarking(null);

        ExcelBatchDto vernonExcelBatchDto = new ExcelBatchDto();
        vernonExcelBatchDto.setItemNumber("vernon");
        vernonExcelBatchDto.setFrom("Location2");
        vernonExcelBatchDto.setQuantity(2);
        vernonExcelBatchDto.setSource(SourceEnum.VERNON.name());

        ExcelBatchDto exoticExcelBatchDto = new ExcelBatchDto();
        exoticExcelBatchDto.setItemNumber("exotic");
        exoticExcelBatchDto.setFrom("Location2");
        exoticExcelBatchDto.setQuantity(2);
        exoticExcelBatchDto.setSource(SourceEnum.EXOTIC.name());

        ExcelBatchDto sevenStarExcelBatchDto = new ExcelBatchDto();
        sevenStarExcelBatchDto.setItemNumber("sevenStar");
        sevenStarExcelBatchDto.setFrom("Location2");
        sevenStarExcelBatchDto.setQuantity(2);
        sevenStarExcelBatchDto.setSource(SourceEnum.SEVEN_STARS.name());

        ExcelBatchDto jetroExcelBatchDto = new ExcelBatchDto();
        jetroExcelBatchDto.setItemNumber("jetro");
        jetroExcelBatchDto.setFrom("Location2");
        jetroExcelBatchDto.setQuantity(2);
        jetroExcelBatchDto.setSource(SourceEnum.JETRO.name());

        ExcelBatchDto smartAndFinalExcelBatchDto = new ExcelBatchDto();
        smartAndFinalExcelBatchDto.setItemNumber("smart");
        smartAndFinalExcelBatchDto.setFrom("Location2");
        smartAndFinalExcelBatchDto.setQuantity(2);
        smartAndFinalExcelBatchDto.setSource(SourceEnum.SMART_AND_FINAL.name());

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.DOWNEY.name(), Collections.singletonList(threePlExcelBatchDto));
        sourceAndListMap.put(SourceEnum.MISSION.name(), Collections.singletonList(missionExcelBatchDto));
        sourceAndListMap.put(SourceEnum.COSTCO.name(), Collections.singletonList(costcoExcelBatchDto));
        sourceAndListMap.put(SourceEnum.VERNON.name(), Collections.singletonList(vernonExcelBatchDto));
        sourceAndListMap.put(SourceEnum.EXOTIC.name(), Collections.singletonList(exoticExcelBatchDto));
        sourceAndListMap.put(SourceEnum.SEVEN_STARS.name(), Collections.singletonList(sevenStarExcelBatchDto));
        sourceAndListMap.put(SourceEnum.JETRO.name(), Collections.singletonList(jetroExcelBatchDto));
        sourceAndListMap.put(SourceEnum.SMART_AND_FINAL.name(), Collections.singletonList(smartAndFinalExcelBatchDto));

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, labelPrintingSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> threePlData =
            EasyExcel.read(fileName).sheet(SourceEnum.DOWNEY.name() + " LABELS").doReadSync();

        List<LinkedHashMap<Integer, String>> missionData =
            EasyExcel.read(fileName).sheet(SourceEnum.MISSION.name() + " LABELS").doReadSync();

        List<LinkedHashMap<Integer, String>> costcoData =
            EasyExcel.read(fileName).sheet(SourceEnum.COSTCO.name() + " LABELS").doReadSync();

        List<LinkedHashMap<Integer, String>> vernonData =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.VERNON_LABELS.getValue()).doReadSync();

        List<LinkedHashMap<Integer, String>> exoticData =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.EXOTIC_LABELS.getValue()).doReadSync();

        List<LinkedHashMap<Integer, String>> sevenStarData =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SEVEN_STARS_LABELS.getValue()).doReadSync();

        List<LinkedHashMap<Integer, String>> jetroData =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.JETRO_LABELS.getValue()).doReadSync();
        List<LinkedHashMap<Integer, String>> smartAndFinalData =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SMART_AND_FINAL_LABELS.getValue()).doReadSync();

        assertEquals(6, threePlData.size());
        assertEquals("downey", threePlData.get(3).get(1));
        assertEquals("3", threePlData.get(3).get(5));
        assertEquals("Location1", threePlData.get(3).get(8));

        assertEquals(5, missionData.size());
        assertEquals("mission", missionData.get(3).get(1));
        assertEquals("2", missionData.get(3).get(5));
        assertEquals("Location2", missionData.get(3).get(8));

        assertEquals(4, costcoData.size());
        assertEquals("costco", costcoData.get(3).get(1));
        assertEquals("1", costcoData.get(3).get(5));
        assertEquals("Location2", costcoData.get(3).get(8));

        assertEquals(5, vernonData.size());
        assertEquals("vernon", vernonData.get(3).get(1));
        assertEquals("2", vernonData.get(3).get(6));
        assertEquals("Location2", vernonData.get(3).get(9));

        assertEquals(5, exoticData.size());
        assertEquals("exotic", exoticData.get(3).get(1));
        assertEquals("2", exoticData.get(3).get(6));
        assertEquals("Location2", exoticData.get(3).get(9));

        assertEquals(5, sevenStarData.size());
        assertEquals("sevenStar", sevenStarData.get(3).get(1));
        assertEquals("2", sevenStarData.get(3).get(6));
        assertEquals("Location2", sevenStarData.get(3).get(9));

        assertEquals(5, jetroData.size());
        assertEquals("jetro", jetroData.get(3).get(1));
        assertEquals("2", jetroData.get(3).get(6));
        assertEquals("Location2", jetroData.get(3).get(9));

        assertEquals(5, smartAndFinalData.size());
        assertEquals("smart", smartAndFinalData.get(3).get(1));
        assertEquals("2", smartAndFinalData.get(3).get(6));
        assertEquals("Location2", smartAndFinalData.get(3).get(9));
    }


}