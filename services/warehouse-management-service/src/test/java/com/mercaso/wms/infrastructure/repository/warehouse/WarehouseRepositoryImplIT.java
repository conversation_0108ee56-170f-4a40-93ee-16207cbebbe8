package com.mercaso.wms.infrastructure.repository.warehouse;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class WarehouseRepositoryImplIT extends AbstractIT {

    @Test
    void when_create_warehouse_then_return_warehouse() {
        // given
        Warehouse warehouse = Warehouse.builder().type(WarehouseType.INTERNAL).name(RandomStringUtils.randomAlphabetic(5)).status(
            WarehouseStatus.ACTIVE).build();

        Warehouse saved = warehouseRepository.save(warehouse);

        assertNotNull(saved);

        Warehouse result = warehouseRepository.findById(saved.getId());

        assertNotNull(result);
        assertEquals(saved.getId(), result.getId());
    }

    @Test
    void when_update_warehouse_then_return_updated_warehouse() {
        // given
        Warehouse warehouse = Warehouse.builder().type(WarehouseType.INTERNAL).name(RandomStringUtils.randomAlphabetic(5)).status(
            WarehouseStatus.ACTIVE).build();

        Warehouse saved = warehouseRepository.save(warehouse);

        assertNotNull(saved);

        Warehouse result = warehouseRepository.findById(saved.getId());

        result.setName(RandomStringUtils.randomAlphabetic(10));
        Warehouse updated = warehouseRepository.update(result);

        assertNotNull(updated);
        assertEquals(result.getName(), updated.getName());
    }

}