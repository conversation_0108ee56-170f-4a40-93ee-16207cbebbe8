package com.mercaso.wms.infrastructure.utils;

import static com.github.javaparser.utils.Utils.assertNotNull;
import com.mercaso.wms.builder.DataBuilder;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import org.junit.jupiter.api.Test;

class StockUtilTest {

    @Test
    void getLocationType_withValidLocation_returnsLocationType() {
        List<Location> locations = DataBuilder.buildLocations(1, LocationType.BIN);
        Location result = StockUtil.getLocationType(locations, "00-01-A-0");
        assertEquals(LocationType.BIN, result.getType());
    }

    @Test
    void getLocationType_withInvalidLocation_returnsNull() {
        List<Location> locations = DataBuilder.buildLocations(1, LocationType.BIN);
        Location result = StockUtil.getLocationType(locations, "InvalidLocation");
        assertNull(result);
    }

    @Test
    void getLocationType_withSpecialUpperBin_returnsBin() {
        List<Location> locations = DataBuilder.buildLocations(1, LocationType.BIN);
        Location first = locations.getFirst();
        first.setName("RD-MFC");
        LocationType locationTypeFromSubLocation = StockUtil.getLocationTypeFromSubLocation(first.getName(), first);
        assertNotNull(locationTypeFromSubLocation);
        assertEquals(LocationType.BIN, locationTypeFromSubLocation);
    }

}