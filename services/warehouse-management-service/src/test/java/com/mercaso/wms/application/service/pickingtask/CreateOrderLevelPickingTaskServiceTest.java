package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import java.util.List;
import java.util.UUID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CreateOrderLevelPickingTaskServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);
    private final LocationRepository locationRepository = mock(LocationRepository.class);
    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = mock(PickingTaskAssignmentConfig.class);
    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);
    private final AutoAssignOrderLevelPickingTaskService autoAssignOrderLevelPickingTaskService = mock(
        AutoAssignOrderLevelPickingTaskService.class);
    private final LocationCache locationCache = mock(LocationCache.class);

    private final CreateOrderLevelPickingTaskService createOrderLevelPickingTask = new CreateOrderLevelPickingTaskService(
        batchItemRepository,
        batchItemQueryService,
        pickingTaskRepository,
        locationRepository,
        pickingTaskAssignmentConfig,
        pickingTaskApplicationService,
        autoAssignOrderLevelPickingTaskService,
        locationCache
    );

    @Test
    void when_batch_created_then_generate_picking_task() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        when(batchItemQueryService.findBigOrdersByBatchIdAndSource(any(), any())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(buildPickingTask(batchItems.getFirst().getBatchId(), 10));

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        List<PickingTask> pickingTasks = createOrderLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
    }

    @Test
    void when_batch_created_then_generate_picking_task_include_RD() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.getFirst().setLocationName(".RD");

        when(batchItemQueryService.findBigOrdersByBatchIdAndSource(any(), any())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(buildPickingTask(batchItems.getFirst().getBatchId(), 10));

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        List<PickingTask> pickingTasks = createOrderLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
    }

}