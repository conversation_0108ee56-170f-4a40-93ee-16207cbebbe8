package com.mercaso.wms.delivery.interfaces;

import static com.mercaso.wms.infrastructure.external.ums.UmsAdaptor.DRIVER_ROLE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.client.dto.CreateUserRequest;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.account.CreateAccountCommand;
import com.mercaso.wms.delivery.application.command.account.UpdateAccountCommand;
import com.mercaso.wms.delivery.application.dto.account.AccountDto;
import com.mercaso.wms.delivery.application.service.AccountService;
import com.mercaso.wms.delivery.application.service.RouteManagerService;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverRequest;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.utils.AccountResourceApi;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Integration tests for AccountResource REST API.
 */
class AccountResourceIT extends AbstractIT {

    @Autowired
    private AccountResourceApi accountResourceApi;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RouteManagerService routeManagerService;

    @Autowired
    private AccountRepository accountRepository;

    private String testEmail;
    private String testUserName;
    private UUID testWarehouseId;
    private UUID testUserId;

    @BeforeEach
    void setUp() {
        testEmail = "test" + RandomStringUtils.randomAlphanumeric(8) + "@example.com";
        testUserName = "TestUser" + RandomStringUtils.randomAlphanumeric(5);
        testWarehouseId = UUID.randomUUID();
        testUserId = UUID.randomUUID();

        // Configure service parameters
        ReflectionTestUtils.setField(routeManagerService, "driverCreationTimeoutSeconds", 3);
        ReflectionTestUtils.setField(routeManagerService, "driverCreationPollIntervalSeconds", 1);
        ReflectionTestUtils.setField(routeManagerService, "publicKey", "test-public-key");
        ReflectionTestUtils.setField(accountService, "publicKey", "test-public-key");

        // Setup mock behavior
        setupRouteManagerBehavior();
        setupUmsBehavior();
    }

    private void setupRouteManagerBehavior() {
        AddDriverResponse response = new AddDriverResponse();
        response.setRequestId(UUID.randomUUID().toString());
        when(routeManagerAdaptor.addDriver(any(AddDriverRequest.class))).thenReturn(response);

        // Mock initial list drivers call with empty list
        List<Driver> initialDrivers = new ArrayList<>();

        // Then add test driver
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        initialDrivers.add(driver);

        // Mock that the driver will be found after it's added
        // This is crucial to avoid the timeout in waitForDriverCreation
        doAnswer(invocation -> {
            String email = invocation.getArgument(0, AddDriverRequest.class).getEmail();

            // Add the driver to initialDrivers list
            Driver newDriver = new Driver();
            newDriver.setId(UUID.randomUUID().toString());
            newDriver.setEmail(email);
            initialDrivers.add(newDriver);

            return response;
        }).when(routeManagerAdaptor).addDriver(any(AddDriverRequest.class));

        // Make listDrivers return the current list of drivers, including newly added ones
        when(routeManagerAdaptor.listDrivers()).thenAnswer(invocation -> initialDrivers);
    }

    private void setupUmsBehavior() {
        UserDto userDto = new UserDto();
        userDto.setId(testUserId);
        userDto.setEmail(testEmail);
        userDto.setName(testUserName);
        // Mock with a more robust implementation to handle different cases
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString())).thenAnswer(invocation -> {
            CreateUserRequest request = invocation.getArgument(0);
            if (request == null) {
                return userDto; // Return default user for null requests
            }

            // Create a custom response based on input
            UserDto response = new UserDto();
            response.setId(UUID.randomUUID());
            response.setEmail(request.getEmail());
            response.setName(request.getName());
            return response;
        });
    }

    @Test
    @DisplayName("When creating account with valid data then return created account")
    void when_createAccount_with_valid_data_then_return_created_account() {
        // Arrange
        CreateAccountCommand command = CreateAccountCommand.builder()
            .email(testEmail)
            .userName(testUserName)
            .warehouseId(testWarehouseId)
            .build();

        // Act
        AccountDto result = accountResourceApi.createAccount(command);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(testEmail, result.getEmail());
        assertEquals(testUserName, result.getUserName());
        assertEquals(AccountStatus.ACTIVE, result.getStatus());

        // Verify data was saved to database
        Optional<Account> savedAccount = accountRepository.findByEmail(testEmail);
        assertNotNull(savedAccount);
        assertEquals(testEmail, savedAccount.get().getEmail());
    }

    @Test
    @DisplayName("When updating account with valid data then return updated account")
    void when_updateAccount_with_valid_data_then_return_updated_account() {
        // Arrange - first create an account
        CreateAccountCommand createCommand = CreateAccountCommand.builder()
            .email(testEmail)
            .userName(testUserName)
            .warehouseId(testWarehouseId)
            .build();

        AccountDto createdAccount = accountResourceApi.createAccount(createCommand);
        assertNotNull(createdAccount);

        // Act - update its status
        UpdateAccountCommand updateCommand = UpdateAccountCommand.builder()
            .status(AccountStatus.INACTIVE)
            .build();

        AccountDto updatedAccount = accountResourceApi.updateAccount(createdAccount.getId(), updateCommand);

        // Assert
        assertNotNull(updatedAccount);
        assertEquals(createdAccount.getId(), updatedAccount.getId());
        assertEquals(AccountStatus.INACTIVE, updatedAccount.getStatus());

        // Verify data was updated in database
        Account savedAccount = accountRepository.findById(createdAccount.getId());
        assertNotNull(savedAccount);
        assertEquals(AccountStatus.INACTIVE, savedAccount.getStatus());
    }

    @Test
    @DisplayName("When updating non-existent account then throw exception")
    void when_updateAccount_with_non_existent_id_then_throw_exception() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();
        UpdateAccountCommand command = UpdateAccountCommand.builder()
            .status(AccountStatus.INACTIVE)
            .build();

        // Act & Assert
        Exception exception = assertThrows(RuntimeException.class, () -> {
            accountResourceApi.updateAccount(nonExistentId, command);
        });

        // Verify exception contains expected message
        String expectedMessage = "Failed to update account";
        String actualMessage = exception.getMessage();

        assertEquals(expectedMessage, actualMessage);
    }

    /**
     * Test pulling drivers from RouteManager and creating accounts
     * When new driver data is pulled from RouteManager, it should successfully create a corresponding account
     */
    @Test
    @DisplayName("When pulling from RouteManager with new drivers then create accounts successfully")
    void when_pullFromRouteManager_with_new_drivers_then_create_accounts_successfully() {
        // Arrange
        List<Driver> mockDrivers = new ArrayList<>();

        // Add existing driver
        String existingEmail = "existing-" + RandomStringUtils.randomAlphanumeric(8) + "@example.com";
        Driver existingDriver = new Driver();
        existingDriver.setId(UUID.randomUUID().toString());
        existingDriver.setEmail(existingEmail);
        existingDriver.setName("ExistingDriver" + RandomStringUtils.randomAlphanumeric(5));
        mockDrivers.add(existingDriver);

        // Create a mock account for the existing driver
        CreateAccountCommand createCommand = CreateAccountCommand.builder()
            .email(existingEmail)
            .userName(existingDriver.getName())
            .warehouseId(testWarehouseId)
            .build();
        accountResourceApi.createAccount(createCommand);

        // Create two new drivers
        Driver newDriver1 = new Driver();
        newDriver1.setId(UUID.randomUUID().toString());
        newDriver1.setEmail("new1-" + RandomStringUtils.randomAlphanumeric(8) + "@example.com");
        newDriver1.setName("NewDriver1" + RandomStringUtils.randomAlphanumeric(5));
        mockDrivers.add(newDriver1);

        Driver newDriver2 = new Driver();
        newDriver2.setId(UUID.randomUUID().toString());
        newDriver2.setEmail("new2-" + RandomStringUtils.randomAlphanumeric(8) + "@example.com");
        newDriver2.setName("NewDriver2" + RandomStringUtils.randomAlphanumeric(5));
        mockDrivers.add(newDriver2);

        // Use doReturn instead of when().thenReturn() to avoid MissingMethodInvocationException
        doReturn(mockDrivers).when(routeManagerAdaptor).listDrivers();

        // Make sure we have drivers available in RouteManager
        AddDriverResponse response = new AddDriverResponse();
        response.setRequestId(UUID.randomUUID().toString());
        doReturn(response).when(routeManagerAdaptor).addDriver(any(AddDriverRequest.class));

        UserDto userDto1 = new UserDto();
        userDto1.setEmail(newDriver1.getEmail());
        userDto1.setId(UUID.randomUUID());

        UserDto userDto2 = new UserDto();
        userDto2.setEmail(newDriver2.getEmail());
        userDto2.setId(UUID.randomUUID());
        // Setup mock UMS responses with simple implementation that can't throw NPE
        doReturn(userDto1, userDto2).when(umsAdaptor).createUser(any(CreateUserRequest.class), eq(DRIVER_ROLE));

        // Act
        accountResourceApi.pullFromRouteManager();

        // Assert
        // Verify RouteManager API was called
        verify(routeManagerAdaptor, times(2)).listDrivers();
    }

    @Test
    @DisplayName("When creating account with driver then successfully add driver to RouteManager")
    void when_createAccount_with_driver_then_successfully_add_driver_to_RouteManager() {
        // Arrange
        String email = "driver-" + RandomStringUtils.randomAlphanumeric(8) + "@example.com";
        String userName = "DriverUser" + RandomStringUtils.randomAlphanumeric(5);

        CreateAccountCommand command = CreateAccountCommand.builder()
            .email(email)
            .userName(userName)
            .warehouseId(testWarehouseId)
            .build();

        // Make sure the driver will be found after being added to RouteManager
        // This is essential to avoid the waitForDriverCreation timeout
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(email);
        driver.setName(userName);

        // Mock the behavior of findDriverByEmail by returning the driver
        // when listDrivers is called after addDriver
        doAnswer(invocation -> {
            // First call returns empty list (no driver yet)
            if (!"driverAdded".equals(driver.getName())) {
                return new ArrayList<>();
            }
            // Subsequent calls return list with the driver (after driver added)
            List<Driver> drivers = new ArrayList<>();
            drivers.add(driver);
            return drivers;
        }).when(routeManagerAdaptor).listDrivers();

        // When addDriver is called, mark the driver as added
        doAnswer(invocation -> {
            driver.setName("driverAdded"); // Mark as added
            AddDriverResponse response = new AddDriverResponse();
            response.setRequestId(UUID.randomUUID().toString());
            return response;
        }).when(routeManagerAdaptor).addDriver(any(AddDriverRequest.class));

        // Act
        AccountDto result = accountResourceApi.createAccount(command);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(email, result.getEmail());
        assertEquals(userName, result.getUserName());
        assertEquals(AccountStatus.ACTIVE, result.getStatus());

        // Verify addDriver was called
        verify(routeManagerAdaptor, times(1)).addDriver(any(AddDriverRequest.class));

        // Verify driver was found (listDrivers was called at least once)
        verify(routeManagerAdaptor, times(1)).listDrivers();

        // Verify account was saved to database
        Optional<Account> savedAccount = accountRepository.findByEmail(email);
        assertNotNull(savedAccount);
        assertEquals(email, savedAccount.get().getEmail());
        assertEquals(userName, savedAccount.get().getUserName());
    }
}