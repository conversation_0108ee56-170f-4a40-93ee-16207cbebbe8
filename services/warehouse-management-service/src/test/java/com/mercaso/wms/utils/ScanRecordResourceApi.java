package com.mercaso.wms.utils;

import com.mercaso.wms.application.command.scanrecord.BatchScanRecordCommand;
import com.mercaso.wms.application.dto.scanrecord.ScanRecordDto;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ScanRecordResourceApi extends IntegrationTestRestUtil {

    public ScanRecordResourceApi(Environment environment) {
        super(environment);
    }

    private static final String CREATE_URL = "/scan-records";
    private static final String FIND_BY_DELIVERY_ORDER_URL = "/query/scan-records/by-delivery-order/%s";

    public void create(BatchScanRecordCommand command) {
        postEntity(CREATE_URL, SerializationUtils.serialize(command), Void.class);
    }

    public List<ScanRecordDto> findByDeliveryOrderId(UUID deliveryOrderId) throws Exception {
        return getEntityList(String.format(FIND_BY_DELIVERY_ORDER_URL, deliveryOrderId.toString()), ScanRecordDto.class);
    }
}
