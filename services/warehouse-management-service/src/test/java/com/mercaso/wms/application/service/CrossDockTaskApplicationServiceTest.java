package com.mercaso.wms.application.service;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand;
import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand.BindItemAndTaskItem;
import com.mercaso.wms.application.command.crossdock.CreateCrossDockTaskCommand;
import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.application.mapper.crossdock.CrossDockTaskDtoApplicationMapper;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.crossdock.CrossDockTask;
import com.mercaso.wms.domain.crossdock.CrossDockTaskRepository;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.domain.shippingorder.ShippingOrderService;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItemRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CrossDockTaskApplicationServiceTest {

    @Mock
    private CrossDockTaskRepository crossDockTaskRepository;

    @Mock
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @Mock
    private PickingTaskItemRepository pickingTaskItemRepository;

    @Mock
    private ReceivingTaskItemRepository receivingTaskItemRepository;

    @Mock
    private PickingTaskRepository pickingTaskRepository;

    @Mock
    private ReceivingTaskRepository receivingTaskRepository;

    @Mock
    private CrossDockTaskDtoApplicationMapper crossDockTaskDtoApplicationMapper;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private ShippingOrderService shippingOrderService;

    @Mock
    private AccountPreferenceRepository accountPreferenceRepository;

    @Mock
    private WarehouseRepository warehouseRepository;

    @Mock
    private ShippingOrderItemRepository shippingOrderItemRepository;

    @InjectMocks
    private CrossDockTaskApplicationService crossDockTaskApplicationService;

    private UUID crossDockTaskId;
    private UUID pickingTaskItemId;
    private UUID receivingTaskItemId;
    private UUID pickingTaskId;
    private UUID receivingTaskId;
    private UUID shippingOrderId;
    private UUID shippingOrderItemId;
    private CrossDockTask crossDockTask;
    private PickingTaskItem pickingTaskItem;
    private ReceivingTaskItem receivingTaskItem;
    private CrossDockTaskItem crossDockTaskItem1;
    private CrossDockTaskItem crossDockTaskItem2;
    private CrossDockTaskItem crossDockTaskItem3;
    private PickingTask pickingTask;
    private ReceivingTask receivingTask;
    private AccountPreference picker;
    private Warehouse warehouse;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        crossDockTaskId = UUID.randomUUID();
        pickingTaskItemId = UUID.randomUUID();
        receivingTaskItemId = UUID.randomUUID();
        pickingTaskId = UUID.randomUUID();
        receivingTaskId = UUID.randomUUID();
        shippingOrderId = UUID.randomUUID();
        shippingOrderItemId = UUID.randomUUID();

        crossDockTask = CrossDockTask.builder()
            .id(crossDockTaskId)
            .crossDockTaskItems(new ArrayList<>())
            .build();

        pickingTaskItem = PickingTaskItem.builder()
            .id(pickingTaskItemId)
            .batchItemId(UUID.randomUUID())
            .skuNumber("SKU001")
            .title("Test Item")
            .itemId(UUID.randomUUID())
            .pickedQty(3)
            .shippingOrderId(shippingOrderId)
            .shippingOrderItemId(shippingOrderItemId)
            .breakdownName("Test Breakdown")
            .pickingTaskId(pickingTaskId)
            .build();

        receivingTaskItem = ReceivingTaskItem.builder()
            .id(receivingTaskItemId)
            .batchItemId(UUID.randomUUID())
            .skuNumber("SKU002")
            .title("Test Receiving Item")
            .itemId(UUID.randomUUID())
            .receivedQty(2)
            .shippingOrderId(shippingOrderId)
            .shippingOrderItemId(shippingOrderItemId)
            .breakdownName("Test Receiving Breakdown")
            .receivingTaskId(receivingTaskId)
            .build();

        crossDockTaskItem1 = CrossDockTaskItem.builder()
            .id(UUID.randomUUID())
            .taskItemId(pickingTaskItemId)
            .shippingOrderId(shippingOrderId)
            .shippingOrderItemId(shippingOrderItemId)
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .pickedQty(1)
            .crossDockedQty(0)
            .sequence(new ArrayList<>())
            .build();

        crossDockTaskItem2 = CrossDockTaskItem.builder()
            .id(UUID.randomUUID())
            .taskItemId(pickingTaskItemId)
            .shippingOrderId(shippingOrderId)
            .shippingOrderItemId(shippingOrderItemId)
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .pickedQty(1)
            .crossDockedQty(0)
            .sequence(new ArrayList<>())
            .build();

        crossDockTaskItem3 = CrossDockTaskItem.builder()
            .id(UUID.randomUUID())
            .taskItemId(pickingTaskItemId)
            .shippingOrderId(shippingOrderId)
            .shippingOrderItemId(shippingOrderItemId)
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .pickedQty(1)
            .crossDockedQty(0)
            .sequence(new ArrayList<>())
            .build();

        pickingTask = PickingTask.builder()
            .id(pickingTaskId)
            .batchId(UUID.randomUUID())
            .build();

        receivingTask = ReceivingTask.builder()
            .id(receivingTaskId)
            .batchId(UUID.randomUUID())
            .vendor("COSTCO")
            .build();

        picker = AccountPreference.builder()
            .userId(UUID.randomUUID())
            .userName("Test Picker")
            .build();

        warehouse = Warehouse.builder()
            .id(UUID.randomUUID())
            .name("MDC")
            .build();
    }

    @Test
    void when_bindItems_withPickingTaskItem_then_bindSuccessfully() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);

        verify(crossDockTaskRepository).findById(crossDockTaskId);
        verify(crossDockTaskItemRepository, times(1)).findByTaskItemId(pickingTaskItemId);
        verify(crossDockTaskDtoApplicationMapper).domainToDto(crossDockTask);
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(
            eq(String.format("bindItems_item_%s", pickingTaskItemId).hashCode()),
            eq(String.format("BindItems item lock for taskItem %s", pickingTaskItemId))
        );
        verify(shippingOrderService).updateFulfilledQty(shippingOrderId, shippingOrderItemId, 1);
    }

    @Test
    void when_bindItems_withReceivingTaskItem_then_bindSuccessfully() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .receivingTaskItemId(receivingTaskItemId)
                .sequence("1/2")
                .build()))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(receivingTaskItemId))
            .thenReturn(Collections.emptyList());
        when(receivingTaskItemRepository.findById(receivingTaskItemId)).thenReturn(receivingTaskItem);
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);

        verify(crossDockTaskRepository).findById(crossDockTaskId);
        verify(crossDockTaskItemRepository, times(1)).findByTaskItemId(receivingTaskItemId);
        verify(receivingTaskItemRepository, times(3)).findById(receivingTaskItemId);
        verify(receivingTaskRepository).findById(receivingTask.getId());
        verify(crossDockTaskItemRepository, times(1)).save(any(CrossDockTaskItem.class));
        verify(crossDockTaskDtoApplicationMapper).domainToDto(crossDockTask);
        verify(pgAdvisoryLock).tryAcquireTransactionalLevelAdvisoryLock(
            eq(String.format("bindItems_item_%s", receivingTaskItemId).hashCode()),
            eq(String.format("BindItems item lock for taskItem %s", receivingTaskItemId))
        );
    }

    @Test
    void when_bindItems_withNoAvailableCrossDockTaskItem_then_createNew() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(Collections.emptyList());
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(pickingTaskRepository.findById(pickingTaskItem.getPickingTaskId())).thenReturn(pickingTask);
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);

        verify(crossDockTaskItemRepository, times(1)).save(any(CrossDockTaskItem.class));
        verify(pickingTaskRepository).findById(pickingTaskItem.getPickingTaskId());
    }

    @Test
    void when_bindItems_withDuplicateSequence_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        crossDockTaskItem1.setSequence(List.of("1/3"));

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);

        // Mock ShippingOrderItem as normal value item (not high value)
        ShippingOrderItem mockShippingOrderItem = ShippingOrderItem.builder()
            .id(shippingOrderItemId)
            .highValueItem(false)
            .build();
        when(shippingOrderItemRepository.findById(shippingOrderItemId)).thenReturn(mockShippingOrderItem);

        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals(ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getCode(), exception.getCode());
        assertEquals(ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_SEQUENCE_DUPLICATE.getMessage(), exception.getMessage());
    }

    @Test
    void when_bindItems_withEmptySequence_then_notConsideredAsDuplicate() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        crossDockTaskItem1.setSequence(new ArrayList<>());

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
    }

    @Test
    void when_bindItems_withMixedItemTypes_then_bindBothTypes() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(asList(
                BindItemAndTaskItem.builder()
                    .pickingTaskItemId(pickingTaskItemId)
                    .sequence("1/3")
                    .build(),
                BindItemAndTaskItem.builder()
                    .receivingTaskItemId(receivingTaskItemId)
                    .sequence("1/2")
                    .build()
            ))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.findByTaskItemId(receivingTaskItemId))
            .thenReturn(Collections.emptyList());
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(receivingTaskItemRepository.findById(receivingTaskItemId)).thenReturn(receivingTaskItem);
        when(receivingTaskRepository.findById(receivingTaskId)).thenReturn(receivingTask);
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);

        verify(crossDockTaskItemRepository, times(1)).findByTaskItemId(pickingTaskItemId);
        verify(crossDockTaskItemRepository, times(1)).findByTaskItemId(receivingTaskItemId);
        verify(receivingTaskItemRepository, times(3)).findById(receivingTaskItemId);
        verify(receivingTaskRepository).findById(receivingTaskId);
    }

    @Test
    void when_bindItems_withBothPickingAndReceivingTaskItemIds_then_usePickingTaskItemId() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .receivingTaskItemId(receivingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);

        verify(crossDockTaskItemRepository, times(1)).findByTaskItemId(pickingTaskItemId);
        verify(receivingTaskItemRepository, never()).findById(any());
    }

    @Test
    void when_bindItems_withNullTaskItemIds_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .sequence("1/3")
                .build()))
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals("Either pickingTaskItemId or receivingTaskItemId must be provided", exception.getMessage());
    }

    @Test
    void when_bindItems_withValidCommand_shouldUpdateShippingOrderItemFulfilledQty() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        verify(shippingOrderService).updateFulfilledQty(shippingOrderId, shippingOrderItemId, 1);
    }


    @Test
    void when_bindItems_withExceededQuantity_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        // Set picked quantity to 1, but try to bind 4 items (3 existing + 1 new)
        pickingTaskItem.setPickedQty(1);
        crossDockTask.getCrossDockTaskItems().add(crossDockTaskItem1);
        crossDockTask.getCrossDockTaskItems().add(crossDockTaskItem2);
        crossDockTask.getCrossDockTaskItems().add(crossDockTaskItem3);

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);

        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals(ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_OUTNUMBER.getCode(), exception.getCode());
        assertEquals(ErrorCodeEnums.CROSS_DOCK_TASK_ITEM_OUTNUMBER.getMessage(), exception.getMessage());
    }

    @Test
    void when_bindItems_withValidQuantity_then_bindSuccessfully() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        // Set picked quantity to 3, and try to bind 1 item (0 existing + 1 new)
        pickingTaskItem.setPickedQty(3);

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(asList(crossDockTaskItem1, crossDockTaskItem2, crossDockTaskItem3));
        when(crossDockTaskItemRepository.save(any(CrossDockTaskItem.class))).thenReturn(crossDockTaskItem1);
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
    }

    @Test
    void when_bindItems_withNullCrossDockTaskId_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(null, command));

        assertEquals("Cross dock task id cannot be null", exception.getMessage());
    }

    @Test
    void when_bindItems_withNullCommand_then_returnExistingTask() {
        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, null);

        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(crossDockTaskRepository).findById(crossDockTaskId);
        verify(crossDockTaskDtoApplicationMapper).domainToDto(crossDockTask);
        verifyNoInteractions(crossDockTaskItemRepository, pickingTaskItemRepository, receivingTaskItemRepository);
    }

    @Test
    void when_bindItems_withEmptyItems_then_returnExistingTask() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.emptyList())
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(crossDockTaskRepository).findById(crossDockTaskId);
        verify(crossDockTaskDtoApplicationMapper).domainToDto(crossDockTask);
        verifyNoInteractions(crossDockTaskItemRepository, pickingTaskItemRepository, receivingTaskItemRepository);
    }

    @Test
    void when_bindItems_withNullSequence_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .build()))
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals("Sequence must not be null", exception.getMessage());
    }

    @Test
    void when_bindItems_withCrossDockTaskNotFound_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals("Cross dock task not found with id: " + crossDockTaskId, exception.getMessage());
    }

    @Test
    void when_bindItems_withPickingTaskItemNotFound_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("1/3")
                .build()))
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(Collections.emptyList());
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals("PickingTaskItem not found with id: " + pickingTaskItemId, exception.getMessage());
    }

    @Test
    void when_bindItems_withReceivingTaskItemNotFound_then_throwException() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .receivingTaskItemId(receivingTaskItemId)
                .sequence("1/2")
                .build()))
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(receivingTaskItemId))
            .thenReturn(Collections.emptyList());
        when(receivingTaskItemRepository.findById(receivingTaskItemId)).thenReturn(null);

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> crossDockTaskApplicationService.bindItems(crossDockTaskId, command));

        assertEquals("ReceivingTaskItem not found with id: " + receivingTaskItemId, exception.getMessage());
    }

    @Test
    void when_bindItems_withExistingUnboundCrossDockTaskItem_then_updateSequence() {
        BindItemAndTaskCommand command = BindItemAndTaskCommand.builder()
            .items(Collections.singletonList(BindItemAndTaskItem.builder()
                .pickingTaskItemId(pickingTaskItemId)
                .sequence("2/3")
                .build()))
            .build();

        CrossDockTaskItem existingUnboundItem = CrossDockTaskItem.builder()
            .id(UUID.randomUUID())
            .taskItemId(pickingTaskItemId)
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .pickedQty(1)
            .crossDockedQty(0)
            .sequence(new ArrayList<>())
            .crossDockTaskId(null) // Unbound
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(crossDockTaskId)
            .build();

        when(crossDockTaskRepository.findById(crossDockTaskId)).thenReturn(crossDockTask);
        when(crossDockTaskItemRepository.findByTaskItemId(pickingTaskItemId))
            .thenReturn(Collections.singletonList(existingUnboundItem));
        when(pickingTaskItemRepository.findById(pickingTaskItemId)).thenReturn(pickingTaskItem);
        when(crossDockTaskItemRepository.save(existingUnboundItem)).thenReturn(existingUnboundItem);
        when(crossDockTaskDtoApplicationMapper.domainToDto(crossDockTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.bindItems(crossDockTaskId, command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
    }

    @Test
    void when_createCrossDockTask_withExistingTask_then_returnExistingTask() {
        CreateCrossDockTaskCommand command = CreateCrossDockTaskCommand.builder()
            .pickerUserId(UUID.randomUUID())
            .build();

        CrossDockTask existingTask = CrossDockTask.builder()
            .id(UUID.randomUUID())
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(existingTask.getId())
            .build();

        when(crossDockTaskRepository.findByDeliveryDateAndPickerUserId(any(), any())).thenReturn(existingTask);
        when(crossDockTaskDtoApplicationMapper.domainToDto(existingTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.createCrossDockTask(command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(crossDockTaskRepository).findByDeliveryDateAndPickerUserId(any(), any());
        verify(crossDockTaskDtoApplicationMapper).domainToDto(existingTask);
    }

    @Test
    void when_createCrossDockTask_withNewTask_then_createAndReturnTask() {
        UUID pickerUserId = UUID.randomUUID();
        CreateCrossDockTaskCommand command = CreateCrossDockTaskCommand.builder()
            .pickerUserId(pickerUserId)
            .build();

        CrossDockTask newTask = CrossDockTask.builder()
            .id(UUID.randomUUID())
            .build();

        CrossDockTaskDto expectedDto = CrossDockTaskDto.builder()
            .id(newTask.getId())
            .build();

        when(crossDockTaskRepository.findByDeliveryDateAndPickerUserId(any(), any())).thenReturn(null);
        when(accountPreferenceRepository.findByUserId(pickerUserId)).thenReturn(picker);
        when(warehouseRepository.findByName("MDC")).thenReturn(warehouse);
        when(crossDockTaskRepository.save(any(CrossDockTask.class))).thenReturn(newTask);
        when(crossDockTaskDtoApplicationMapper.domainToDto(newTask)).thenReturn(expectedDto);

        CrossDockTaskDto result = crossDockTaskApplicationService.createCrossDockTask(command);

        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(crossDockTaskRepository).save(any(CrossDockTask.class));
        verify(crossDockTaskDtoApplicationMapper).domainToDto(newTask);
    }
}