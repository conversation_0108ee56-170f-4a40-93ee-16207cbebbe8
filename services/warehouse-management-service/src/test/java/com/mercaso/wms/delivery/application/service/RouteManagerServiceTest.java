package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.user.client.dto.CreateUserRequest;
import com.mercaso.user.client.dto.IdentityDto;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverRequest;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class RouteManagerServiceTest {

    @Mock
    private RouteManagerAdaptor routeManagerAdaptor;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private UmsAdaptor umsAdaptor;

    @Mock
    private WarehouseRepository warehouseRepository;

    @InjectMocks
    private RouteManagerService routeManagerService;

    private final String testEmail = "<EMAIL>";
    private final String testUserName = "Test Driver";
    private final String testSecretKey = "secretKey123.Test@1";
    private final String testPublicKey = "test-public-key";
    private UUID testUserId;
    private UUID testAccountId;
    private UUID testWarehouseId;
    private Account testAccount;
    private Warehouse testMdcWarehouse;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        testAccountId = UUID.randomUUID();
        testWarehouseId = UUID.randomUUID();

        // Setup test account
        testAccount = Account.builder()
            .id(testAccountId)
            .userId(testUserId)
            .email(testEmail)
            .userName(testUserName)
            .warehouseId(testWarehouseId)
            .status(AccountStatus.ACTIVE)
            .build();

        // Setup test warehouse
        testMdcWarehouse = Warehouse.builder()
            .id(testWarehouseId)
            .name("MDC")
            .build();

        // Set private fields via reflection
        ReflectionTestUtils.setField(routeManagerService, "driverCreationTimeoutSeconds", 1);
        ReflectionTestUtils.setField(routeManagerService, "driverCreationPollIntervalSeconds", 1);
        ReflectionTestUtils.setField(routeManagerService, "publicKey", testPublicKey);
    }

    @Test
    @DisplayName("When adding driver with valid data Then return account")
    void when_addDriver_with_valid_data_then_return_account() {
        // Arrange
        AddDriverResponse response = new AddDriverResponse();
        response.setRequestId(UUID.randomUUID().toString());

        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        driver.setName(testUserName);

        when(routeManagerAdaptor.addDriver(any(AddDriverRequest.class))).thenReturn(response);
        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver));

        // Act
        Account result = routeManagerService.addDriver(testAccount);

        // Assert
        assertNotNull(result);
        assertEquals(testAccount, result);
        verify(routeManagerAdaptor, times(1)).addDriver(any(AddDriverRequest.class));
        verify(routeManagerAdaptor, times(1)).listDrivers();
    }

    @Test
    @DisplayName("When pulling drivers with no drivers in RouteManager Then do nothing")
    void when_pulling_drivers_with_no_drivers_in_route_manager_then_do_nothing() {
        // Arrange
        when(routeManagerAdaptor.listDrivers()).thenReturn(Collections.emptyList());

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(accountRepository, times(1)).findAllActiveDriver();
        verify(warehouseRepository, never()).findByName(anyString());
        verify(umsAdaptor, never()).getUserByEmailAndConnectionIsMobileConnection(anyString());
    }

    @Test
    @DisplayName("When pulling drivers with all drivers already in system Then do nothing")
    void when_pulling_drivers_with_all_drivers_already_in_system_then_do_nothing() {
        // Arrange
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        driver.setName(testUserName);

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver));
        when(accountRepository.findAll()).thenReturn(List.of(testAccount));

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(warehouseRepository, never()).findByName(anyString());
        verify(umsAdaptor, never()).getUserByEmailAndConnectionIsMobileConnection(anyString());
    }

    @Test
    @DisplayName("When pulling drivers with new drivers and user exists in UMS with Mobile-connection Then create account only")
    void when_pulling_drivers_with_new_drivers_and_user_exists_in_ums_with_mobile_connection_then_create_account_only() {
        // Arrange
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        driver.setName(testUserName);

        UserDto userDto = new UserDto();
        userDto.setId(testUserId);
        userDto.setEmail(testEmail);
        userDto.setName(testUserName);

        IdentityDto identityDto = new IdentityDto();
        identityDto.setConnection("Mobile-connection");
        List<IdentityDto> identities = new ArrayList<>();
        identities.add(identityDto);
        userDto.setIdentities(identities);

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver));
        when(accountRepository.findAllActiveDriver()).thenReturn(Collections.emptyList());
        when(warehouseRepository.findByName("MDC")).thenReturn(testMdcWarehouse);
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(userDto);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(umsAdaptor, times(1)).getUserByEmailAndConnectionIsMobileConnection(testEmail);
        verify(umsAdaptor, times(1)).assignDriverRolesToUser(testUserId);
        verify(umsAdaptor, never()).createUser(any(), anyString());
        verify(accountRepository, times(1)).save(any(Account.class));
    }

    @Test
    @DisplayName("When pulling drivers with new drivers and user does not exist in UMS Then create user and account")
    void when_pulling_drivers_with_new_drivers_and_user_does_not_exist_in_ums_then_create_user_and_account() {
        // Arrange
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        driver.setName(testUserName);

        UserDto userDto = new UserDto();
        userDto.setId(testUserId);
        userDto.setEmail(testEmail);
        userDto.setName(testUserName);

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver));
        when(accountRepository.findAllActiveDriver()).thenReturn(Collections.emptyList());
        when(warehouseRepository.findByName("MDC")).thenReturn(testMdcWarehouse);
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString())).thenReturn(userDto);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(umsAdaptor, times(1)).getUserByEmailAndConnectionIsMobileConnection(testEmail);
        verify(umsAdaptor, times(1)).createUser(any(CreateUserRequest.class), anyString());
        verify(accountRepository, times(1)).save(any(Account.class));
    }

    @Test
    @DisplayName("When pulling drivers with multiple new drivers Then create accounts for all")
    void when_pulling_drivers_with_multiple_new_drivers_then_create_accounts_for_all() {
        // Arrange
        Driver driver1 = new Driver();
        driver1.setId(UUID.randomUUID().toString());
        driver1.setEmail(testEmail);
        driver1.setName(testUserName);

        Driver driver2 = new Driver();
        driver2.setId(UUID.randomUUID().toString());
        driver2.setEmail("<EMAIL>");
        driver2.setName("Driver 2");

        UserDto userDto1 = new UserDto();
        userDto1.setId(testUserId);
        userDto1.setEmail(testEmail);
        userDto1.setName(testUserName);

        UserDto userDto2 = new UserDto();
        userDto2.setId(UUID.randomUUID());
        userDto2.setEmail("<EMAIL>");
        userDto2.setName("Driver 2");

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver1, driver2));
        when(accountRepository.findAllActiveDriver()).thenReturn(Collections.emptyList());
        when(warehouseRepository.findByName("MDC")).thenReturn(testMdcWarehouse);
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(any())).thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString()))
            .thenReturn(userDto1)
            .thenReturn(userDto2);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(umsAdaptor, times(2)).getUserByEmailAndConnectionIsMobileConnection(any());
        verify(umsAdaptor, times(2)).createUser(any(CreateUserRequest.class), anyString());
        verify(accountRepository, times(2)).save(any(Account.class));
    }

    @Test
    @DisplayName("When pulling drivers and exception occurs during account creation Then continue with other drivers")
    void when_pulling_drivers_and_exception_occurs_during_account_creation_then_continue_with_other_drivers() {
        // Arrange
        Driver driver1 = new Driver();
        driver1.setId(UUID.randomUUID().toString());
        driver1.setEmail(testEmail);
        driver1.setName(testUserName);

        Driver driver2 = new Driver();
        driver2.setId(UUID.randomUUID().toString());
        driver2.setEmail("<EMAIL>");
        driver2.setName("Driver 2");

        UserDto userDto2 = new UserDto();
        userDto2.setId(UUID.randomUUID());
        userDto2.setEmail("<EMAIL>");
        userDto2.setName("Driver 2");

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver1, driver2));
        when(accountRepository.findAllActiveDriver()).thenReturn(Collections.emptyList());
        when(warehouseRepository.findByName("MDC")).thenReturn(testMdcWarehouse);

        // First driver causes exception
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail))
            .thenThrow(new RuntimeException("UMS service error"));

        // Second driver works fine
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection("<EMAIL>"))
            .thenReturn(null);
        when(umsAdaptor.createUser(any(CreateUserRequest.class), anyString()))
            .thenReturn(userDto2);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(umsAdaptor, times(2)).getUserByEmailAndConnectionIsMobileConnection(any());
        verify(umsAdaptor, times(1)).createUser(any(CreateUserRequest.class), anyString());
        verify(accountRepository, times(1)).save(any(Account.class));
    }

    @Test
    @DisplayName("When pulling drivers and Mobile-connection user exists Then use existing user")
    void when_pulling_drivers_and_mobile_connection_user_exists_then_use_existing_user() {
        // Arrange
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(testEmail);
        driver.setName(testUserName);

        UserDto userDto = new UserDto();
        userDto.setId(testUserId);
        userDto.setEmail(testEmail);
        userDto.setName(testUserName);

        // Set up Mobile-connection identity
        IdentityDto identityDto = new IdentityDto();
        identityDto.setConnection("Mobile-connection");
        List<IdentityDto> identities = new ArrayList<>();
        identities.add(identityDto);
        userDto.setIdentities(identities);

        when(routeManagerAdaptor.listDrivers()).thenReturn(List.of(driver));
        when(accountRepository.findAllActiveDriver()).thenReturn(Collections.emptyList());
        when(warehouseRepository.findByName("MDC")).thenReturn(testMdcWarehouse);
        when(umsAdaptor.getUserByEmailAndConnectionIsMobileConnection(testEmail)).thenReturn(userDto);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // Act
        routeManagerService.pullFromRouteManager();

        // Assert
        verify(umsAdaptor, times(1)).getUserByEmailAndConnectionIsMobileConnection(testEmail);
        verify(umsAdaptor, times(1)).assignDriverRolesToUser(testUserId);
        verify(umsAdaptor, never()).createUser(any(), anyString());
        verify(accountRepository, times(1)).save(any(Account.class));
    }
} 