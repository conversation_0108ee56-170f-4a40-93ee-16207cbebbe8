package com.mercaso.wms.infrastructure.repository.pickingtaskitem;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PickingTaskItemRepositoryIT extends AbstractIT {

    @Autowired
    private PickingTaskItemRepository pickingTaskItemRepository;
    @Autowired
    private BatchRepository batchRepository;
    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Test
    void when_find_by_delivery_date_then_return_data() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .errorInfo("errorInfo")
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.FAILED)
            .batchId(savedBatch.getId())
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();

        // when
        PickingTask saved = pickingTaskRepository.save(pickingTask);

        List<PickingTaskItem> pickingTaskItemList = pickingTaskItemRepository.findFiledPickingTaskItemBy(batch.getTag(),
            List.of());

        assertEquals(1, pickingTaskItemList.size());
        assertEquals(pickingTaskItemDo.getItemId(), pickingTaskItemList.getFirst().getItemId());

        pickingTaskItemList = pickingTaskItemRepository.findFiledPickingTaskItemBy(null, List.of(saved.getNumber()));

        assertEquals(1, pickingTaskItemList.size());
        assertEquals(pickingTaskItemDo.getItemId(), pickingTaskItemList.getFirst().getItemId());
    }
}