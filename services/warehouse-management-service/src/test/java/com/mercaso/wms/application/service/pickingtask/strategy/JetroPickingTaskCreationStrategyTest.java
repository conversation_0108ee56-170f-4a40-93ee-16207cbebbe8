package com.mercaso.wms.application.service.pickingtask.strategy;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class JetroPickingTaskCreationStrategyTest {

    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final JetroPickingTaskCreationStrategy strategy = new JetroPickingTaskCreationStrategy(batchItemQueryService);

    @Test
    void getSource_ShouldReturnJetro() {
        assertEquals(SourceEnum.JETRO, strategy.getSource());
    }

    @Test
    void extractData_ShouldCallFindUnprocessedBy() {
        UUID batchId = UUID.randomUUID();
        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<BatchItem> expectedItems = buildBatchItems(batchId, 5);
        when(batchItemQueryService.findUnprocessedBy(batchId, SourceEnum.JETRO.name()))
            .thenReturn(expectedItems);

        List<BatchItem> result = strategy.extractData(context);

        assertEquals(expectedItems, result);
        verify(batchItemQueryService).findUnprocessedBy(batchId, SourceEnum.JETRO.name());
    }

    @Test
    void createAndSave_WithEmptyItems_ShouldReturnEmptyList() {
        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(UUID.randomUUID())
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> result = strategy.createAndSave(Collections.emptyList(), context);

        assertTrue(result.isEmpty());
        verify(pickingTaskRepository, never()).saveAll(anyList());
    }

    @Test
    void createAndSave_WithRegularItems_ShouldCreateTasks() {
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = buildBatchItems(batchId, 5);
        batchItems.forEach(item -> {
            item.setSource(SourceEnum.JETRO.name());
            item.setLocationName("100");
            item.setDepartment("General Merchandise");
            item.setExpectQty(10);
        });

        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> expectedTasks = Collections.singletonList(
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build()
        );
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(expectedTasks);

        List<PickingTask> result = strategy.createAndSave(batchItems, context);

        assertEquals(expectedTasks, result);
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithCandyAndSnacksItems_ShouldGroupByCategory() {
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = buildBatchItems(batchId, 4);

        // First two items with category A
        batchItems.getFirst().setSource(SourceEnum.JETRO.name());
        batchItems.getFirst().setLocationName("100");
        batchItems.getFirst().setDepartment("Candy & Snacks");
        batchItems.getFirst().setCategory("CategoryA");
        batchItems.getFirst().setExpectQty(10);

        batchItems.get(1).setSource(SourceEnum.JETRO.name());
        batchItems.get(1).setLocationName("100");
        batchItems.get(1).setDepartment("Candy & Snacks");
        batchItems.get(1).setCategory("CategoryA");
        batchItems.get(1).setExpectQty(10);

        // Last two items with category B
        batchItems.get(2).setSource(SourceEnum.JETRO.name());
        batchItems.get(2).setLocationName("101");
        batchItems.get(2).setDepartment("Candy & Snacks");
        batchItems.get(2).setCategory("CategoryB");
        batchItems.get(2).setExpectQty(10);

        batchItems.get(3).setSource(SourceEnum.JETRO.name());
        batchItems.get(3).setLocationName("101");
        batchItems.get(3).setDepartment("Candy & Snacks");
        batchItems.get(3).setCategory("CategoryB");
        batchItems.get(3).setExpectQty(10);

        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> expectedTasks = Arrays.asList(
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build(),
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build()
        );
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(expectedTasks);

        List<PickingTask> result = strategy.createAndSave(batchItems, context);

        assertEquals(expectedTasks, result);
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithFrozenItems_ShouldCreateTasks() {
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = buildBatchItems(batchId, 3);
        batchItems.forEach(item -> {
            item.setSource(SourceEnum.JETRO.name());
            item.setLocationName("102");
            item.setDepartment("Frozen");
            item.setExpectQty(10);
        });

        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> expectedTasks = Collections.singletonList(
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build()
        );
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(expectedTasks);

        List<PickingTask> result = strategy.createAndSave(batchItems, context);

        assertEquals(expectedTasks, result);
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithMixedDepartments_ShouldCreateSeparateTasks() {
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = buildBatchItems(batchId, 6);

        // General Merchandise items
        for (int i = 0; i < 3; i++) {
            batchItems.get(i).setSource(SourceEnum.JETRO.name());
            batchItems.get(i).setLocationName("100");
            batchItems.get(i).setDepartment("General Merchandise");
            batchItems.get(i).setExpectQty(10);
        }

        // Frozen items
        for (int i = 3; i < 6; i++) {
            batchItems.get(i).setSource(SourceEnum.JETRO.name());
            batchItems.get(i).setLocationName("102");
            batchItems.get(i).setDepartment("Frozen");
            batchItems.get(i).setExpectQty(10);
        }

        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> expectedTasks = Arrays.asList(
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build(),
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build()
        );
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(expectedTasks);

        List<PickingTask> result = strategy.createAndSave(batchItems, context);

        assertEquals(expectedTasks, result);
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithSameLocationDifferentDepartments_ShouldCreateSeparateTasks() {
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = buildBatchItems(batchId, 4);

        // First two items in same location but different departments
        batchItems.getFirst().setSource(SourceEnum.JETRO.name());
        batchItems.getFirst().setLocationName("100");
        batchItems.getFirst().setDepartment("General Merchandise");
        batchItems.getFirst().setExpectQty(10);

        batchItems.get(1).setSource(SourceEnum.JETRO.name());
        batchItems.get(1).setLocationName("100");
        batchItems.get(1).setDepartment("Candy & Snacks");
        batchItems.get(1).setExpectQty(10);

        // Last two items in different location
        batchItems.get(2).setSource(SourceEnum.JETRO.name());
        batchItems.get(2).setLocationName("101");
        batchItems.get(2).setDepartment("General Merchandise");
        batchItems.get(2).setExpectQty(10);

        batchItems.get(3).setSource(SourceEnum.JETRO.name());
        batchItems.get(3).setLocationName("101");
        batchItems.get(3).setDepartment("Candy & Snacks");
        batchItems.get(3).setExpectQty(10);

        PickingTaskCreationContext context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(new HashMap<>())
            .pickingTaskRepository(pickingTaskRepository)
            .build();

        List<PickingTask> expectedTasks = Arrays.asList(
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build(),
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build(),
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build(),
            PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.JETRO).type(PickingTaskType.BATCH).build()
        );
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(expectedTasks);

        List<PickingTask> result = strategy.createAndSave(batchItems, context);

        assertEquals(expectedTasks, result);
        verify(pickingTaskRepository).saveAll(anyList());
    }
} 