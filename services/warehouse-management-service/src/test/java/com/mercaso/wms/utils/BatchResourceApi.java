package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.BatchItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.sql.Date;
import java.time.Instant;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class BatchResourceApi extends IntegrationTestRestUtil {

    private static final String CREATE_BATCH_URL = "/batches";

    public static final String GET_BATCH_URL = "/doc-records/%s";

    public static final String GET_BATCHES_URL = "/doc-records/all";

    public static final String SEARCH_BATCH_ITEMS_URL = "/search/batch-items";

    public BatchResourceApi(Environment environment) {
        super(environment);
    }

    @SuppressWarnings("rawtypes")
    public Response createBatch(CreateBatchDto createBatchDto) {
        return createEntity(CREATE_BATCH_URL, SerializationUtils.serialize(createBatchDto), Response.class);
    }

    public BatchDo getBatch(UUID id) {
        return getEntity(String.format(GET_BATCH_URL, id), BatchDo.class).getBody();
    }

    public Result<BatchDo> getBatches(String deliveryDate, Instant updatedAt, String updateBy) throws Exception {
        String url = GET_BATCHES_URL.concat("?pageSize=20&page=1");
        if (null != deliveryDate) {
            url = url.concat("&deliveryDate=").concat(deliveryDate);
        }
        if (null != updatedAt) {
            url = url.concat("&updatedAt=").concat(Date.from(updatedAt).toString());
        }
        if (null != updateBy) {
            url = url.concat("&updatedBy=").concat(updateBy);
        }
        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public Result<BatchItemDto> searchBatchItems(String source, String deliveryDate) throws Exception {
        String url = SEARCH_BATCH_ITEMS_URL.concat("?pageSize=20&page=1");
        if (null != source) {
            url = url.concat("&source=").concat(source);
        }
        if (null != deliveryDate) {
            url = url.concat("&deliveryDate=").concat(deliveryDate);
        }
        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public Response<String> cancelBatch(UUID batchId) throws Exception {
        String url = String.format("/batches/%s/cancel", batchId);
        return updateEntity(url, null, Response.class);
    }

}
