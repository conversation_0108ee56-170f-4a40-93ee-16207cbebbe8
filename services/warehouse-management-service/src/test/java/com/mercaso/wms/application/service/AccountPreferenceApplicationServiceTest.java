package com.mercaso.wms.application.service;

import static com.mercaso.wms.domain.pickingtask.enums.PickingTaskType.ORDER;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.mapper.accountpreference.AccountPreferenceDtoApplicationMapper;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class AccountPreferenceApplicationServiceTest {

    private final AccountPreferenceRepository accountPreferenceRepository = mock(AccountPreferenceRepository.class);
    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);
    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);
    private final AccountPreferenceDtoApplicationMapper mapper = mock(AccountPreferenceDtoApplicationMapper.class);
    private final UmsAdaptor umsAdaptor = mock(UmsAdaptor.class);

    private final AccountPreferenceApplicationService service = new AccountPreferenceApplicationService(
        accountPreferenceRepository,
        warehouseRepository,
        businessEventDispatcher,
        mapper,
        umsAdaptor
    );

    @Test
    void createAccountPreference_ShouldCreateSuccessfully() {
        // Arrange
        UUID workWarehouseId = UUID.randomUUID();
        UUID externalWarehouseId = UUID.randomUUID();

        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName("Test Picker")
            .workWarehouseId(workWarehouseId)
            .externalWarehouseId(externalWarehouseId)
            .isFullTime(true)
            .dailyWorkHours(8)
            .taskType(ORDER)
            .preferredDepartment("Grocery")
            .build();

        Warehouse workWarehouse = mock(Warehouse.class);
        Warehouse externalWarehouse = mock(Warehouse.class);
        AccountPreference accountPreference = mock(AccountPreference.class);
        AccountPreferenceDto expectedDto = mock(AccountPreferenceDto.class);

        when(accountPreferenceRepository.findByEmail(any())).thenReturn(null);
        when(warehouseRepository.findById(workWarehouseId)).thenReturn(workWarehouse);
        when(warehouseRepository.findById(externalWarehouseId)).thenReturn(externalWarehouse);
        when(umsAdaptor.createUser(any(), any())).thenReturn(mock(UserDto.class));
        when(accountPreference.create(any(), any(), any())).thenReturn(accountPreference);
        when(mapper.domainToDto(any(), any())).thenReturn(expectedDto);

        // Act
        AccountPreferenceDto result = service.createAccountPreference(command);

        // Assert
        assertThat(result).isEqualTo(expectedDto);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void createAccountPreference_ShouldThrowException_WhenPreferenceExists() {
        // Arrange
        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName("Test Picker")
            .build();

        when(accountPreferenceRepository.findByEmail(any()))
            .thenReturn(mock(AccountPreference.class));

        // Act & Assert
        assertThatThrownBy(() -> service.createAccountPreference(command))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Account preference already exists");
    }

    @Test
    void updateAccountPreference_ShouldUpdateSuccessfully() {
        // Arrange
        UUID preferenceId = UUID.randomUUID();
        UUID workWarehouseId = UUID.randomUUID();
        UUID externalWarehouseId = UUID.randomUUID();

        UpdateAccountPreferenceCommand command = UpdateAccountPreferenceCommand.builder()
            .workWarehouseId(workWarehouseId)
            .externalWarehouseId(externalWarehouseId)
            .isFullTime(false)
            .dailyWorkHours(6)
            .taskType(ORDER)
            .preferredDepartment("Produce")
            .name("Updated Picker")
            .build();

        AccountPreference existingPreference = mock(AccountPreference.class);
        Warehouse workWarehouse = mock(Warehouse.class);
        Warehouse externalWarehouse = mock(Warehouse.class);
        AccountPreferenceDto expectedDto = mock(AccountPreferenceDto.class);

        when(accountPreferenceRepository.findById(preferenceId)).thenReturn(existingPreference);
        when(warehouseRepository.findById(workWarehouseId)).thenReturn(workWarehouse);
        when(warehouseRepository.findById(externalWarehouseId)).thenReturn(externalWarehouse);
        when(existingPreference.update(any(), any(), any())).thenReturn(existingPreference);
        when(mapper.domainToDto(any(), any())).thenReturn(expectedDto);

        // Act
        AccountPreferenceDto result = service.updateAccountPreference(command, preferenceId);

        // Assert
        assertThat(result).isEqualTo(expectedDto);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void updateAccountPreference_ShouldThrowException_WhenPreferenceNotFound() {
        // Arrange
        UUID preferenceId = UUID.randomUUID();
        UpdateAccountPreferenceCommand command = UpdateAccountPreferenceCommand.builder()
            .build();

        when(accountPreferenceRepository.findById(preferenceId)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> service.updateAccountPreference(command, preferenceId))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Account preference not found");
    }

    @Test
    void getWarehouse_ShouldThrowException_WhenWarehouseNotFound() {
        // Arrange
        UUID warehouseId = UUID.randomUUID();
        when(warehouseRepository.findById(warehouseId)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> service.createAccountPreference(
            CreateAccountPreferenceCommand.builder()
                .workWarehouseId(warehouseId)
                .build()))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Work warehouse not found");
    }

    @Test
    void createAccountPreference_WhenUserIdIsNull_ShouldCreateNewUser() {
        // Arrange
        CreateAccountPreferenceCommand command = createCommand(null);
        Warehouse workWarehouse = createWarehouse();
        Warehouse externalWarehouse = createWarehouse();
        UserDto userDto = createUserDto();
        AccountPreference accountPreference = createAccountPreference();
        AccountPreferenceDto expectedDto = createAccountPreferenceDto();

        when(warehouseRepository.findById(command.getWorkWarehouseId())).thenReturn(workWarehouse);
        when(warehouseRepository.findById(command.getExternalWarehouseId())).thenReturn(externalWarehouse);
        when(umsAdaptor.createUser(any(), any())).thenReturn(userDto);
        when(accountPreferenceRepository.save(any())).thenReturn(accountPreference);
        when(mapper.domainToDto(any(), any())).thenReturn(expectedDto);

        // Act
        AccountPreferenceDto result = service.createAccountPreference(command);

        // Assert
        verify(umsAdaptor).createUser(argThat(request ->
            request.getEmail().equals(command.getEmail()) &&
                request.getName().equals(command.getUserName())
        ), any());
        verify(accountPreferenceRepository).save(any());
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(expectedDto, result);
    }

    @Test
    void createAccountPreference_WhenUserIdExists_ShouldAssignRoles() {
        // Arrange
        UUID userId = UUID.randomUUID();
        CreateAccountPreferenceCommand command = createCommand(userId);
        Warehouse workWarehouse = createWarehouse();
        Warehouse externalWarehouse = createWarehouse();
        AccountPreference accountPreference = createAccountPreference();
        AccountPreferenceDto expectedDto = createAccountPreferenceDto();

        when(warehouseRepository.findById(command.getWorkWarehouseId())).thenReturn(workWarehouse);
        when(warehouseRepository.findById(command.getExternalWarehouseId())).thenReturn(externalWarehouse);
        when(accountPreferenceRepository.save(any())).thenReturn(accountPreference);
        when(mapper.domainToDto(any(), any())).thenReturn(expectedDto);

        // Act
        AccountPreferenceDto result = service.createAccountPreference(command);

        // Assert
        verify(umsAdaptor).assignPickerRolesToUser(userId);
        verify(accountPreferenceRepository).save(any());
        verify(businessEventDispatcher).dispatch(any());
        assertEquals(expectedDto, result);
    }

    @Test
    void createAccountPreference_WhenUserCreationFails_ShouldThrowException() {
        // Arrange
        CreateAccountPreferenceCommand command = createCommand(null);
        Warehouse workWarehouse = createWarehouse();
        Warehouse externalWarehouse = createWarehouse();

        when(warehouseRepository.findById(command.getWorkWarehouseId())).thenReturn(workWarehouse);
        when(warehouseRepository.findById(command.getExternalWarehouseId())).thenReturn(externalWarehouse);
        when(umsAdaptor.createUser(any(), any())).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> service.createAccountPreference(command));
        assertEquals("Create picker error.", exception.getMessage());
    }

    @Test
    void createAccountPreference_WhenWorkWarehouseNotFound_ShouldThrowException() {
        // Arrange
        CreateAccountPreferenceCommand command = createCommand(null);
        when(warehouseRepository.findById(command.getWorkWarehouseId())).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> service.createAccountPreference(command));
        assertEquals("Work warehouse not found.", exception.getMessage());
    }

    @Test
    void createAccountPreference_WhenAccountPreferenceExists_ShouldThrowException() {
        // Arrange
        CreateAccountPreferenceCommand command = createCommand(null);
        AccountPreference existingPreference = createAccountPreference();

        when(accountPreferenceRepository.findByEmail(command.getEmail())).thenReturn(existingPreference);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> service.createAccountPreference(command));
        assertEquals("Account preference already exists.", exception.getMessage());
    }

    // Helper methods
    private CreateAccountPreferenceCommand createCommand(UUID userId) {
        return CreateAccountPreferenceCommand.builder()
            .userId(userId)
            .email("<EMAIL>")
            .userName("Test User")
            .workWarehouseId(UUID.randomUUID())
            .externalWarehouseId(UUID.randomUUID())
            .build();
    }

    private Warehouse createWarehouse() {
        return Warehouse.builder()
            .id(UUID.randomUUID())
            .name("Test Warehouse")
            .build();
    }

    private UserDto createUserDto() {
        UserDto userDto = new UserDto();
        userDto.setId(UUID.randomUUID());
        userDto.setEmail("<EMAIL>");
        userDto.setName("Test User");
        return userDto;
    }

    private AccountPreference createAccountPreference() {
        return AccountPreference.builder()
            .id(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User")
            .build();
    }

    private AccountPreferenceDto createAccountPreferenceDto() {
        return AccountPreferenceDto.builder()
            .id(UUID.randomUUID())
            .email("<EMAIL>")
            .userName("Test User")
            .build();
    }
}