package com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

class PickingTaskItemJdbcTemplateIT extends AbstractIT {

    @Autowired
    private PickingTaskItemJdbcTemplate pickingTaskItemJdbcTemplate;
    @Autowired
    private BatchRepository batchRepository;
    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Test
    void when_searchBy_then_returnFailedPickingTaskItemDto() {
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .errorInfo("errorInfo")
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.FAILED)
            .batchId(savedBatch.getId())
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();

        // when
        PickingTask saved = pickingTaskRepository.save(pickingTask);

        Page<FailedPickingTaskItemDto> failedPickingTaskItems = pickingTaskItemJdbcTemplate.searchBy(batch.getTag(),
            List.of(saved.getNumber()), SourceEnum.MFC.name(), Pageable.ofSize(10));

        assertEquals(1, failedPickingTaskItems.getContent().size());
        assertEquals(saved.getNumber(), failedPickingTaskItems.getContent().getFirst().getPickingTaskNumber());

        failedPickingTaskItems = pickingTaskItemJdbcTemplate.searchBy(null,
            List.of(saved.getNumber()),
            SourceEnum.MFC.name(),
            Pageable.ofSize(10));

        assertEquals(1, failedPickingTaskItems.getContent().size());
        assertEquals(saved.getId(), failedPickingTaskItems.getContent().getFirst().getPickingTaskId());
    }

}