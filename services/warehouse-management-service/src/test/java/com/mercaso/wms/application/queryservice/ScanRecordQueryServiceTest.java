package com.mercaso.wms.application.queryservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.scanrecord.ScanRecordDto;
import com.mercaso.wms.application.mapper.scanrecord.ScanRecordDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.scanrecords.ScanRecord;
import com.mercaso.wms.domain.scanrecords.ScanRecordRepository;
import com.mercaso.wms.domain.scanrecords.enums.ScanType;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScanRecordQueryServiceTest {

    private final ScanRecordRepository scanRecordRepository = mock(ScanRecordRepository.class);
    private final DeliveryOrderRepository deliveryOrderRepository = mock(DeliveryOrderRepository.class);
    private final ScanRecordDtoApplicationMapper scanRecordDtoApplicationMapper = mock(ScanRecordDtoApplicationMapper.class);

    private final ScanRecordQueryService service = new ScanRecordQueryService(
        scanRecordRepository, deliveryOrderRepository, scanRecordDtoApplicationMapper);

    private UUID deliveryOrderId;
    private UUID deliveryOrderItemId1;
    private UUID deliveryOrderItemId2;
    private DeliveryOrder deliveryOrder;
    private DeliveryOrderItem deliveryOrderItem1;
    private DeliveryOrderItem deliveryOrderItem2;
    private ScanRecord scanRecord1;
    private ScanRecord scanRecord2;
    private ScanRecordDto scanRecordDto1;
    private ScanRecordDto scanRecordDto2;

    @BeforeEach
    void setUp() {
        deliveryOrderId = UUID.randomUUID();
        deliveryOrderItemId1 = UUID.randomUUID();
        deliveryOrderItemId2 = UUID.randomUUID();

        deliveryOrderItem1 = DeliveryOrderItem.builder()
            .id(deliveryOrderItemId1)
            .skuNumber("SKU001")
            .qty(BigDecimal.valueOf(5))
            .build();
        deliveryOrderItem2 = DeliveryOrderItem.builder()
            .id(deliveryOrderItemId2)
            .skuNumber("SKU002")
            .qty(BigDecimal.valueOf(3))
            .build();

        deliveryOrder = DeliveryOrder.builder()
            .id(deliveryOrderId)
            .orderNumber("ORDER001")
            .deliveryOrderItems(Arrays.asList(deliveryOrderItem1, deliveryOrderItem2))
            .build();

        scanRecord1 = ScanRecord.builder().build();
        scanRecord1.setId(UUID.randomUUID());
        scanRecord1.setEntityId(deliveryOrderItemId1);
        scanRecord1.setEntityType(EntityEnums.DELIVERY_ORDER_ITEM);
        scanRecord1.setScanType(ScanType.DELIVERY_SCAN_REMOVED);
        scanRecord1.setQty(-1);

        scanRecord2 = ScanRecord.builder().build();
        scanRecord2.setId(UUID.randomUUID());
        scanRecord2.setEntityId(deliveryOrderItemId2);
        scanRecord2.setEntityType(EntityEnums.DELIVERY_ORDER_ITEM);
        scanRecord2.setScanType(ScanType.DELIVERY_SCAN_REMOVED);
        scanRecord2.setQty(-2);

        scanRecordDto1 = ScanRecordDto.builder()
            .id(scanRecord1.getId())
            .entityId(scanRecord1.getEntityId())
            .entityType(scanRecord1.getEntityType().name())
            .scanType(scanRecord1.getScanType().name())
            .qty(scanRecord1.getQty())
            .createdAt(Instant.now())
            .build();

        scanRecordDto2 = ScanRecordDto.builder()
            .id(scanRecord2.getId())
            .entityId(scanRecord2.getEntityId())
            .entityType(scanRecord2.getEntityType().name())
            .scanType(scanRecord2.getScanType().name())
            .qty(scanRecord2.getQty())
            .createdAt(Instant.now())
            .build();
    }

    @Test
    void findByDeliveryOrderId_whenValidId_thenReturnScanRecords() {
        // Arrange
        List<UUID> itemIds = Arrays.asList(deliveryOrderItemId1, deliveryOrderItemId2);
        List<ScanRecord> scanRecords = Arrays.asList(scanRecord1, scanRecord2);
        List<ScanRecordDto> expectedDtos = Arrays.asList(scanRecordDto1, scanRecordDto2);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(scanRecordRepository.findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED))
            .thenReturn(scanRecords);
        when(scanRecordDtoApplicationMapper.domainToDtos(scanRecords)).thenReturn(expectedDtos);

        // Act
        List<ScanRecordDto> result = service.findByDeliveryOrderId(deliveryOrderId);

        // Assert
        assertEquals(2, result.size());
        assertEquals(expectedDtos, result);
        verify(deliveryOrderRepository).findById(deliveryOrderId);
        verify(scanRecordRepository).findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED);
        verify(scanRecordDtoApplicationMapper).domainToDtos(scanRecords);
    }

    @Test
    void findByDeliveryOrderId_whenDeliveryOrderNotFound_thenThrowException() {
        // Arrange
        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> service.findByDeliveryOrderId(deliveryOrderId));
        assertEquals("Delivery order not found with id: " + deliveryOrderId, exception.getMessage());

        verify(deliveryOrderRepository).findById(deliveryOrderId);
        verify(scanRecordRepository, never()).findByEntityIdsAndScanType(anyList(), any(ScanType.class));
        verify(scanRecordDtoApplicationMapper, never()).domainToDtos(anyList());
    }

    @Test
    void findByDeliveryOrderId_whenNoScanRecords_thenReturnEmptyList() {
        // Arrange
        List<UUID> itemIds = Arrays.asList(deliveryOrderItemId1, deliveryOrderItemId2);
        List<ScanRecord> emptyScanRecords = Collections.emptyList();
        List<ScanRecordDto> emptyDtos = Collections.emptyList();

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(scanRecordRepository.findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED))
            .thenReturn(emptyScanRecords);
        when(scanRecordDtoApplicationMapper.domainToDtos(emptyScanRecords)).thenReturn(emptyDtos);

        // Act
        List<ScanRecordDto> result = service.findByDeliveryOrderId(deliveryOrderId);

        // Assert
        assertEquals(0, result.size());
        assertEquals(emptyDtos, result);
        verify(deliveryOrderRepository).findById(deliveryOrderId);
        verify(scanRecordRepository).findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED);
        verify(scanRecordDtoApplicationMapper).domainToDtos(emptyScanRecords);
    }

    @Test
    void findByDeliveryOrderId_whenSingleItem_thenReturnSingleScanRecord() {
        // Arrange
        deliveryOrder.setDeliveryOrderItems(Collections.singletonList(deliveryOrderItem1));
        List<UUID> itemIds = Collections.singletonList(deliveryOrderItemId1);
        List<ScanRecord> scanRecords = Collections.singletonList(scanRecord1);
        List<ScanRecordDto> expectedDtos = Collections.singletonList(scanRecordDto1);

        when(deliveryOrderRepository.findById(deliveryOrderId)).thenReturn(deliveryOrder);
        when(scanRecordRepository.findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED))
            .thenReturn(scanRecords);
        when(scanRecordDtoApplicationMapper.domainToDtos(scanRecords)).thenReturn(expectedDtos);

        // Act
        List<ScanRecordDto> result = service.findByDeliveryOrderId(deliveryOrderId);

        // Assert
        assertEquals(1, result.size());
        assertEquals(expectedDtos, result);
        verify(deliveryOrderRepository).findById(deliveryOrderId);
        verify(scanRecordRepository).findByEntityIdsAndScanType(itemIds, ScanType.DELIVERY_SCAN_REMOVED);
        verify(scanRecordDtoApplicationMapper).domainToDtos(scanRecords);
    }
}
