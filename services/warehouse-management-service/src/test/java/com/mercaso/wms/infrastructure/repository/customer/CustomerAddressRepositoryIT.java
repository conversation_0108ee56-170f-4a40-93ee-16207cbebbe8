package com.mercaso.wms.infrastructure.repository.customer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class CustomerAddressRepositoryIT extends AbstractIT {

    @Autowired
    private CustomerAddressRepositoryImpl customerAddressRepositoryImpl;

    @Test
    void when_create_customer_address_then_return_customer_address() {
        // given
        CustomerAddress customerAddress = CustomerAddress.builder().build();
        customerAddress.setName(RandomStringUtils.randomAlphabetic(10));
        customerAddress.setAddressOne(RandomStringUtils.randomAlphabetic(10));
        customerAddress.setAddressTwo(RandomStringUtils.randomAlphabetic(10));
        // when
        CustomerAddress savedCustomerAddress = customerAddressRepositoryImpl.save(customerAddress);

        assertNotNull(savedCustomerAddress.getId());

        CustomerAddress result = customerAddressRepositoryImpl.findById(savedCustomerAddress.getId());

        // then
        assertEquals(customerAddress.getName(), result.getName());
        assertEquals(customerAddress.getAddressOne(), result.getAddressOne());
        assertEquals(customerAddress.getAddressTwo(), result.getAddressTwo());

        result.setPostalCode("newZipCode");
        CustomerAddress updated = customerAddressRepositoryImpl.save(result);

        assertEquals("newZipCode", updated.getPostalCode());
    }

}