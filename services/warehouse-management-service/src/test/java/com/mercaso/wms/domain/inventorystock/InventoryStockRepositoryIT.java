package com.mercaso.wms.domain.inventorystock;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.query.InventoryStockQuery;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.InventoryStockJpaDao;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

class InventoryStockRepositoryIT extends AbstractIT {

    @Autowired
    private InventoryStockRepository inventoryStockRepository;
    @Autowired
    private InventoryStockJpaDao inventoryStockJpaDao;

    @Test
    void when_create_inventory_stock_then_return_inventory_stock() {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().getFirst();
        // given
        InventoryStock inventoryStock = InventoryStock.builder()
            .warehouse(warehouses.getFirst())
            .item(Item.builder()
                .skuNumber("skuNumber")
                .title("title")
                .id(UUID.randomUUID())
                .build())
            .location(location)
            .lotNumber("lotNumber")
            .productionDate(LocalDate.now())
            .expirationDate(LocalDate.now())
            .qty(BigDecimal.ONE)
            .reservedQty(BigDecimal.ZERO)
            .availableQty(BigDecimal.ONE)
            .status(InventoryStockStatus.AVAILABLE)
            .lpnNumber("lpnNumber")
            .vendorId(UUID.randomUUID())
            .build();

        // when
        InventoryStock savedInventoryStock = inventoryStockRepository.save(inventoryStock);

        assertNotNull(savedInventoryStock.getId());

        InventoryStock result = inventoryStockRepository.findById(savedInventoryStock.getId());

        // then
        assertEquals(inventoryStock.getWarehouse().getId(), result.getWarehouse().getId());
        assertEquals(inventoryStock.getItem().getId(), result.getItem().getId());
        assertEquals(inventoryStock.getLocation().getId(), result.getLocation().getId());
        assertEquals(inventoryStock.getLotNumber(), result.getLotNumber());
        assertEquals(inventoryStock.getProductionDate(), result.getProductionDate());
        assertEquals(inventoryStock.getExpirationDate(), result.getExpirationDate());
        assertEquals(inventoryStock.getQty().setScale(2, RoundingMode.HALF_UP), result.getQty());
        assertEquals(inventoryStock.getReservedQty().setScale(2, RoundingMode.HALF_UP), result.getReservedQty());
        assertEquals(inventoryStock.getAvailableQty().setScale(2, RoundingMode.HALF_UP), result.getAvailableQty());
        assertEquals(inventoryStock.getStatus(), result.getStatus());
        assertEquals(inventoryStock.getLpnNumber(), result.getLpnNumber());
        assertEquals(inventoryStock.getVendorId(), result.getVendorId());

        result.setQty(BigDecimal.TEN);
        inventoryStockRepository.update(result);
        InventoryStock updateResult = inventoryStockRepository.findById(result.getId());

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_UP), updateResult.getQty());

        inventoryStockJpaDao.deleteById(result.getId());
        InventoryStock deletedResult = inventoryStockRepository.findById(result.getId());

        assertNull(deletedResult);
    }

    @Test
    void when_search_inventory_stock_then_return_inventory_stock() {
        inventoryStockJpaDao.deleteAll();
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().get(1000);
        // given
        InventoryStock inventoryStock = InventoryStock.builder()
            .warehouse(warehouses.getFirst())
            .item(Item.builder()
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .id(UUID.randomUUID())
                .build())
            .location(location)
            .lotNumber(RandomStringUtils.randomAlphabetic(10))
            .productionDate(LocalDate.now().plusDays(20))
            .expirationDate(LocalDate.now().minusDays(20))
            .qty(BigDecimal.ONE)
            .reservedQty(BigDecimal.ZERO)
            .availableQty(BigDecimal.ONE)
            .status(InventoryStockStatus.AVAILABLE)
            .lpnNumber(RandomStringUtils.randomAlphabetic(10))
            .vendorId(UUID.randomUUID())
            .build();

        // when
        InventoryStock savedInventoryStock = inventoryStockRepository.save(inventoryStock);

        assertNotNull(savedInventoryStock.getId());

        InventoryStockQuery query = InventoryStockQuery.builder()
            .locationName(location.getName())
            .build();

        Page<InventoryStock> result = inventoryStockRepository.search(query, PageRequest.of(0, 10));

        // then
        assertEquals(1, result.getTotalElements());
        assertEquals(inventoryStock.getWarehouse().getId(), result.getContent().getFirst().getWarehouse().getId());
        assertEquals(inventoryStock.getItem().getId(), result.getContent().getFirst().getItem().getId());
        assertEquals(inventoryStock.getLocation().getId(), result.getContent().getFirst().getLocation().getId());
        assertEquals(inventoryStock.getLotNumber(), result.getContent().getFirst().getLotNumber());
        assertEquals(inventoryStock.getProductionDate(), result.getContent().getFirst().getProductionDate());
        assertEquals(inventoryStock.getExpirationDate(), result.getContent().getFirst().getExpirationDate());
        assertEquals(inventoryStock.getQty().setScale(2, RoundingMode.HALF_UP), result.getContent().getFirst().getQty());
        assertEquals(inventoryStock.getReservedQty().setScale(2, RoundingMode.HALF_UP),
            result.getContent().getFirst().getReservedQty());
    }

    @Test
    void when_find_inventory_stock_then_return_inventory_stock() {
        inventoryStockJpaDao.deleteAll();
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().get(1000);
        // given
        InventoryStock inventoryStock = InventoryStock.builder()
            .warehouse(warehouses.getFirst())
            .item(Item.builder()
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .id(UUID.randomUUID())
                .build())
            .location(location)
            .lotNumber(RandomStringUtils.randomAlphabetic(10))
            .productionDate(LocalDate.now().plusDays(20))
            .expirationDate(LocalDate.now().minusDays(20))
            .qty(BigDecimal.ONE)
            .reservedQty(BigDecimal.ZERO)
            .availableQty(BigDecimal.ONE)
            .status(InventoryStockStatus.AVAILABLE)
            .lpnNumber(RandomStringUtils.randomAlphabetic(10))
            .vendorId(UUID.randomUUID())
            .build();

        // when
        InventoryStock savedInventoryStock = inventoryStockRepository.save(inventoryStock);

        assertNotNull(savedInventoryStock.getId());

        InventoryStock result = inventoryStockRepository.findBy(inventoryStock.getWarehouse().getId(),
            inventoryStock.getItem().getSkuNumber(), inventoryStock.getLocation().getId(), inventoryStock.getLotNumber(),
            inventoryStock.getExpirationDate());

        // then
        assertEquals(inventoryStock.getWarehouse().getId(), result.getWarehouse().getId());
        assertEquals(inventoryStock.getItem().getId(), result.getItem().getId());
        assertEquals(inventoryStock.getLocation().getId(), result.getLocation().getId());
        assertEquals(inventoryStock.getLotNumber(), result.getLotNumber());
        assertEquals(inventoryStock.getProductionDate(), result.getProductionDate());
        assertEquals(inventoryStock.getExpirationDate(), result.getExpirationDate());
        assertEquals(inventoryStock.getQty().setScale(2, RoundingMode.HALF_UP), result.getQty());
        assertEquals(inventoryStock.getReservedQty().setScale(2, RoundingMode.HALF_UP), result.getReservedQty());
        assertEquals(inventoryStock.getAvailableQty().setScale(2, RoundingMode.HALF_UP), result.getAvailableQty());
        assertEquals(inventoryStock.getStatus(), result.getStatus());
        assertEquals(inventoryStock.getLpnNumber(), result.getLpnNumber());
        assertEquals(inventoryStock.getVendorId(), result.getVendorId());
    }

}