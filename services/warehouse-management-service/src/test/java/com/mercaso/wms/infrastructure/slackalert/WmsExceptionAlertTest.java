package com.mercaso.wms.infrastructure.slackalert;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.service.SlackService;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class WmsExceptionAlertTest {

    @Mock
    private SlackService slackService;

    @InjectMocks
    private WmsExceptionAlert wmsExceptionAlert;

    @BeforeEach
    void setUp() {
        // Set admin portal base URL for testing
        ReflectionTestUtils.setField(wmsExceptionAlert, "wmsAdminPortalBaseUrl", "https://admin.mercaso.com");
    }

    @Test
    void when_sendWmsExceptionAlert_with_ghostInventoryCleanupFailure_then_sendCorrectSlackMessage() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-001";
        String deliveryItemsSummary = "[{sku=SKU001,qty=2},{sku=SKU002,qty=1}]";
        String errorMessage = "Database connection timeout";

        WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        when(slackService.sendWmsExceptionAlert(anyString())).thenReturn(true);

        // When
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(slackService, times(1)).sendWmsExceptionAlert(messageCaptor.capture());

        String slackMessage = messageCaptor.getValue();

        // Verify message structure and content
        assertThat(slackMessage).contains("🟠 *Ghost Inventory Cleanup Failed* 🟠");
        assertThat(slackMessage).contains("📋 *Context Information:*");
        // Verify order link format
        String expectedOrderLink = String.format("<https://admin.mercaso.com/shipping-orders?deliveryDate=&orderNumber=%s|%s>",
            orderNumber,
            orderNumber);
        assertThat(slackMessage).contains(expectedOrderLink);
    }

    @Test
    void when_sendWmsExceptionAlert_with_ghostInventoryCleanupFailureWithNullOrderNumber_then_handleGracefully() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = null;
        String deliveryItemsSummary = "[{sku=SKU001,qty=2}]";
        String errorMessage = "Service unavailable";

        WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        when(slackService.sendWmsExceptionAlert(anyString())).thenReturn(true);

        // When
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(slackService, times(1)).sendWmsExceptionAlert(messageCaptor.capture());

        String slackMessage = messageCaptor.getValue();

        // Verify N/A handling
        assertThat(slackMessage).contains("N/A");
        String expectedOrderLink = "<https://admin.mercaso.com/shipping-orders?deliveryDate=&orderNumber=N/A|N/A>";
        assertThat(slackMessage).contains(expectedOrderLink);
    }

    @Test
    void when_sendWmsExceptionAlert_with_ghostInventoryCleanupFailureWithNoItems_then_showNoItems() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-002";
        String deliveryItemsSummary = "NO_ITEMS";
        String errorMessage = "Connection failed";

        WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        when(slackService.sendWmsExceptionAlert(anyString())).thenReturn(true);

        // When
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(slackService, times(1)).sendWmsExceptionAlert(messageCaptor.capture());

        String slackMessage = messageCaptor.getValue();

        // Verify "No items found" handling
        assertThat(slackMessage).contains("No items found");
    }

    @Test
    void when_sendWmsExceptionAlert_with_nullAlert_then_returnEarly() {
        // Given
        WmsExceptionAlertDto alertDto = null;

        // When
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        verify(slackService, never()).sendWmsExceptionAlert(anyString());
    }

    @Test
    void when_sendWmsExceptionAlert_with_slackServiceFailure_then_handleGracefully() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-20250123-003";
        String deliveryItemsSummary = "[{sku=SKU003,qty=5}]";
        String errorMessage = "Failed to process";

        WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createGhostInventoryCleanupFailureAlert(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        when(slackService.sendWmsExceptionAlert(anyString())).thenReturn(false);

        // When - should not throw exception
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        verify(slackService, times(1)).sendWmsExceptionAlert(anyString());
    }

    @Test
    void when_buildGhostInventoryCleanupContext_with_allFields_then_buildCorrectContext() throws Exception {
        // Given
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("orderNumber", "ORD-20250123-001");
        contextData.put("deliveryOrderId", UUID.randomUUID().toString());
        contextData.put("errorMessage", "Database timeout");
        contextData.put("deliveryItems", "[{sku=SKU001,qty=2}]");
        contextData.put("requiresRecovery", true);
        contextData.put("alertTime", "2025-01-23T14:30:45.123-08:00");

        StringBuilder context = new StringBuilder();

        // When
        Method method = WmsExceptionAlert.class.getDeclaredMethod("buildGhostInventoryCleanupContext",
            StringBuilder.class,
            Map.class);
        method.setAccessible(true);
        method.invoke(wmsExceptionAlert, context, contextData);

        // Then
        String result = context.toString();
        assertThat(result).contains("• *Error:* Database timeout");
        assertThat(result).contains("• *Delivery Items:* [{sku=SKU001,qty=2}]");
        assertThat(result).contains("• *Recovery Status:* ⚠️ Manual data recovery required");
        assertThat(result).contains("• *Alert Time (LA):* 2025-01-23T14:30:45.123-08:00");
    }

    @Test
    void when_buildGhostInventoryCleanupContext_with_noItemsDelivery_then_showNoItemsMessage() throws Exception {
        // Given
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("orderNumber", "ORD-20250123-002");
        contextData.put("deliveryItems", "NO_ITEMS");
        contextData.put("requiresRecovery", true);

        StringBuilder context = new StringBuilder();

        // When
        Method method = WmsExceptionAlert.class.getDeclaredMethod("buildGhostInventoryCleanupContext",
            StringBuilder.class,
            Map.class);
        method.setAccessible(true);
        method.invoke(wmsExceptionAlert, context, contextData);

        // Then
        String result = context.toString();
        assertThat(result).contains("• *Delivery Items:* No items found");
    }

    @Test
    void when_buildGhostInventoryCleanupContext_with_missingFields_then_handleGracefully() throws Exception {
        // Given
        Map<String, Object> contextData = new HashMap<>();
        contextData.put("orderNumber", "ORD-20250123-003");
        // Missing other fields

        StringBuilder context = new StringBuilder();

        // When
        Method method = WmsExceptionAlert.class.getDeclaredMethod("buildGhostInventoryCleanupContext",
            StringBuilder.class,
            Map.class);
        method.setAccessible(true);
        method.invoke(wmsExceptionAlert, context, contextData);

        // Then
        String result = context.toString();
        assertThat(result).contains("• *Shipping Order:*");
        // Should not crash with missing fields
    }

    @Test
    void when_sendWmsExceptionAlert_with_orderAfterBatchAlert_then_useGenericContext() {
        // Given - test that existing functionality still works
        String orderNumber = "ORD-20250123-004";
        UUID orderId = UUID.randomUUID();
        String batchNumber = "BATCH-001";
        UUID batchId = UUID.randomUUID();
        java.time.LocalDate deliveryDate = java.time.LocalDate.of(2025, 1, 23);

        WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createOrderAfterBatchAlert(
            orderNumber, orderId, batchNumber, batchId, deliveryDate);

        when(slackService.sendWmsExceptionAlert(anyString())).thenReturn(true);

        // When
        wmsExceptionAlert.sendWmsExceptionAlert(alertDto);

        // Then
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(slackService, times(1)).sendWmsExceptionAlert(messageCaptor.capture());

        String slackMessage = messageCaptor.getValue();

        // Verify it uses generic context (not ghost inventory specific context)
        assertThat(slackMessage).contains("Order Created After Batch");
        assertThat(slackMessage).contains(orderNumber);
        assertThat(slackMessage).contains(batchNumber);
        assertThat(slackMessage).doesNotContain("Recovery Status");
        assertThat(slackMessage).doesNotContain("Manual data recovery required");
    }
}
