package com.mercaso.wms.infrastructure.repository.batchitems.jpa;


import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.dataobject.BatchItemsDo;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.mapper.BatchItemDoMapper;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

class BatchItemJpaDaoIT extends AbstractIT {

    @Autowired
    private BatchItemsJpaDao batchItemsJpaDao;
    @Autowired
    private BatchJpaDao batchJpaDao;
    @Autowired
    private BatchItemDoMapper mapper;

    @Test
    void when_find_by_batch_id_and_source_and_prep_in_and_breakdown_name_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        List<BatchItemsDo> result = batchItemsJpaDao.findBy(
            batchItems.get(0).getBatchId(),
            batchItems.get(0).getSource(),
            List.of(batchItems.get(0).getDepartment()),
            true
        );

        assertEquals(10, result.size());
        assertEquals(batchItems.get(0).getBatchId(), result.get(0).getBatchId());
        assertEquals(batchItems.get(0).getSource(), result.get(0).getSource());
    }

    @Test
    void when_find_by_batch_id_and_source_and_prep_in_and_small_breakdown_name_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.forEach(batchItem -> batchItem.setBigOrder(false));

        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        List<BatchItemsDo> result = batchItemsJpaDao.findBy(
            batchItems.getFirst().getBatchId(),
            batchItems.getFirst().getSource(),
            List.of(batchItems.getFirst().getDepartment()),
            false
        );

        assertEquals(10, result.size());
        assertEquals(batchItems.getFirst().getBatchId(), result.getFirst().getBatchId());
        assertEquals(batchItems.getFirst().getSource(), result.getFirst().getSource());
    }

    @Test
    void when_find_by_batch_id_and_source_and_prep_not_in_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.forEach(batchItem -> batchItem.setDepartment(BatchConstants.CANDY));

        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        List<BatchItemsDo> result = batchItemsJpaDao.findByBatchIdAndSourceAndDepartmentNotIn(
            batchItems.getFirst().getBatchId(),
            batchItems.getFirst().getSource(),
            List.of(BatchConstants.BEVERAGE)
        );

        assertEquals(10, result.size());
        assertEquals(batchItems.getFirst().getBatchId(), result.getFirst().getBatchId());
        assertEquals(batchItems.getFirst().getSource(), result.getFirst().getSource());
    }

    @Test
    void when_find_by_batch_id_and_source_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.forEach(batchItem -> batchItem.setSource(SourceEnum.DOWNEY.name()));

        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        List<BatchItemsDo> result = batchItemsJpaDao.findByBatchIdAndSource(
            batchItems.get(0).getBatchId(),
            SourceEnum.DOWNEY.name()
        );

        assertEquals(10, result.size());
        assertEquals(batchItems.get(0).getBatchId(), result.get(0).getBatchId());
        assertEquals(SourceEnum.DOWNEY.name(), result.get(0).getSource());
    }

    @Test
    void findOrderNumbersByBatchId_ShouldReturnDistinctOrderNumbers() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));
        // Act
        List<String> orderNumbers = batchItemsJpaDao.findOrderNumbersByBatchId(batchItems.getFirst().getBatchId());

        // Assert
        assertThat(orderNumbers)
            .hasSize(2);
    }

    @Test
    void findOrderNumbersByBatchId_WhenBatchIdNotExists_ShouldReturnEmptyList() {
        // Act
        List<String> orderNumbers = batchItemsJpaDao.findOrderNumbersByBatchId(UUID.randomUUID());

        // Assert
        assertThat(orderNumbers).isEmpty();
    }

    @Test
    void findByBatchIdAndOrderNumberIn_ShouldReturnMatchingItems() {
        // Arrange
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        // Act
        List<BatchItemsDo> items = batchItemsJpaDao.findByBatchIdAndOrderNumberIn(batchItems.getFirst().getBatchId(),
            List.of(batchItems.getFirst().getOrderNumber(), batchItems.getLast().getOrderNumber()));

        // Assert
        assertThat(items)
            .hasSize(2)
            .extracting(BatchItemsDo::getOrderNumber)
            .containsExactlyInAnyOrder(batchItems.getFirst().getOrderNumber(), batchItems.getLast().getOrderNumber());
    }

    @Test
    void findByBatchIdAndOrderNumberIn_WhenNoMatchingItems_ShouldReturnEmptyList() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));
        // Arrange
        List<String> orderNumbers = Arrays.asList("NON-EXISTENT-ORDER");

        // Act
        List<BatchItemsDo> items = batchItemsJpaDao.findByBatchIdAndOrderNumberIn(batchItems.getFirst().getBatchId(),
            orderNumbers);

        // Assert
        assertThat(items).isEmpty();
    }

    @Test
    void findBySourceAndDeliveryDate_ShouldReturnMatchingItems() {
        batchJpaDao.deleteAll();
        batchItemsJpaDao.deleteAll();
        // Arrange
        BatchDo batch = new BatchDo();
        batch.setLastModifiedBy("test");
        batch.setTag("2024-08-20");
        BatchDo savedBatch = batchJpaDao.save(batch);
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItems.forEach(batchItem -> batchItem.setBatchId(savedBatch.getId()));
        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        // Act
        Page<BatchItemsDo> items = batchItemsJpaDao.findBySourceAndDeliveryDate(batchItems.getFirst().getSource(),
            batch.getTag(), Pageable.ofSize(10));

        // Assert
        assertThat(items)
            .hasSize(2)
            .extracting(BatchItemsDo::getSource)
            .containsExactlyInAnyOrder(batchItems.getFirst().getSource(), batchItems.getLast().getSource());
    }

}