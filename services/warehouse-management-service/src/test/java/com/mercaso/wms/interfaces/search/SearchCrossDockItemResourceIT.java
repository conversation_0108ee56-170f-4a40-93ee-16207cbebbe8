package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildCrossTaskItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrders;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.CrossDockTaskResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchCrossDockItemResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskResourceApi crossDockTaskResourceApi;

    @Autowired
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Autowired
    private ShippingOrderRepository shippingOrderRepository;

    @Autowired
    private WarehouseRepository warehouseRepository;

    private List<ShippingOrder> lastPreparedOrders;

    @BeforeEach
    void setUp() {
        crossDockTaskItemRepository.deleteAll();
        pickingTaskRepository.deleteAll();
        shippingOrderRepository.deleteAll();
    }

    @Test
    void searchCrossDockTaskItems_withValidParameters_returnsResult() throws Exception {
        // Create shipping order first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        
        List<CrossDockTaskItem> items = buildCrossTaskItems(5);
        UUID sameTaskItemId = items.get(0).getTaskItemId();
        items.get(1).setTaskItemId(sameTaskItemId);

        // Set shipping order relationships
        items.forEach(item -> {
            item.setShippingOrderId(savedShippingOrder.getId());
            item.setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId());
        });
        
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertNotNull(result.getTotalCount());
        long uniqueTaskItemCount = items.stream().map(CrossDockTaskItem::getTaskItemId).distinct().count();
        assertEquals(uniqueTaskItemCount, result.getData().size());
        assertEquals(uniqueTaskItemCount, result.getTotalCount());
    }

    @Test
    void searchCrossDockTaskItems_filterByStatus_crossed() throws Exception {
        // Create shipping order first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        items.get(0).setCrossDockedQty(1);
        items.get(1).setCrossDockedQty(0);
        items.get(2).setCrossDockedQty(null);

        // Set shipping order relationships
        items.forEach(item -> {
            item.setShippingOrderId(savedShippingOrder.getId());
            item.setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId());
        });
        
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "CROSSED", null, null
        );
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().stream().allMatch(i -> i.getCrossDockedQty() != null && i.getCrossDockedQty() > 0));
    }

    @Test
    void searchCrossDockTaskItems_filterByStatus_notCrossed() throws Exception {
        // Create shipping order first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        items.get(0).setCrossDockedQty(0);
        items.get(1).setCrossDockedQty(null);
        items.get(2).setCrossDockedQty(1);

        // Set shipping order relationships
        items.forEach(item -> {
            item.setShippingOrderId(savedShippingOrder.getId());
            item.setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId());
        });
        
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED", null, null
        );
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().stream().allMatch(i -> i.getCrossDockedQty() == null || i.getCrossDockedQty() == 0));
    }

    @Test
    void searchCrossDockTaskItems_aggregationWithStatusFilter() throws Exception {
        // Create shipping orders first
        List<ShippingOrder> shippingOrders = buildShippingOrders(2, ShippingOrderStatus.IN_PROGRESS);
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);

        for (ShippingOrder shippingOrder : shippingOrders) {
            shippingOrder.setWarehouse(warehouses.getFirst());
        }

        List<ShippingOrder> savedShippingOrders = shippingOrderRepository.saveAll(shippingOrders);
        
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        UUID sameTaskItemId = items.getFirst().getTaskItemId();
        items.forEach(i -> i.setTaskItemId(sameTaskItemId));

        items.getFirst().setPickedQty(1);
        items.getFirst().setCrossDockedQty(1);
        items.getFirst().setSequence(List.of("1/3"));
        items.get(1).setPickedQty(1);
        items.get(1).setCrossDockedQty(1);
        items.get(1).setSequence(List.of("3/3"));
        items.get(1).setCrossDockTaskId(UUID.randomUUID());
        items.get(2).setPickedQty(1);
        items.get(2).setCrossDockedQty(1);
        items.get(2).setSequence(List.of("2/3"));
        items.get(2).setCrossDockTaskId(UUID.randomUUID());

        // Set shipping order relationships for first group
        items.forEach(item -> {
            item.setShippingOrderId(savedShippingOrders.getFirst().getId());
            item.setShippingOrderItemId(savedShippingOrders.getFirst().getShippingOrderItems().getFirst().getId());
        });
        
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> crossedResult = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "CROSSED", null, null
        );
        assertNotNull(crossedResult);
        assertNotNull(crossedResult.getData());
        assertTrue(crossedResult.getData().stream().anyMatch(i -> i.getTaskItemId().equals(sameTaskItemId)));

        Result<SearchCrossDockTaskItemView> notCrossedResult = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED", null, null
        );
        assertNotNull(notCrossedResult);
        assertNotNull(notCrossedResult.getData());
        assertTrue(notCrossedResult.getData().stream().noneMatch(i -> i.getTaskItemId().equals(sameTaskItemId)));

        List<CrossDockTaskItem> notCrossedItems = buildCrossTaskItems(2);
        UUID notCrossedTaskItemId = notCrossedItems.getFirst().getTaskItemId();
        notCrossedItems.forEach(i -> i.setTaskItemId(notCrossedTaskItemId));

        notCrossedItems.get(0).setPickedQty(1);
        notCrossedItems.get(0).setCrossDockedQty(1);
        notCrossedItems.get(1).setPickedQty(1);
        notCrossedItems.get(1).setCrossDockedQty(null);

        // Set shipping order relationships for second group
        notCrossedItems.forEach(item -> {
            item.setShippingOrderId(savedShippingOrders.get(1).getId());
            item.setShippingOrderItemId(savedShippingOrders.get(1).getShippingOrderItems().getFirst().getId());
        });
        
        notCrossedItems.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> notCrossedResult2 = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_CROSSED", null, null
        );
        assertNotNull(notCrossedResult2);
        assertNotNull(notCrossedResult2.getData());
        assertTrue(notCrossedResult2.getData().stream().anyMatch(i -> i.getTaskItemId().equals(notCrossedTaskItemId)));
    }

    @Test
    void searchCrossDockTaskItems_filterByTaskNumber() throws Exception {
        // Create shipping order first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        
        UUID batchId = UUID.randomUUID();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1);
        PickingTask pickingTask = pickingTasks.getFirst();

        PickingTask savedPickingTask = pickingTaskRepository.save(pickingTask);
        PickingTaskItem savedPickingTaskItem = savedPickingTask.getPickingTaskItems().getFirst();

        CrossDockTaskItem crossDockTaskItem = CrossDockTaskItem.builder()
            .pickedQty(1)
            .crossDockedQty(1)
            .batchId(batchId)
            .itemId(UUID.randomUUID())
            .taskItemId(savedPickingTaskItem.getId())
            .shippingOrderId(savedShippingOrder.getId())
            .shippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId())
            .skuNumber("SKU-TEST")
            .title("Test Item")
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .orderNumber("ORDER-123")
            .taskNumber(savedPickingTask.getNumber())
            .source(savedPickingTask.getSource())
            .build();
        crossDockTaskItemRepository.save(crossDockTaskItem);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, savedPickingTask.getNumber(), null
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(crossDockTaskItem.getTaskItemId(), result.getData().getFirst().getTaskItemId());

        Result<SearchCrossDockTaskItemView> resultEmpty = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, "PICK-99999", null
        );

        assertNotNull(resultEmpty);
        assertNotNull(resultEmpty.getData());
        assertTrue(resultEmpty.getData().isEmpty());

        Result<SearchCrossDockTaskItemView> resultAll = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null
        );

        assertNotNull(resultAll);
        assertNotNull(resultAll.getData());
        assertEquals(1, resultAll.getData().size());
    }

    @Test
    void searchCrossDockTaskItems_filterBySource() throws Exception {
        // Create shipping order first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        
        UUID batchId = UUID.randomUUID();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1);
        PickingTask pickingTask = pickingTasks.getFirst();

        PickingTask savedPickingTask = pickingTaskRepository.save(pickingTask);
        PickingTaskItem savedPickingTaskItem = savedPickingTask.getPickingTaskItems().getFirst();

        CrossDockTaskItem crossDockTaskItem = CrossDockTaskItem.builder()
            .pickedQty(1)
            .crossDockedQty(1)
            .batchId(batchId)
            .itemId(UUID.randomUUID())
            .taskItemId(savedPickingTaskItem.getId())
            .shippingOrderId(savedShippingOrder.getId())
            .shippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId())
            .skuNumber("SKU-TEST")
            .title("Test Item")
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .orderNumber("ORDER-123")
            .source(savedPickingTask.getSource())
            .build();
        crossDockTaskItemRepository.save(crossDockTaskItem);

        Result<SearchCrossDockTaskItemView> resultWithSource = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, savedPickingTask.getSource().name()
        );
        assertNotNull(resultWithSource);
        assertNotNull(resultWithSource.getData());
        assertEquals(1, resultWithSource.getData().size());
        assertEquals(crossDockTaskItem.getTaskItemId(), resultWithSource.getData().getFirst().getTaskItemId());
        assertEquals(savedPickingTask.getSource().name(), resultWithSource.getData().getFirst().getSource());

        Result<SearchCrossDockTaskItemView> resultWithWrongSource = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, "COSTCO"
        );
        assertNotNull(resultWithWrongSource);
        assertNotNull(resultWithWrongSource.getData());
        assertTrue(resultWithWrongSource.getData().isEmpty());
    }

    @Test
    void searchCrossDockTaskItems_filters() throws Exception {
        prepareCrossDockData(
            List.of("TRUCK-001", "TRUCK-002", "TRUCK-003"),
            List.of("Alice", "Jane Smith", "Bob"),
            List.of(Boolean.TRUE, Boolean.FALSE, Boolean.TRUE),
            List.of("ORDER-001", "ORDER-002", "ORDER-003")
        );

        // 1) Filter by truck number
        Result<SearchCrossDockTaskItemView> byTruck = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, "TRUCK-001", null, null, null
        );
        assertNotNull(byTruck);
        assertNotNull(byTruck.getData());
        assertEquals(1, byTruck.getData().size());
        assertEquals("TRUCK-001", byTruck.getData().getFirst().getTruckNumber());
        assertEquals("ORDER-001", byTruck.getData().getFirst().getOrderNumber());

        // 2) Filter by driverUserId (exact)
        UUID driverUserId = lastPreparedOrders.stream()
            .filter(o -> "Jane Smith".equals(o.getDriverUserName()))
            .findFirst()
            .map(ShippingOrder::getDriverUserId)
            .orElseThrow();
        Result<SearchCrossDockTaskItemView> byDriver = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, null, null, driverUserId.toString(), null, null
        );
        assertNotNull(byDriver);
        assertNotNull(byDriver.getData());
        assertEquals(1, byDriver.getData().size());
        assertEquals("Jane Smith", byDriver.getData().getFirst().getDriverUserName());
        assertEquals("ORDER-002", byDriver.getData().getFirst().getOrderNumber());

        // 3) Filter by high value item = true
        Result<SearchCrossDockTaskItemView> highValueTrue = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, null, null, true, null
        );
        assertNotNull(highValueTrue);
        assertNotNull(highValueTrue.getData());
        assertEquals(2, highValueTrue.getData().size());
        assertTrue(highValueTrue.getData().stream().allMatch(v -> Boolean.TRUE.equals(v.getHighValueItem())));

        // 4) Combined filter: truck number + high value + sort by driver
        Result<SearchCrossDockTaskItemView> combined = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, "TRUCK-001", null, true, "DRIVER_USER_NAME_ASC"
        );
        assertNotNull(combined);
        assertNotNull(combined.getData());
        assertEquals(1, combined.getData().size());
        assertEquals("TRUCK-001", combined.getData().getFirst().getTruckNumber());
        assertEquals("Alice", combined.getData().getFirst().getDriverUserName());
        assertEquals(Boolean.TRUE, combined.getData().getFirst().getHighValueItem());
        assertEquals("ORDER-001", combined.getData().getFirst().getOrderNumber());
    }

    @Test
    void searchCrossDockTaskItems_sorts() throws Exception {
        // Reuse the same unified dataset as filters test
        prepareCrossDockData(
            List.of("TRUCK-001", "TRUCK-002", "TRUCK-003"),
            List.of("Alice", "Jane Smith", "Bob"),
            List.of(Boolean.TRUE, Boolean.FALSE, Boolean.TRUE),
            List.of("ORDER-001", "ORDER-002", "ORDER-003")
        );

        // Sort by truck number ASC
        Result<SearchCrossDockTaskItemView> byTruckAsc = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, null, null, null, "TRUCK_NUMBER_ASC"
        );
        assertNotNull(byTruckAsc);
        assertNotNull(byTruckAsc.getData());
        assertEquals(3, byTruckAsc.getData().size());
        assertEquals("TRUCK-001", byTruckAsc.getData().get(0).getTruckNumber());
        assertEquals("TRUCK-002", byTruckAsc.getData().get(1).getTruckNumber());
        assertEquals("TRUCK-003", byTruckAsc.getData().get(2).getTruckNumber());

        // Sort by driver ASC
        Result<SearchCrossDockTaskItemView> byDriverAsc = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, null, null, null, "DRIVER_USER_NAME_ASC"
        );
        assertNotNull(byDriverAsc);
        assertNotNull(byDriverAsc.getData());
        assertEquals(3, byDriverAsc.getData().size());
        assertEquals("Alice", byDriverAsc.getData().get(0).getDriverUserName());
        assertEquals("Bob", byDriverAsc.getData().get(1).getDriverUserName());
        assertEquals("Jane Smith", byDriverAsc.getData().get(2).getDriverUserName());
    }

    @Test
    void searchCrossDockTaskItems_filterByStatus_notValidated() throws Exception {
        // Create shipping orders first
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.VALIDATED);
        List<ShippingOrder> shippingOrders2 = buildShippingOrders(1, ShippingOrderStatus.IN_PROGRESS);
        ShippingOrder shippingOrder = shippingOrders.getFirst();
        ShippingOrder shippingOrder2 = shippingOrders2.getFirst();
        shippingOrder.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        shippingOrder2.setWarehouse(warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst());
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrder);
        ShippingOrder savedShippingOrder2 = shippingOrderRepository.save(shippingOrder2);

        // Create test data with items that have different picked vs cross docked quantities
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        UUID sameTaskItemId = items.getFirst().getTaskItemId();
        items.forEach(i -> i.setTaskItemId(sameTaskItemId));

        // Set up items where cross_docked_qty != picked_qty (NOT_VALIDATED condition)
        items.get(0).setPickedQty(3);
        items.get(0).setCrossDockedQty(1); // Different quantities
        items.get(1).setPickedQty(2);
        items.get(1).setCrossDockedQty(2); // Same quantities
        items.get(2).setPickedQty(1);
        items.get(2).setCrossDockedQty(0); // Null cross docked quantity
        items.get(2).setTaskItemId(UUID.randomUUID()); // Null cross docked quantity

        items.getFirst().setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId());
        items.getFirst().setShippingOrderId(savedShippingOrder.getId());
        items.get(1).setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().getFirst().getId());
        items.get(1).setShippingOrderId(savedShippingOrder.getId());
        items.get(2).setShippingOrderItemId(savedShippingOrder2.getShippingOrderItems().getFirst().getId());
        items.get(2).setShippingOrderId(savedShippingOrder2.getId());

        crossDockTaskItemRepository.saveAll(items);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, "NOT_VALIDATED", null, null
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(result.getData().getFirst().getTaskItemId(), items.get(2).getTaskItemId());
    }

    private void prepareCrossDockData(List<String> truckNumbers,
        List<String> drivers,
        List<Boolean> highValueFlags,
        List<String> orderNumbers) {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<ShippingOrder> shippingOrders = buildShippingOrders(truckNumbers.size(), ShippingOrderStatus.IN_PROGRESS);

        for (int i = 0; i < shippingOrders.size(); i++) {
            ShippingOrder order = shippingOrders.get(i);
            order.setTruckNumber(truckNumbers.get(i));
            order.setDriverUserName(drivers.get(i));
            order.setDriverUserId(java.util.UUID.randomUUID());
            order.setWarehouse(warehouses.getFirst());
            if (highValueFlags != null && i < highValueFlags.size()) {
                boolean flag = Boolean.TRUE.equals(highValueFlags.get(i));
                order.getShippingOrderItems().forEach(item -> item.setHighValueItem(flag));
            }
        }

        List<ShippingOrder> savedOrders = shippingOrderRepository.saveAll(shippingOrders);
        lastPreparedOrders = savedOrders;

        List<CrossDockTaskItem> items = buildCrossTaskItems(savedOrders.size());
        for (int i = 0; i < items.size(); i++) {
            items.get(i).setShippingOrderId(savedOrders.get(i).getId());
            items.get(i).setShippingOrderItemId(savedOrders.get(i).getShippingOrderItems().getFirst().getId());
            items.get(i).setOrderNumber(orderNumbers.get(i));
        }
        crossDockTaskItemRepository.saveAll(items);
    }

    @Test
    void searchCrossDockTaskItems_filterByOrderNumber() throws Exception {
        List<CrossDockTaskItem> items = buildCrossTaskItems(3);
        items.get(0).setOrderNumber("ORDER-123");
        items.get(1).setOrderNumber("ORDER-456");
        items.get(2).setOrderNumber("ORDER-123");
        items.forEach(crossDockTaskItemRepository::save);

        Result<SearchCrossDockTaskItemView> result = crossDockTaskResourceApi.searchCrossDockTaskItems(
            null, null, null, null, null, null, null, null, null, null, null, null, "ORDER-123"
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());
        assertTrue(result.getData().stream().allMatch(item -> "ORDER-123".equals(item.getOrderNumber())));
    }
}
