package com.mercaso.wms.application.queryservice;

import static com.mercaso.wms.utils.MockDataUtils.buildAccountPreference;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class AccountPreferenceQueryServiceTest {

    private final AccountPreferenceRepository accountPreferenceRepository = mock(AccountPreferenceRepository.class);

    private final AccountPreferenceQueryService accountPreferenceQueryService = new AccountPreferenceQueryService(
        accountPreferenceRepository);

    @Test
    void when_find_by_id_then_return_account_preference_dto() {
        AccountPreference accountPreference = buildAccountPreference(UUID.randomUUID(), UUID.randomUUID());

        when(accountPreferenceRepository.findById(accountPreference.getId())).thenReturn(accountPreference);

        AccountPreferenceDto accountPreferenceDto = accountPreferenceQueryService.findById(accountPreference.getId());

        assertNotNull(accountPreferenceDto);
    }

}