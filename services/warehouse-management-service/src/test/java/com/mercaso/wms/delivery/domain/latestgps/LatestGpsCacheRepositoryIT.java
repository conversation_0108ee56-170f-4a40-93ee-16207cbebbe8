package com.mercaso.wms.delivery.domain.latestgps;

import static com.mercaso.wms.infrastructure.utils.DateUtils.getNowInLA;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;

class LatestGpsCacheRepositoryIT extends AbstractIT {

    @Autowired
    private LatestGpsCacheRepository latestGpsCacheRepository;

    @Test
    void when_create_update_latest_gps_cache_then_return_latest_gps_cache() {
        Instant nowInLA = getNowInLA();
        GeometryFactory geometryFactory = new GeometryFactory();
        LatestGpsCache latestGpsCacheInvalid = LatestGpsCache.builder()
            .userId(UUID.randomUUID())
            .userName(RandomStringUtils.randomAlphabetic(5))
            .coordinates(geometryFactory.createPoint(new Coordinate(120.123456, 30.123456)))
            .accuracy(BigDecimal.valueOf(10.0))
            .speed(BigDecimal.valueOf(20.0))
            .heading("N")
            .reportAt(nowInLA.minus(2, ChronoUnit.HOURS))
            .build();

        LatestGpsCache result = latestGpsCacheRepository.save(latestGpsCacheInvalid);

        assertNotNull(latestGpsCacheInvalid.getUserId());
        assertEquals(latestGpsCacheInvalid.getUserName(), result.getUserName());
        assertEquals(latestGpsCacheInvalid.getAccuracy(), result.getAccuracy());
        assertEquals(latestGpsCacheInvalid.getSpeed(), result.getSpeed());
        assertEquals(latestGpsCacheInvalid.getHeading(), result.getHeading());
        assertEquals(latestGpsCacheInvalid.getReportAt(), result.getReportAt());

        LatestGpsCache latestGpsCache1 = latestGpsCacheRepository.findById(latestGpsCacheInvalid.getUserId());

        assertNotNull(latestGpsCache1);
        assertEquals(latestGpsCacheInvalid.getUserId(), latestGpsCache1.getUserId());

        Point point = geometryFactory.createPoint(new Coordinate(150.123456, 50.123456));
        latestGpsCache1.setCoordinates(point);
        latestGpsCache1.setHeading("E");

        LatestGpsCache updated = latestGpsCacheRepository.update(latestGpsCache1);

        assertNotNull(updated);
        assertEquals(latestGpsCache1.getUserId(), updated.getUserId());
        assertEquals(latestGpsCache1.getUserName(), updated.getUserName());
        assertEquals(point, updated.getCoordinates());
        assertEquals(latestGpsCache1.getAccuracy(), updated.getAccuracy());
        assertEquals(latestGpsCache1.getSpeed(), updated.getSpeed());
        assertEquals(latestGpsCache1.getHeading(), updated.getHeading());

        LatestGpsCache latestGpsCacheValid = LatestGpsCache.builder()
            .userId(UUID.randomUUID())
            .userName(RandomStringUtils.randomAlphabetic(5))
            .coordinates(geometryFactory.createPoint(new Coordinate(120.123456, 30.123456)))
            .accuracy(BigDecimal.valueOf(10.0))
            .speed(BigDecimal.valueOf(20.0))
            .heading("N")
            .reportAt(nowInLA.minus(30, ChronoUnit.MINUTES))
            .build();

        latestGpsCacheRepository.save(latestGpsCacheValid);

        List<LatestGpsCache> latestGpsCacheList = latestGpsCacheRepository.findAllByRecentHour();

        assert latestGpsCacheList.size() == 1;
    }

}
