package com.mercaso.wms.infrastructure.repository.batch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.time.LocalDate;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class BatchDoRepositoryIT extends AbstractIT {

    @Autowired
    private BatchRepositoryImpl batchRepository;

    @Test
    void when_create_batch_then_return_batch() {
        Batch batch = Batch.builder().build();
        batch.setLastModifiedBy("test");
        batch.setTag("2024-08-20");
        DocumentResponse originalDocumentResponse = new DocumentResponse();
        originalDocumentResponse.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        originalDocumentResponse.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setOriginal(SerializationUtils.toTree(originalDocumentResponse));

        DocumentResponse generated = new DocumentResponse();
        generated.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        generated.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setGenerated(SerializationUtils.toTree(generated));
        batch.setStatus(BatchStatus.IN_PROGRESS);

        Batch saved = batchRepository.save(batch);

        Batch result = batchRepository.findById(saved.getId());

        assertNotNull(result);
    }

    @Test
    void when_update_batch_then_return_batch() {
        Batch batch = Batch.builder().build();
        batch.setLastModifiedBy("test");
        batch.setTag("2024-08-20");
        DocumentResponse originalDocumentResponse = new DocumentResponse();
        originalDocumentResponse.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        originalDocumentResponse.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setOriginal(SerializationUtils.toTree(originalDocumentResponse));

        DocumentResponse generated = new DocumentResponse();
        generated.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        generated.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setGenerated(SerializationUtils.toTree(generated));
        batch.setStatus(BatchStatus.IN_PROGRESS);

        Batch saved = batchRepository.save(batch);

        saved.setStatus(BatchStatus.COMPLETED);
        Batch updated = batchRepository.update(saved);

        Batch result = batchRepository.findById(updated.getId());

        assertNotNull(result);
        assertEquals(BatchStatus.COMPLETED, result.getStatus());
    }

    @Test
    void when_find_batch_by_delivery_date_then_should_successfully() {
        Batch batch = Batch.builder().build();
        batch.setLastModifiedBy("test");
        batch.setTag(LocalDate.now().plusDays(20).toString());
        DocumentResponse originalDocumentResponse = new DocumentResponse();
        originalDocumentResponse.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        originalDocumentResponse.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setOriginal(SerializationUtils.toTree(originalDocumentResponse));

        DocumentResponse generated = new DocumentResponse();
        generated.setName(RandomStringUtils.randomAlphabetic(10).concat(".xlxs"));
        generated.setSignedUrl(RandomStringUtils.randomAlphabetic(10));
        batch.setGenerated(SerializationUtils.toTree(generated));
        batch.setStatus(BatchStatus.IN_PROGRESS);

        Batch saved = batchRepository.save(batch);

        List<Batch> batchList = batchRepository.findByTag(saved.getTag());

        assertNotNull(batchList);
    }

}