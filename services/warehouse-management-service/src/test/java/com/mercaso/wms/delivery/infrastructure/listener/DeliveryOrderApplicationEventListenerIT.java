package com.mercaso.wms.delivery.infrastructure.listener;


import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderDeliveredCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.DeliveryOrderUnloadCommand;
import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderItemCommand;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderResourceApi;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DeliveryOrderApplicationEventListenerIT extends AbstractIT {

    @Autowired
    DeliveryOrderResourceApi deliveryOrderResourceApi;
    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;
    @Autowired
    DeliveryTaskJpaDao deliveryTaskJpaDao;
    @Autowired
    DeliveryDocumentRepository deliveryDocumentRepository;

    @Test
    void when_delivery_order_delivered_then_generate_invoice() throws Exception {
        deliveryOrderJpaDao.deleteAll();

        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(documentOperations.uploadDocument(any())).thenReturn(new DocumentResponse(
            "path/to/invoice.pdf",
            "invoice.pdf"));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", LocalDate.now(), "TRUCK-001", "Driver1");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.save(taskDo);
        deliveryOrderDo.setDeliveryTaskId(savedTask.getId());
        deliveryOrderDo.setStatus(DeliveryOrderStatus.ARRIVED);
        deliveryOrderJpaDao.save(deliveryOrderDo);

        List<UpdateDeliveryOrderItemCommand> updateDeliveryOrderItemDtos = Lists.newArrayList();
        deliveryOrderDo.getDeliveryOrderItems().forEach(item -> {
            updateDeliveryOrderItemDtos.add(UpdateDeliveryOrderItemCommand.builder()
                .id(item.getId())
                .deliveredQty(item.getQty())
                .build());
        });
        DeliveryOrderUnloadCommand command = DeliveryOrderUnloadCommand.builder()
            .updateDeliveryOrderItemDtos(updateDeliveryOrderItemDtos)
            .build();

        DeliveryOrderDto updated = deliveryOrderResourceApi.updateToUnloaded(deliveryOrderDo.getId(), command);

        assertNotNull(updated);
        assertNotNull(updated.getUnloadedAt());
        updated.getDeliveryOrderItems().forEach(item -> {
            assertEquals(item.getQty(), item.getDeliveredQty());
        });

        DeliveryOrderDto updatedToDelivered = deliveryOrderResourceApi.updateToDelivered(deliveryOrderDo.getId(),
            DeliveryOrderDeliveredCommand.builder()
                .paymentType(List.of(PaymentType.CASH, PaymentType.OTHER))
                .notes(RandomStringUtils.randomAlphabetic(10))
                .build());

        assertEquals(DeliveryOrderStatus.DELIVERED, updatedToDelivered.getStatus());
        assertNotNull(updatedToDelivered.getDeliveredAt());
        assertNotNull(updatedToDelivered.getNotes());

        assert updatedToDelivered.getPaymentType().contains(PaymentType.CASH);
        assert updatedToDelivered.getPaymentType().contains(PaymentType.OTHER);

        List<DeliveryDocument> deliveryOrderDoc = deliveryDocumentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            updatedToDelivered.getId(),
            "DELIVERY_ORDER",
            List.of(DeliveryDocumentType.INVOICE));
        assertNotNull(deliveryOrderDoc);
    }
}