package com.mercaso.wms.infrastructure.repository.location;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;

class LocationRepositoryImplIT extends AbstractIT {

    @Autowired
    private LocationRepositoryImpl locationRepository;


    @Test
    void when_create_location_then_return_location() {
        // given
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = Location.builder()
            .type(LocationType.BIN)
            .warehouse(warehouses.get(0))
            .aisleNumber("01")
            .bayNumber("01")
            .name(RandomStringUtils.randomAlphabetic(10))
            .build();

        // when
        Location saved = locationRepository.save(location);

        // then
        assertNotNull(saved);

        Location result = locationRepository.findById(saved.getId());

        assertNotNull(result);
        assertEquals(location.getName(), result.getName());
    }

    @Test
    void when_update_location_then_return_location() {
        // given
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = Location.builder()
            .type(LocationType.BIN)
            .warehouse(warehouses.get(0))
            .aisleNumber("01")
            .bayNumber("01")
            .name(
                RandomStringUtils.randomAlphabetic(10))
            .build();

        // when
        Location saved = locationRepository.save(location);

        // then
        assertNotNull(saved);

        Location result = locationRepository.findById(saved.getId());

        assertNotNull(result);
        assertEquals(location.getName(), result.getName());

        // when
        result = result.updateLocation(RandomStringUtils.randomAlphabetic(10), LocationType.BIN, "01", "01");
        Location updated = locationRepository.update(result);

        // then
        assertNotNull(updated);
        assertEquals(result.getName(), updated.getName());
    }

    @Test
    void when_search_location_then_return_location() {
        // given
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = Location.builder()
            .type(LocationType.BIN)
            .warehouse(warehouses.getFirst())
            .aisleNumber("01")
            .bayNumber("01")
            .name(RandomStringUtils.randomAlphabetic(10))
            .build();

        // when
        Location saved = locationRepository.save(location);

        // then
        assertNotNull(saved);

        List<Location> result = locationRepository.findBy(location.getName(), Pageable.ofSize(10)).getContent();

        assertNotNull(result);
        assertEquals(1, result.size());
    }

}