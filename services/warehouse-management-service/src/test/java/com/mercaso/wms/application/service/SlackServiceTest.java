package com.mercaso.wms.application.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.infrastructure.config.SlackConfig;
import com.mercaso.wms.infrastructure.external.slack.WmsSlackAdaptor;
import com.mercaso.wms.infrastructure.external.slack.dto.SlackMessageDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SlackServiceTest {

    @Mock
    private WmsSlackAdaptor wmsSlackAdaptor;

    @Mock
    private SlackConfig slackConfig;

    @InjectMocks
    private SlackService slackService;

    private static final String TEST_MESSAGE = "Test message";
    private static final String TEST_WEBHOOK = "https://hooks.slack.com/test";



    @Test
    void when_sendWmsExceptionAlert_and_enabled_then_sendMessage() {
        // Given
        when(slackConfig.getWmsExceptionWebhook()).thenReturn(TEST_WEBHOOK);
        when(wmsSlackAdaptor.sendMessage(any(String.class), any(SlackMessageDto.class))).thenReturn(true);

        // When
        boolean result = slackService.sendWmsExceptionAlert(TEST_MESSAGE);

        // Then
        assertThat(result).isTrue();

        ArgumentCaptor<SlackMessageDto> messageCaptor = ArgumentCaptor.forClass(SlackMessageDto.class);
        verify(wmsSlackAdaptor, times(1)).sendMessage(eq(TEST_WEBHOOK), messageCaptor.capture());

        SlackMessageDto capturedMessage = messageCaptor.getValue();
        assertThat(capturedMessage.getText()).isEqualTo(TEST_MESSAGE);
        assertThat(capturedMessage.getUsername()).isEqualTo("WMS Alert Exception Bot");
        assertThat(capturedMessage.getIconEmoji()).isEqualTo(":exclamation:");
    }

    @Test
    void when_sendCustomMessage_then_sendWithCustomFormatting() {
        // Given
        String customBotName = "Custom Bot";
        String customIcon = ":robot_face:";
        when(wmsSlackAdaptor.sendMessage(any(String.class), any(SlackMessageDto.class))).thenReturn(true);

        // When
        boolean result = slackService.sendCustomMessage(TEST_WEBHOOK, TEST_MESSAGE, customBotName, customIcon);

        // Then
        assertThat(result).isTrue();

        ArgumentCaptor<SlackMessageDto> messageCaptor = ArgumentCaptor.forClass(SlackMessageDto.class);
        verify(wmsSlackAdaptor, times(1)).sendMessage(eq(TEST_WEBHOOK), messageCaptor.capture());

        SlackMessageDto capturedMessage = messageCaptor.getValue();
        assertThat(capturedMessage.getText()).isEqualTo(TEST_MESSAGE);
        assertThat(capturedMessage.getUsername()).isEqualTo(customBotName);
        assertThat(capturedMessage.getIconEmoji()).isEqualTo(customIcon);
    }

    @Test
    void when_sendSimpleMessage_then_sendWithoutFormatting() {
        // Given
        when(wmsSlackAdaptor.sendMessage(TEST_WEBHOOK, TEST_MESSAGE)).thenReturn(true);

        // When
        boolean result = slackService.sendSimpleMessage(TEST_WEBHOOK, TEST_MESSAGE);

        // Then
        assertThat(result).isTrue();
        verify(wmsSlackAdaptor, times(1)).sendMessage(TEST_WEBHOOK, TEST_MESSAGE);
    }

    @Test
    void when_sendCriticalAlert_then_sendWithRetry() {
        // Given
        int retryCount = 2;
        when(wmsSlackAdaptor.sendMessageWithRetry(any(String.class),
            any(SlackMessageDto.class),
            eq(retryCount))).thenReturn(true);

        // When
        boolean result = slackService.sendCriticalAlert(TEST_WEBHOOK, TEST_MESSAGE, retryCount);

        // Then
        assertThat(result).isTrue();

        ArgumentCaptor<SlackMessageDto> messageCaptor = ArgumentCaptor.forClass(SlackMessageDto.class);
        verify(wmsSlackAdaptor, times(1)).sendMessageWithRetry(eq(TEST_WEBHOOK), messageCaptor.capture(), eq(retryCount));

        SlackMessageDto capturedMessage = messageCaptor.getValue();
        assertThat(capturedMessage.getText()).isEqualTo(TEST_MESSAGE);
        assertThat(capturedMessage.getUsername()).isEqualTo("Critical Alert Bot");
        assertThat(capturedMessage.getIconEmoji()).isEqualTo(":fire:");
    }
} 