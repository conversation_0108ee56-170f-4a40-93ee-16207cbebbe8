package com.mercaso.wms.infrastructure.repository.batch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.repository.batch.jpa.mapper.BatchDoMapper;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BatchRepositoryImplTest {

    @Mock
    private BatchJpaDao jpaDao;

    @Mock
    private BatchDoMapper mapper;

    @InjectMocks
    private BatchRepositoryImpl batchRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void findByDeliveryDateRange_Should_Call_JpaDao_With_Correct_DateStrings() {
        LocalDate startDate = LocalDate.of(2024, 9, 2);
        LocalDate endDate = LocalDate.of(2024, 9, 8);
        String expectedStartDateStr = "2024-09-02";
        String expectedEndDateStr = "2024-09-08";

        BatchDo batchDo = new BatchDo();
        batchDo.setId(UUID.randomUUID());
        List<BatchDo> batchDoList = List.of(batchDo);

        Batch batch = Batch.builder().id(batchDo.getId()).build();
        List<Batch> expectedBatches = List.of(batch);

        when(jpaDao.findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(batchDoList);
        when(mapper.doToDomains(batchDoList)).thenReturn(expectedBatches);

        List<Batch> result = batchRepository.findByDeliveryDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(batch.getId(), result.get(0).getId());
        verify(jpaDao, times(1)).findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr);
        verify(mapper, times(1)).doToDomains(batchDoList);
    }

    @Test
    void findByDeliveryDateRange_Should_Return_Empty_List_When_No_Batches_Found() {
        LocalDate startDate = LocalDate.of(2024, 9, 2);
        LocalDate endDate = LocalDate.of(2024, 9, 8);
        String expectedStartDateStr = "2024-09-02";
        String expectedEndDateStr = "2024-09-08";

        when(jpaDao.findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(Collections.emptyList());
        when(mapper.doToDomains(Collections.emptyList())).thenReturn(Collections.emptyList());

        List<Batch> result = batchRepository.findByDeliveryDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(0, result.size());
        verify(jpaDao, times(1)).findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr);
        verify(mapper, times(1)).doToDomains(Collections.emptyList());
    }

    @Test
    void findByDeliveryDateRange_Should_Handle_Same_Start_And_End_Date() {
        LocalDate sameDate = LocalDate.of(2024, 9, 5);
        String expectedDateStr = "2024-09-05";

        BatchDo batchDo = new BatchDo();
        batchDo.setId(UUID.randomUUID());
        List<BatchDo> batchDoList = List.of(batchDo);

        Batch batch = Batch.builder().id(batchDo.getId()).build();
        List<Batch> expectedBatches = List.of(batch);

        when(jpaDao.findByDeliveryDateRange(expectedDateStr, expectedDateStr)).thenReturn(batchDoList);
        when(mapper.doToDomains(batchDoList)).thenReturn(expectedBatches);

        List<Batch> result = batchRepository.findByDeliveryDateRange(sameDate, sameDate);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jpaDao, times(1)).findByDeliveryDateRange(expectedDateStr, expectedDateStr);
    }

    @Test
    void findUntransferredBatchesByDateRange_Should_Call_JpaDao_With_Correct_DateStrings() {
        LocalDate startDate = LocalDate.of(2024, 9, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 10);
        String expectedStartDateStr = "2024-09-01";
        String expectedEndDateStr = "2024-09-10";

        BatchDo batchDo1 = new BatchDo();
        batchDo1.setId(UUID.randomUUID());
        BatchDo batchDo2 = new BatchDo();
        batchDo2.setId(UUID.randomUUID());
        List<BatchDo> batchDoList = Arrays.asList(batchDo1, batchDo2);

        Batch batch1 = Batch.builder().id(batchDo1.getId()).build();
        Batch batch2 = Batch.builder().id(batchDo2.getId()).build();
        List<Batch> expectedBatches = Arrays.asList(batch1, batch2);

        when(jpaDao.findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(batchDoList);
        when(mapper.doToDomains(batchDoList)).thenReturn(expectedBatches);

        List<Batch> result = batchRepository.findUntransferredBatchesByDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(batch1.getId(), result.get(0).getId());
        assertEquals(batch2.getId(), result.get(1).getId());
        verify(jpaDao, times(1)).findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr);
        verify(mapper, times(1)).doToDomains(batchDoList);
    }

    @Test
    void findUntransferredBatchesByDateRange_Should_Return_Empty_List_When_No_Untransferred_Batches() {
        LocalDate startDate = LocalDate.of(2024, 9, 1);
        LocalDate endDate = LocalDate.of(2024, 9, 10);
        String expectedStartDateStr = "2024-09-01";
        String expectedEndDateStr = "2024-09-10";

        when(jpaDao.findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(Collections.emptyList());
        when(mapper.doToDomains(Collections.emptyList())).thenReturn(Collections.emptyList());

        List<Batch> result = batchRepository.findUntransferredBatchesByDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(0, result.size());
        verify(jpaDao, times(1)).findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr);
        verify(mapper, times(1)).doToDomains(Collections.emptyList());
    }

    @Test
    void findByDeliveryDateRange_Should_Handle_Cross_Month_Date_Range() {
        LocalDate startDate = LocalDate.of(2024, 8, 30);
        LocalDate endDate = LocalDate.of(2024, 9, 3);
        String expectedStartDateStr = "2024-08-30";
        String expectedEndDateStr = "2024-09-03";

        BatchDo batchDo = new BatchDo();
        batchDo.setId(UUID.randomUUID());
        List<BatchDo> batchDoList = List.of(batchDo);

        Batch batch = Batch.builder().id(batchDo.getId()).build();
        List<Batch> expectedBatches = List.of(batch);

        when(jpaDao.findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(batchDoList);
        when(mapper.doToDomains(batchDoList)).thenReturn(expectedBatches);

        List<Batch> result = batchRepository.findByDeliveryDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jpaDao, times(1)).findByDeliveryDateRange(expectedStartDateStr, expectedEndDateStr);
    }

    @Test
    void findUntransferredBatchesByDateRange_Should_Handle_Cross_Year_Date_Range() {
        LocalDate startDate = LocalDate.of(2023, 12, 30);
        LocalDate endDate = LocalDate.of(2024, 1, 5);
        String expectedStartDateStr = "2023-12-30";
        String expectedEndDateStr = "2024-01-05";

        BatchDo batchDo = new BatchDo();
        batchDo.setId(UUID.randomUUID());
        List<BatchDo> batchDoList = List.of(batchDo);

        Batch batch = Batch.builder().id(batchDo.getId()).build();
        List<Batch> expectedBatches = List.of(batch);

        when(jpaDao.findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr)).thenReturn(batchDoList);
        when(mapper.doToDomains(batchDoList)).thenReturn(expectedBatches);

        List<Batch> result = batchRepository.findUntransferredBatchesByDateRange(startDate, endDate);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jpaDao, times(1)).findUntransferredBatchesByDateRange(expectedStartDateStr, expectedEndDateStr);
    }
}
