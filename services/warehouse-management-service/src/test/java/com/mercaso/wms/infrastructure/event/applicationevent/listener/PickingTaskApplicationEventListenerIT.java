package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import static com.mercaso.wms.builder.DataBuilder.buildLocation;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrders;
import static com.mercaso.wms.utils.MockDataUtils.createBatch;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedApplicationEvent;
import com.mercaso.wms.application.dto.event.PickingTaskCompletedPayloadDto;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedApplicationEvent;
import com.mercaso.wms.application.dto.event.ReceivingTaskReceivedPayloadDto;
import com.mercaso.wms.application.mapper.pickingtask.PickingTaskDtoApplicationMapper;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskDtoApplicationMapper;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PickingTaskApplicationEventListenerIT extends AbstractIT {

    @Autowired
    private BatchRepository batchRepository;
    @Autowired
    private BatchItemRepository batchItemRepository;
    @Autowired
    private PickingTaskApplicationEventListener listener;
    @Autowired
    private PickingTaskRepository pickingTaskRepository;
    @Autowired
    private BatchJpaDao batchJpaDao;
    @Autowired
    private PickingTaskDtoApplicationMapper pickingTaskDtoApplicationMapper;
    @Autowired
    private ReceivingTaskDtoApplicationMapper receivingTaskDtoApplicationMapper;

    @Test
    void when_batch_created_then_should_create_picking_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 10);
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.MDC, pt.getSource()));

        Batch retrieve = batchRepository.findById(saved.getId());

        assertNotNull(retrieve);
        assertNotNull(retrieve.getNumber());
    }

    @Test
    void when_batch_created_then_should_create_downey_picking_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> bi.setSource("DOWNEY"));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 5);
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(5, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.DOWNEY, pt.getSource()));
    }

    @Test
    void when_batch_created_then_should_create_NA_picking_task_separated() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> bi.setLocationName("N/A"));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 1);
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(1, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.MDC, pt.getSource()));
    }

    @Test
    void when_batch_created_then_should_create_Costco_picking_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> bi.setSource("COSTCO"));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 5);
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(5, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.COSTCO, pt.getSource()));
    }

    @Test
    void when_order_level_picking_task_completed_then_should_update_order_status() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        shippingOrderRepository.deleteAll();

        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.OPEN);

        Batch saved = batchRepository.save(createBatch());
        shippingOrders.forEach(so -> {
            so.setStatus(ShippingOrderStatus.IN_PROGRESS);
            so.setWarehouse(warehouses.getFirst());
            so.setBatchId(saved.getId());
        });
        ShippingOrder savedShippingOrder = shippingOrderRepository.save(shippingOrders.getFirst());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(), 1, PickingTaskType.ORDER);

        pickingTasks.forEach(pt -> {
            pt.setStatus(PickingTaskStatus.COMPLETED);
            for (int i = 0; i < pt.getPickingTaskItems().size(); i++) {
                pt.getPickingTaskItems().get(i).setShippingOrderId(savedShippingOrder.getId());
                pt.getPickingTaskItems().get(i).setShippingOrderItemId(savedShippingOrder.getShippingOrderItems().get(i).getId());
                pt.getPickingTaskItems().get(i).setPickedQty(pt.getPickingTaskItems().get(i).getExpectQty());
            }
        });
        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        PickingTaskCompletedPayloadDto payload = PickingTaskCompletedPayloadDto.builder()
            .pickingTaskId(result.getFirst().getId())
            .data(pickingTaskDtoApplicationMapper.domainToDto(result.getFirst()))
            .build();
        listener.handlePickingTaskCompletedEvent(new PickingTaskCompletedApplicationEvent(payload, payload));
        waitForShippingOrderUpdate(savedShippingOrder.getId(), ShippingOrderStatus.PICKED);
        List<ShippingOrder> byBatchId = shippingOrderRepository.findByBatchId(saved.getId());
        assertEquals(1, byBatchId.size());
        byBatchId.forEach(so -> {
            assertEquals(ShippingOrderStatus.PICKED, so.getStatus());
            so.getShippingOrderItems().forEach(soi -> assertEquals(soi.getQty(), soi.getPickedQty()));
        });
    }

    @Test
    void when_batch_level_picking_task_completed_then_should_update_order_status() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        shippingOrderRepository.deleteAll();
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = buildLocation("MFC-Stage", LocationType.STAGING_AREA);
        location.setWarehouse(warehouses.getFirst());
        try {
            locationRepository.save(location);
        } catch (Exception ignored) {
        }
        List<Location> locations = locationRepository.findAll();

        List<ShippingOrder> shippingOrders = buildShippingOrders(2, ShippingOrderStatus.OPEN);

        Batch saved = batchRepository.save(createBatch());
        shippingOrders.forEach(so -> {
            so.setStatus(ShippingOrderStatus.IN_PROGRESS);
            so.setWarehouse(warehouses.getFirst());
            so.setBatchId(saved.getId());
        });
        List<ShippingOrder> savedShippingOrders = shippingOrderRepository.saveAll(shippingOrders);

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(), 1, PickingTaskType.BATCH);

        pickingTasks.forEach(pt -> {
            pt.setStatus(PickingTaskStatus.COMPLETED);
            for (int i = 0; i < pt.getPickingTaskItems().size(); i++) {
                pt.getPickingTaskItems().get(i).setShippingOrderId(savedShippingOrders.get(i).getId());
                pt.getPickingTaskItems()
                    .get(i)
                    .setShippingOrderItemId(savedShippingOrders.get(i).getShippingOrderItems().get(i).getId());
                pt.getPickingTaskItems().get(i).setPickedQty(pt.getPickingTaskItems().get(i).getExpectQty());
                pt.getPickingTaskItems().get(i).setLocationName(locations.get(i).getName());
                pt.getPickingTaskItems().get(i).setLocationId(locations.get(i).getId());
            }
        });
        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_FINALE_TRANSFER_FEATURE)).thenReturn(Boolean.TRUE);

        PickingTaskCompletedPayloadDto payload = PickingTaskCompletedPayloadDto.builder()
            .pickingTaskId(result.getFirst().getId())
            .data(pickingTaskDtoApplicationMapper.domainToDto(result.getFirst()))
            .build();

        listener.handlePickingTaskCompletedEvent(new PickingTaskCompletedApplicationEvent(payload, payload));
        List<ShippingOrder> byBatchId = shippingOrderRepository.findByBatchId(saved.getId());
        assertEquals(2, byBatchId.size());
        byBatchId.forEach(so -> assertEquals(ShippingOrderStatus.IN_PROGRESS, so.getStatus()));
    }

    @Test
    void when_picking_task_picked_then_should_update_order_status() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        shippingOrderRepository.deleteAll();

        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<ShippingOrder> shippingOrders = buildShippingOrders(10, ShippingOrderStatus.OPEN);

        Batch saved = batchRepository.save(createBatch());
        shippingOrders.forEach(so -> {
            so.setWarehouse(warehouses.getFirst());
            so.setBatchId(saved.getId());
        });
        shippingOrderRepository.saveAll(shippingOrders);

        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);
        listener.handleBatchCreatedEvent(saved.getId());

        List<ShippingOrder> byBatchId = shippingOrderRepository.findByBatchId(saved.getId());
        assertEquals(10, byBatchId.size());
        byBatchId.forEach(so -> assertEquals(ShippingOrderStatus.IN_PROGRESS, so.getStatus()));
    }

    @Test
    void when_batch_created_then_should_create_vernon_receiving_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> bi.setSource("VERNON"));
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForReceivingTask(saved.getId(), 1);
        List<ReceivingTask> receivingTasks = receivingTaskRepository.findByBatchId(saved.getId());

        assertNotNull(receivingTasks);
        assertEquals(SourceEnum.VERNON.name(), receivingTasks.getFirst().getVendor());
    }

    @Test
    void when_receiving_task_received_then_should_update_order_status() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        shippingOrderRepository.deleteAll();

        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        List<ShippingOrder> shippingOrders = buildShippingOrders(1, ShippingOrderStatus.OPEN);

        Batch saved = batchRepository.save(createBatch());
        shippingOrders.forEach(so -> {
            so.setStatus(ShippingOrderStatus.IN_PROGRESS);
            so.setWarehouse(warehouses.getFirst());
            so.setBatchId(saved.getId());
        });
        List<ShippingOrder> shippingOrders1 = shippingOrderRepository.saveAll(shippingOrders);

        List<ReceivingTask> receivingTasks = buildReceivingTask(saved.getId(),
            1,
            SourceEnum.VERNON,
            ReceivingTaskStatus.RECEIVED,
            ReceivingTaskType.ONLINE_RECEIVING);

        receivingTasks.getFirst().getReceivingTaskItems().getFirst().setShippingOrderId(shippingOrders1.getFirst().getId());
        receivingTasks.getFirst().getReceivingTaskItems().getLast().setShippingOrderId(shippingOrders1.getFirst().getId());
        receivingTasks.getFirst()
            .getReceivingTaskItems()
            .getFirst()
            .setShippingOrderItemId(shippingOrders1.getFirst().getShippingOrderItems().getFirst().getId());
        receivingTasks.getFirst()
            .getReceivingTaskItems()
            .getLast()
            .setShippingOrderItemId(shippingOrders1.getFirst().getShippingOrderItems().getLast().getId());
        receivingTasks.getFirst().getReceivingTaskItems().forEach(rti -> {
            rti.setExpectQty(shippingOrders1.getFirst().getShippingOrderItems().getFirst().getQty());
            rti.setReceivedQty(shippingOrders1.getFirst().getShippingOrderItems().getFirst().getQty());
        });

        List<ReceivingTask> result = receivingTaskRepository.saveAll(receivingTasks);

        ReceivingTaskReceivedPayloadDto receivingTaskReceivedPayloadDto = ReceivingTaskReceivedPayloadDto.builder()
            .receivingTaskId(result.getFirst().getId())
            .data(receivingTaskDtoApplicationMapper.domainToDto(result.getFirst())).build();
        listener.handleReceivingTaskReceivedEvent(new ReceivingTaskReceivedApplicationEvent(receivingTaskReceivedPayloadDto,
            receivingTaskReceivedPayloadDto));
        waitForShippingOrderUpdate(shippingOrders1.getFirst().getId(), ShippingOrderStatus.PICKED);
        List<ShippingOrder> byBatchId = shippingOrderRepository.findByBatchId(saved.getId());
        assertEquals(1, byBatchId.size());
        byBatchId.forEach(so -> assertEquals(ShippingOrderStatus.PICKED, so.getStatus()));
    }

    @Test
    void when_batch_created_then_should_create_photo_studio_picking_task_separated() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> bi.setLocationName("PHOTO-STUDIO"));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 1);
        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(1, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.MDC, pt.getSource()));
    }

    @Test
    void when_batch_created_then_should_create_mfc_picking_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItems.forEach(bi -> {
            bi.setSource("MFC");
            bi.setBigOrder(false);
            bi.setLocationName("MFC-A1-01");
            bi.setDepartment("Beverage");
            bi.setExpectQty(8);
        });
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTasksWithTotalItems(saved.getId(), 10);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create at least one task");

        pickingTasks.forEach(pt -> assertEquals(SourceEnum.MFC, pt.getSource()));

        int totalItems = pickingTasks.stream()
            .mapToInt(pt -> pt.getPickingTaskItems().size())
            .sum();
        assertEquals(10, totalItems, "All MFC items should be included in tasks");
    }

    @Test
    void when_batch_created_then_should_create_jetro_picking_task() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        for (int i = 0; i < batchItems.size(); i++) {
            batchItems.get(i).setSource("JETRO");
            batchItems.get(i).setBigOrder(false);
            batchItems.get(i).setLocationName(String.valueOf(100 + (i / 2)));
            batchItems.get(i).setExpectQty(10);
        }
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTasksWithTotalItems(saved.getId(), 10);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create at least one task");

        pickingTasks.forEach(pt -> assertEquals(SourceEnum.JETRO, pt.getSource()));

        // Verify total item count is correct
        int totalItems = pickingTasks.stream()
            .mapToInt(pt -> pt.getPickingTaskItems().size())
            .sum();
        assertEquals(10, totalItems, "All JETRO items should be included in tasks");
    }

    @Test
    void when_batch_created_then_should_create_cooler_picking_task_with_correct_source() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 6);

        // 3 MDC cooler items
        for (int i = 0; i < 3; i++) {
            batchItems.get(i).setLocationName(".COOLER");
            batchItems.get(i).setSource("MDC");
            batchItems.get(i).setBigOrder(false);
            batchItems.get(i).setExpectQty(10);
        }
        // 3 MFC items in regular location (not cooler) to ensure they get processed
        for (int i = 3; i < 6; i++) {
            batchItems.get(i).setLocationName("MFC-A1-01");
            batchItems.get(i).setSource("MFC");
            batchItems.get(i).setBigOrder(false);
            batchItems.get(i).setExpectQty(10);
            batchItems.get(i).setDepartment("Beverage");
        }

        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());

        waitForPickingTasksWithTotalItems(saved.getId(), 6);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create at least one task");

        boolean hasMdcTasks = pickingTasks.stream()
            .anyMatch(pt -> pt.getSource() == SourceEnum.MDC);
        boolean hasMfcTasks = pickingTasks.stream()
            .anyMatch(pt -> pt.getSource() == SourceEnum.MFC);
        assertTrue(hasMdcTasks, "Should have MDC tasks for cooler items");
        assertTrue(hasMfcTasks, "Should have MFC tasks for regular items");

        boolean hasCoolerTasks = pickingTasks.stream()
            .flatMap(pt -> pt.getPickingTaskItems().stream())
            .anyMatch(pti -> ".COOLER".equals(pti.getLocationName()));
        assertTrue(hasCoolerTasks, "Should have at least one task for cooler location");
    }

    @Test
    void when_batch_has_mixed_sources_then_mdc_should_execute_first() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);

        // Create mixed sources: 5 MDC with special location, 5 MFC regular
        for (int i = 0; i < 5; i++) {
            batchItems.get(i).setSource("MDC");
            batchItems.get(i).setLocationName("PHOTO-STUDIO");
            batchItems.get(i).setBigOrder(false);
            batchItems.get(i).setExpectQty(10);
        }
        for (int i = 5; i < 10; i++) {
            batchItems.get(i).setSource("MFC");
            batchItems.get(i).setBigOrder(false);
            batchItems.get(i).setLocationName("MFC-A1-01");
            batchItems.get(i).setDepartment("Beverage");
            batchItems.get(i).setExpectQty(8); // 5*8=40, but will be split based on business logic
        }
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTasksWithTotalItems(saved.getId(), 10);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create some tasks for mixed sources");

        boolean hasMdcTasks = pickingTasks.stream().anyMatch(pt -> pt.getSource() == SourceEnum.MDC);
        boolean hasMfcTasks = pickingTasks.stream().anyMatch(pt -> pt.getSource() == SourceEnum.MFC);
        assertTrue(hasMdcTasks || hasMfcTasks, "Should have tasks from at least one source");

        int totalItems = pickingTasks.stream()
            .mapToInt(pt -> pt.getPickingTaskItems().size())
            .sum();
        assertEquals(10, totalItems, "All items should be included in tasks");

        boolean hasPhotoStudioTask = pickingTasks.stream()
            .flatMap(pt -> pt.getPickingTaskItems().stream())
            .anyMatch(pti -> "PHOTO-STUDIO".equals(pti.getLocationName()));
        assertTrue(hasPhotoStudioTask, "Should handle MDC special location items");
    }

    @Test
    void when_batch_has_big_orders_then_should_create_order_level_tasks() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        // Set items as big orders with unique order numbers
        for (int i = 0; i < batchItems.size(); i++) {
            batchItems.get(i).setBigOrder(true);
            batchItems.get(i).setOrderNumber("ORDER-" + i); // Unique order number for each item
            batchItems.get(i).setSource("MDC"); // Explicit MDC source
            batchItems.get(i).setExpectQty(10); // Consistent expectQty
        }
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTasksWithTotalItems(saved.getId(), 10);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create at least one task for big orders");

        pickingTasks.forEach(pt -> {
            assertEquals(SourceEnum.MDC, pt.getSource());
            assertEquals(PickingTaskType.ORDER, pt.getType());
        });

        int totalItems = pickingTasks.stream()
            .mapToInt(pt -> pt.getPickingTaskItems().size())
            .sum();
        assertEquals(10, totalItems, "All big order items should be included in tasks");
    }

    @Test
    void when_batch_has_big_orders_with_non_allowed_departments_then_should_create_batch_level_tasks() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 6);
        // Set items with non-allowed departments (should not create ORDER level tasks)
        for (int i = 0; i < batchItems.size(); i++) {
            batchItems.get(i).setBigOrder(false); // Set to false, let the system decide based on department
            batchItems.get(i).setOrderNumber("ORDER-" + i);
            batchItems.get(i).setSource("MDC");
            batchItems.get(i).setExpectQty(10);
            // Set non-allowed departments
            if (i < 3) {
                batchItems.get(i).setDepartment("Grocery");
            } else {
                batchItems.get(i).setDepartment("Candy & Snacks");
            }
        }
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTasksWithTotalItems(saved.getId(), 6);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertFalse(pickingTasks.isEmpty(), "Should create at least one task");

        // All tasks should be BATCH type since departments are not allowed for ORDER level
        pickingTasks.forEach(pt -> {
            assertEquals(SourceEnum.MDC, pt.getSource());
            assertEquals(PickingTaskType.BATCH, pt.getType());
        });

        int totalItems = pickingTasks.stream()
            .mapToInt(pt -> pt.getPickingTaskItems().size())
            .sum();
        assertEquals(6, totalItems, "All items should be included in BATCH level tasks");
    }

    @Test
    void when_batch_has_no_items_then_should_handle_gracefully() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(true);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());

        // For empty batch, we wait a bit to ensure no tasks are created
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(0, pickingTasks.size());
    }

    @Test
    void when_feature_flag_disabled_then_should_use_legacy_logic() {
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_PICKING_TASK_CREATION_LOGIC)).thenReturn(false);

        batchJpaDao.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId(), 10);

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        assertNotNull(pickingTasks);
        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pt -> assertEquals(SourceEnum.MDC, pt.getSource()));
    }

    private void waitForPickingTask(UUID batchId, int size) {
        await().atMost(10, SECONDS)
            .until(() -> {
                List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(batchId);
                return pickingTasks.size() == size;
            });
    }

    private void waitForPickingTasksWithTotalItems(UUID batchId, int expectedTotalItems) {
        await().atMost(10, SECONDS)
            .until(() -> {
                List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(batchId);
                int totalItems = pickingTasks.stream()
                    .mapToInt(pt -> pt.getPickingTaskItems().size())
                    .sum();
                return totalItems == expectedTotalItems;
            });
    }

    private void waitForReceivingTask(UUID batchId, int size) {
        await().atMost(10, SECONDS)
            .until(() -> {
                List<ReceivingTask> receivingTasks = receivingTaskRepository.findByBatchId(batchId);
                return receivingTasks.size() == size;
            });
    }

    private void waitForShippingOrderUpdate(UUID shippingOrderId, ShippingOrderStatus shippingOrderStatus) {
        await().atMost(10, SECONDS)
            .until(() -> {
                ShippingOrder shippingOrder = shippingOrderRepository.findById(shippingOrderId);
                return shippingOrder.getStatus() == shippingOrderStatus;
            });
    }

}