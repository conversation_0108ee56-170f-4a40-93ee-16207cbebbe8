package com.mercaso.wms.application.service.pickingtask.strategy;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MdcPickingTaskCreationStrategyTest {

    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private MdcPickingTaskCreationStrategy strategy;
    private PickingTaskCreationContext context;
    private UUID batchId;
    private Map<UUID, Location> locationMap;

    @BeforeEach
    void setUp() {
        strategy = new MdcPickingTaskCreationStrategy(batchItemQueryService);
        batchId = UUID.randomUUID();
        locationMap = new HashMap<>();
        context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(locationMap)
            .pickingTaskRepository(pickingTaskRepository)
            .build();
    }

    @Test
    void getSource_ShouldReturnMDC() {
        assertEquals(SourceEnum.MDC, strategy.getSource());
    }

    @Test
    void extractData_ShouldCallFindByWithCorrectParameters() {
        // Given
        List<BatchItem> expectedItems = buildBatchItems(batchId, 5);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MDC.name())).thenReturn(expectedItems);

        // When
        List<BatchItem> result = strategy.extractData(context);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MDC.name());
        assertEquals(expectedItems, result);
    }

    @Test
    void extractData_WhenNoItemsFound_ShouldReturnEmptyList() {
        // Given
        when(batchItemQueryService.findBy(batchId, SourceEnum.MDC.name())).thenReturn(Collections.emptyList());

        // When
        List<BatchItem> result = strategy.extractData(context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void createAndSave_WhenEmptyItemsList_ShouldReturnEmptyList() {
        // When
        List<PickingTask> result = strategy.createAndSave(Collections.emptyList(), context);

        // Then
        assertTrue(result.isEmpty());
        verify(pickingTaskRepository, never()).saveAll(anyList());
    }

    @Test
    void createAndSave_WithPhotoStudioItems_ShouldCreateSpecialLocationTask() {
        // Given
        List<BatchItem> photoStudioItems = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("PHOTO-STUDIO")
                .department("Electronics")
                .expectQty(5)
                .skuNumber("PHOTO-SKU-001")
                .title("Camera Product")
                .orderNumber("ORD-PHOTO-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("PHOTO-STUDIO")
                .department("Electronics")
                .expectQty(3)
                .skuNumber("PHOTO-SKU-002")
                .title("Lens Product")
                .orderNumber("ORD-PHOTO-002")
                .bigOrder(false)
                .build()
        );

        // Photo Studio items create special location tasks (only one saveAll call)
        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(photoStudioItems, context);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(SourceEnum.MDC, result.getFirst().getSource());
        assertEquals(PickingTaskType.BATCH, result.getFirst().getType());
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithCoolerItems_ShouldCreateSpecialLocationTaskGroupedBySource() {
        // Given
        List<BatchItem> coolerItems = Arrays.asList(
            // MDC Cooler items
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(BatchConstants.COOLER_LOCATION_NAME)
                .department("Beverage")
                .expectQty(10)
                .skuNumber("COOLER-MDC-001")
                .title("Cold Beverage 1")
                .orderNumber("ORD-COOL-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(BatchConstants.COOLER_LOCATION_NAME)
                .department("Beverage")
                .expectQty(8)
                .skuNumber("COOLER-MDC-002")
                .title("Cold Beverage 2")
                .orderNumber("ORD-COOL-002")
                .bigOrder(false)
                .build(),
            // MFC Cooler items (should be grouped separately)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MFC.name())
                .locationName(BatchConstants.COOLER_LOCATION_NAME)
                .department("Beverage")
                .expectQty(5)
                .skuNumber("COOLER-MFC-001")
                .title("Cold Beverage 3")
                .orderNumber("ORD-COOL-003")
                .bigOrder(false)
                .build()
        );

        // Mock two picking tasks - one for each source
        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.getFirst().setSource(SourceEnum.MDC);
        builtPickingTasks.getFirst().setType(PickingTaskType.BATCH);
        builtPickingTasks.get(1).setSource(SourceEnum.MFC);
        builtPickingTasks.get(1).setType(PickingTaskType.BATCH);

        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(coolerItems, context);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should create 2 tasks: one for MDC source, one for MFC source

        Set<SourceEnum> resultSources = result.stream()
            .map(PickingTask::getSource)
            .collect(Collectors.toSet());
        assertTrue(resultSources.contains(SourceEnum.MDC));
        assertTrue(resultSources.contains(SourceEnum.MFC));

        result.forEach(task -> assertEquals(PickingTaskType.BATCH, task.getType()));
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithNAItems_ShouldCreateSpecialLocationTaskGroupedBySource() {
        // Given
        List<BatchItem> naItems = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("N/A")
                .department("General")
                .expectQty(3)
                .skuNumber("NA-SKU-001")
                .title("N/A Item 1")
                .orderNumber("ORD-NA-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("N/A")
                .department("General")
                .expectQty(7)
                .skuNumber("NA-SKU-002")
                .title("N/A Item 2")
                .orderNumber("ORD-NA-002")
                .bigOrder(false)
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(naItems, context);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(SourceEnum.MDC, result.getFirst().getSource());
        assertEquals(PickingTaskType.BATCH, result.getFirst().getType());
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithBigOrderItems_ShouldCreateOrderLevelTasks() {
        // Given
        List<BatchItem> bigOrderItems = Arrays.asList(
            // RD location items
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".RD")
                .department("Beverage")
                .expectQty(50)
                .skuNumber("BIG-RD-001")
                .title("Large Beverage Order")
                .orderNumber("ORD-BIG-001")
                .bigOrder(true)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".RD")
                .department("Beverage")
                .expectQty(30)
                .skuNumber("BIG-RD-002")
                .title("Large Beverage Order 2")
                .orderNumber("ORD-BIG-001") // Same order
                .bigOrder(true)
                .build(),
            // TOBACCO location items
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".TOBACCO")
                .department("Tobacco")
                .expectQty(25)
                .skuNumber("BIG-TOB-001")
                .title("Large Tobacco Order")
                .orderNumber("ORD-BIG-002")
                .bigOrder(true)
                .build()
        );

        // Big Order items create order level tasks (only one saveAll call)
        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.ORDER); // Big orders create ORDER type tasks
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(bigOrderItems, context);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should create 2 tasks: one for RD, one for TOBACCO
        result.forEach(task -> {
            assertEquals(SourceEnum.MDC, task.getSource());
            assertEquals(PickingTaskType.ORDER, task.getType());
        });
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createAndSave_WithSmallBeverageItems_ShouldCreateBatchLevelTasks() {
        // Given - Similar to the original CreateBatchLevelPickingTaskServiceTest.when_batch_created_then_generate_small_order_picking_task
        List<BatchItem> smallBeverageItems = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-01")
                .department("Beverage")
                .expectQty(8)
                .skuNumber("SMALL-BEV-001")
                .title("Small Beverage 1")
                .orderNumber("ORD-SMALL-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-02")
                .department("Beverage")
                .expectQty(12)
                .skuNumber("SMALL-BEV-002")
                .title("Small Beverage 2")
                .orderNumber("ORD-SMALL-002")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-03")
                .department("Beverage")
                .expectQty(6)
                .skuNumber("SMALL-BEV-003")
                .title("Small Beverage 3")
                .orderNumber("ORD-SMALL-003")
                .bigOrder(false)
                .build()
        );

        // Mock that these items ARE found by the small beverage query
        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(smallBeverageItems);
        when(batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.BATCH);
            // Mock picking task items
            List<PickingTaskItem> mockItems = Arrays.asList(
                PickingTaskItem.builder().expectQty(10).build(),
                PickingTaskItem.builder().expectQty(5).build()
            );
            task.setPickingTaskItems(mockItems);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(smallBeverageItems, context);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Should create 1 task for all small beverage items
        result.forEach(task -> {
            assertEquals(SourceEnum.MDC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
            assertEquals(batchId, task.getBatchId());
        });
        verify(pickingTaskRepository).saveAll(anyList());
        verify(batchItemQueryService).findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name());
    }

    @Test
    void createAndSave_WithMixedSpecialAndRegularItems_ShouldCreateCorrectTasks() {
        // Given
        List<BatchItem> mixedItems = Arrays.asList(
            // Photo studio item (special location)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("PHOTO-STUDIO")
                .department("Electronics")
                .expectQty(2)
                .skuNumber("PHOTO-SKU-001")
                .title("Camera")
                .orderNumber("ORD-PHOTO-001")
                .bigOrder(false)
                .build(),
            // N/A item (special location)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("N/A")
                .department("General")
                .expectQty(1)
                .skuNumber("NA-SKU-001")
                .title("Unknown Item")
                .orderNumber("ORD-NA-001")
                .bigOrder(false)
                .build(),
            // Cooler item (special location)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(BatchConstants.COOLER_LOCATION_NAME)
                .department("Beverage")
                .expectQty(5)
                .skuNumber("COOLER-SKU-001")
                .title("Cold Drink")
                .orderNumber("ORD-COOL-001")
                .bigOrder(false)
                .build(),
            // Big order item (order level)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".RD")
                .department("Beverage")
                .expectQty(100)
                .skuNumber("BIG-RD-001")
                .title("Large Order")
                .orderNumber("ORD-BIG-001")
                .bigOrder(true)
                .build(),
            // Regular small item
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-01")
                .department("General")
                .expectQty(3)
                .skuNumber("REG-SKU-001")
                .title("Regular Item")
                .orderNumber("ORD-REG-001")
                .bigOrder(false)
                .build()
        );

        // Mock that only the regular item would be found by special queries (but not others)
        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());
        when(batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());

        // Mock tasks for special locations (first saveAll call)
        List<PickingTask> specialTasks = buildPickingTask(batchId, 3);
        specialTasks.get(0).setSource(SourceEnum.MDC);
        specialTasks.get(0).setType(PickingTaskType.BATCH); // Photo studio
        specialTasks.get(1).setSource(SourceEnum.MDC);
        specialTasks.get(1).setType(PickingTaskType.BATCH); // N/A
        specialTasks.get(2).setSource(SourceEnum.MDC);
        specialTasks.get(2).setType(PickingTaskType.BATCH); // Cooler

        // Mock tasks for other items (second saveAll call)
        List<PickingTask> otherTasks = buildPickingTask(batchId, 1);
        otherTasks.get(0).setSource(SourceEnum.MDC);
        otherTasks.get(0).setType(PickingTaskType.BATCH); // .RD big order (creates BATCH task)

        // Mock saveAll to return the input tasks (since both calls will happen)
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        List<PickingTask> result = strategy.createAndSave(mixedItems, context);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        /*
         * Expected 4 tasks:
         * 1. Photo studio task (BATCH type)
         * 2. N/A task (BATCH type)
         * 3. Cooler task (BATCH type)
         * 4. Big order .RD location task (BATCH type) - processed by processBatchItemsForOrderLevel
         * Note: Regular item is NOT processed by MDC strategy
         * Note: .RD location big order items create BATCH tasks, not ORDER tasks
         */

        long batchTasks = result.stream().filter(task -> task.getType() == PickingTaskType.BATCH).count();
        long orderTasks = result.stream().filter(task -> task.getType() == PickingTaskType.ORDER).count();

        assertEquals(4, batchTasks); // Photo studio, N/A, Cooler, .RD big order
        assertEquals(0, orderTasks); // No ORDER tasks in this scenario

        result.forEach(task -> assertEquals(SourceEnum.MDC, task.getSource()));
        // Note: saveAll is called twice - once for special location tasks, once for big order tasks
    }

    @Test
    void createAndSave_WithRegularItemsOnly_ShouldNotCreateAnyTasks() {
        // Given - only regular items that are NOT in special queries
        // MDC strategy only processes: special locations, big orders, small beverages, and candy items
        List<BatchItem> regularItems = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-01")
                .department("General")
                .expectQty(5)
                .skuNumber("REG-SKU-001")
                .title("Regular Item 1")
                .orderNumber("ORD-REG-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-02")
                .department("General")
                .expectQty(8)
                .skuNumber("REG-SKU-002")
                .title("Regular Item 2")
                .orderNumber("ORD-REG-002")
                .bigOrder(false)
                .build()
        );

        // Mock that these items are NOT found by special queries
        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());
        when(batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());

        // When
        List<PickingTask> result = strategy.createAndSave(regularItems, context);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size()); // No tasks should be created for regular items
        verify(pickingTaskRepository, never()).saveAll(anyList());
    }

    @Test
    void createAndSave_WithSmallBeverageAndCandyItems_ShouldCreateBatchTasks() {
        // Given - items that will be found by special queries
        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-01")
                .department("Beverage")
                .expectQty(8)
                .skuNumber("SMALL-BEV-001")
                .title("Small Beverage 1")
                .orderNumber("ORD-BEVERAGE-001")
                .bigOrder(false)
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-02")
                .department("Candy & Snacks")
                .expectQty(5)
                .skuNumber("CANDY-001")
                .title("Candy Item 1")
                .orderNumber("ORD-CANDY-001")
                .bigOrder(false)
                .build()
        );

        // Mock that these items ARE found by special queries
        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Arrays.asList(items.get(0))); // Return the beverage item
        when(batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Arrays.asList(items.get(1))); // Return the candy item

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should create 2 tasks: one for beverage, one for candy
        result.forEach(task -> {
            assertEquals(SourceEnum.MDC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
        verify(pickingTaskRepository).saveAll(anyList());
        verify(batchItemQueryService).findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name());
        verify(batchItemQueryService).findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name());
    }

    @Test
    void createAndSave_WithDifferentMDCLocationTypes_ShouldCreateCorrectTaskTypes() {
        // Given - Test different MDC location patterns
        List<BatchItem> mixedLocationItems = Arrays.asList(
            // RD location (big order pattern)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".RD-A1-01")
                .department("Beverage")
                .expectQty(20)
                .skuNumber("RD-SKU-001")
                .title("RD Location Item")
                .orderNumber("ORD-RD-001")
                .bigOrder(true)
                .build(),
            // TOBACCO location (big order pattern)
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName(".TOBACCO-B1-01")
                .department("Tobacco")
                .expectQty(15)
                .skuNumber("TOBACCO-SKU-001")
                .title("Tobacco Location Item")
                .orderNumber("ORD-TOBACCO-001")
                .bigOrder(true)
                .build(),
            // Regular location
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("A01-NORMAL")
                .department("General")
                .expectQty(5)
                .skuNumber("NORMAL-SKU-001")
                .title("Normal Location Item")
                .orderNumber("ORD-NORMAL-001")
                .bigOrder(false)
                .build()
        );

        // Mock that the regular item won't be processed by special queries
        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());
        when(batchItemQueryService.findCandyBatchItemsByBatchId(batchId, SourceEnum.MDC.name()))
            .thenReturn(Collections.emptyList());

        // Big order items create order level tasks (only one saveAll call)
        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.get(0).setSource(SourceEnum.MDC);
        builtPickingTasks.get(0).setType(PickingTaskType.ORDER); // RD
        builtPickingTasks.get(1).setSource(SourceEnum.MDC);
        builtPickingTasks.get(1).setType(PickingTaskType.ORDER); // TOBACCO

        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(mixedLocationItems, context);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Only big order tasks (RD and TOBACCO), regular item not processed

        long orderTasks = result.stream().filter(task -> task.getType() == PickingTaskType.ORDER).count();
        long batchTasks = result.stream().filter(task -> task.getType() == PickingTaskType.BATCH).count();

        assertEquals(2, orderTasks); // RD and TOBACCO
        assertEquals(0, batchTasks); // Regular location item is NOT processed by MDC strategy

        result.forEach(task -> assertEquals(SourceEnum.MDC, task.getSource()));
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void execute_WhenNoItemsFound_ShouldReturnEmptyList() {
        // Given
        when(batchItemQueryService.findBy(batchId, SourceEnum.MDC.name())).thenReturn(Collections.emptyList());

        // When
        List<PickingTask> result = strategy.execute(context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void execute_WithValidItems_ShouldReturnCreatedTasks() {
        // Given
        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MDC.name())
                .locationName("PHOTO-STUDIO")
                .department("Electronics")
                .expectQty(1)
                .skuNumber("TEST-SKU")
                .title("Test Item")
                .orderNumber("TEST-ORDER")
                .bigOrder(false)
                .build()
        );

        when(batchItemQueryService.findBy(batchId, SourceEnum.MDC.name())).thenReturn(items);

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MDC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.execute(context);

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MDC.name());
        verify(pickingTaskRepository).saveAll(anyList());
        result.forEach(task -> assertEquals(SourceEnum.MDC, task.getSource()));
    }
} 