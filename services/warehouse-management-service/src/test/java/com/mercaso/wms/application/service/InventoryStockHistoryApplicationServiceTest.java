package com.mercaso.wms.application.service;

import static com.mercaso.wms.utils.MockDataUtils.buildTransferTask;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistory;
import com.mercaso.wms.domain.inventorystockhistory.InventoryStockHistoryRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferResponseDto;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InventoryStockHistoryApplicationServiceTest {

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final FinaleProductService finaleProductService = mock(FinaleProductService.class);

    private final LocationCache locationCache = mock(LocationCache.class);

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final InventoryStockHistoryRepository inventoryStockHistoryRepository = mock(InventoryStockHistoryRepository.class);

    private final FinaleConfigProperties finaleConfigProperties = mock(FinaleConfigProperties.class);

    private final TransferTaskRepository transferTaskRepository = mock(TransferTaskRepository.class);

    private final InventoryStockHistoryApplicationService service = new InventoryStockHistoryApplicationService(
        pickingTaskRepository,
        finaleProductService,
        locationCache,
        locationRepository,
        inventoryStockHistoryRepository,
        finaleConfigProperties,
        transferTaskRepository
    );

    private UUID pickingTaskId;
    private PickingTask pickingTask;
    private Location mfcStageLocation;
    private Map<UUID, Location> locationMap;

    @BeforeEach
    void setUp() {
        pickingTaskId = UUID.randomUUID();
        UUID locationId = UUID.randomUUID();

        // Setup PickingTaskItem
        PickingTaskItem pickingTaskItem = PickingTaskItem.builder().build();
        pickingTaskItem.setLocationId(locationId);
        pickingTaskItem.setSkuNumber("SKU123");
        pickingTaskItem.setPickedQty(10);

        // Setup PickingTask
        pickingTask = PickingTask.builder().id(pickingTaskId).build();
        pickingTask.setStatus(PickingTaskStatus.COMPLETED);
        pickingTask.setPickingTaskItems(Collections.singletonList(pickingTaskItem));
        pickingTask.setSource(SourceEnum.MFC);

        // Setup Locations
        Location sourceLocation = Location.builder().id(locationId).name("Source").build();

        mfcStageLocation = Location.builder().name("MFC_Stage").build();

        // Setup LocationMap
        locationMap = new HashMap<>();
        locationMap.put(locationId, sourceLocation);
    }

    @Test
    void handlePickingTaskEvent_Success() {
        // Given
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(finaleConfigProperties.getMfcStage()).thenReturn("MFC_STAGE");
        when(locationRepository.findByName("MFC_STAGE")).thenReturn(mfcStageLocation);
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(finaleProductService.quickTransferStock(any(),
            anyInt(),
            any(),
            any(),
            any())).thenReturn(new FinaleTransferResponseDto());

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(inventoryStockHistoryRepository).save(any(InventoryStockHistory.class));
    }

    @Test
    void handleMdcPickingTaskEvent_Success() {
        // Given
        pickingTask.setSource(SourceEnum.MDC);
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP-SB");
        when(locationRepository.findByName("SHIP-SB")).thenReturn(Location.builder().name("SHIP-SB").build());
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(finaleProductService.quickTransferStock(any(),
            anyInt(),
            any(),
            any(),
            any())).thenReturn(new FinaleTransferResponseDto());

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(inventoryStockHistoryRepository).save(any(InventoryStockHistory.class));
    }

    @Test
    void handlePickingTaskEvent_PickingTaskNotFound() {
        // Given
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(null);

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(inventoryStockHistoryRepository, times(0)).save(any());
    }

    @Test
    void handlePickingTaskEvent_InvalidStatus() {
        // Given
        pickingTask.setStatus(PickingTaskStatus.CREATED);
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(inventoryStockHistoryRepository, times(0)).save(any());
    }

    @Test
    void handlePickingTaskEvent_MfcStageLocationNotFound() {
        // Given
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(finaleConfigProperties.getMfcStage()).thenReturn("MFC_Stage");
        when(locationRepository.findByName("MFC_Stage")).thenReturn(null);

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(inventoryStockHistoryRepository, times(0)).save(any());
    }

    @Test
    void handlePickingTaskEvent_SourceLocationNotFound() {
        // Given
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(finaleConfigProperties.getMfcStage()).thenReturn("MFC_STAGE");
        when(locationRepository.findByName("MFC_STAGE")).thenReturn(mfcStageLocation);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(inventoryStockHistoryRepository, times(0)).save(any());
    }

    @Test
    void handlePickingTaskEvent_TransferFailed() {
        // Given
        when(pickingTaskRepository.findById(pickingTaskId)).thenReturn(pickingTask);
        when(finaleConfigProperties.getMfcStage()).thenReturn("MFC_STAGE");
        when(locationRepository.findByName("MFC_STAGE")).thenReturn(mfcStageLocation);
        when(locationCache.getLocationMap()).thenReturn(locationMap);
        when(finaleProductService.quickTransferStock(any(), anyInt(), any(), any(), any())).thenReturn(null);

        // When
        service.handlePickingTaskEvent(pickingTaskId);

        // Then
        verify(inventoryStockHistoryRepository, times(1)).save(any());
    }

    @Test
    void transferInventory_Success() {
        // Given
        UUID transferTaskId = UUID.randomUUID();
        when(transferTaskRepository.findById(transferTaskId)).thenReturn(buildTransferTask(1,
            TransferTaskStatus.RECEIVED).getFirst());
        when(locationRepository.findByName("Origin")).thenReturn(Location.builder().name("Origin").build());
        when(locationRepository.findByName("Destination")).thenReturn(Location.builder().name("Destination").build());
        when(finaleProductService.quickTransferStock(any(),
            anyInt(),
            any(),
            any(),
            any())).thenReturn(new FinaleTransferResponseDto());

        service.transferInventory(transferTaskId, "Origin", "Destination");

        verify(inventoryStockHistoryRepository, times(1)).save(any());
    }

    @Test
    void transferInventory_TransferTaskNotFound() {
        // Given
        UUID transferTaskId = UUID.randomUUID();
        when(transferTaskRepository.findById(transferTaskId)).thenReturn(null);

        service.transferInventory(transferTaskId, "Origin", "Destination");

        verify(inventoryStockHistoryRepository, times(0)).save(any());
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(locationRepository, times(0)).findByName("Origin");
        verify(locationRepository, times(0)).findByName("Destination");
    }

    @Test
    void transferInventory_OriginLocationNotFound() {
        // Given
        UUID transferTaskId = UUID.randomUUID();
        when(transferTaskRepository.findById(transferTaskId)).thenReturn(buildTransferTask(1,
            TransferTaskStatus.RECEIVED).getFirst());
        when(locationRepository.findByName("Origin")).thenReturn(null);

        service.transferInventory(transferTaskId, "Origin", "Destination");

        verify(inventoryStockHistoryRepository, times(0)).save(any());
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(locationRepository, times(1)).findByName("Destination");
        verify(locationRepository, times(1)).findByName("Origin");
    }

    @Test
    void transferInventory_DestinationLocationNotFound() {
        // Given
        UUID transferTaskId = UUID.randomUUID();
        when(transferTaskRepository.findById(transferTaskId)).thenReturn(buildTransferTask(1,
            TransferTaskStatus.RECEIVED).getFirst());
        when(locationRepository.findByName("Origin")).thenReturn(Location.builder().name("Origin").build());
        when(locationRepository.findByName("Destination")).thenReturn(null);

        service.transferInventory(transferTaskId, "Origin", "Destination");

        verify(inventoryStockHistoryRepository, times(0)).save(any());
        verify(finaleProductService, times(0)).quickTransferStock(any(), anyInt(), any(), any(), any());
        verify(locationRepository, times(1)).findByName("Destination");
        verify(locationRepository, times(1)).findByName("Origin");
    }

}