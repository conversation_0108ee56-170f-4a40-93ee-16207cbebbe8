package com.mercaso.wms.infrastructure.utils;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("AddressNormalizer Tests")
class AddressNormalizerTest {

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "  "})
    @DisplayName("Should return empty string when input is empty or whitespace only")
    void normalize_shouldReturnEmptyString_whenInputIsEmptyOrWhitespace(String input) {
        String result = AddressNormalizer.normalize(input);
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should normalize street suffixes")
    void normalize_shouldNormalizeStreetSuffixes() {
        assertThat(AddressNormalizer.normalize("123 Main st")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("456 Oak rd.")).isEqualTo("456 Oak Road");
        assertThat(AddressNormalizer.normalize("789 Pine ave")).isEqualTo("789 Pine Avenue");
        assertThat(AddressNormalizer.normalize("101 Elm blvd.")).isEqualTo("101 Elm Boulevard");
        assertThat(AddressNormalizer.normalize("202 Maple ln")).isEqualTo("202 Maple Lane");
        assertThat(AddressNormalizer.normalize("303 Cedar dr")).isEqualTo("303 Cedar Drive");
        assertThat(AddressNormalizer.normalize("404 Birch ct")).isEqualTo("404 Birch Court");
        assertThat(AddressNormalizer.normalize("505 Spruce cir")).isEqualTo("505 Spruce Circle");
        assertThat(AddressNormalizer.normalize("606 Willow pl")).isEqualTo("606 Willow Place");
        assertThat(AddressNormalizer.normalize("707 Ash ter")).isEqualTo("707 Ash Terrace");
        assertThat(AddressNormalizer.normalize("808 Poplar pkwy")).isEqualTo("808 Poplar Parkway");
        assertThat(AddressNormalizer.normalize("909 Sycamore hwy")).isEqualTo("909 Sycamore Highway");
        assertThat(AddressNormalizer.normalize("111 Dogwood sq")).isEqualTo("111 Dogwood Square");
        assertThat(AddressNormalizer.normalize("222 Magnolia trl")).isEqualTo("222 Magnolia Trail");
    }

    @Test
    @DisplayName("Should normalize direction abbreviations")
    void normalize_shouldNormalizeDirectionAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main St n")).isEqualTo("123 Main Street North");
        assertThat(AddressNormalizer.normalize("456 Oak Rd s.")).isEqualTo("456 Oak Road South");
        assertThat(AddressNormalizer.normalize("789 Pine Ave e")).isEqualTo("789 Pine Avenue East");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd w.")).isEqualTo("101 Elm Boulevard West");
        assertThat(AddressNormalizer.normalize("202 Maple Ln ne")).isEqualTo("202 Maple Lane Northeast");
        assertThat(AddressNormalizer.normalize("303 Cedar Dr nw.")).isEqualTo("303 Cedar Drive Northwest");
        assertThat(AddressNormalizer.normalize("404 Birch Ct se")).isEqualTo("404 Birch Court Southeast");
        assertThat(AddressNormalizer.normalize("505 Spruce Cir sw.")).isEqualTo("505 Spruce Circle Southwest");
    }

    @Test
    @DisplayName("Should normalize state abbreviations")
    void normalize_shouldNormalizeStateAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main St, NY")).isEqualTo("123 Main Street, New York");
        assertThat(AddressNormalizer.normalize("456 Oak Rd, CA")).isEqualTo("456 Oak Road, California");
        assertThat(AddressNormalizer.normalize("789 Pine Ave, TX")).isEqualTo("789 Pine Avenue, Texas");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd, FL")).isEqualTo("101 Elm Boulevard, Florida");
        assertThat(AddressNormalizer.normalize("202 Maple Ln, IL")).isEqualTo("202 Maple Lane, Illinois");
    }

    @Test
    @DisplayName("Should not normalize non-state abbreviations")
    void normalize_shouldNotNormalizeNonStateAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main St, AB")).isEqualTo("123 Main Street, Ab");
        assertThat(AddressNormalizer.normalize("456 Oak Rd, CD")).isEqualTo("456 Oak Road, Cd");
        assertThat(AddressNormalizer.normalize("789 Pine Ave, EF")).isEqualTo("789 Pine Avenue, Ef");
    }

    @Test
    @DisplayName("Should normalize PO Box variations")
    void normalize_shouldNormalizePoBoxVariations() {
        assertThat(AddressNormalizer.normalize("PO Box 123")).isEqualTo("PO Box 123");
        assertThat(AddressNormalizer.normalize("P.O. Box 456")).isEqualTo("PO Box 456");
        assertThat(AddressNormalizer.normalize("po box 789")).isEqualTo("PO Box 789");
        assertThat(AddressNormalizer.normalize("p.o. box 101")).isEqualTo("PO Box 101");
    }

    @Test
    @DisplayName("Should normalize apartment and unit variations")
    void normalize_shouldNormalizeApartmentAndUnitVariations() {
        assertThat(AddressNormalizer.normalize("123 Main St Apt 4")).isEqualTo("123 Main Street Apt 4");
        assertThat(AddressNormalizer.normalize("456 Oak Rd Unit 5")).isEqualTo("456 Oak Road Unit 5");
        assertThat(AddressNormalizer.normalize("789 Pine Ave Ste 6")).isEqualTo("789 Pine Avenue Ste 6");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd Suite 7")).isEqualTo("101 Elm Boulevard Suite 7");
        assertThat(AddressNormalizer.normalize("202 Maple Ln apt 8")).isEqualTo("202 Maple Lane Apt 8");
        assertThat(AddressNormalizer.normalize("303 Cedar Dr unit 9")).isEqualTo("303 Cedar Drive Unit 9");
        assertThat(AddressNormalizer.normalize("404 Birch Ct ste 10")).isEqualTo("404 Birch Court Ste 10");
        assertThat(AddressNormalizer.normalize("505 Spruce Cir suite 11")).isEqualTo("505 Spruce Circle Suite 11");
    }

    @Test
    @DisplayName("Should handle numbers and mixed alphanumeric")
    void normalize_shouldHandleNumbersAndMixedAlphanumeric() {
        assertThat(AddressNormalizer.normalize("123 Main St")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("456 7th Ave")).isEqualTo("456 7TH Avenue");
        assertThat(AddressNormalizer.normalize("789 1st St")).isEqualTo("789 1ST Street");
        assertThat(AddressNormalizer.normalize("101 2nd Ave")).isEqualTo("101 2ND Avenue");
        assertThat(AddressNormalizer.normalize("202 Building A")).isEqualTo("202 Building A");
        assertThat(AddressNormalizer.normalize("303 Floor 5")).isEqualTo("303 Floor 5");
    }

    @Test
    @DisplayName("Should clean up whitespace and commas")
    void normalize_shouldCleanUpWhitespaceAndCommas() {
        assertThat(AddressNormalizer.normalize("  123   Main   St  ")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("123,Main,St")).isEqualTo("123, Main, Street");
        assertThat(AddressNormalizer.normalize("123 , Main , St")).isEqualTo("123, Main, Street");
        assertThat(AddressNormalizer.normalize("123, Main, St,")).isEqualTo("123, Main, Street");
        assertThat(AddressNormalizer.normalize("123, Main, St, ")).isEqualTo("123, Main, Street");
    }

    @Test
    @DisplayName("Should handle complex address combinations")
    void normalize_shouldHandleComplexAddressCombinations() {
        assertThat(AddressNormalizer.normalize("123 Main St N, NY 10001"))
            .isEqualTo("123 Main Street North, New York 10001");

        assertThat(AddressNormalizer.normalize("456 Oak Rd SW, CA 90210"))
            .isEqualTo("456 Oak Road Southwest, California 90210");

        assertThat(AddressNormalizer.normalize("789 Pine Ave E, TX 75001"))
            .isEqualTo("789 Pine Avenue East, Texas 75001");

        assertThat(AddressNormalizer.normalize("101 Elm Blvd NW, FL 33101"))
            .isEqualTo("101 Elm Boulevard Northwest, Florida 33101");
    }

    @Test
    @DisplayName("Should handle special characters and punctuation")
    void normalize_shouldHandleSpecialCharactersAndPunctuation() {
        assertThat(AddressNormalizer.normalize("123 Main St #4")).isEqualTo("123 Main Street #4");
        assertThat(AddressNormalizer.normalize("456 Oak Rd - Unit 5")).isEqualTo("456 Oak Road - Unit 5");
        assertThat(AddressNormalizer.normalize("789 Pine Ave & 6th St")).isEqualTo("789 Pine Avenue 6TH Street");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd (Building C)")).isEqualTo("101 Elm Boulevard Building C");
    }

    @Test
    @DisplayName("Should preserve case for numbers and mixed content")
    void normalize_shouldPreserveCaseForNumbersAndMixedContent() {
        assertThat(AddressNormalizer.normalize("123 Main St")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("456 7TH Ave")).isEqualTo("456 7TH Avenue");
        assertThat(AddressNormalizer.normalize("789 1st St")).isEqualTo("789 1ST Street");
        assertThat(AddressNormalizer.normalize("101 2ND Ave")).isEqualTo("101 2ND Avenue");
        assertThat(AddressNormalizer.normalize("202 Building-A")).isEqualTo("202 Building-a");
    }

    @Test
    @DisplayName("Should handle edge cases with empty tokens")
    void normalize_shouldHandleEdgeCasesWithEmptyTokens() {
        assertThat(AddressNormalizer.normalize("123 Main St, , NY")).isEqualTo("123 Main Street,, New York");
        assertThat(AddressNormalizer.normalize("123 Main St, , , NY")).isEqualTo("123 Main Street,,, New York");
        assertThat(AddressNormalizer.normalize("123 Main St, NY, ")).isEqualTo("123 Main Street, New York");
    }

    @Test
    @DisplayName("Should handle addresses with multiple commas")
    void normalize_shouldHandleAddressesWithMultipleCommas() {
        assertThat(AddressNormalizer.normalize("123 Main St, Apt 4, NY, 10001"))
            .isEqualTo("123 Main Street, Apt 4, New York, 10001");

        assertThat(AddressNormalizer.normalize("456 Oak Rd, Unit 5, CA, 90210"))
            .isEqualTo("456 Oak Road, Unit 5, California, 90210");
    }

    @Test
    @DisplayName("Should handle addresses with no abbreviations")
    void normalize_shouldHandleAddressesWithNoAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main Street")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("456 Oak Road")).isEqualTo("456 Oak Road");
        assertThat(AddressNormalizer.normalize("789 Pine Avenue")).isEqualTo("789 Pine Avenue");
        assertThat(AddressNormalizer.normalize("101 Elm Boulevard")).isEqualTo("101 Elm Boulevard");
    }

    @Test
    @DisplayName("Should handle addresses with mixed case abbreviations")
    void normalize_shouldHandleAddressesWithMixedCaseAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main St")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("123 Main ST")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("123 Main st")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("123 Main St.")).isEqualTo("123 Main Street");
    }

    @Test
    @DisplayName("Should handle addresses with multiple direction abbreviations")
    void normalize_shouldHandleAddressesWithMultipleDirectionAbbreviations() {
        assertThat(AddressNormalizer.normalize("123 Main St N W")).isEqualTo("123 Main Street North West");
        assertThat(AddressNormalizer.normalize("456 Oak Rd S E")).isEqualTo("456 Oak Road South East");
    }

    @Test
    @DisplayName("Should handle addresses with postal codes")
    void normalize_shouldHandleAddressesWithPostalCodes() {
        assertThat(AddressNormalizer.normalize("123 Main St, NY 10001")).isEqualTo("123 Main Street, New York 10001");
        assertThat(AddressNormalizer.normalize("456 Oak Rd, CA 90210")).isEqualTo("456 Oak Road, California 90210");
        assertThat(AddressNormalizer.normalize("789 Pine Ave, TX 75001")).isEqualTo("789 Pine Avenue, Texas 75001");
    }

    @Test
    @DisplayName("Should handle addresses with country names")
    void normalize_shouldHandleAddressesWithCountryNames() {
        assertThat(AddressNormalizer.normalize("123 Main St, NY, USA")).isEqualTo("123 Main Street, New York, Usa");
        assertThat(AddressNormalizer.normalize("456 Oak Rd, CA, United States")).isEqualTo(
            "456 Oak Road, California, United States");
    }

    @Test
    @DisplayName("Should handle addresses with building names")
    void normalize_shouldHandleAddressesWithBuildingNames() {
        assertThat(AddressNormalizer.normalize("Building A, 123 Main St")).isEqualTo("Building A, 123 Main Street");
        assertThat(AddressNormalizer.normalize("Tower 1, 456 Oak Rd")).isEqualTo("Tower 1, 456 Oak Road");
        assertThat(AddressNormalizer.normalize("Floor 5, 789 Pine Ave")).isEqualTo("Floor 5, 789 Pine Avenue");
    }

    @Test
    @DisplayName("Should demonstrate special character filtering behavior")
    void normalize_shouldDemonstrateSpecialCharacterFilteringBehavior() {
        // Test that special characters are filtered out
        assertThat(AddressNormalizer.normalize("123 Main St & Ave")).isEqualTo("123 Main Street Avenue");
        assertThat(AddressNormalizer.normalize("456 Oak Rd (North)")).isEqualTo("456 Oak Road North");
        assertThat(AddressNormalizer.normalize("789 Pine Ave @ Building")).isEqualTo("789 Pine Avenue Building");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd % Complex")).isEqualTo("101 Elm Boulevard Complex");

        // Test that allowed special characters are preserved
        assertThat(AddressNormalizer.normalize("123 Main St #4")).isEqualTo("123 Main Street #4");
        assertThat(AddressNormalizer.normalize("456 Oak Rd - Unit 5")).isEqualTo("456 Oak Road - Unit 5");
    }

    @Test
    @DisplayName("Should demonstrate number handling behavior")
    void normalize_shouldDemonstrateNumberHandlingBehavior() {
        // Test that words with numbers are converted to uppercase
        assertThat(AddressNormalizer.normalize("123 Main St 1st Ave")).isEqualTo("123 Main Street 1ST Avenue");
        assertThat(AddressNormalizer.normalize("456 Oak Rd 2nd St")).isEqualTo("456 Oak Road 2ND Street");
        assertThat(AddressNormalizer.normalize("789 Pine Ave 3rd Ln")).isEqualTo("789 Pine Avenue 3RD Lane");
        assertThat(AddressNormalizer.normalize("101 Elm Blvd 4th Dr")).isEqualTo("101 Elm Boulevard 4TH Drive");

        // Test that pure numbers are preserved
        assertThat(AddressNormalizer.normalize("123 Main St")).isEqualTo("123 Main Street");
        assertThat(AddressNormalizer.normalize("456 7th Ave")).isEqualTo("456 7TH Avenue");
    }
} 