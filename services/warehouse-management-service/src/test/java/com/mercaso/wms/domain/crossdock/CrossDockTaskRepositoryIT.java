package com.mercaso.wms.domain.crossdock;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.infrastructure.repository.crossdock.CrossDockTaskRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.criteria.CrossDockTaskSearchCriteria;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.utils.MockDataUtils;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

class CrossDockTaskRepositoryIT extends AbstractIT {

    @Autowired
    private CrossDockTaskRepositoryImpl crossDockTaskRepository;

    @BeforeEach
    void setUp() {
        crossDockTaskRepository.deleteAll();
    }

    @Test
    void search_cross_dock_task_with_picker_user_id_than_success() {
        List<CrossDockTask> crossDockTasks = MockDataUtils.buildCrossDockTasks(2);
        crossDockTasks = crossDockTaskRepository.saveAll(crossDockTasks);

        CrossDockTaskSearchCriteria criteria = new CrossDockTaskSearchCriteria();
        criteria.setPickerUserId(crossDockTasks.getFirst().getPickerUserId());

        Page<SearchCrossDockTaskView> result = crossDockTaskRepository.search(criteria, PageRequest.of(0, 10));
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
    }

    @Test
    void search_cross_dock_task_with_delivery_date_than_success() {
        UUID batchId = UUID.randomUUID();
        Batch batch = MockDataUtils.buildBatch(batchId);
        batch.setTag(DateUtils.getCurrentDeliveryDate());
        Batch saved = batchRepository.save(batch);

        List<CrossDockTask> crossDockTasks = MockDataUtils.buildCrossDockTasks(2);
        crossDockTasks.getFirst().setDeliveryDate(DateUtils.getCurrentDeliveryDate());
        for (CrossDockTaskItem crossDockTaskItem : crossDockTasks.getFirst().getCrossDockTaskItems()) {
            crossDockTaskItem.setBatchId(saved.getId());
        }
        crossDockTaskRepository.saveAll(crossDockTasks);

        CrossDockTaskSearchCriteria criteria = new CrossDockTaskSearchCriteria();
        criteria.setDeliveryDate(DateUtils.getCurrentDeliveryDate());

        Page<SearchCrossDockTaskView> result = crossDockTaskRepository.search(criteria, PageRequest.of(0, 10));
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
    }

}