package com.mercaso.wms.utils;

import com.mercaso.wms.application.command.crossdock.AssignNextSequenceCommand;
import com.mercaso.wms.application.dto.CrossDockItemDto;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class CrossDockTaskItemResourceApi extends IntegrationTestRestUtil {

    private static final String ASSIGN_NEXT_SEQUENCE_URL = "/cross-dock-task-items/assign-next-sequence";

    public CrossDockTaskItemResourceApi(Environment environment) {
        super(environment);
    }

    public CrossDockItemDto assignNextSequence(UUID shippingOrderItemId) throws Exception {
        AssignNextSequenceCommand command = AssignNextSequenceCommand.builder()
            .shippingOrderItemId(shippingOrderItemId)
            .build();
        return updateEntity(ASSIGN_NEXT_SEQUENCE_URL, command, CrossDockItemDto.class);
    }
}