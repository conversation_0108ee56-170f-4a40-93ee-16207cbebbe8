package com.mercaso.wms.application.service;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.application.command.batch.CreateBatchCommand;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mockito;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class BatchApplicationServiceTest {

    private final BatchRepository batchRepository = Mockito.mock(BatchRepository.class);

    private final BusinessEventDispatcher businessEventDispatcher = Mockito.mock(BusinessEventDispatcher.class);

    private final BatchApplicationService batchApplicationService = new BatchApplicationService(batchRepository,
        businessEventDispatcher);

    @Test
    void when_create_batch_then_success() {
        CreateBatchCommand createBatchCommand = CreateBatchCommand.builder().build();
        createBatchCommand.setTag("tag");
        Batch batch = Batch.builder().tag(createBatchCommand.getTag()).status(BatchStatus.CREATED).build();
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        DispatchResponseDto dispatchResponseDto = DispatchResponseDto.builder().build();

        when(batchRepository.save(any())).thenReturn(batch);
        when(businessEventDispatcher.dispatch(any())).thenReturn(dispatchResponseDto);

        batchApplicationService.createBatch(createBatchCommand);

        verify(batchRepository, times(1)).save(batch);
        verify(businessEventDispatcher, times(1)).dispatch(any());
    }

}