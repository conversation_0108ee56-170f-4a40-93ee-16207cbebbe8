package com.mercaso.wms.batch.config;

import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.queryservice.AccountPreferenceQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class PickingTaskAssignmentConfigTest {

    private final AccountPreferenceQueryService accountPreferenceQueryService = mock(AccountPreferenceQueryService.class);

    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = new PickingTaskAssignmentConfig(
        accountPreferenceQueryService);


    @Test
    void shouldAssignMdcPickersCorrectly() {
        // Given
        List<AccountPreferenceDto> preferences = createMdcPreferences(3);
        when(accountPreferenceQueryService.findBy(AccountPreferenceStatus.ACTIVE, true)).thenReturn(preferences);

        // When
        PickingTaskAssignmentProperties result = pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAutoAssignment()).isTrue();
        assertThat(result.getMdcSmallBevragePicker().getUserName()).isEqualTo("user1");
        assertThat(result.getMdcSpecialBinsPicker().getUserName()).isEqualTo("user2");
        assertThat(result.getMdcCandyPickers()).hasSize(1);
        assertThat(result.getMdcCandyPickers().getFirst().getUserName()).isEqualTo("user3");
    }

    @Test
    void shouldAssignMfcPickersCorrectly() {
        // Given
        List<AccountPreferenceDto> preferences = createMfcPreferences(3);
        when(accountPreferenceQueryService.findBy(AccountPreferenceStatus.ACTIVE, true)).thenReturn(preferences);

        // When
        PickingTaskAssignmentProperties result = pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAutoAssignment()).isTrue();
        assertThat(result.getMfcSmallBevragePicker().getUserName()).isEqualTo("user1");
        assertThat(result.getMfcSpecialBinsPicker().getUserName()).isEqualTo("user1");
        assertThat(result.getMfcCandyPickers()).hasSize(2);
        assertThat(result.getMfcCandyPickers().getFirst().getUserName()).isEqualTo("user2");
    }

    @Test
    void shouldHandleEmptyPickersList() {
        // Given
        when(accountPreferenceQueryService.findBy(AccountPreferenceStatus.ACTIVE, true))
            .thenReturn(Collections.emptyList());

        // When
        PickingTaskAssignmentProperties result = pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getAutoAssignment()).isTrue();
        assertThat(result.getMdcSmallBevragePicker()).isNull();
        assertThat(result.getMfcSmallBevragePicker()).isNull();
        assertThat(result.getMdcCandyPickers()).isNull();
        assertThat(result.getMfcCandyPickers()).isNull();
    }

    private List<AccountPreferenceDto> createMdcPreferences(int count) {
        return createPreferences(count, SourceEnum.MDC);
    }

    private List<AccountPreferenceDto> createMfcPreferences(int count) {
        return createPreferences(count, SourceEnum.MFC);
    }

    private List<AccountPreferenceDto> createPreferences(int count, SourceEnum source) {
        List<AccountPreferenceDto> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            AccountPreferenceDto dto = new AccountPreferenceDto();
            dto.setUserId(UUID.randomUUID());
            dto.setUserName("user" + (i + 1));
            dto.setTaskType(PickingTaskType.BATCH);

            WarehouseDto warehouse = WarehouseDto.builder().name(source.name()).build();
            warehouse.setName(source.name());
            dto.setWorkWarehouse(warehouse);

            result.add(dto);
        }
        return result;
    }
}