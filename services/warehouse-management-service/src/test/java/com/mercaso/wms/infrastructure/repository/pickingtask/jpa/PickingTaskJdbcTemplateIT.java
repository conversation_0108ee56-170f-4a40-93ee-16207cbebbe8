package com.mercaso.wms.infrastructure.repository.pickingtask.jpa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickedItemsDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.criteria.PickingTaskSearchCriteria;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

class PickingTaskJdbcTemplateIT extends AbstractIT {

    @Autowired
    private PickingTaskJdbcTemplate pickingTaskJdbcTemplate;
    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    @Test
    void when_fetch_picked_items_then_returnPickedItems() {
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);
        PickingTaskItem pickingTaskItemDo = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .title("title")
            .locationName("locationName")
            .errorInfo("errorInfo")
            .pickedQty(5)
            .expectQty(10)
            .pickingSequence(1)
            .prep(RandomStringUtils.randomAlphabetic(5))
            .build();
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .status(PickingTaskStatus.FAILED)
            .batchId(savedBatch.getId())
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(pickingTaskItemDo))
            .build();

        // when
        PickingTask saved = pickingTaskRepository.save(pickingTask);

        List<PickedItemsDto> pickedItemsDtos = pickingTaskJdbcTemplate.fetchPickedItemsForDeliveryDate(savedBatch.getTag());

        assertEquals(1, pickedItemsDtos.size());
        assertEquals(saved.getSource().name(), pickedItemsDtos.getFirst().getFinalFrom());
    }

    @Test
    void when_find_picking_task_ids_with_total_fields_filter_then_returnFilteredIds() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);

        // Create picking task with specific total values
        PickingTaskItem item1 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku1")
            .title("title1")
            .locationName("location1")
            .pickedQty(5)
            .expectQty(10)
            .pickingSequence(1)
            .prep("prep1")
            .build();

        PickingTaskItem item2 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku2")
            .title("title2")
            .locationName("location2")
            .pickedQty(3)
            .expectQty(8)
            .pickingSequence(2)
            .prep("prep2")
            .build();

        PickingTask pickingTask = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item1, item2))
            .build();

        PickingTask saved = pickingTaskRepository.save(pickingTask);

        // when - test basic search without filters
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        criteria.setDeliveryDate(savedBatch.getTag());
        Pageable pageable = PageRequest.of(0, 10);
        Page<UUID> result = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then
        assertEquals(1, result.getContent().size());
        assertEquals(saved.getId(), result.getContent().getFirst());
    }

    @Test
    void when_find_picking_task_ids_with_total_qty_sort_then_returnSortedIds() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);

        // Create picking tasks with different total quantities
        PickingTaskItem item1 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku1")
            .title("title1")
            .locationName("location1")
            .pickedQty(2)
            .expectQty(5)
            .pickingSequence(1)
            .prep("prep1")
            .build();

        PickingTaskItem item2 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku2")
            .title("title2")
            .locationName("location2")
            .pickedQty(3)
            .expectQty(8)
            .pickingSequence(2)
            .prep("prep2")
            .build();

        PickingTaskItem item3 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku3")
            .title("title3")
            .locationName("location3")
            .pickedQty(1)
            .expectQty(3)
            .pickingSequence(3)
            .prep("prep3")
            .build();

        PickingTask task1 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item1, item2)) // totalQty = 13
            .build();
        task1.calculateTotals();

        PickingTask task2 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item3)) // totalQty = 3
            .build();
        task2.calculateTotals();

        PickingTask savedTask1 = pickingTaskRepository.save(task1);
        PickingTask savedTask2 = pickingTaskRepository.save(task2);

        // when - test ascending sort
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        criteria.setDeliveryDate(savedBatch.getTag());
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.asc("totalQty")));
        Page<UUID> ascendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify ascending order
        assertEquals(2, ascendingResult.getContent().size());
        // Task with totalQty=3 should come first, then task with totalQty=13
        assertEquals(savedTask2.getId(), ascendingResult.getContent().get(0));
        assertEquals(savedTask1.getId(), ascendingResult.getContent().get(1));

        // when - test descending sort
        pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("totalQty")));
        Page<UUID> descendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify descending order
        assertEquals(2, descendingResult.getContent().size());
        // Task with totalQty=13 should come first, then task with totalQty=3
        assertEquals(savedTask1.getId(), descendingResult.getContent().get(0));
        assertEquals(savedTask2.getId(), descendingResult.getContent().get(1));
    }

    @Test
    void when_find_picking_task_ids_with_total_lines_sort_then_returnSortedIds() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);

        // Create picking tasks with different numbers of lines
        PickingTaskItem item1 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku1")
            .title("title1")
            .locationName("location1")
            .pickedQty(2)
            .expectQty(5)
            .pickingSequence(1)
            .prep("prep1")
            .build();

        PickingTaskItem item2 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku2")
            .title("title2")
            .locationName("location2")
            .pickedQty(3)
            .expectQty(8)
            .pickingSequence(2)
            .prep("prep2")
            .build();

        PickingTaskItem item3 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku3")
            .title("title3")
            .locationName("location3")
            .pickedQty(1)
            .expectQty(3)
            .pickingSequence(3)
            .prep("prep3")
            .build();

        PickingTask task1 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item1)) // 1 line
            .build();
        task1.calculateTotals();

        PickingTask task2 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item2, item3)) // 2 lines
            .build();
        task2.calculateTotals();

        PickingTask savedTask1 = pickingTaskRepository.save(task1);
        PickingTask savedTask2 = pickingTaskRepository.save(task2);

        // when - test ascending sort
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        criteria.setDeliveryDate(savedBatch.getTag());
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.asc("totalLines")));
        Page<UUID> ascendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify ascending order
        assertEquals(2, ascendingResult.getContent().size());
        // Task with 1 line should come first, then task with 2 lines
        assertEquals(savedTask1.getId(), ascendingResult.getContent().get(0));
        assertEquals(savedTask2.getId(), ascendingResult.getContent().get(1));

        // when - test descending sort
        pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("totalLines")));
        Page<UUID> descendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify descending order
        assertEquals(2, descendingResult.getContent().size());
        // Task with 2 lines should come first, then task with 1 line
        assertEquals(savedTask2.getId(), descendingResult.getContent().get(0));
        assertEquals(savedTask1.getId(), descendingResult.getContent().get(1));
    }

    @Test
    void when_find_picking_task_ids_with_total_picked_qty_sort_then_returnSortedIds() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);

        // Create picking tasks with different total picked quantities
        PickingTaskItem item1 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku1")
            .title("title1")
            .locationName("location1")
            .pickedQty(5)
            .expectQty(10)
            .pickingSequence(1)
            .prep("prep1")
            .build();

        PickingTaskItem item2 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku2")
            .title("title2")
            .locationName("location2")
            .pickedQty(3)
            .expectQty(8)
            .pickingSequence(2)
            .prep("prep2")
            .build();

        PickingTaskItem item3 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku3")
            .title("title3")
            .locationName("location3")
            .pickedQty(8)
            .expectQty(12)
            .pickingSequence(3)
            .prep("prep3")
            .build();

        PickingTask task1 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item1, item2)) // totalPickedQty = 8
            .build();
        task1.calculateTotals();

        PickingTask task2 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item3)) // totalPickedQty = 8
            .build();
        task2.calculateTotals();

        PickingTask savedTask1 = pickingTaskRepository.save(task1);
        PickingTask savedTask2 = pickingTaskRepository.save(task2);

        // when - test ascending sort
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        criteria.setDeliveryDate(savedBatch.getTag());
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.asc("totalPickedQty")));
        Page<UUID> ascendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify ascending order
        assertEquals(2, ascendingResult.getContent().size());
        // Both tasks have same totalPickedQty=8, order may vary but both should be included
        assertTrue(ascendingResult.getContent().contains(savedTask1.getId()));
        assertTrue(ascendingResult.getContent().contains(savedTask2.getId()));

        // when - test descending sort
        pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("totalPickedQty")));
        Page<UUID> descendingResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify descending order
        assertEquals(2, descendingResult.getContent().size());
        // Both tasks have same totalPickedQty=8, order may vary but both should be included
        assertTrue(descendingResult.getContent().contains(savedTask1.getId()));
        assertTrue(descendingResult.getContent().contains(savedTask2.getId()));
    }

    @Test
    void when_find_picking_task_ids_with_multiple_sort_fields_then_returnSortedIds() {
        // given
        Batch batch = Batch.builder()
            .tag(RandomStringUtils.randomAlphabetic(5))
            .build();
        Batch savedBatch = batchRepository.save(batch);

        // Create picking tasks with same totalQty but different totalPickedQty
        PickingTaskItem item1 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku1")
            .title("title1")
            .locationName("location1")
            .pickedQty(5)
            .expectQty(10)
            .pickingSequence(1)
            .prep("prep1")
            .build();

        PickingTaskItem item2 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku2")
            .title("title2")
            .locationName("location2")
            .pickedQty(3)
            .expectQty(10)
            .pickingSequence(2)
            .prep("prep2")
            .build();

        PickingTaskItem item3 = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber("sku3")
            .title("title3")
            .locationName("location3")
            .pickedQty(8)
            .expectQty(10)
            .pickingSequence(3)
            .prep("prep3")
            .build();

        PickingTask task1 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item1, item2)) // totalQty=20, totalPickedQty=8
            .build();
        task1.calculateTotals();

        PickingTask task2 = PickingTask.builder()
            .batchId(savedBatch.getId())
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MFC)
            .pickingTaskItems(List.of(item3)) // totalQty=10, totalPickedQty=8
            .build();
        task2.calculateTotals();

        PickingTask savedTask1 = pickingTaskRepository.save(task1);
        PickingTask savedTask2 = pickingTaskRepository.save(task2);

        // when - test multiple sort fields: totalQty DESC, totalPickedQty ASC
        PickingTaskSearchCriteria criteria = new PickingTaskSearchCriteria();
        criteria.setDeliveryDate(savedBatch.getTag());
        Pageable pageable = PageRequest.of(0, 10,
            Sort.by(Sort.Order.desc("totalQty"), Sort.Order.asc("totalPickedQty")));
        Page<UUID> multiSortResult = pickingTaskJdbcTemplate.findPickingTaskIds(criteria, pageable);

        // then - verify multi-field sort order
        assertEquals(2, multiSortResult.getContent().size());
        // Task with totalQty=20 should come first, then task with totalQty=10
        assertEquals(savedTask1.getId(), multiSortResult.getContent().get(0));
        assertEquals(savedTask2.getId(), multiSortResult.getContent().get(1));
    }

}