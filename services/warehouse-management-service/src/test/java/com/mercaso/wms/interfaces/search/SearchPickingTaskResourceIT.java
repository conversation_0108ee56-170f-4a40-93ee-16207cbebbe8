package com.mercaso.wms.interfaces.search;


import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTaskItems;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.createBatch;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickingTaskDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchPickingTaskResourceIT extends AbstractIT {

    @Autowired
    PickingTaskResourceApi pickingTaskResourceApi;

    @Autowired
    PickingTaskRepository pickingTaskRepository;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Autowired
    private PickingTaskApplicationEventListener listener;

    @Autowired
    BatchJpaDao batchJpaDao;

    @BeforeEach
    void setUp() {
        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);
    }

    @Test
    void searchPickingTasksV2_sortByDepartment_returnsSortedResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            3,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);

        // Task 0 departments -> "Beverage,Candy"
        java.util.concurrent.atomic.AtomicInteger i0 = new java.util.concurrent.atomic.AtomicInteger();
        pickingTasks.getFirst()
            .getPickingTaskItems()
            .forEach(item -> item.setDepartment(i0.getAndIncrement() % 2 == 0 ? "Beverage" : "Candy"));

        // Task 1 departments -> "Zebra"
        pickingTasks.get(1).getPickingTaskItems().forEach(item -> item.setDepartment("Zebra"));

        // Task 2 departments -> "Apple,Deli"
        java.util.concurrent.atomic.AtomicInteger i2 = new java.util.concurrent.atomic.AtomicInteger();
        pickingTasks.get(2)
            .getPickingTaskItems()
            .forEach(item -> item.setDepartment(i2.getAndIncrement() % 2 == 0 ? "Apple" : "Deli"));
        pickingTasks.forEach(PickingTask::calculateTotals);
        pickingTaskRepository.saveAll(pickingTasks);

        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);

        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            saved.getNumber(),
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.DEPARTMENT_ASC)
        );

        assertNotNull(result);
        assertNotNull(result.getData());
        assertThat(result.getData().size()).isGreaterThanOrEqualTo(3);

        List<String> departments = result.getData().stream()
            .map(PickingTaskDto::getDepartment)
            .toList();

        // Expect ASC by aggregated department string
        assertThat(departments.get(0)).startsWith("Apple");
        assertThat(departments.get(1)).startsWith("Beverage");
        assertThat(departments.get(2)).startsWith("Zebra");
    }

    @Test
    void searchPickingTasks_withValidParameters_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        List<PickingTask> costcoPickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.COSTCO,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        pickingTasks.addAll(costcoPickingTasks);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            DateUtils.getNextDeliveryDate(),
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC),
            null
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals(5, pickingTaskDtoResult.getTotalCount());

        pickingTaskDtoResult.getData().forEach(pickingTaskDto -> result.forEach(pickingTask -> {
            if (pickingTask.getId().equals(pickingTaskDto.getId())) {
                assertEquals(pickingTask.getId(), pickingTaskDto.getId());
                assertEquals(pickingTask.getSource().name(), pickingTaskDto.getSource());
                assertEquals(pickingTask.getStatus(), pickingTaskDto.getStatus());
                assertEquals(pickingTask.getPickingTaskItems().size(), pickingTaskDto.getPickingTaskItems().size());
                pickingTaskDto.getPickingTaskItems().forEach(itemDto -> 
                    assertThat(itemDto.getPickedAt()).isNull());
            }
        }));
    }

    @Test
    void searchPickingTasks_with_numbers_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        List<PickingTask> pickingTaskResult = pickingTaskRepository.findByIds(result.stream().map(PickingTask::getId).toList());

        List<String> numbers = pickingTaskResult.stream().map(PickingTask::getNumber).toList().subList(0, 3);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            numbers,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(3, pickingTaskDtoResult.getData().size());
        assertEquals(3, pickingTaskDtoResult.getTotalCount());
    }

    @Test
    void when_search_picking_tasks_by_delivery_date_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId());

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            LocalDate.parse(saved.getTag()),
            null,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(10, pickingTaskDtoResult.getData().size());
    }

    private void waitForPickingTask(UUID batchId) {
        await().atMost(10, SECONDS)
            .until(() -> {
                List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(batchId);
                return pickingTasks.size() == 10;
            });
    }

    @Test
    void when_search_picking_tasks_by_batch_type_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 10);
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());
        waitForPickingTask(saved.getId());

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            PickingTaskType.BATCH,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(0, pickingTaskDtoResult.getData().size());
    }

    @Test
    void when_search_picking_tasks_by_order_type_then_returnsResult() throws Exception {
        batchJpaDao.deleteAll();
        pickingTaskRepository.deleteAll();
        String orderNumber = RandomStringUtils.randomAlphabetic(10);

        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(orderNumber);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 1);
        batchItems.forEach(batchItem -> batchItem.setOrderNumber(orderNumber));
        batchItemRepository.saveAll(batchItems);

        listener.handleBatchCreatedEvent(saved.getId());

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            PickingTaskType.ORDER,
            null,
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals(100, pickingTaskDtoResult.getData().getFirst().getOrderQty());
    }

    @Test
    void searchPickingTasks_with_departments_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();

        Batch saved = batchRepository.save(createBatch());
        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setDepartment("DEPT1"));

        pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            List.of("DEPT1"),
            null,
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals("DEPT1", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getDepartment());
    }

    @Test
    void searchPickingTasks_with_categories_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setCategory("category1"));

        pickingTaskRepository.saveAll(pickingTasks);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            DateUtils.getNextDeliveryDate(),
            null,
            null,
            List.of("category1"),
            null,
            List.of(SortType.CREATED_AT_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals("category1", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getCategory());
    }

    @Test
    void searchPickingTasks_with_sort_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        for (int i = 0; i < pickingTasks.size(); i++) {
            PickingTask pickingTask = pickingTasks.get(i);
            List<PickingTaskItem> pickingTaskItems = pickingTask.getPickingTaskItems();
            for (PickingTaskItem pickingTaskItem : pickingTaskItems) {
                pickingTaskItem.setAisleNumber("Aisle_" + i);
            }
        }

        pickingTaskRepository.saveAll(pickingTasks);
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.AISLE_NUMBER_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals("Aisle_4", pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getAisleNumber());
    }
    
    @Test
    void searchPickingTasks_with_picker_sort_returnsResult() throws Exception {
    
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        
        for (int i = 0; i < pickingTasks.size(); i++) {
            PickingTask pickingTask = pickingTasks.get(i);
            pickingTask.setPickerUserName("Picker_" + i);
        }
    
        pickingTaskRepository.saveAll(pickingTasks);
        
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.PICKER_USER_NAME_DESC)
        );
    
        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals("Picker_4", pickingTaskDtoResult.getData().getFirst().getPickerUserName());
    }
    

    @Test
    void searchPickingTasksV2_withValidParameters_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        List<PickingTask> costcoPickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.COSTCO,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER);
        pickingTasks.addAll(costcoPickingTasks);

        List<PickingTask> result = pickingTaskRepository.saveAll(pickingTasks);

        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);

        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            saved.getNumber(),
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.BREAKDOWN_NAME_DESC)
        );

        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(5, pickingTaskDtoResult.getData().size());
        assertEquals(5, pickingTaskDtoResult.getTotalCount());

        pickingTaskDtoResult.getData().forEach(pickingTaskDto -> result.forEach(pickingTask -> {
            if (pickingTask.getId().equals(pickingTaskDto.getId())) {
                assertEquals(pickingTask.getId(), pickingTaskDto.getId());
                assertEquals(pickingTask.getSource().name(), pickingTaskDto.getSource());
                assertEquals(pickingTask.getStatus(), pickingTaskDto.getStatus());
                // 新增断言
                assertEquals(pickingTask.getTotalQty(), pickingTaskDto.getTotalQty());
                assertEquals(pickingTask.getTotalLines(), pickingTaskDto.getTotalLines());
                assertEquals(pickingTask.getTotalPickedQty(), pickingTaskDto.getTotalPickedQty());
            }
        }));
    }

    @Test
    void searchPickingTasks_with_skuNumbers_returnsResult() throws Exception {

        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        List<PickingTask> pickingTasks = buildPickingTask(saved.getId(),
            5,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask pickingTask = pickingTasks.getFirst();
    
        List<String> skuNumbers = List.of("DW30960-6", "CO30960-6");
        pickingTask.getPickingTaskItems().forEach(pickingTaskItem -> pickingTaskItem.setSkuNumber(skuNumbers.getFirst()));
    
        pickingTaskRepository.saveAll(pickingTasks);
    
        Result<PickingTaskDto> pickingTaskDtoResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null,
            null,
            null,
            null,
            skuNumbers, 
            List.of(SortType.CREATED_AT_DESC)
        );
    
        assertNotNull(pickingTaskDtoResult);
        assertNotNull(pickingTaskDtoResult.getData());
        assertNotNull(pickingTaskDtoResult.getTotalCount());
        assertEquals(1, pickingTaskDtoResult.getData().size());
        assertEquals(skuNumbers.getFirst(),
            pickingTaskDtoResult.getData().getFirst().getPickingTaskItems().getFirst().getSkuNumber());
    }

    @Test
    void searchPickingTasksV2_withTotalPickedQtySort_returnsSortedResult() throws Exception {
        // given
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        // Create picking tasks with different total picked quantities
        List<PickingTask> pickingTasks = new ArrayList<>();

        // Task 1: totalPickedQty = 5
        PickingTask task1 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task1.getPickingTaskItems().getFirst().setPickedQty(5);
        task1.calculateTotals();
        pickingTasks.add(task1);

        // Task 2: totalPickedQty = 10
        PickingTask task2 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task2.getPickingTaskItems().getFirst().setPickedQty(10);
        task2.calculateTotals();
        pickingTasks.add(task2);

        // Task 3: totalPickedQty = 3
        PickingTask task3 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task3.getPickingTaskItems().getFirst().setPickedQty(3);
        task3.calculateTotals();
        pickingTasks.add(task3);

        pickingTaskRepository.saveAll(pickingTasks);

        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);
        Result<PickingTaskDto> ascendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_PICKED_QTY_ASC)
        );

        // then - verify ascending order
        assertNotNull(ascendingResult);
        assertNotNull(ascendingResult.getData());
        assertEquals(3, ascendingResult.getData().size());
        assertEquals(3, ascendingResult.getData().getFirst().getTotalPickedQty());
        assertEquals(5, ascendingResult.getData().get(1).getTotalPickedQty());
        assertEquals(10, ascendingResult.getData().get(2).getTotalPickedQty());

        // when - test descending sort
        Result<PickingTaskDto> descendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_PICKED_QTY_DESC)
        );

        // then - verify descending order
        assertNotNull(descendingResult);
        assertNotNull(descendingResult.getData());
        assertEquals(3, descendingResult.getData().size());
        assertEquals(10, descendingResult.getData().getFirst().getTotalPickedQty());
        assertEquals(5, descendingResult.getData().get(1).getTotalPickedQty());
        assertEquals(3, descendingResult.getData().get(2).getTotalPickedQty());
    }

    @Test
    void searchPickingTasksV2_withTotalQtySort_returnsSortedResult() throws Exception {
        // given
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        // Create picking tasks with different total quantities
        List<PickingTask> pickingTasks = new ArrayList<>();

        // Task 1: totalQty = 15 (5 + 10)
        PickingTask task1 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task1.getPickingTaskItems().getFirst().setExpectQty(5);
        task1.getPickingTaskItems().get(1).setExpectQty(10);
        task1.calculateTotals();
        pickingTasks.add(task1);

        // Task 2: totalQty = 25 (8 + 17)
        PickingTask task2 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task2.getPickingTaskItems().getFirst().setExpectQty(8);
        task2.getPickingTaskItems().get(1).setExpectQty(17);
        task2.calculateTotals();
        pickingTasks.add(task2);

        // Task 3: totalQty = 12 (3 + 9)
        PickingTask task3 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task3.getPickingTaskItems().getFirst().setExpectQty(3);
        task3.getPickingTaskItems().get(1).setExpectQty(9);
        task3.calculateTotals();
        pickingTasks.add(task3);

        pickingTaskRepository.saveAll(pickingTasks);
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);

        // when - test ascending sort
        Result<PickingTaskDto> ascendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_QTY_ASC)
        );

        // then - verify ascending order
        assertNotNull(ascendingResult);
        assertNotNull(ascendingResult.getData());
        assertEquals(3, ascendingResult.getData().size());
        assertEquals(12, ascendingResult.getData().getFirst().getTotalQty());
        assertEquals(15, ascendingResult.getData().get(1).getTotalQty());
        assertEquals(25, ascendingResult.getData().get(2).getTotalQty());

        // when - test descending sort
        Result<PickingTaskDto> descendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_QTY_DESC)
        );

        // then - verify descending order
        assertNotNull(descendingResult);
        assertNotNull(descendingResult.getData());
        assertEquals(3, descendingResult.getData().size());
        assertEquals(25, descendingResult.getData().getFirst().getTotalQty());
        assertEquals(15, descendingResult.getData().get(1).getTotalQty());
        assertEquals(12, descendingResult.getData().get(2).getTotalQty());
    }

    @Test
    void searchPickingTasksV2_withTotalLinesSort_returnsSortedResult() throws Exception {
        // given
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        // Create picking tasks with different numbers of lines
        List<PickingTask> pickingTasks = new ArrayList<>();

        // Task 1: 1 line
        PickingTask task1 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task1.setPickingTaskItems(buildPickingTaskItems(1));
        task1.calculateTotals();
        pickingTasks.add(task1);

        // Task 2: 3 lines
        PickingTask task2 = buildPickingTask(saved.getId(),
            3,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task2.setPickingTaskItems(buildPickingTaskItems(2));
        task2.calculateTotals();
        pickingTasks.add(task2);

        // Task 3: 2 lines
        PickingTask task3 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task3.setPickingTaskItems(buildPickingTaskItems(3));
        task3.calculateTotals();
        pickingTasks.add(task3);

        pickingTaskRepository.saveAll(pickingTasks);

        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);
        // when - test ascending sort
        Result<PickingTaskDto> ascendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_LINES_ASC)
        );

        // then - verify ascending order
        assertNotNull(ascendingResult);
        assertNotNull(ascendingResult.getData());
        assertEquals(3, ascendingResult.getData().size());
        assertEquals(1, ascendingResult.getData().getFirst().getTotalLines());
        assertEquals(2, ascendingResult.getData().get(1).getTotalLines());
        assertEquals(3, ascendingResult.getData().get(2).getTotalLines());

        // when - test descending sort
        Result<PickingTaskDto> descendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_LINES_DESC)
        );

        // then - verify descending order
        assertNotNull(descendingResult);
        assertNotNull(descendingResult.getData());
        assertEquals(3, descendingResult.getData().size());
        assertEquals(3, descendingResult.getData().getFirst().getTotalLines());
        assertEquals(2, descendingResult.getData().get(1).getTotalLines());
        assertEquals(1, descendingResult.getData().get(2).getTotalLines());
    }

    @Test
    void searchPickingTasksV2_withMultipleSortFields_returnsSortedResult() throws Exception {
        // given
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        // Create picking tasks with the same totalQty but different totalPickedQty
        List<PickingTask> pickingTasks = new ArrayList<>();

        // Task 1: totalQty=20, totalPickedQty=5
        PickingTask task1 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task1.getPickingTaskItems().getFirst().setExpectQty(10);
        task1.getPickingTaskItems().getFirst().setPickedQty(3);
        task1.getPickingTaskItems().get(1).setExpectQty(10);
        task1.getPickingTaskItems().get(1).setPickedQty(2);
        task1.calculateTotals();
        pickingTasks.add(task1);

        // Task 2: totalQty=20, totalPickedQty=15
        PickingTask task2 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task2.getPickingTaskItems().getFirst().setExpectQty(10);
        task2.getPickingTaskItems().getFirst().setPickedQty(8);
        task2.getPickingTaskItems().get(1).setExpectQty(10);
        task2.getPickingTaskItems().get(1).setPickedQty(7);
        task2.calculateTotals();
        pickingTasks.add(task2);

        // Task 3: totalQty=20, totalPickedQty=10
        PickingTask task3 = buildPickingTask(saved.getId(),
            2,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task3.getPickingTaskItems().getFirst().setExpectQty(10);
        task3.getPickingTaskItems().getFirst().setPickedQty(5);
        task3.getPickingTaskItems().get(1).setExpectQty(10);
        task3.getPickingTaskItems().get(1).setPickedQty(5);
        task3.calculateTotals();
        pickingTasks.add(task3);

        pickingTaskRepository.saveAll(pickingTasks);
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);
        // when - test multiple sort fields: totalQty DESC, totalPickedQty ASC
        Result<PickingTaskDto> multiSortResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_LINES_ASC, SortType.TOTAL_PICKED_QTY_ASC)
        );

        // then - verify multi-field sort order
        assertNotNull(multiSortResult);
        assertNotNull(multiSortResult.getData());
        assertEquals(3, multiSortResult.getData().size());

        // All tasks have same totalQty=20, so should be sorted by totalPickedQty ASC
        assertEquals(5, multiSortResult.getData().getFirst().getTotalPickedQty());
        assertEquals(10, multiSortResult.getData().get(1).getTotalPickedQty());
        assertEquals(15, multiSortResult.getData().get(2).getTotalPickedQty());
    }

    @Test
    void searchPickingTasksV2_withNullValuesInSortFields_returnsSortedResult() throws Exception {
        // given
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());

        // Create picking tasks with some null values
        List<PickingTask> pickingTasks = new ArrayList<>();

        // Task 1: null totalQty (empty items)
        List<PickingTask> tasks = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER);
        PickingTask task1 = tasks.getFirst();
        task1.calculateTotals();
        pickingTasks.add(task1);

        // Task 2: totalQty = 10
        PickingTask task2 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task2.getPickingTaskItems().getFirst().setExpectQty(10);
        task2.calculateTotals();
        pickingTasks.add(task2);

        // Task 3: totalQty = 5
        PickingTask task3 = buildPickingTask(saved.getId(),
            1,
            SourceEnum.MFC,
            PickingTaskStatus.CREATED,
            PickingTaskType.ORDER).getFirst();
        task3.getPickingTaskItems().getFirst().setExpectQty(5);
        task3.calculateTotals();
        pickingTasks.add(task3);

        pickingTaskRepository.saveAll(pickingTasks);

        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);
        // when - test ascending sort with null values
        Result<PickingTaskDto> ascendingResult = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            List.of(SortType.TOTAL_QTY_ASC)
        );

        // then - verify ascending order (nulls should be handled properly)
        assertNotNull(ascendingResult);
        assertNotNull(ascendingResult.getData());
        assertEquals(3, ascendingResult.getData().size());
        // The order depends on how the database handles nulls in sorting
        // Typically nulls come first in ASC order
        assertNotNull(ascendingResult.getData().getFirst().getTotalQty());
        assertNotNull(ascendingResult.getData().get(1).getTotalQty());
        assertNotNull(ascendingResult.getData().get(2).getTotalQty());
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskParameter_acceptsParameter() throws Exception {
        // Given
        Boolean isSingleItemTask = true;

        // When & Then - should not throw exception
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            isSingleItemTask
        );

        // Then
        assertThat(result).isNotNull();
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskTrue_oldLogic_returnsOnlySingleItemTasks() throws Exception {
        // Given - test old logic (feature flag off)
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(false);

        // Create test data
        pickingTaskRepository.deleteAll();

        // Create batch with today's delivery date
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            LocalDate.parse(saved.getTag()), // Use the batch's delivery date
            null,
            null,
            null,
            null,
            null,
            true // isSingleItemTask = true
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getData()).hasSize(1);
        assertThat(result.getData().getFirst().getId()).isEqualTo(savedTasks.getFirst().getId());
        assertThat(result.getData().getFirst().getPickingTaskItems()).allMatch(item -> item.getExpectQty() == 1);
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskTrue_newLogic_returnsOnlySingleItemTasks() throws Exception {
        // Given - test new logic (feature flag on)
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(true);

        // Create test data
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            LocalDate.parse(saved.getTag()), // Use the batch's delivery date
            null,
            null,
            null,
            null,
            null,
            true // isSingleItemTask = true
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getData()).hasSize(1);
        assertThat(result.getData().getFirst().getId()).isEqualTo(savedTasks.getFirst().getId());
        assertThat(result.getData().getFirst().getPickingTaskItems()).allMatch(item -> item.getExpectQty() == 1);
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskFalse_returnsAllTasks() throws Exception {
        // Given
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(false);

        // Create test data
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            LocalDate.parse(saved.getTag()), // Use the batch's delivery date
            null,
            null,
            null,
            null,
            null,
            false // isSingleItemTask = false
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getData()).hasSize(2); // Should return both tasks
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskNull_returnsAllTasks() throws Exception {
        // Given
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(false);

        // Create test data
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            LocalDate.parse(saved.getTag()), // Use the batch's delivery date
            null,
            null,
            null,
            null,
            null,
            null // isSingleItemTask = null
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getData()).hasSize(2); // Should return both tasks
    }

    @Test
    void searchPickingTasks_withIsSingleItemTaskTrue_simpleTest() throws Exception {
        // Given - test old logic (feature flag off)
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(false);

        // Create test data
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When - search without delivery date to avoid automatic date setting
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            null, // No delivery date
            null,
            null,
            null,
            null,
            null,
            true // isSingleItemTask = true
        );

        // Then
        assertThat(result).isNotNull();
        System.out.println("Query returned " + result.getData().size() + " results");
        // Note: This might still return 0 results due to delivery date logic, but we're testing the parameter acceptance
    }

    @Test
    void searchPickingTasks_withoutIsSingleItemTask_returnsAllTasks() throws Exception {
        // Given - test old logic (feature flag off)
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_SEARCH_PICKING_TASK_LOGIC))
            .thenReturn(false);

        // Create test data
        pickingTaskRepository.deleteAll();
        Batch saved = batchRepository.save(createBatch());
        System.out.println("Created batch with tag: " + saved.getTag());

        // Create single item task (expectQty = 1)
        PickingTask singleItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        singleItemTask.getPickingTaskItems().forEach(item -> item.setExpectQty(1));

        // Create multi item task (expectQty = 2)
        PickingTask multiItemTask = buildPickingTask(saved.getId(), 1, SourceEnum.MFC,
            PickingTaskStatus.CREATED, PickingTaskType.ORDER).getFirst();
        multiItemTask.getPickingTaskItems().getFirst().setExpectQty(2);

        List<PickingTask> savedTasks = pickingTaskRepository.saveAll(List.of(singleItemTask, multiItemTask));
        System.out.println("Saved " + savedTasks.size() + " picking tasks");

        // When - search without isSingleItemTask filter
        Result<PickingTaskDto> result = pickingTaskResourceApi.searchPickingTasks(
            null,
            null,
            null,
            SourceEnum.MFC,
            new PickingTaskStatus[]{PickingTaskStatus.CREATED},
            LocalDate.parse(saved.getTag()),
            null,
            null,
            null,
            null,
            null,
            null // isSingleItemTask = null
        );

        // Then
        assertThat(result).isNotNull();
        System.out.println("Query returned " + result.getData().size() + " results");
        assertThat(result.getData()).hasSize(2); // Should return both tasks
    }
    

    
}