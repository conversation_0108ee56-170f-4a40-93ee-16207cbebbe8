package com.mercaso.wms.batch.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class BreakdownNoUtilTest {

    @Test
    void whenInputIsNullThenReturnAA() {
        assertEquals("[AA]", BreakdownNoUtil.nextLetterBreakdown(null));
    }

    @Test
    void whenInputAAThenReturnAB() {
        assertEquals("[AB]", BreakdownNoUtil.nextLetterBreakdown("[AA]"));
    }

    @Test
    void whenInputIsABThenReturnAC() {
        assertEquals("[AC]", BreakdownNoUtil.nextLetterBreakdown("[AB]"));
    }

    @Test
    void whenInputIsAZThenReturnBA() {
        assertEquals("[BA]", BreakdownNoUtil.nextLetterBreakdown("[AZ]"));
    }

    @Test
    void whenInputIsBAThenReturnBB() {
        assertEquals("[BB]", BreakdownNoUtil.nextLetterBreakdown("[BA]"));
    }

    @Test
    void whenInputIsBZThenReturnCA() {
        assertEquals("[CA]", BreakdownNoUtil.nextLetterBreakdown("[BZ]"));
    }

    @Test
    void whenInputIsCAThenReturnCB() {
        assertEquals("[CB]", BreakdownNoUtil.nextLetterBreakdown("[CA]"));
    }

    @Test
    void whenInputIsCZThenReturnDA() {
        assertEquals("[DA]", BreakdownNoUtil.nextLetterBreakdown("[CZ]"));
    }

    @Test
    void whenInputIsDBThenReturnDC() {
        assertEquals("[DC]", BreakdownNoUtil.nextLetterBreakdown("[DB]"));
    }

    @Test
    void whenInputIsEAThenReturnDZ() {
        assertEquals("[EA]", BreakdownNoUtil.nextLetterBreakdown("[DZ]"));
    }

    @Test
    void whenInputIsEZThenReturnFA() {
        assertEquals("[FA]", BreakdownNoUtil.nextLetterBreakdown("[EZ]"));
    }

    @Test
    void when_input_000_then_return_001() {
        assertEquals("001", BreakdownNoUtil.nextNumberBreakdown("000"));
    }

    @Test
    void when_input_001_then_return_002() {
        assertEquals("002", BreakdownNoUtil.nextNumberBreakdown("001"));
    }

    @Test
    void when_input_099_then_return_100() {
        assertEquals("100", BreakdownNoUtil.nextNumberBreakdown("099"));
    }

    @Test
    void when_input_999_then_return_1000() {
        assertEquals("1000", BreakdownNoUtil.nextNumberBreakdown("999"));
    }

    @Test
    void when_input_1000_then_return_1001() {
        assertEquals("1001", BreakdownNoUtil.nextNumberBreakdown("1000"));
    }

}