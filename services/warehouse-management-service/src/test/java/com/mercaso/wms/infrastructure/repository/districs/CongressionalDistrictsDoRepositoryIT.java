package com.mercaso.wms.infrastructure.repository.districs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class CongressionalDistrictsDoRepositoryIT extends AbstractIT {

    @Autowired
    private CongressionalDistrictsRepository congressionalDistrictsRepository;

    @Test
    void when_create_congressional_district_then_return_congressional_district() {
        CongressionalDistrictsDo congressionalDistricts = new CongressionalDistrictsDo();
        congressionalDistricts.setZipCode("12345");
        congressionalDistricts.setPostalAreaName("postalAreaName");
        congressionalDistricts.setDistrictName("districtName");
        congressionalDistricts.setDistrictNumber(1);

        CongressionalDistrictsDo saved = congressionalDistrictsRepository.save(congressionalDistricts);

        Optional<CongressionalDistrictsDo> result = congressionalDistrictsRepository.findById(saved.getId());

        assertNotNull(result.get());
        assertEquals(congressionalDistricts.getZipCode(), result.get().getZipCode());
    }

}
