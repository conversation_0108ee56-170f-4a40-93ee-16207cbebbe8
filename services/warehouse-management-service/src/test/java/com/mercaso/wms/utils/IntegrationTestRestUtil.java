package com.mercaso.wms.utils;

import static org.springframework.http.HttpMethod.PUT;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.List;
import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;


public abstract class IntegrationTestRestUtil {

    private final LocalHostUriTemplateHandler uriTemplateHandler;

    private final RestTemplate restTemplate = new RestTemplate();

    private final ObjectMapper objectMapper = new ObjectMapper()
        .registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    public IntegrationTestRestUtil(Environment environment) {
        this.uriTemplateHandler = new LocalHostUriTemplateHandler(environment);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
    }

    private HttpHeaders createHeaders() {
        return createHeaders(MediaType.APPLICATION_JSON);
    }

    private HttpHeaders createHeaders(MediaType type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        return headers;
    }

    public void createShopifyWebhook(String path, Object payload) {
        HttpHeaders headers = createHeaders();
        headers.add("X-Shopify-Topic", "orders/create");
        headers.add("X-Shopify-Shop-Domain", "dev-2-mercaso.myshopify.com");
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, headers);
        String url = uriTemplateHandler.getRootUri() + path;
        restTemplate.postForObject(url, httpentity, Void.class);
    }

    public <T> T createEntity(String path, Object payload, Class<T> dtoClass) {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForObject(url, httpentity, dtoClass);
    }

    public <T> ResponseEntity<T> postEntity(String path, Object payload, Class<T> dtoClass) {
        HttpEntity<Object> httpEntity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.exchange(url, HttpMethod.POST, httpEntity, dtoClass);
    }

    public <T> T updateEntity(String path, Object payload, Class<T> dtoClass) throws JsonProcessingException {
        String url = uriTemplateHandler.getRootUri() + path;
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, PUT, httpentity, String.class);

        return responseEntity.getBody() != null ? objectMapper.readValue(responseEntity.getBody(), dtoClass) : null;
    }

    public <T> List<T> updateEntities(String path, Object payload, Class<T> dtoClass) throws JsonProcessingException {
        String url = uriTemplateHandler.getRootUri() + path;
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, PUT, httpentity, String.class);

        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(responseEntity.getBody(), listType);
    }



    public <T> ResponseEntity<T> getEntity(String path, Class<T> dtoClass) {
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.getForEntity(url, dtoClass);
    }

    public <T> List<T> getEntityList(String path, Class<T> dtoClass)
        throws Exception {
        String url = uriTemplateHandler.getRootUri() + path;
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(response.getBody(), listType);
    }

}
