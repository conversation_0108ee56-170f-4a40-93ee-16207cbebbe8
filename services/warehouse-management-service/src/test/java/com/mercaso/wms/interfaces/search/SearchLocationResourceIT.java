package com.mercaso.wms.interfaces.search;


import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.utils.LocationResourceApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchLocationResourceIT extends AbstractIT {

    @Autowired
    LocationResourceApi locationResourceApi;

    @Test
    void when_search_location_then_success() throws Exception {
        Result<LocationDto> search = locationResourceApi.search("00-01");
        assertNotNull(search);

        search.getData().forEach(locationDto -> {
            assertNotNull(locationDto);
            assertNotNull(locationDto.getId());
            assertNotNull(locationDto.getName());
            assertTrue(locationDto.getName().contains("00-01"));
        });
    }


}