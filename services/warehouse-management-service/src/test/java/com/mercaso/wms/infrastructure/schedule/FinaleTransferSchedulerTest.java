package com.mercaso.wms.infrastructure.schedule;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.infrastructure.config.BatchProcessingProperties;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class FinaleTransferSchedulerTest {

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private PickingTaskApplicationService pickingTaskApplicationService;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private BatchProcessingProperties batchProcessingProperties;

    @InjectMocks
    private FinaleTransferScheduler scheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void finaleInventoryTransfer_Does_Nothing_When_No_Lock() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        scheduler.finaleInventoryTransfer();

        verifyNoInteractions(batchRepository, pickingTaskApplicationService);
    }

    @Test
    void finaleInventoryTransfer_Does_Nothing_When_Lock_Is_Null() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(null);

        scheduler.finaleInventoryTransfer();

        verifyNoInteractions(batchRepository, pickingTaskApplicationService);
    }

    @Test
    void finaleInventoryTransfer_Does_Nothing_When_No_Batches_Found() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(Collections.emptyList());

        scheduler.finaleInventoryTransfer();

        verify(batchRepository, times(1)).findUntransferredBatchesByDateRange(any(), any());
        verifyNoInteractions(pickingTaskApplicationService);
    }

    @Test
    void finaleInventoryTransfer_Processes_Untransferred_Batches() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.finaleInventoryTransfer();

        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch);
    }

    @Test
    void finaleInventoryTransfer_With_Specific_DeliveryDate() {
        String specificDate = "2024-12-25";
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(2);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(4);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.finaleInventoryTransfer(specificDate);

        verify(batchRepository, times(1)).findUntransferredBatchesByDateRange(any(), any());
        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch);
    }

    @Test
    void finaleInventoryTransfer_With_Feature_Flag_Off_Skips_Transfer() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(false);

        scheduler.finaleInventoryTransfer();

        verify(pickingTaskApplicationService, never()).bulkInventoryTransfer(any(Batch.class));
    }

    @Test
    void finaleInventoryTransfer_With_Multiple_Batches() {
        UUID batchId1 = UUID.randomUUID();
        UUID batchId2 = UUID.randomUUID();
        UUID batchId3 = UUID.randomUUID();
        
        Batch batch1 = buildBatch(batchId1);
        Batch batch2 = buildBatch(batchId2);
        Batch batch3 = buildBatch(batchId3);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch1, batch2, batch3));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.finaleInventoryTransfer();

        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch1);
        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch2);
        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch3);
    }

    @Test
    void finaleInventoryTransfer_With_Custom_Configuration_Values() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(7);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(1);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.finaleInventoryTransfer();

        verify(batchProcessingProperties, times(2)).getDaysBefore(); // Called twice: once for date range calculation, once for logging
        verify(batchProcessingProperties, times(2)).getDaysAfter(); // Called twice: once for date range calculation, once for logging
        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch);
    }

    @Test
    void finaleInventoryTransfer_With_Zero_Days_Configuration() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(0);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(0);
        when(batchRepository.findUntransferredBatchesByDateRange(any(), any())).thenReturn(List.of(batch));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.finaleInventoryTransfer();

        verify(batchProcessingProperties, times(2)).getDaysBefore(); // Called twice: once for date range calculation, once for logging
        verify(batchProcessingProperties, times(2)).getDaysAfter(); // Called twice: once for date range calculation, once for logging
        verify(pickingTaskApplicationService, times(1)).bulkInventoryTransfer(batch);
    }
}
