package com.mercaso.wms.utils;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.FinaleAvailableStockItemsOnHandDto;
import com.mercaso.data.client.dto.Location;
import com.mercaso.data.client.dto.ShopifyOrderDto;
import com.mercaso.data.client.dto.ShopifyOrderLineItemDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.RandomStringUtils;

public class DataServiceUtil {

    public static List<ShopifyOrderDto> mockGetShopifyOrders() {
        List<ShopifyOrderDto> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ShopifyOrderDto shopifyOrderDto = new ShopifyOrderDto();
            shopifyOrderDto.setName("M-" + RandomStringUtils.randomNumeric(5));
            List<ShopifyOrderLineItemDto> lineItems = new ArrayList<>();
            for (int j = 0; j < 10; j++) {
                ShopifyOrderLineItemDto shopifyOrderLineItemDto = new ShopifyOrderLineItemDto();
                shopifyOrderLineItemDto.setName(RandomStringUtils.randomAlphabetic(20));
                shopifyOrderLineItemDto.setQuantity(i);
                shopifyOrderLineItemDto.setName(RandomStringUtils.randomAlphabetic(20));
                shopifyOrderLineItemDto.setSku("JC" + RandomStringUtils.randomNumeric(5));
                lineItems.add(shopifyOrderLineItemDto);
            }
            shopifyOrderDto.setLineItems(lineItems);
            list.add(shopifyOrderDto);
        }
        return list;
    }

    public static FinaleAvailableStockDto mockGetFinaleProducts(String sku) {
        FinaleAvailableStockDto finaleAvailableStockDto = new FinaleAvailableStockDto();
        finaleAvailableStockDto.setName(RandomStringUtils.randomAlphabetic(20));
        finaleAvailableStockDto.setMfcQoh(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
        finaleAvailableStockDto.setSku(sku);
        FinaleAvailableStockItemsOnHandDto finaleAvailableStockItemsOnHandDto = new FinaleAvailableStockItemsOnHandDto();
        finaleAvailableStockItemsOnHandDto.setQuantityOnHand(Objects.requireNonNull(finaleAvailableStockDto.getMfcQoh())
            .longValue());
        Location location = new Location();
        location.setName("00-".concat(RandomStringUtils.randomNumeric(2)) + "-A-1");
        finaleAvailableStockItemsOnHandDto.setSubLocation(location);
        finaleAvailableStockDto.setStockItemsOnHand(List.of(finaleAvailableStockItemsOnHandDto));
        return finaleAvailableStockDto;
    }

}
