package com.mercaso.wms.delivery.infrastructure.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.infrastructure.exception.SingleDeviceLoginException;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LoginVersionServiceTest {

    @Mock
    private AccountRepository accountRepository;

    @InjectMocks
    private LoginVersionService loginVersionService;

    private UUID testUserId;
    private Account testAccount;
    private String userIdString;
    private String currentTimestamp;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        userIdString = testUserId.toString();
        currentTimestamp = String.valueOf(Instant.now().toEpochMilli());
        testAccount = Account.builder()
            .id(UUID.randomUUID())
            .userId(testUserId)
            .lastLoginTime(Instant.ofEpochMilli(Long.parseLong(currentTimestamp)))
            .build();
    }

    @Test
    @DisplayName("When validateLoginVersion with null userId Then throw SingleDeviceLoginException")
    void when_validateLoginVersion_with_null_userId_then_throw_SingleDeviceLoginException() {
        // Act & Assert
        SingleDeviceLoginException exception = assertThrows(SingleDeviceLoginException.class,
            () -> loginVersionService.validateLoginVersion(null, currentTimestamp));

        assertEquals("User ID or login version is missing", exception.getMessage());
        verify(accountRepository, never()).findByUserId(any());
    }

    @Test
    @DisplayName("When validateLoginVersion with null tokenLoginVersion Then throw SingleDeviceLoginException")
    void when_validateLoginVersion_with_null_tokenLoginVersion_then_throw_SingleDeviceLoginException() {
        // Act & Assert
        SingleDeviceLoginException exception = assertThrows(SingleDeviceLoginException.class,
            () -> loginVersionService.validateLoginVersion(userIdString, null));

        assertEquals("User ID or login version is missing", exception.getMessage());
        verify(accountRepository, never()).findByUserId(any());
    }

    @Test
    @DisplayName("When validateLoginVersion with invalid userId format Then throw SingleDeviceLoginException")
    void when_validateLoginVersion_with_invalid_userId_format_then_throw_SingleDeviceLoginException() {
        // Arrange
        String invalidUuid = "invalid-uuid";

        // Act & Assert
        SingleDeviceLoginException exception = assertThrows(SingleDeviceLoginException.class,
            () -> loginVersionService.validateLoginVersion(invalidUuid, currentTimestamp));

        assertEquals("Invalid user ID format", exception.getMessage());
        verify(accountRepository, never()).findByUserId(any());
    }

    @Test
    @DisplayName("When validateLoginVersion with non-existent userId Then skip")
    void when_validateLoginVersion_with_non_existent_userId_then_skip() {
        // Arrange
        when(accountRepository.findByUserId(testUserId)).thenReturn(Optional.empty());

        // Act & Assert
        loginVersionService.validateLoginVersion(userIdString, currentTimestamp);

        verify(accountRepository, times(1)).findByUserId(testUserId);
    }

    @Test
    @DisplayName("When validateLoginVersion with newer token version Then update account login time")
    void when_validateLoginVersion_with_newer_token_version_then_update_account_login_time() {
        // Arrange
        String newerTimestamp = String.valueOf(Instant.now().plusMillis(5000).toEpochMilli());
        Account accountWithOlderTimestamp = Account.builder()
            .id(UUID.randomUUID())
            .userId(testUserId)
            .lastLoginTime(Instant.ofEpochMilli(Long.parseLong(currentTimestamp)))
            .build();

        when(accountRepository.findByUserId(testUserId)).thenReturn(Optional.of(accountWithOlderTimestamp));

        // Act
        loginVersionService.validateLoginVersion(userIdString, newerTimestamp);

        // Assert
        verify(accountRepository, times(1)).findByUserId(testUserId);
        verify(accountRepository, times(1)).save(accountWithOlderTimestamp);
        assertEquals(Instant.ofEpochMilli(Long.parseLong(newerTimestamp)), accountWithOlderTimestamp.getLastLoginTime());
    }

    @Test
    @DisplayName("When validateLoginVersion with older token version Then throw SingleDeviceLoginException")
    void when_validateLoginVersion_with_older_token_version_then_throw_SingleDeviceLoginException() {
        // Arrange
        String olderTimestamp = String.valueOf(Instant.now().minusMillis(5000).toEpochMilli());
        Account accountWithNewerTimestamp = Account.builder()
            .id(UUID.randomUUID())
            .userId(testUserId)
            .lastLoginTime(Instant.ofEpochMilli(Long.parseLong(currentTimestamp)))
            .build();

        when(accountRepository.findByUserId(testUserId)).thenReturn(Optional.of(accountWithNewerTimestamp));

        // Act & Assert
        SingleDeviceLoginException exception = assertThrows(SingleDeviceLoginException.class,
            () -> loginVersionService.validateLoginVersion(userIdString, olderTimestamp));

        verify(accountRepository, times(1)).findByUserId(testUserId);
        verify(accountRepository, never()).save(any());
        assertEquals(userIdString, exception.getUserId());
        assertEquals(olderTimestamp, exception.getTokenVersion());
        assertEquals(String.valueOf(accountWithNewerTimestamp.getLastLoginTime().toEpochMilli()), exception.getDbVersion());
    }

    @Test
    @DisplayName("When validateLoginVersion with equal token version Then validation succeeds")
    void when_validateLoginVersion_with_equal_token_version_then_validation_succeeds() {
        // Arrange
        when(accountRepository.findByUserId(testUserId)).thenReturn(Optional.of(testAccount));

        // Act
        loginVersionService.validateLoginVersion(userIdString, currentTimestamp);

        // Assert
        verify(accountRepository, times(1)).findByUserId(testUserId);
        verify(accountRepository, never()).save(any());
    }

    @Test
    @DisplayName("When validateLoginVersion with account having null lastLoginTime Then update account login time")
    void when_validateLoginVersion_with_account_having_null_lastLoginTime_then_update_account_login_time() {
        // Arrange
        Account accountWithNullLastLoginTime = Account.builder()
            .id(UUID.randomUUID())
            .userId(testUserId)
            .lastLoginTime(null)
            .build();

        when(accountRepository.findByUserId(testUserId)).thenReturn(Optional.of(accountWithNullLastLoginTime));

        // Act
        loginVersionService.validateLoginVersion(userIdString, currentTimestamp);

        // Assert
        verify(accountRepository, times(1)).findByUserId(testUserId);
        verify(accountRepository, times(1)).save(accountWithNullLastLoginTime);
        assertEquals(Instant.ofEpochMilli(Long.parseLong(currentTimestamp)), accountWithNullLastLoginTime.getLastLoginTime());
    }

    @Test
    @DisplayName("When compareLoginVersions with null second version Then return positive value")
    void when_compareLoginVersions_with_null_second_version_then_return_positive_value() {
        // Act
        int result = loginVersionService.compareLoginVersions(currentTimestamp, null);

        // Assert
        assertEquals(1, result);
    }

    @Test
    @DisplayName("When compareLoginVersions with invalid version format Then throw IllegalArgumentException")
    void when_compareLoginVersions_with_invalid_version_format_then_throw_IllegalArgumentException() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class,
            () -> loginVersionService.compareLoginVersions("not-a-number", currentTimestamp));

        assertThrows(IllegalArgumentException.class,
            () -> loginVersionService.compareLoginVersions(currentTimestamp, "not-a-number"));
    }

    @Test
    @DisplayName("When compareLoginVersions with first greater than second Then return positive value")
    void when_compareLoginVersions_with_first_greater_than_second_then_return_positive_value() {
        // Arrange
        String v1 = "1000";
        String v2 = "500";

        // Act
        int result = loginVersionService.compareLoginVersions(v1, v2);

        // Assert
        assertEquals(1, result);
    }

    @Test
    @DisplayName("When compareLoginVersions with first less than second Then return negative value")
    void when_compareLoginVersions_with_first_less_than_second_then_return_negative_value() {
        // Arrange
        String v1 = "500";
        String v2 = "1000";

        // Act
        int result = loginVersionService.compareLoginVersions(v1, v2);

        // Assert
        assertEquals(-1, result);
    }

    @Test
    @DisplayName("When compareLoginVersions with equal versions Then return zero")
    void when_compareLoginVersions_with_equal_versions_then_return_zero() {
        // Arrange
        String v1 = "1000";
        String v2 = "1000";

        // Act
        int result = loginVersionService.compareLoginVersions(v1, v2);

        // Assert
        assertEquals(0, result);
    }
} 