package com.mercaso.wms.delivery.interfaces.query;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryShopifyOrderDto;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDocument;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.dto.shopify.ShopifyOrderForDeliveryDto;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject.DeliveryOrderDo;
import com.mercaso.wms.delivery.utils.DeliveryOrderWebhookResourceApi;
import com.mercaso.wms.delivery.utils.DocumentResourceApi;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryDeliveryDocumentResourceIT extends AbstractIT {

    @Autowired
    DocumentResourceApi documentResourceApi;
    @Autowired
    DeliveryDocumentRepository deliveryDocumentRepository;
    @Autowired
    DeliveryOrderWebhookResourceApi deliveryOrderWebhookResourceApi;

    @Test
    void when_find_documents_then_return_documents() throws Exception {
        DeliveryDocument deliveryDocument = buildDocument();
        DeliveryDocument invoiceDeliveryDocument = buildDocument();
        invoiceDeliveryDocument.setEntityId(deliveryDocument.getEntityId());
        invoiceDeliveryDocument.setDocumentType(DeliveryDocumentType.INVOICE);
        deliveryDocumentRepository.save(deliveryDocument);
        deliveryDocumentRepository.save(invoiceDeliveryDocument);

        when(documentOperations.getSignedUrl(any())).thenReturn(RandomStringUtils.randomAlphabetic(10));

        List<DeliveryDocumentDto> invoiceDocuments = documentResourceApi.findDocuments(deliveryDocument.getEntityId(),
            EntityEnums.valueOf(deliveryDocument.getEntityName()), DeliveryDocumentType.INVOICE);

        assertNotNull(invoiceDocuments);
        assertEquals(1, invoiceDocuments.size());
        assertEquals(invoiceDeliveryDocument.getEntityId(), invoiceDocuments.getFirst().getEntityId());
        assertEquals(invoiceDeliveryDocument.getEntityName(), invoiceDocuments.getFirst().getEntityName());
        assertEquals(invoiceDeliveryDocument.getFileName(), invoiceDocuments.getFirst().getFileName());

        List<DeliveryDocumentDto> allDocuments = documentResourceApi.findDocuments(deliveryDocument.getEntityId(),
            EntityEnums.valueOf(deliveryDocument.getEntityName()), null);

        assertNotNull(allDocuments);
        assertEquals(2, allDocuments.size());
    }

    @Test
    void when_find_documents_by_delivery_order_id_then_return() throws Exception {
        ShopifyOrderForDeliveryDto shopifyOrderDto = buildDeliveryShopifyOrderDto();

        when(documentOperations.uploadDocument(any())).thenReturn(new DocumentResponse(
            "path/to/invoice.pdf",
            "invoice.pdf"));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());
        deliveryOrderWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderDo deliveryOrderDo = deliveryOrderJpaDao.findByOrderNumberAndShopifyOrderId(shopifyOrderDto.getName(),
            shopifyOrderDto.getId());

        deliveryOrderDo.getDeliveryOrderItems().getFirst().setReasonCode("RETURNS");
        deliveryOrderDo.getDeliveryOrderItems().getLast().setReasonCode("EXPIRED");

        deliveryOrderJpaDao.save(deliveryOrderDo);

        DeliveryDocument returnsDeliveryDocument = buildDocument();
        returnsDeliveryDocument.setDocumentType(DeliveryDocumentType.RETURNS);
        returnsDeliveryDocument.setEntityName(EntityEnums.DELIVERY_ORDER_ITEM.name());
        returnsDeliveryDocument.setEntityId(deliveryOrderDo.getDeliveryOrderItems().getFirst().getId());

        DeliveryDocument expiredDeliveryDocument = buildDocument();
        expiredDeliveryDocument.setEntityId(deliveryOrderDo.getDeliveryOrderItems().getLast().getId());
        expiredDeliveryDocument.setDocumentType(DeliveryDocumentType.EXPIRED);
        expiredDeliveryDocument.setEntityName(EntityEnums.DELIVERY_ORDER_ITEM.name());
        deliveryDocumentRepository.save(returnsDeliveryDocument);
        deliveryDocumentRepository.save(expiredDeliveryDocument);

        when(documentOperations.getSignedUrl(any())).thenReturn(RandomStringUtils.randomAlphabetic(10));

        List<DeliveryDocumentDto> itemDocuments = documentResourceApi.findItemDocuments(deliveryOrderDo.getId());

        assertNotNull(itemDocuments);
        assertEquals(2, itemDocuments.size());
        assertEquals(returnsDeliveryDocument.getEntityId(), itemDocuments.getFirst().getEntityId());
        assertEquals(expiredDeliveryDocument.getEntityId(), itemDocuments.getLast().getEntityId());
    }
}