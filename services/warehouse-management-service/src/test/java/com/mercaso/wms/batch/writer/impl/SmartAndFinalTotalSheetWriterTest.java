package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class SmartAndFinalTotalSheetWriterTest extends Writer {

    private final SmartAndFinalTotalSheetWriter smartAndFinalTotalSheetWriter = new SmartAndFinalTotalSheetWriter();

    @Test
    void when_write_smart_and_final_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> smartAndFinals = Lists.newArrayList();
        ExcelBatchDto excelBatchDto = new ExcelBatchDto();
        excelBatchDto.setItemNumber("smart");
        excelBatchDto.setFrom("Location2");
        excelBatchDto.setQuantity(1);
        excelBatchDto.setSource(SourceEnum.SMART_AND_FINAL.name());

        ExcelBatchDto excelBatchDto1 = new ExcelBatchDto();
        excelBatchDto1.setItemNumber("smart1");
        excelBatchDto1.setFrom("Location2");
        excelBatchDto1.setQuantity(1);
        excelBatchDto1.setSource(SourceEnum.SMART_AND_FINAL.name());

        ExcelBatchDto excelBatchDto2 = new ExcelBatchDto();
        excelBatchDto2.setItemNumber("smart1");
        excelBatchDto2.setFrom("Location2");
        excelBatchDto2.setQuantity(1);
        excelBatchDto2.setSource(SourceEnum.SMART_AND_FINAL.name());

        smartAndFinals.add(excelBatchDto);
        smartAndFinals.add(excelBatchDto1);
        smartAndFinals.add(excelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.SMART_AND_FINAL.name(), smartAndFinals));

        writeBatchTemplate(condition, smartAndFinalTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> totals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SMART_AND_FINAL_TOTALS.getValue()).doReadSync();

        assertEquals(5, totals.size());
        assertEquals("1", totals.get(4).get(6));
        assertEquals("2", totals.get(3).get(6));
    }

    @Test
    void when_write_empty_tobacco_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> smartAndFinals = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.SMART_AND_FINAL.name(), smartAndFinals));

        writeBatchTemplate(condition, smartAndFinalTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> totals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SMART_AND_FINAL_TOTALS.getValue()).doReadSync();

        assertEquals(3, totals.size());
    }

}