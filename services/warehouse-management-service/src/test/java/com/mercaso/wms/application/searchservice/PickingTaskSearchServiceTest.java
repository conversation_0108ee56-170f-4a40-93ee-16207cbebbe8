package com.mercaso.wms.application.searchservice;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.PickedItemsDto;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.PickingTaskJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PickingTaskSearchServiceTest {

    @Mock
    private PickingTaskJdbcTemplate pickingTaskJdbcTemplate;

    @InjectMocks
    private PickingTaskSearchService pickingTaskSearchService;

    @Test
    void pickedItemsExport_Success() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedItemsDto> mockDtos = Arrays.asList(
            createMockPickedItemsDto("Item1", 10),
            createMockPickedItemsDto("Item2", 20)
        );
        when(pickingTaskJdbcTemplate.fetchPickedItemsForDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = pickingTaskSearchService.pickedItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        verify(pickingTaskJdbcTemplate).fetchPickedItemsForDeliveryDate(deliveryDate);
    }

    @Test
    void pickedItemsExport_with_duplicate_line_success() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedItemsDto> mockDtos = new ArrayList<>();
        PickedItemsDto item1 = createMockPickedItemsDto("Item1", 10);
        PickedItemsDto item2 = createMockPickedItemsDto("Item2", 20);
        item1.setOrderNumber("123");
        item1.setLine("1");
        item1.setItemNumber("1");

        item2.setOrderNumber("123");
        item2.setLine("1");
        item2.setItemNumber("1");

        mockDtos.add(item1);
        mockDtos.add(item2);

        when(pickingTaskJdbcTemplate.fetchPickedItemsForDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = pickingTaskSearchService.pickedItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        verify(pickingTaskJdbcTemplate).fetchPickedItemsForDeliveryDate(deliveryDate);
    }

    private PickedItemsDto createMockPickedItemsDto(String itemName, int quantity) {
        PickedItemsDto dto = new PickedItemsDto();
        dto.setItemNumber(itemName);
        dto.setPickedQty(quantity);
        return dto;
    }

}