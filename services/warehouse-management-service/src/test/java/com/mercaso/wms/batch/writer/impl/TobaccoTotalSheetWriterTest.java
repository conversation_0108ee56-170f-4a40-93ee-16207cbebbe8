package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class TobaccoTotalSheetWriterTest extends Writer {

    private final TobaccoTotalSheetWriter tobaccoTotalSheetWriter = new TobaccoTotalSheetWriter();

    @Test
    void when_write_tobacco_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> costcos = Lists.newArrayList();
        ExcelBatchDto costcoExcelBatchDto = new ExcelBatchDto();
        costcoExcelBatchDto.setItemNumber("costco");
        costcoExcelBatchDto.setFrom("Location2");
        costcoExcelBatchDto.setQuantity(1);
        costcoExcelBatchDto.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto.setFrom("121");
        costcoExcelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto costcoExcelBatchDto1 = new ExcelBatchDto();
        costcoExcelBatchDto1.setItemNumber("costco");
        costcoExcelBatchDto1.setFrom("Location2");
        costcoExcelBatchDto1.setQuantity(1);
        costcoExcelBatchDto1.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto1.setFrom("10B");
        costcoExcelBatchDto1.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto costcoExcelBatchDto2 = new ExcelBatchDto();
        costcoExcelBatchDto2.setItemNumber("costco");
        costcoExcelBatchDto2.setFrom("Location2");
        costcoExcelBatchDto2.setQuantity(1);
        costcoExcelBatchDto2.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto2.setFrom("10A");
        costcoExcelBatchDto2.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        costcos.add(costcoExcelBatchDto);
        costcos.add(costcoExcelBatchDto1);
        costcos.add(costcoExcelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.COSTCO.getValue(), costcos));

        writeBatchTemplate(condition, tobaccoTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> tobaccoTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.COSTCO_TOBACCO_TOTALS.getValue()).doReadSync();

        assertEquals(4, tobaccoTotals.size());
        assertEquals("3", tobaccoTotals.get(3).get(5));
    }

    @Test
    void when_write_empty_tobacco_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> costcos = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.COSTCO.getValue(), costcos));

        writeBatchTemplate(condition, tobaccoTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> tobaccoTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.COSTCO_TOBACCO_TOTALS.getValue()).doReadSync();

        assertEquals(3, tobaccoTotals.size());
    }

}