package com.mercaso.wms.interfaces.search;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static com.mercaso.wms.utils.MockDataUtils.createBatch;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.event.applicationevent.listener.PickingTaskApplicationEventListener;
import com.mercaso.wms.utils.PickingTaskResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchFailedPickingTaskItemResourceIT extends AbstractIT {

    @Autowired
    PickingTaskResourceApi pickingTaskResourceApi;

    @Autowired
    PickingTaskRepository pickingTaskRepository;

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Autowired
    private PickingTaskApplicationEventListener listener;

    @Test
    void when_search_failed_picking_task_items_then_return_data() throws Exception {
        pickingTaskRepository.deleteAll();
        String orderNumber = RandomStringUtils.randomAlphabetic(10);

        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setName(orderNumber);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        Batch saved = batchRepository.save(createBatch());
        List<BatchItem> batchItems = buildBatchItems(saved.getId(), 1);
        batchItems.forEach(batchItem -> batchItem.setOrderNumber(orderNumber));
        batchItemRepository.saveAll(batchItems);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);
        listener.handleBatchCreatedEvent(saved.getId());

        List<PickingTask> pickingTasks = pickingTaskRepository.findByBatchId(saved.getId());
        pickingTasks.getFirst().getPickingTaskItems().getFirst().setErrorInfo("error");
        pickingTasks.getFirst().setState(PickingTaskStatus.FAILED);

        pickingTaskRepository.save(pickingTasks.getFirst());

        Result<FailedPickingTaskItemDto> pickingTaskItemDtos = pickingTaskResourceApi.searchFailedPickingTaskItems(saved.getTag(),
            List.of(pickingTasks.getFirst().getNumber()), SourceEnum.MDC.name());

        assertEquals(1, pickingTaskItemDtos.getData().size());
        assertEquals("error", pickingTaskItemDtos.getData().getFirst().getErrorInfo());
    }

    @Test
    void when_search_failed_picking_task_items_with_sorting_then_return_sorted_data() throws Exception {
        setupMultipleFailedPickingTaskItems();

        // Test task number DESC sorting
        Result<FailedPickingTaskItemDto> taskNumberResult = pickingTaskResourceApi.searchFailedPickingTaskItems(
            null, null, null, List.of(SortType.TASK_NUMBER_DESC));
        assertTrue(taskNumberResult.getData().size() >= 2);
        for (int i = 0; i < taskNumberResult.getData().size() - 1; i++) {
            assertTrue(taskNumberResult.getData().get(i).getPickingTaskNumber().compareTo(
                taskNumberResult.getData().get(i + 1).getPickingTaskNumber()) >= 0);
        }

        // Test order number ASC sorting
        Result<FailedPickingTaskItemDto> orderNumberResult = pickingTaskResourceApi.searchFailedPickingTaskItems(
            null, null, null, List.of(SortType.ORDER_NUMBER_ASC));
        assertTrue(orderNumberResult.getData().size() >= 2);
        for (int i = 0; i < orderNumberResult.getData().size() - 1; i++) {
            assertTrue(orderNumberResult.getData().get(i).getOrderNumber().compareTo(
                orderNumberResult.getData().get(i + 1).getOrderNumber()) <= 0);
        }

        // Test picker name ASC sorting
        Result<FailedPickingTaskItemDto> pickerResult = pickingTaskResourceApi.searchFailedPickingTaskItems(
            null, null, null, List.of(SortType.PICKER_USER_NAME_ASC));
        assertTrue(pickerResult.getData().size() >= 2);
        for (int i = 0; i < pickerResult.getData().size() - 1; i++) {
            assertTrue(pickerResult.getData().get(i).getPickerUserName().compareTo(
                pickerResult.getData().get(i + 1).getPickerUserName()) <= 0);
        }

        // Test title DESC sorting
        Result<FailedPickingTaskItemDto> titleResult = pickingTaskResourceApi.searchFailedPickingTaskItems(
            null, null, null, List.of(SortType.TITLE_DESC));
        assertTrue(titleResult.getData().size() >= 2);
        for (int i = 0; i < titleResult.getData().size() - 1; i++) {
            assertTrue(titleResult.getData().get(i).getTitle().compareTo(
                titleResult.getData().get(i + 1).getTitle()) >= 0);
        }
    }

    private void setupMultipleFailedPickingTaskItems() {
        pickingTaskRepository.deleteAll();

        // Create multiple failed picking tasks with different properties
        String[] orderNumbers = {"ORDER001", "ORDER002", "ORDER003"};
        String[] skuNumbers = {"SKU-C", "SKU-A", "SKU-B"};
        String[] sources = {"MDC", "MFC", "DOWNEY"};
        String[] locations = {"A-01-01", "B-02-02", "C-03-03"};
        String[] departments = {"Beverage", "Candy & Snacks", "Baby"};
        String[] pickerNames = {"John Doe", "Jane Smith", "Bob Wilson"};
        String[] titles = {"Product C", "Product A", "Product B"};

        for (int i = 0; i < orderNumbers.length; i++) {
            List<PickingTask> pickingTasks = buildPickingTask(UUID.randomUUID(), 1,
                SourceEnum.valueOf(sources[i]), PickingTaskStatus.FAILED, PickingTaskType.ORDER);

            PickingTask task = pickingTasks.getFirst();
            task.setPickerUserName(pickerNames[i]);

            // Set properties for the first picking task item
            PickingTaskItem item = task.getPickingTaskItems().getFirst();
            item.setOrderNumber(orderNumbers[i]);
            item.setSkuNumber(skuNumbers[i]);
            item.setLocationName(locations[i]);
            item.setDepartment(departments[i]);
            item.setTitle(titles[i]);
            item.setErrorInfo("error " + i);
            item.setExpectQty(10 + i);
            item.setPickedQty(i);

            pickingTaskRepository.save(task);
        }
    }
}