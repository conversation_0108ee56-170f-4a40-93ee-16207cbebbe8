package com.mercaso.wms.infrastructure.external.finale;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class FinaleEndpointsTest {

    @Test
    void endpointConstants_shouldHaveCorrectValues() {
        // Test internal endpoint names
        assertEquals("shipment.fill", FinaleEndpoints.ENDPOINT_FILL);
        assertEquals("shipment.pack", FinaleEndpoints.ENDPOINT_PACK);
        assertEquals("inventoryvariance.update", FinaleEndpoints.ENDPOINT_INVENTORY_VARIANCE);
    }

    @Test
    void retryKeys_shouldHaveCorrectFormat() {
        // Test retry configuration keys
        assertEquals("finale.shipment.fill", FinaleEndpoints.RETRY_KEY_SHIPMENT_FILL);
        assertEquals("finale.shipment.pack", FinaleEndpoints.RETRY_KEY_SHIPMENT_PACK);
        assertEquals("finale.inventoryvariance.update", FinaleEndpoints.RETRY_KEY_INVENTORY_VARIANCE);
    }

    @Test
    void getRetryKey_shouldBuildCorrectKey() {
        // Test dynamic key building
        assertEquals("finale.shipment.fill", FinaleEndpoints.getRetryKey("shipment.fill"));
        assertEquals("finale.shipment.pack", FinaleEndpoints.getRetryKey("shipment.pack"));
        assertEquals("finale.inventoryvariance.update", FinaleEndpoints.getRetryKey("inventoryvariance.update"));
    }

    @Test
    void getAllRetryKeys_shouldReturnAllKeys() {
        // Test getting all keys
        String[] allKeys = FinaleEndpoints.getAllRetryKeys();

        assertEquals(3, allKeys.length);
        assertArrayEquals(new String[]{
            "finale.shipment.fill",
            "finale.shipment.pack",
            "finale.inventoryvariance.update"
        }, allKeys);
    }

    @Test
    void getServicePrefix_shouldReturnCorrectPrefix() {
        assertEquals("finale", FinaleEndpoints.getServicePrefix());
    }

    @Test
    void retryKeys_shouldStartWithServicePrefix() {
        String prefix = FinaleEndpoints.getServicePrefix() + ".";

        assertTrue(FinaleEndpoints.RETRY_KEY_SHIPMENT_FILL.startsWith(prefix));
        assertTrue(FinaleEndpoints.RETRY_KEY_SHIPMENT_PACK.startsWith(prefix));
        assertTrue(FinaleEndpoints.RETRY_KEY_INVENTORY_VARIANCE.startsWith(prefix));
    }

    @Test
    void allRetryKeys_shouldBeUnique() {
        String[] allKeys = FinaleEndpoints.getAllRetryKeys();

        // Check for uniqueness by comparing array length with distinct count
        long distinctCount = java.util.Arrays.stream(allKeys).distinct().count();
        assertEquals(allKeys.length, distinctCount, "All retry keys should be unique");
    }

    @Test
    void constants_shouldNotBeNull() {
        // Ensure no constants are null
        assertNotNull(FinaleEndpoints.ENDPOINT_FILL);
        assertNotNull(FinaleEndpoints.ENDPOINT_PACK);
        assertNotNull(FinaleEndpoints.ENDPOINT_INVENTORY_VARIANCE);
        assertNotNull(FinaleEndpoints.RETRY_KEY_SHIPMENT_FILL);
        assertNotNull(FinaleEndpoints.RETRY_KEY_SHIPMENT_PACK);
        assertNotNull(FinaleEndpoints.RETRY_KEY_INVENTORY_VARIANCE);
    }

    @Test
    void constants_shouldNotBeEmpty() {
        // Ensure no constants are empty
        assertFalse(FinaleEndpoints.ENDPOINT_FILL.isEmpty());
        assertFalse(FinaleEndpoints.ENDPOINT_PACK.isEmpty());
        assertFalse(FinaleEndpoints.ENDPOINT_INVENTORY_VARIANCE.isEmpty());
        assertFalse(FinaleEndpoints.RETRY_KEY_SHIPMENT_FILL.isEmpty());
        assertFalse(FinaleEndpoints.RETRY_KEY_SHIPMENT_PACK.isEmpty());
        assertFalse(FinaleEndpoints.RETRY_KEY_INVENTORY_VARIANCE.isEmpty());
    }
}
