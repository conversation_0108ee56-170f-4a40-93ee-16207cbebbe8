package com.mercaso.wms.interfaces.search;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.repository.inventorystock.jpa.InventoryStockJpaDao;
import com.mercaso.wms.utils.InventoryStockResourceApi;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchInventoryStockResourceIT extends AbstractIT {

    @Autowired
    InventoryStockResourceApi inventoryStockResourceApi;
    @Autowired
    InventoryStockJpaDao inventoryStockJpaDao;

    @Test
    void when_search_inventory_stock_then_success() throws Exception {
        inventoryStockJpaDao.deleteAll();
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().get(10);

        List<String> skuNumbers = Lists.newArrayList();
        List<String> titles = Lists.newArrayList();
        List<String> lotNumbers = Lists.newArrayList();
        List<String> lpnNumbers = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
                .warehouseId(warehouses.getFirst().getId())
                .locationId(location.getId())
                .itemId(UUID.randomUUID())
                .skuNumber(RandomStringUtils.randomAlphabetic(10))
                .title(RandomStringUtils.randomAlphabetic(10))
                .lotNumber(RandomStringUtils.randomAlphabetic(10))
                .lpnNumber(RandomStringUtils.randomAlphabetic(10))
                .expirationDate(LocalDate.now().plusDays(10))
                .productionDate(LocalDate.now().minusDays(10))
                .qty(BigDecimal.ONE)
                .build();
            skuNumbers.add(command.getSkuNumber());
            titles.add(command.getTitle());
            lotNumbers.add(command.getLotNumber());
            lpnNumbers.add(command.getLpnNumber());
            inventoryStockResourceApi.create(command);
        }

        Result<InventoryStockDto> searchByStatus = inventoryStockResourceApi.search(null,
            null,
            InventoryStockStatus.AVAILABLE,
            null,
            null,
            null,
            null);

        assertNotNull(searchByStatus.getData());

        Result<InventoryStockDto> searchBySkuNumber = inventoryStockResourceApi.search(List.of(skuNumbers.getFirst()),
            null,
            null,
            null,
            null,
            null,
            null);

        assertEquals(1, searchBySkuNumber.getData().size());
        assertEquals(skuNumbers.getFirst(), searchBySkuNumber.getData().getFirst().getSkuNumber());

        Result<InventoryStockDto> searchByLocationName = inventoryStockResourceApi.search(null,
            location.getName(),
            null,
            null,
            null,
            null,
            null);
        assertEquals(10, searchByLocationName.getData().size());
        searchByLocationName.getData().forEach(inventoryStockDto -> {
            assertEquals(location.getName(), inventoryStockDto.getLocation().getName());
        });

        Result<InventoryStockDto> searchByTitle = inventoryStockResourceApi.search(null,
            null,
            null,
            titles.getFirst(),
            null,
            null,
            null);
        assertEquals(1, searchByTitle.getData().size());
        assertEquals(titles.getFirst(), searchByTitle.getData().getFirst().getTitle());

        Result<InventoryStockDto> searchByLotNumber = inventoryStockResourceApi.search(null,
            null,
            null,
            null,
            lotNumbers.getFirst(),
            null,
            null);
        assertEquals(1, searchByLotNumber.getData().size());
        assertEquals(lotNumbers.getFirst(), searchByLotNumber.getData().getFirst().getLotNumber());

        Result<InventoryStockDto> searchByLpnNumber = inventoryStockResourceApi.search(null,
            null,
            null,
            null,
            null,
            lpnNumbers.getFirst(),
            null);
        assertEquals(1, searchByLpnNumber.getData().size());
        assertEquals(lpnNumbers.getFirst(), searchByLpnNumber.getData().getFirst().getLpnNumber());
    }

}