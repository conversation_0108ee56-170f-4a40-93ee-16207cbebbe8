package com.mercaso.wms.application.searchservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.mapper.location.LocationDtoApplicationMapper;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class LocationSearchServiceTest {

    @Mock
    private LocationCache locationCache;

    @Mock
    private LocationDtoApplicationMapper locationDtoApplicationMapper;

    @InjectMocks
    private LocationSearchService locationSearchService;

    private Map<UUID, Location> emptyLocationMap;
    private Map<UUID, Location> richLocationMap;

    @BeforeEach
    void setUp() {
        emptyLocationMap = Collections.emptyMap();

        richLocationMap = new HashMap<>();

        addLocation("Warehouse A1");
        addLocation("Warehouse A2");
        addLocation("Shelf B1");
        addLocation("Shelf B2");
        addLocation("Picking Area C1");
        addLocation("Packing Area D1");
        addLocation("Shipping Area E1");
        addLocation("Return Area F1");
        addLocation("Special Chars!@#$%");
        addLocation("");
    }

    private void addLocation(String name) {
        UUID id = UUID.randomUUID();
        Location location = Location.builder()
            .id(id)
            .name(name)
            .build();
        richLocationMap.put(id, location);
    }

    @Test
    void returns_empty_page_when_location_cache_is_empty() {
        when(locationCache.getLocationMap()).thenReturn(emptyLocationMap);
        Pageable pageable = PageRequest.of(0, 10);

        Page<LocationDto> result = locationSearchService.search("Warehouse", pageable);

        assertTrue(result.isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void returns_all_locations_when_name_filter_is_empty() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        Pageable pageable = PageRequest.of(0, 20);
        Page<LocationDto> result = locationSearchService.search("", pageable);

        assertEquals(10, result.getTotalElements());
    }

    @Test
    void returns_all_locations_when_name_filter_is_null() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        Pageable pageable = PageRequest.of(0, 20);
        Page<LocationDto> result = locationSearchService.search(null, pageable);

        assertEquals(10, result.getTotalElements());
    }

    @Test
    void filters_locations_by_name_case_insensitive() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        Pageable pageable = PageRequest.of(0, 20);
        Page<LocationDto> result = locationSearchService.search("warehouse", pageable);

        assertEquals(2, result.getTotalElements());
        assertTrue(result.getContent().stream()
            .allMatch(dto -> dto.getName().contains("WAREHOUSE")));
    }

    @Test
    void handles_pagination_correctly() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        // First page, 2 items per page
        Pageable firstPage = PageRequest.of(0, 2);
        Page<LocationDto> firstPageResult = locationSearchService.search("", firstPage);
        assertEquals(2, firstPageResult.getContent().size());
        assertEquals(10, firstPageResult.getTotalElements());

        // Second page, 3 items per page
        Pageable secondPage = PageRequest.of(1, 3);
        Page<LocationDto> secondPageResult = locationSearchService.search("", secondPage);
        assertEquals(3, secondPageResult.getContent().size());
        assertEquals(10, secondPageResult.getTotalElements());
    }

    @Test
    void returns_empty_page_when_pagination_exceeds_data() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);

        // Out of bounds page
        Pageable outOfBoundsPage = PageRequest.of(5, 10);
        Page<LocationDto> result = locationSearchService.search("", outOfBoundsPage);

        assertTrue(result.isEmpty());
        assertEquals(10, result.getTotalElements());
    }

    @Test
    void filters_special_character_locations() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        Pageable pageable = PageRequest.of(0, 10);
        Page<LocationDto> result = locationSearchService.search("!@#$%", pageable);

        assertEquals(1, result.getTotalElements());
        assertEquals("SPECIAL CHARS!@#$%", result.getContent().getFirst().getName());
    }

    @Test
    void filters_by_partial_name_match() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);
        when(locationDtoApplicationMapper.domainToDtos(anyList()))
            .thenAnswer(invocation -> {
                List<Location> locations = invocation.getArgument(0);
                return locations.stream()
                    .map(loc -> {
                        LocationDto dto = new LocationDto();
                        dto.setName(loc.getName().toUpperCase());
                        return dto;
                    })
                    .collect(Collectors.toList());
            });

        // Test partial match
        Pageable pageable = PageRequest.of(0, 10);
        Page<LocationDto> result = locationSearchService.search("Area", pageable);

        assertEquals(4, result.getTotalElements());
    }

    @Test
    void returns_empty_list_when_no_locations_match_filter() {
        when(locationCache.getLocationMap()).thenReturn(richLocationMap);

        Pageable pageable = PageRequest.of(0, 10);
        Page<LocationDto> result = locationSearchService.search("NonExistentLocationName", pageable);

        assertTrue(result.isEmpty());
        assertEquals(0, result.getTotalElements());
    }
}