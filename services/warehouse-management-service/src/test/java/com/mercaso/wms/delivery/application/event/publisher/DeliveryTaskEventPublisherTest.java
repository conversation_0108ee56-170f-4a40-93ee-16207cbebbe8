package com.mercaso.wms.delivery.application.event.publisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DeliveryTaskEventPublisherTest {

    @Mock
    private BusinessEventClient businessEventClient;

    @InjectMocks
    private DeliveryTaskEventPublisher deliveryTaskEventPublisher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void when_publishDeliveryTaskBuildEvent_with_null_task_then_do_nothing() {
        // Arrange
        DeliveryTask task = null;
        List<DeliveryOrder> orders = new ArrayList<>();

        // Act
        deliveryTaskEventPublisher.publishDeliveryTaskBuildEvent(task, orders);

        // Assert
        verify(businessEventClient, never()).dispatch(any());
    }

    @Test
    void when_publishDeliveryTaskBuildEvent_with_empty_orders_then_do_nothing() {
        // Arrange
        DeliveryTask task = createDeliveryTask();
        List<DeliveryOrder> orders = Collections.emptyList();

        // Act
        deliveryTaskEventPublisher.publishDeliveryTaskBuildEvent(task, orders);

        // Assert
        verify(businessEventClient, never()).dispatch(any());
    }

    @Test
    void when_publishDeliveryTaskBuildEvent_with_no_matching_orders_then_do_nothing() {
        // Arrange
        UUID taskId = UUID.fromString("2a1ef1ef-dfdf-441b-aad9-683c0758b9a1");
        DeliveryTask task = createDeliveryTask(taskId);

        UUID differentTaskId = UUID.fromString("3b2ef2ef-efef-442c-bbd9-794d0869c0b2");
        List<DeliveryOrder> orders = List.of(
            createDeliveryOrder(differentTaskId, "ORDER-001"),
            createDeliveryOrder(differentTaskId, "ORDER-002")
        );

        // Act
        deliveryTaskEventPublisher.publishDeliveryTaskBuildEvent(task, orders);

        // Assert
        verify(businessEventClient, never()).dispatch(any());
    }

    @Test
    void when_publishDeliveryTaskBuildEvent_with_matching_orders_then_publish_event() {
        // Arrange
        UUID taskId = UUID.fromString("2a1ef1ef-dfdf-441b-aad9-683c0758b9a1");
        DeliveryTask task = createDeliveryTask(taskId);

        List<DeliveryOrder> orders = List.of(
            createDeliveryOrder(taskId, "ORDER-001"),
            createDeliveryOrder(taskId, "ORDER-002"),
            createDeliveryOrder(null, "ORDER-003")  // 不属于任何任务的订单
        );

        // Act
        deliveryTaskEventPublisher.publishDeliveryTaskBuildEvent(task, orders);

        // Assert
        verify(businessEventClient, times(1)).dispatch(any());
    }

    private DeliveryTask createDeliveryTask() {
        return createDeliveryTask(UUID.randomUUID());
    }

    private DeliveryTask createDeliveryTask(UUID id) {
        return DeliveryTask.builder()
            .id(id)
            .number("TASK-" + id.toString().substring(0, 8))
            .deliveryDate("2024-04-01")
            .truckNumber("TRUCK-001")
            .driverUserId(UUID.randomUUID())
            .driverUserName("Test Driver")
            .build();
    }

    private DeliveryOrder createDeliveryOrder(UUID deliveryTaskId, String orderNumber) {
        return DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .deliveryTaskId(deliveryTaskId)
            .orderNumber(orderNumber)
            .deliveryDate("2024-04-01")
            .status(DeliveryOrderStatus.ASSIGNED)
            .build();
    }
} 