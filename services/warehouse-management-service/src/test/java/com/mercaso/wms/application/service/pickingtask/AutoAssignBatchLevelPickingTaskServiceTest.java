package com.mercaso.wms.application.service.pickingtask;

import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties;
import com.mercaso.wms.batch.config.PickingTaskAssignmentProperties.Picker;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;

class AutoAssignBatchLevelPickingTaskServiceTest {

    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);

    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);

    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = mock(PickingTaskAssignmentConfig.class);

    private final AutoAssignBatchLevelPickingTaskService autoAssignBatchLevelPickingTaskService = new AutoAssignBatchLevelPickingTaskService(
        pickingTaskApplicationService,
        pickingTaskRepository,
        pickingTaskAssignmentConfig);

    @Test
    void assignBatchLevelPickingTask_WhenNoTasksExist_ShouldDoNothing() {
        UUID batchId = UUID.randomUUID();

        when(pickingTaskRepository.findByBatchIdAndTypeAndSource(batchId,
            PickingTaskType.BATCH,
            SourceEnum.MDC)).thenReturn(List.of());

        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);

        verify(pickingTaskRepository, times(0)).findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC);
        verifyNoMoreInteractions(pickingTaskRepository);
    }

    @Test
    void assignBatchLevelPickingTask_WhenConfigIsNull_ShouldDoNothing() {
        UUID batchId = UUID.randomUUID();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 10, PickingTaskType.BATCH);

        when(pickingTaskRepository.findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC)).thenReturn(
            pickingTasks);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(null);

        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);

        verify(pickingTaskRepository, times(0)).findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC);
        verifyNoMoreInteractions(pickingTaskRepository);
    }

    @Test
    void assignBatchLevelPickingTask_WhenAutoAssignmentIsFalse_ShouldDoNothing() {
        UUID batchId = UUID.randomUUID();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 10, PickingTaskType.BATCH);

        when(pickingTaskRepository.findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC)).thenReturn(
            pickingTasks);

        PickingTaskAssignmentProperties config = new PickingTaskAssignmentProperties();
        config.setAutoAssignment(false);
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(config);

        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);

        verifyNoMoreInteractions(pickingTaskRepository);
    }

    private List<Picker> getPickers(int size) {
        List<Picker> pickers = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            Picker picker = new Picker();
            picker.setUserId(UUID.randomUUID());
            picker.setUserName("Picker" + i);
            pickers.add(picker);
        }
        return pickers;
    }

    @Test
    void shouldNotAssignWhenNoMdcTasksFound() {
        // Given
        UUID batchId = UUID.randomUUID();
        when(pickingTaskRepository.findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC))
            .thenReturn(new ArrayList<>());

        // When
        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);

        // Then
        verify(pickingTaskApplicationService, never()).assignPickingTask(any(), any());
    }

    @Test
    void shouldAssignMdcTasksCorrectly() {
        // Given
        UUID batchId = UUID.randomUUID();
        PickingTaskAssignmentProperties pickingTaskAssignmentProperties = new PickingTaskAssignmentProperties();
        pickingTaskAssignmentProperties.setAutoAssignment(true);
        pickingTaskAssignmentProperties.setMdcCandyPickers(getPickers(5));
        when(pickingTaskAssignmentConfig.getPickingTaskAssignmentConfig()).thenReturn(pickingTaskAssignmentProperties);
        List<PickingTask> mdcTasks = buildPickingTask(batchId, 1);
        mdcTasks.forEach(task -> task.getPickingTaskItems().forEach(item -> item.setLocationName("00-01-A")));
        when(pickingTaskRepository.findByBatchIdAndTypeAndSource(batchId, PickingTaskType.BATCH, SourceEnum.MDC))
            .thenReturn(mdcTasks);

        // When
        autoAssignBatchLevelPickingTaskService.assignPickingTask(batchId);

        // Then
        verify(pickingTaskApplicationService, times(1)).assignPickingTask(any(), any());
    }

}