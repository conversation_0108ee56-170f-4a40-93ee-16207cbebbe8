package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.PickedShippingOrderItemExportDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShippingOrderJdbcTemplateIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;
    @Autowired
    ShippingOrderJdbcTemplate shippingOrderJdbcTemplate;

    @Test
    void when_count_sku_by_delivery_date() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(LocalDate.now().plusDays(20) + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        List<SkuCountByDeliveryDate> skuCountByDeliveryDate = shippingOrderJdbcTemplate.skuCountByDeliveryDate(LocalDate.now()
            .plusDays(20)
            .toString());

        assertEquals(10, skuCountByDeliveryDate.size());
    }

    @Test
    void fetchPickedShippingOrderItemsByDeliveryDate_NoData_ReturnsEmptyList() {
        // Arrange
        String deliveryDate = "2024-12-31";

        // Act
        List<PickedShippingOrderItemExportDto> result = shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void fetchPickedShippingOrderItemsByDeliveryDate_ValidDeliveryDate_ExecutesSuccessfully() {
        // Arrange
        String deliveryDate = LocalDate.now().toString();

        // Act
        List<PickedShippingOrderItemExportDto> result = shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);

        // Assert
        assertNotNull(result);
    }

    @Test
    void fetchPickedShippingOrderItemsByDeliveryDate_FutureDate_ReturnsEmptyList() {
        // Arrange
        String futureDate = LocalDate.now().plusDays(100).toString();

        // Act
        List<PickedShippingOrderItemExportDto> result = shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(futureDate);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void fetchPickedShippingOrderItemsByDeliveryDate_PastDate_ExecutesSuccessfully() {
        // Arrange
        String pastDate = LocalDate.now().minusDays(30).toString();

        // Act
        List<PickedShippingOrderItemExportDto> result = shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(pastDate);

        // Assert
        assertNotNull(result);
    }

}