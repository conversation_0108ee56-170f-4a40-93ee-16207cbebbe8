package com.mercaso.wms.infrastructure.slackalert;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.client.BusinessEventClient;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.BusinessEventPageDto;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WmsExceptionAlertServiceTest {

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private BusinessEventClient businessEventClient;

    @Mock
    private ApplicationEventDispatcher applicationEventDispatcher;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @InjectMocks
    private WmsExceptionAlertService wmsExceptionAlertService;

    @Test
    void when_checkOrderAfterBatchCreation_and_batchExists_then_publishAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();
        Batch existingBatch = createTestBatch();

        when(batchRepository.findActiveByDeliveryDate(any(LocalDate.class)))
            .thenReturn(Arrays.asList(existingBatch));

        // Mock businessEventClient to return empty page (no existing alerts)
        @SuppressWarnings("unchecked")
        BusinessEventPageDto<BusinessEventDto> emptyPage = mock(BusinessEventPageDto.class);
        when(emptyPage.getContent()).thenReturn(Collections.emptyList());
        when(businessEventClient.getEventByEntityIdAndEventTypes(any(), any(), any()))
            .thenReturn(emptyPage);

        // When
        wmsExceptionAlertService.checkOrderAfterBatchCreation(shippingOrder);

        // Then
        verify(businessEventClient, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void when_checkOrderAfterBatchCreation_and_noBatchExists_then_noAlert() {
        // Given
        ShippingOrder shippingOrder = createTestShippingOrder();

        when(batchRepository.findActiveByDeliveryDate(any(LocalDate.class)))
            .thenReturn(Collections.emptyList());

        // When
        wmsExceptionAlertService.checkOrderAfterBatchCreation(shippingOrder);

        // Then
        verify(businessEventClient, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    @Test
    void when_checkOrderAfterBatchCreation_and_orderHasNoDeliveryDate_then_noAlert() {
        // Given
        ShippingOrder shippingOrder = ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORD-001")
            .deliveryDate(null) // No delivery date
            .build();

        // When
        wmsExceptionAlertService.checkOrderAfterBatchCreation(shippingOrder);

        // Then
        verify(batchRepository, never()).findActiveByDeliveryDate(any(LocalDate.class));
        verify(businessEventClient, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    private ShippingOrder createTestShippingOrder() {
        return ShippingOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("ORD-001")
            .deliveryDate(LocalDate.now())
            .build();
    }

    private Batch createTestBatch() {
        return Batch.builder()
            .id(UUID.randomUUID())
            .number("BATCH-001")
            .tag(LocalDate.now().toString())
            .build();
    }

    @Test
    void when_checkPickingTasksIncompleteAfter8PM_and_timeIsBefore8PM_then_noAlert() {
        // Given
        UUID batchId = UUID.randomUUID();
        String batchNumber = "BATCH-001";
        String vendorName = "Test Vendor";
        String source = SourceEnum.MFC.name();
        String deliveryDate = LocalDate.now().toString();
        PickingTask incompleteTask = createTestIncompletePickingTask();

        // When (method handles time check internally, but we test the behavior)
        wmsExceptionAlertService.checkPickingTasksIncompleteAfter8PM(
            batchId, batchNumber, vendorName, source, deliveryDate, Arrays.asList(incompleteTask));

        // Then - alert behavior depends on current time, but method should not crash
        // This test primarily ensures the method executes without errors
    }

    @Test
    void when_checkPickingTasksIncompleteAfter8PM_and_noIncompleteTasks_then_noAlert() {
        // Given
        UUID batchId = UUID.randomUUID();
        String batchNumber = "BATCH-001";
        String vendorName = "Test Vendor";
        String source = SourceEnum.MFC.name();
        String deliveryDate = LocalDate.now().toString();

        // When
        wmsExceptionAlertService.checkPickingTasksIncompleteAfter8PM(
            batchId, batchNumber, vendorName, source, deliveryDate, Collections.emptyList());

        // Then - should not crash and handle empty list gracefully
    }

    @Test
    void when_checkPickingTasksIncompleteAfter8PM_and_nullIncompleteTasks_then_noAlert() {
        // Given
        UUID batchId = UUID.randomUUID();
        String batchNumber = "BATCH-001";
        String vendorName = "Test Vendor";
        String source = SourceEnum.MFC.name();
        String deliveryDate = LocalDate.now().toString();

        // When
        wmsExceptionAlertService.checkPickingTasksIncompleteAfter8PM(
            batchId, batchNumber, vendorName, source, deliveryDate, null);

        // Then - should not crash and handle null gracefully
    }

    private PickingTask createTestIncompletePickingTask() {
        return PickingTask.builder()
            .id(UUID.randomUUID())
            .number("PT-001")
            .status(PickingTaskStatus.PICKING)
            .source(SourceEnum.MFC)
            .createdAt(Instant.now())
            .pickingTaskItems(Collections.emptyList()) // Simplified for test
            .build();
    }

    @Test
    void when_alertGhostInventoryCleanupFailure_with_validParams_then_publishAlert() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-************";
        String deliveryItemsSummary = "[{sku=SKU001,qty=2},{sku=SKU002,qty=1}]";
        String errorMessage = "Database connection timeout";

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(Boolean.TRUE);

        // Mock businessEventClient to return empty page (no existing alerts)
        @SuppressWarnings("unchecked")
        BusinessEventPageDto<BusinessEventDto> emptyPage = mock(BusinessEventPageDto.class);
        when(emptyPage.getContent()).thenReturn(Collections.emptyList());
        when(businessEventClient.getEventByEntityIdAndEventTypes(any(), any(), any()))
            .thenReturn(emptyPage);

        // When
        wmsExceptionAlertService.alertGhostInventoryCleanupFailure(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        verify(businessEventClient, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void when_alertGhostInventoryCleanupFailure_with_nullOrderNumber_then_publishAlert() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = null;
        String deliveryItemsSummary = "[{sku=SKU001,qty=2}]";
        String errorMessage = "Service unavailable";

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(Boolean.TRUE);

        // Mock businessEventClient to return empty page (no existing alerts)
        @SuppressWarnings("unchecked")
        BusinessEventPageDto<BusinessEventDto> emptyPage = mock(BusinessEventPageDto.class);
        when(emptyPage.getContent()).thenReturn(Collections.emptyList());
        when(businessEventClient.getEventByEntityIdAndEventTypes(any(), any(), any()))
            .thenReturn(emptyPage);

        // When
        wmsExceptionAlertService.alertGhostInventoryCleanupFailure(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        verify(businessEventClient, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void when_alertGhostInventoryCleanupFailure_with_emptyDeliveryItems_then_publishAlert() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-************";
        String deliveryItemsSummary = "NO_ITEMS";
        String errorMessage = "Inventory lock timeout";

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(Boolean.TRUE);

        // Mock businessEventClient to return empty page (no existing alerts)
        @SuppressWarnings("unchecked")
        BusinessEventPageDto<BusinessEventDto> emptyPage = mock(BusinessEventPageDto.class);
        when(emptyPage.getContent()).thenReturn(Collections.emptyList());
        when(businessEventClient.getEventByEntityIdAndEventTypes(any(), any(), any()))
            .thenReturn(emptyPage);

        // When
        wmsExceptionAlertService.alertGhostInventoryCleanupFailure(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        verify(businessEventClient, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void when_alertGhostInventoryCleanupFailure_with_nullErrorMessage_then_publishAlert() {
        // Given
        UUID deliveryOrderId = UUID.randomUUID();
        String orderNumber = "ORD-************";
        String deliveryItemsSummary = "[{sku=SKU003,qty=5}]";
        String errorMessage = null;

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(Boolean.TRUE);

        // Mock businessEventClient to return empty page (no existing alerts)
        @SuppressWarnings("unchecked")
        BusinessEventPageDto<BusinessEventDto> emptyPage = mock(BusinessEventPageDto.class);
        when(emptyPage.getContent()).thenReturn(Collections.emptyList());
        when(businessEventClient.getEventByEntityIdAndEventTypes(any(), any(), any()))
            .thenReturn(emptyPage);

        // When
        wmsExceptionAlertService.alertGhostInventoryCleanupFailure(
            deliveryOrderId, orderNumber, deliveryItemsSummary, errorMessage);

        // Then
        verify(businessEventClient, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }
} 