package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.ColourMarkingEnum;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.LinkedHashMap;
import java.util.List;
import org.junit.jupiter.api.Test;

class MfcDeductionSheetWriterTest extends Writer {

    private final MfcDeductionSheetWriter mfcDeductionSheetWriter = new MfcDeductionSheetWriter();

    @Test
    void writeAndReadExcelWithValidBatches() throws IOException {

        try {
            ExcelBatchDto batch1 = new ExcelBatchDto();
            batch1.setItemNumber("Item1");
            batch1.setFrom("Location1");
            batch1.setQuantity(10);
            batch1.setSource(SourceEnum.MFC.name());
            batch1.setColourMarking(ColourMarkingEnum.YELLOW.name());

            ExcelBatchDto batch2 = new ExcelBatchDto();
            batch2.setItemNumber("Item1");
            batch2.setFrom("Location1");
            batch2.setQuantity(5);
            batch2.setSource(SourceEnum.MFC.name());
            batch2.setColourMarking(null);

            WriteTemplateCondition condition = WriteTemplateCondition.builder()
                .excelBatchDtos(List.of(batch1, batch2))
                .sourceAndListMap(new LinkedHashMap<>())
                .build();

            writeBatchTemplate(condition, mfcDeductionSheetWriter);

            // Read from Excel
            List<LinkedHashMap<Integer, String>> readDtos =
                EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_PICK_DEDUCTIONS.getValue()).doReadSync();

            // Verify the data
            assertEquals(1, readDtos.size());
            assertEquals("Item1", readDtos.getFirst().get(0));
            assertEquals("5", readDtos.getFirst().get(1));
            assertEquals("Location1", readDtos.getFirst().get(2));

        } finally {
            Files.delete(Path.of(fileName));
        }
    }

    @Test
    void writeAndReadExcelWithSameItems() throws IOException {

        try {
            ExcelBatchDto batch1 = new ExcelBatchDto();
            batch1.setItemNumber("Item1");
            batch1.setFrom("01-01-A-1");
            batch1.setQuantity(10);
            batch1.setSource(SourceEnum.MFC.name());

            ExcelBatchDto batch2 = new ExcelBatchDto();
            batch2.setItemNumber("Item1");
            batch2.setFrom("101-01-A-1");
            batch2.setQuantity(5);
            batch2.setSource(SourceEnum.MDC.name());

            WriteTemplateCondition condition = WriteTemplateCondition.builder()
                .excelBatchDtos(List.of(batch1, batch2))
                .sourceAndListMap(new LinkedHashMap<>())
                .build();

            writeBatchTemplate(condition, mfcDeductionSheetWriter);

            List<LinkedHashMap<Integer, String>> readDtos =
                EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_PICK_DEDUCTIONS.getValue()).doReadSync();

            assertEquals(2, readDtos.size());
            assertEquals("Item1", readDtos.getFirst().get(0));
            assertEquals("10", readDtos.getFirst().get(1));
            assertEquals("01-01-A-1", readDtos.getFirst().get(2));

            assertEquals("Item1", readDtos.getLast().get(0));
            assertEquals("5", readDtos.getLast().get(1));
            assertEquals("101-01-A-1", readDtos.getLast().get(2));


        } finally {
            Files.delete(Path.of(fileName));
        }
    }
}