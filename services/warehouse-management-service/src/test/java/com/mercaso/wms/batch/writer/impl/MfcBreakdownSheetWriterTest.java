package com.mercaso.wms.batch.writer.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class MfcBreakdownSheetWriterTest extends Writer {

    private final MfcBreakdownSheetWriter sheetWriter = new MfcBreakdownSheetWriter();

    @Test
    void when_mfc_batchDto_empty_write_then_return_empty() {
        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of())
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, sheetWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> mfcBigBeverages =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_BEVERAGES_BIG_ORDER_WMS_PICKING.getValue()).doReadSync();

        List<LinkedHashMap<Integer, String>> mfcSmallBeverages =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_BEVERAGES_SMALL_ORDER.getValue()).doReadSync();

        // Verify the data
        assertEquals(4, mfcBigBeverages.size());
        assertEquals(4, mfcSmallBeverages.size());
    }

    @Test
    void when_big_mfc_batchDto_write_then_return_big_sheet() {
        ExcelBatchDto mfcExcelBatchDto = new ExcelBatchDto();
        mfcExcelBatchDto.setItemNumber("mfc");
        mfcExcelBatchDto.setFrom("Location1");
        mfcExcelBatchDto.setQuantity(16);
        mfcExcelBatchDto.setSource(SourceEnum.MFC.name());
        mfcExcelBatchDto.setPrep(BatchConstants.BEVERAGE);
        mfcExcelBatchDto.setOriginalBreakdown("11");
        mfcExcelBatchDto.setBigOrder(true);
        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.MFC.name(), List.of(mfcExcelBatchDto));

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(mfcExcelBatchDto))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, sheetWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> mfcBigBeverages =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_BEVERAGES_BIG_ORDER_WMS_PICKING.getValue()).doReadSync();

        List<LinkedHashMap<Integer, String>> mfcSmallBeverages =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MFC_BEVERAGES_SMALL_ORDER.getValue()).doReadSync();

        // Verify the data
        assertEquals(5, mfcBigBeverages.size());
        assertEquals(4, mfcSmallBeverages.size());
    }

}