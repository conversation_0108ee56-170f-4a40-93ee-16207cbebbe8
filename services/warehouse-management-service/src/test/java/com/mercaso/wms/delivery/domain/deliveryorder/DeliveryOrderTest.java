package com.mercaso.wms.delivery.domain.deliveryorder;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.delivery.application.command.deliveryorder.UpdateDeliveryOrderItemCommand;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.ItemExceptionType;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DeliveryOrderTest {

    private DeliveryOrder deliveryOrder;
    private DeliveryOrderItem item1;
    private DeliveryOrderItem item2;
    Map<String, BigDecimal> reasonCodeTotals;

    @BeforeEach
    void setUp() {
        deliveryOrder = DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("TEST-ORDER-001")
            .deliveryOrderItems(List.of())
            .build();
        deliveryOrder.setState(DeliveryOrderStatus.CREATED);

        item1 = DeliveryOrderItem.builder()
            .id(UUID.randomUUID())
            .deliveryOrderId(deliveryOrder.getId())
            .skuNumber("SKU001")
            .title("Test Item 1")
            .qty(BigDecimal.valueOf(10))
            .currentQty(BigDecimal.valueOf(10))
            .deliveredQty(BigDecimal.valueOf(8))
            .price(BigDecimal.valueOf(10.00))
            .line(1)
            .build();

        item2 = DeliveryOrderItem.builder()
            .id(UUID.randomUUID())
            .deliveryOrderId(deliveryOrder.getId())
            .skuNumber("SKU002")
            .title("Test Item 2")
            .qty(BigDecimal.valueOf(5))
            .currentQty(BigDecimal.valueOf(5))
            .deliveredQty(BigDecimal.valueOf(3))
            .price(BigDecimal.valueOf(20.00))
            .line(2)
            .build();

        deliveryOrder.setDeliveryOrderItems(List.of(item1, item2));

        reasonCodeTotals = ItemExceptionType.initializeAllReasonCodeTotals();
    }

    @Test
    void calculateExtraPrice_WithDamagedAndMissingItems_ShouldCalculateCorrectly() {
        // Given
        UpdateDeliveryOrderItemCommand command1 = UpdateDeliveryOrderItemCommand.builder()
            .id(item1.getId())
            .deliveredQty(BigDecimal.valueOf(8))
            .reasonCode("{\"DAMAGED\":2}")
            .build();

        UpdateDeliveryOrderItemCommand command2 = UpdateDeliveryOrderItemCommand.builder()
            .id(item2.getId())
            .deliveredQty(BigDecimal.valueOf(3))
            .reasonCode("{\"MISSING\":2}")
            .build();

        // When
        deliveryOrder.updateDeliveredItems(List.of(command1, command2));

        // Then
        assertNotNull(deliveryOrder.getAdjustedPrice());
        assertTrue(deliveryOrder.getAdjustedPrice().contains("DAMAGED"));
        assertTrue(deliveryOrder.getAdjustedPrice().contains("MISSING"));

        assertTrue(deliveryOrder.getAdjustedPrice().contains("20.00"), "Expected DAMAGED price 20.00");
        assertTrue(deliveryOrder.getAdjustedPrice().contains("40.00"), "Expected MISSING price 40.00");
    }

    @Test
    void calculateExtraPrice_WithNoReasonCodes_ShouldSetNull() {
        // Given
        UpdateDeliveryOrderItemCommand command = UpdateDeliveryOrderItemCommand.builder()
            .id(item1.getId())
            .deliveredQty(BigDecimal.valueOf(10))
            .reasonCode(null)
            .build();

        // When
        deliveryOrder.updateDeliveredItems(List.of(command));

        // Then
        assertEquals(deliveryOrder.getAdjustedPrice(), SerializationUtils.serialize(reasonCodeTotals));
    }

    @Test
    void calculateExtraPrice_WithInvalidJson_ShouldHandleGracefully() {
        // Given
        UpdateDeliveryOrderItemCommand command = UpdateDeliveryOrderItemCommand.builder()
            .id(item1.getId())
            .deliveredQty(BigDecimal.valueOf(8))
            .reasonCode("invalid json")
            .build();

        // When
        deliveryOrder.updateDeliveredItems(List.of(command));

        // Then
        assertEquals(deliveryOrder.getAdjustedPrice(), SerializationUtils.serialize(reasonCodeTotals));
    }
} 