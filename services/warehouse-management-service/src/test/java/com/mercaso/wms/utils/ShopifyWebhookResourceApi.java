package com.mercaso.wms.utils;

import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class ShopifyWebhookResourceApi extends IntegrationTestRestUtil {

    private static final String SHOPIFY_WEBHOOK_URL = "/shopify/webhook";

    public ShopifyWebhookResourceApi(Environment environment) {
        super(environment);
    }

    public void webhook(ShopifyOrderDto shopifyOrderDto) throws Exception {
        createShopifyWebhook(SHOPIFY_WEBHOOK_URL, SerializationUtils.serialize(shopifyOrderDto));
    }

    public void webhook(String payload) {
        createShopifyWebhook(SHOPIFY_WEBHOOK_URL, payload);
    }

}
