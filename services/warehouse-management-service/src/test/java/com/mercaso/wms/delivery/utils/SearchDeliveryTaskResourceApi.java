package com.mercaso.wms.delivery.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.dto.view.SearchDeliveryTaskView;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SearchDeliveryTaskResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_DELIVERY_TASK_URL = "/delivery/search/delivery-tasks";

    public SearchDeliveryTaskResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchDeliveryTaskView> search(
        List<String> taskNumbers,
        List<DeliveryTaskStatus> statuses,
        LocalDate deliveryDate,
        List<String> driverNames,
        List<String> truckNumbers,
        Boolean hasRescheduledOrders,
        List<SortType> sortTypes,
        List<UUID> driverUserIds) throws Exception {

        StringBuilder urlBuilder = new StringBuilder(SEARCH_DELIVERY_TASK_URL + "?page=1&pageSize=20");

        if (CollectionUtils.isNotEmpty(taskNumbers)) {
            urlBuilder.append("&taskNumbers=").append(String.join(",", taskNumbers));
        }

        if (CollectionUtils.isNotEmpty(statuses)) {
            String statusValues = statuses.stream()
                .map(Enum::name)
                .collect(Collectors.joining(","));
            urlBuilder.append("&statuses=").append(statusValues);
        }

        if (deliveryDate != null) {
            urlBuilder.append("&deliveryDate=").append(deliveryDate);
        }

        if (CollectionUtils.isNotEmpty(driverNames)) {
            urlBuilder.append("&driverNames=").append(String.join(",", driverNames));
        }

        if (CollectionUtils.isNotEmpty(truckNumbers)) {
            urlBuilder.append("&truckNumbers=").append(String.join(",", truckNumbers));
        }

        if (CollectionUtils.isNotEmpty(driverUserIds)) {
            urlBuilder.append("&driverUserIds=").append(String.join(",", driverUserIds.stream().map(UUID::toString).toList()));
        }

        if (hasRescheduledOrders != null) {
            urlBuilder.append("&hasRescheduledOrders=").append(hasRescheduledOrders);
        }

        if (CollectionUtils.isNotEmpty(sortTypes)) {
            String sortTypeValues = sortTypes.stream()
                .map(Enum::name)
                .collect(Collectors.joining(","));
            urlBuilder.append("&sortTypes=").append(sortTypeValues);
        }

        String url = urlBuilder.toString();
        log.debug("Sending request to: {}", url);

        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }
}