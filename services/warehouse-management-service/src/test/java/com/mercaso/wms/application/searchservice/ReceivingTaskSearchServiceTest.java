package com.mercaso.wms.application.searchservice;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.ReceivedItemsDto;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.ReceivingTaskJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ReceivingTaskSearchServiceTest {

    @Mock
    private ReceivingTaskJdbcTemplate receivingTaskJdbcTemplate;

    @InjectMocks
    private ReceivingTaskSearchService receivingTaskSearchService;

    @Test
    void receivedItemsExport_Success() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<ReceivedItemsDto> mockDtos = Arrays.asList(
            createMockReceivedItemsDto("Item1", 10),
            createMockReceivedItemsDto("Item2", 20)
        );
        when(receivingTaskJdbcTemplate.fetchReceivedItemsByDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = receivingTaskSearchService.receivedItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        verify(receivingTaskJdbcTemplate).fetchReceivedItemsByDeliveryDate(deliveryDate);
    }

    private ReceivedItemsDto createMockReceivedItemsDto(String itemName, int quantity) {
        ReceivedItemsDto dto = new ReceivedItemsDto();
        dto.setItemNumber(itemName);
        dto.setReceivedQty(quantity);
        return dto;
    }
}