package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;

class PickSheetsWriterTest extends Writer {

    private final PickSheetsWriter pickSheetsWriter = new PickSheetsWriter();

    @Test
    void when_write_threePL_then_sorting() {
        List<ExcelBatchDto> threePLs = Lists.newArrayList();
        ExcelBatchDto threePlExcelBatchDto = new ExcelBatchDto();
        threePlExcelBatchDto.setItemNumber("downey");
        threePlExcelBatchDto.setFrom("Location1");
        threePlExcelBatchDto.setQuantity(3);
        threePlExcelBatchDto.setSource(SourceEnum.DOWNEY.name());
        threePlExcelBatchDto.setFrom(".CANDY.");
        threePlExcelBatchDto.setPrep(".CANDY.");
        threePlExcelBatchDto.setDepartment("Candy & Snacks");

        ExcelBatchDto threePlExcelBatchDto1 = new ExcelBatchDto();
        threePlExcelBatchDto1.setItemNumber("downey");
        threePlExcelBatchDto1.setFrom("Location1");
        threePlExcelBatchDto1.setQuantity(3);
        threePlExcelBatchDto1.setSource(SourceEnum.DOWNEY.name());
        threePlExcelBatchDto1.setFrom(".CANDY.");
        threePlExcelBatchDto1.setPrep("71842.CANDY.22");
        threePlExcelBatchDto1.setDepartment("Candy & Snacks");

        ExcelBatchDto threePlExcelBatchDto2 = new ExcelBatchDto();
        threePlExcelBatchDto2.setItemNumber("downey");
        threePlExcelBatchDto2.setFrom("Location1");
        threePlExcelBatchDto2.setQuantity(3);
        threePlExcelBatchDto2.setSource(SourceEnum.DOWNEY.name());
        threePlExcelBatchDto2.setFrom(".CANDY.");
        threePlExcelBatchDto2.setPrep("OTHER");
        threePlExcelBatchDto2.setDepartment("Candy & Snacks");

        ExcelBatchDto threePlExcelBatchDto3 = new ExcelBatchDto();
        threePlExcelBatchDto3.setItemNumber("downey");
        threePlExcelBatchDto3.setFrom("Location1");
        threePlExcelBatchDto3.setQuantity(3);
        threePlExcelBatchDto3.setSource(SourceEnum.DOWNEY.name());
        threePlExcelBatchDto3.setFrom(".CANDY.");
        threePlExcelBatchDto3.setPrep("71852.CANDY.22");
        threePlExcelBatchDto3.setDepartment("Candy & Snacks");

        threePLs.add(threePlExcelBatchDto);
        threePLs.add(threePlExcelBatchDto1);
        threePLs.add(threePlExcelBatchDto3);
        threePLs.add(threePlExcelBatchDto2);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.DOWNEY.name(), threePLs);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(threePlExcelBatchDto, threePlExcelBatchDto1, threePlExcelBatchDto2, threePlExcelBatchDto3))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> threePL =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.DOWNEY.getValue()).doReadSync();

        // Verify the data
        assertEquals(7, threePL.size());
        assertEquals(threePlExcelBatchDto3.getPrep(), threePL.get(3).get(9));
        assertEquals(threePlExcelBatchDto1.getPrep(), threePL.get(4).get(9));
        assertEquals(threePlExcelBatchDto.getPrep(), threePL.get(5).get(9));
        assertEquals(threePlExcelBatchDto2.getPrep(), threePL.get(6).get(9));
    }

    @Test
    void when_write_costco_then_sorting() {
        List<ExcelBatchDto> costcos = Lists.newArrayList();
        ExcelBatchDto costcoExcelBatchDto = new ExcelBatchDto();
        costcoExcelBatchDto.setItemNumber("costco");
        costcoExcelBatchDto.setFrom("Location2");
        costcoExcelBatchDto.setQuantity(1);
        costcoExcelBatchDto.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto.setFrom("121");
        costcoExcelBatchDto.setDepartment("Health & Beauty");

        ExcelBatchDto costcoExcelBatchDto1 = new ExcelBatchDto();
        costcoExcelBatchDto1.setItemNumber("costco");
        costcoExcelBatchDto1.setFrom("Location2");
        costcoExcelBatchDto1.setQuantity(1);
        costcoExcelBatchDto1.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto1.setFrom("10B");
        costcoExcelBatchDto1.setDepartment("Grocery");

        ExcelBatchDto costcoExcelBatchDto2 = new ExcelBatchDto();
        costcoExcelBatchDto2.setItemNumber("costco");
        costcoExcelBatchDto2.setFrom("Location2");
        costcoExcelBatchDto2.setQuantity(1);
        costcoExcelBatchDto2.setSource(SourceEnum.COSTCO.name());
        costcoExcelBatchDto2.setFrom("10A");
        costcoExcelBatchDto2.setDepartment("Candy & Snacks");
        costcos.add(costcoExcelBatchDto);
        costcos.add(costcoExcelBatchDto1);
        costcos.add(costcoExcelBatchDto2);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.COSTCO.name(), costcos);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(costcoExcelBatchDto, costcoExcelBatchDto1, costcoExcelBatchDto2))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> costco =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.COSTCO.getValue()).doReadSync();

        assertEquals(6, costco.size());
        assertEquals(costcoExcelBatchDto2.getFrom(), costco.get(3).get(8));
        assertEquals(costcoExcelBatchDto1.getFrom(), costco.get(4).get(8));
        assertEquals(costcoExcelBatchDto.getFrom(), costco.get(5).get(8));
    }

    @Test
    void when_write_vernon_items_should_successfully() {
        List<ExcelBatchDto> vernons = Lists.newArrayList();
        ExcelBatchDto vernonExcelBatchDto = new ExcelBatchDto();
        vernonExcelBatchDto.setItemNumber("vernon");
        vernonExcelBatchDto.setVendorItemNumber("test001");
        vernonExcelBatchDto.setFrom("Location2");
        vernonExcelBatchDto.setQuantity(1);
        vernonExcelBatchDto.setSource(SourceEnum.VERNON.name());
        vernonExcelBatchDto.setDepartment("Health & Beauty");

        ExcelBatchDto vernonExcelBatchDto1 = new ExcelBatchDto();
        vernonExcelBatchDto1.setItemNumber("vernon1");
        vernonExcelBatchDto1.setVendorItemNumber("test002");
        vernonExcelBatchDto1.setFrom("Location2");
        vernonExcelBatchDto1.setQuantity(1);
        vernonExcelBatchDto1.setSource(SourceEnum.VERNON.name());
        vernonExcelBatchDto1.setDepartment("Grocery");

        vernons.add(vernonExcelBatchDto);
        vernons.add(vernonExcelBatchDto1);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.VERNON.name(), vernons);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(vernonExcelBatchDto, vernonExcelBatchDto1))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> vernon =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.VERNON.getValue()).doReadSync();

        assertEquals(5, vernon.size());
        assertEquals(vernonExcelBatchDto.getFrom(), vernon.get(3).get(9));
        assertEquals(vernonExcelBatchDto.getVendorItemNumber(), vernon.get(3).get(2));
        assertEquals(vernonExcelBatchDto1.getFrom(), vernon.get(4).get(9));
        assertEquals(vernonExcelBatchDto1.getVendorItemNumber(), vernon.get(4).get(2));
    }

    @Test
    void when_write_exotic_items_should_successfully() {
        List<ExcelBatchDto> exotics = Lists.newArrayList();
        ExcelBatchDto exoticExcelBatchDto = new ExcelBatchDto();
        exoticExcelBatchDto.setItemNumber("exotic");
        exoticExcelBatchDto.setVendorItemNumber("test001");
        exoticExcelBatchDto.setFrom("Location2");
        exoticExcelBatchDto.setQuantity(1);
        exoticExcelBatchDto.setSource(SourceEnum.EXOTIC.name());
        exoticExcelBatchDto.setDepartment("Health & Beauty");

        ExcelBatchDto exoticExcelBatchDto1 = new ExcelBatchDto();
        exoticExcelBatchDto1.setItemNumber("exotic1");
        exoticExcelBatchDto1.setVendorItemNumber("test002");
        exoticExcelBatchDto1.setFrom("Location2");
        exoticExcelBatchDto1.setQuantity(1);
        exoticExcelBatchDto1.setSource(SourceEnum.EXOTIC.name());
        exoticExcelBatchDto1.setDepartment("Grocery");

        exotics.add(exoticExcelBatchDto);
        exotics.add(exoticExcelBatchDto1);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.EXOTIC.name(), exotics);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(exoticExcelBatchDto, exoticExcelBatchDto1))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> vernon =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.EXOTIC.getValue()).doReadSync();

        assertEquals(5, vernon.size());
        assertEquals(exoticExcelBatchDto.getFrom(), vernon.get(3).get(9));
        assertEquals(exoticExcelBatchDto.getVendorItemNumber(), vernon.get(3).get(2));
        assertEquals(exoticExcelBatchDto1.getFrom(), vernon.get(4).get(9));
        assertEquals(exoticExcelBatchDto1.getVendorItemNumber(), vernon.get(4).get(2));
    }

    @Test
    void when_write_seven_star_items_should_successfully() {
        List<ExcelBatchDto> sevenStars = Lists.newArrayList();
        ExcelBatchDto sevenStarExcelBatchDto = new ExcelBatchDto();
        sevenStarExcelBatchDto.setItemNumber("sevenStar");
        sevenStarExcelBatchDto.setVendorItemNumber("test001");
        sevenStarExcelBatchDto.setFrom("Location2");
        sevenStarExcelBatchDto.setQuantity(1);
        sevenStarExcelBatchDto.setSource(SourceEnum.SEVEN_STARS.name());
        sevenStarExcelBatchDto.setDepartment("Health & Beauty");

        ExcelBatchDto sevenStarExcelBatchDto1 = new ExcelBatchDto();
        sevenStarExcelBatchDto1.setItemNumber("sevenStar1");
        sevenStarExcelBatchDto1.setVendorItemNumber("test002");
        sevenStarExcelBatchDto1.setFrom("Location2");
        sevenStarExcelBatchDto1.setQuantity(1);
        sevenStarExcelBatchDto1.setSource(SourceEnum.SEVEN_STARS.name());
        sevenStarExcelBatchDto1.setDepartment("Grocery");

        sevenStars.add(sevenStarExcelBatchDto);
        sevenStars.add(sevenStarExcelBatchDto1);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.SEVEN_STARS.name(), sevenStars);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(sevenStarExcelBatchDto, sevenStarExcelBatchDto1))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> sevenStar =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.SEVEN_STARS.getValue()).doReadSync();

        assertEquals(5, sevenStar.size());
        assertEquals(sevenStarExcelBatchDto.getFrom(), sevenStar.get(3).get(9));
        assertEquals(sevenStarExcelBatchDto.getVendorItemNumber(), sevenStar.get(3).get(2));
        assertEquals(sevenStarExcelBatchDto1.getFrom(), sevenStar.get(4).get(9));
        assertEquals(sevenStarExcelBatchDto1.getVendorItemNumber(), sevenStar.get(4).get(2));
    }

    @Test
    void when_write_jetro_items_should_successfully() {
        List<ExcelBatchDto> jetros = Lists.newArrayList();
        ExcelBatchDto jetroExcelBatchDto = new ExcelBatchDto();
        jetroExcelBatchDto.setItemNumber("jetro");
        jetroExcelBatchDto.setVendorItemNumber("test001");
        jetroExcelBatchDto.setFrom("Location2");
        jetroExcelBatchDto.setQuantity(1);
        jetroExcelBatchDto.setSource(SourceEnum.JETRO.name());
        jetroExcelBatchDto.setDepartment("Health & Beauty");

        ExcelBatchDto jetroExcelBatchDto1 = new ExcelBatchDto();
        jetroExcelBatchDto1.setItemNumber("jetro1");
        jetroExcelBatchDto1.setVendorItemNumber("test002");
        jetroExcelBatchDto1.setFrom("Location2");
        jetroExcelBatchDto1.setQuantity(1);
        jetroExcelBatchDto1.setSource(SourceEnum.JETRO.name());
        jetroExcelBatchDto1.setDepartment("Grocery");

        jetros.add(jetroExcelBatchDto);
        jetros.add(jetroExcelBatchDto1);

        Map<String, List<ExcelBatchDto>> sourceAndListMap = new HashMap<>();
        sourceAndListMap.put(SourceEnum.JETRO.name(), jetros);

        WriteTemplateCondition condition = WriteTemplateCondition.builder()
            .excelBatchDtos(List.of(jetroExcelBatchDto, jetroExcelBatchDto1))
            .sourceAndListMap(sourceAndListMap)
            .build();
        writeBatchTemplate(condition, pickSheetsWriter);

        // Read from Excel
        List<LinkedHashMap<Integer, String>> sevenStar =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.JETRO.getValue()).doReadSync();

        assertEquals(5, sevenStar.size());
        assertEquals(jetroExcelBatchDto.getFrom(), sevenStar.get(3).get(9));
        assertEquals(jetroExcelBatchDto.getVendorItemNumber(), sevenStar.get(3).get(2));
        assertEquals(jetroExcelBatchDto1.getFrom(), sevenStar.get(4).get(9));
        assertEquals(jetroExcelBatchDto1.getVendorItemNumber(), sevenStar.get(4).get(2));
    }
}