package com.mercaso.wms.domain.inventorystockhistory;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.inventorystockhistory.enums.TransactionEvent;
import com.mercaso.wms.domain.location.Location;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class InventoryStockHistoryRepositoryIT extends AbstractIT {

    @Autowired
    private InventoryStockHistoryRepository inventoryStockHistoryRepository;

    @Test
    void when_create_inventory_stock_history_then_return_inventory_stock_history() {
        List<Location> locations = locationRepository.findAll();
        // given
        InventoryStockHistory inventoryStockHistory = InventoryStockHistory.builder()
            .fromStockId(UUID.randomUUID())
            .skuNumber("skuNumber")
            .itemId(UUID.randomUUID())
            .fromLocation(locations.getFirst())
            .toLocation(locations.get(1))
            .lotNumber("lotNumber")
            .productionDate(LocalDate.now())
            .expirationDate(LocalDate.now())
            .beforeQty(BigDecimal.TEN)
            .afterQty(BigDecimal.ONE)
            .changeQty(BigDecimal.valueOf(9))
            .transactionEvent(TransactionEvent.PICK)
            .build();

        // when
        InventoryStockHistory savedInventoryStockHistory = inventoryStockHistoryRepository.save(inventoryStockHistory);

        assertNotNull(savedInventoryStockHistory.getId());

        InventoryStockHistory result = inventoryStockHistoryRepository.findById(savedInventoryStockHistory.getId());

        // then
        assertEquals(inventoryStockHistory.getFromStockId(), result.getFromStockId());
        assertEquals(inventoryStockHistory.getSkuNumber(), result.getSkuNumber());
        assertEquals(inventoryStockHistory.getItemId(), result.getItemId());
        assertEquals(inventoryStockHistory.getFromLocation().getId(), result.getFromLocation().getId());
        assertEquals(inventoryStockHistory.getToLocation().getId(), result.getToLocation().getId());
        assertEquals(inventoryStockHistory.getLotNumber(), result.getLotNumber());
        assertEquals(inventoryStockHistory.getProductionDate(), result.getProductionDate());
        assertEquals(inventoryStockHistory.getExpirationDate(), result.getExpirationDate());
        assertEquals(inventoryStockHistory.getBeforeQty().setScale(2, RoundingMode.HALF_UP), result.getBeforeQty());
        assertEquals(inventoryStockHistory.getAfterQty().setScale(2, RoundingMode.HALF_UP), result.getAfterQty());
        assertEquals(inventoryStockHistory.getChangeQty().setScale(2, RoundingMode.HALF_UP), result.getChangeQty());

        result.setChangeQty(BigDecimal.valueOf(15));
        InventoryStockHistory updated = inventoryStockHistoryRepository.update(result);

        assertEquals(BigDecimal.valueOf(15), updated.getChangeQty());

        inventoryStockHistoryJpaDao.deleteById(updated.getId());

        assertNull(inventoryStockHistoryRepository.findById(updated.getId()));
    }
}