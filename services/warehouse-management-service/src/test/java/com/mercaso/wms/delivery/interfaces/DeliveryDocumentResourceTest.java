package com.mercaso.wms.delivery.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.command.DeliveryUploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.service.DeliveryDocumentApplicationService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DeliveryDocumentResourceTest {

    private final DeliveryDocumentApplicationService deliveryDocumentApplicationService = mock(DeliveryDocumentApplicationService.class);
    private final DeliveryDocumentResource deliveryDocumentResource = new DeliveryDocumentResource(
        deliveryDocumentApplicationService);

    private UUID deliveryOrderId;
    private MockMultipartFile file;
    private DeliveryDocumentDto deliveryDocumentDto;

    @BeforeEach
    void setUp() {
        deliveryOrderId = UUID.randomUUID();
        file = new MockMultipartFile(
            "file",
            "test.pdf",
            "application/pdf",
            "test content".getBytes()
        );

        deliveryDocumentDto = DeliveryDocumentDto.builder()
            .id(UUID.randomUUID())
            .fileName("test.pdf")
            .fileUrl("https://example.com/test.pdf")
            .build();
    }

    @Test
    void uploadDocuments_WithValidData_ShouldUploadDocument() throws Exception {
        // Given
        when(deliveryDocumentApplicationService.uploadDocuments(any(DeliveryUploadDocumentCommand.class), any()))
            .thenReturn(List.of(deliveryDocumentDto));

        // When
        List<DeliveryDocumentDto> result = deliveryDocumentResource.uploadDocuments(DeliveryUploadDocumentCommand.builder()
            .entityId(deliveryOrderId)
            .build(), List.of(file).toArray(new MultipartFile[0]));

        // Then
        assertNotNull(result);
        assertEquals(deliveryDocumentDto.getId(), result.getFirst().getId());
        assertEquals(deliveryDocumentDto.getFileName(), result.getFirst().getFileName());
        assertEquals(deliveryDocumentDto.getFileUrl(), result.getFirst().getFileUrl());

        verify(deliveryDocumentApplicationService).uploadDocuments(any(DeliveryUploadDocumentCommand.class), any());
    }

} 