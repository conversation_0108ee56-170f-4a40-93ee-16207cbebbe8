package com.mercaso.wms.batch.writer.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.excel.converter.ListToStringConverter;
import com.mercaso.wms.batch.excel.handler.ColourMarkingWriteHandler;
import com.mercaso.wms.batch.writer.SheetWriter;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

@Slf4j
public abstract class Writer {

    private static final String FILE_PATH = "1-Mercaso Pick Sheet Template_V7.9.xlsx";

    protected static String fileName;

    @BeforeEach
    void setUp() {
        String rootPath = System.getProperty("user.dir");
        fileName = rootPath + "/test_" + RandomStringUtils.randomNumeric(6) + ".xlsx";
    }

    @AfterEach
    void tearDown() {
        try {
            Files.deleteIfExists(Path.of(fileName));
        } catch (IOException e) {
            log.warn("Error deleting file: {}", fileName);
        }
    }

    protected void writeBatchTemplate(WriteTemplateCondition condition, SheetWriter writer) {
        if (StringUtils.isEmpty(fileName)) {
            throw new WmsBusinessException("Failed to generate batch template.");
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Resource resource = new ClassPathResource("template/".concat(FILE_PATH));

        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream)
            .withTemplate(resource.getInputStream())
            .registerConverter(new ListToStringConverter())
            .registerWriteHandler(new ColourMarkingWriteHandler())
            .inMemory(true)
            .build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            writer.write(excelWriter, fillConfig, condition);
            excelWriter.finish();

            byte[] bytes = outputStream.toByteArray();
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(bytes);
            }
        } catch (IOException e) {
            throw new WmsBusinessException("Failed to generate batch template.", e);
        }
    }

}
