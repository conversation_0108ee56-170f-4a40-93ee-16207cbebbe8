package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import static com.mercaso.wms.utils.MockDataUtils.buildTransferTask;
import com.mercaso.wms.utils.TransferTaskResourceApi;
import java.util.List;
import java.util.UUID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryTransferTaskResourceIT extends AbstractIT {

    @Autowired
    TransferTaskResourceApi transferTaskResourceApi;
    @Autowired
    private TransferTaskRepository transferTaskRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;

    @Test
    void when_find_transfer_task_by_picking_task_item_id_return_shipping_order() {
        UUID pickingTaskItemId = UUID.randomUUID();
        List<TransferTask> transferTasks = buildTransferTask(2, TransferTaskStatus.DRAFT);
        TransferTask first = transferTasks.getFirst();
        Warehouse save = warehouseRepository.save(first.getOriginWarehouse());
        Warehouse save1 = warehouseRepository.save(first.getDestinationWarehouse());
        first.getTransferTaskItems().getFirst().setPickingTaskItemId(pickingTaskItemId);
        first.setOriginWarehouse(save);
        first.setDestinationWarehouse(save1);
        transferTaskRepository.saveAll(transferTasks);

        List<TransferTaskDto> byPickingTaskItemId = transferTaskResourceApi.findByPickingTaskItemId(pickingTaskItemId);
        assertNotNull(byPickingTaskItemId);
        assertEquals(1, byPickingTaskItemId.size());
        assertEquals(pickingTaskItemId, byPickingTaskItemId.getFirst().getTransferTaskItems().getFirst().getPickingTaskItemId());
    }

}