package com.mercaso.wms.delivery.application.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.command.GpsRawCommand.GpsReportDto;
import com.mercaso.wms.delivery.application.mapper.ReportGpsCommandApplicationMapper;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.deliverytask.enums.DeliveryTaskStatus;
import com.mercaso.wms.delivery.domain.gps.GpsRaw;
import com.mercaso.wms.delivery.domain.gps.GpsRawRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class GpsRawServiceTest {

    @Mock
    private GpsRawRepository gpsRawRepository;

    @Mock
    private ReportGpsCommandApplicationMapper reportGpsCommandApplicationMapper;

    @Mock
    private AccountRepository accountRepository;

    @Mock
    private DeliveryTaskRepository deliveryTaskRepository;

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    @InjectMocks
    private GpsRawService gpsRawService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void when_report_gps_with_valid_data_then_successfully() {
        // Given
        UUID driverUserId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        GpsRawCommand command = new GpsRawCommand();
        command.setUserId(driverUserId);
        command.setGpsReportDtos(List.of(GpsReportDto.builder().latitude(11.11).longitude(22.22).build()));
        List<GpsRaw> gpsRaws = List.of(GpsRaw.builder()
            .userId(driverUserId)
            .build());

        Account account = Account.builder()
            .userId(driverUserId)
            .userName("TestDriver")
            .build();

        DeliveryTask deliveryTask = DeliveryTask.builder()
            .id(taskId)
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.IN_PROGRESS)
            .build();

        DeliveryOrder deliveryOrder = DeliveryOrder.builder()
            .id(orderId)
            .sequence(1)
            .status(DeliveryOrderStatus.IN_TRANSIT)
            .build();

        // When
        when(reportGpsCommandApplicationMapper.commandToDomains(command.getGpsReportDtos())).thenReturn(gpsRaws);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(account));
        when(deliveryTaskRepository.findByDriverUserIdAndStatus(driverUserId, DeliveryTaskStatus.IN_PROGRESS))
            .thenReturn(Optional.of(deliveryTask));
        when(deliveryOrderRepository.findAllByDeliveryTaskIdAndStatusIn(
            taskId,
            List.of(
                DeliveryOrderStatus.ASSIGNED,
                DeliveryOrderStatus.IN_TRANSIT,
                DeliveryOrderStatus.ARRIVED,
                DeliveryOrderStatus.DELIVERED
            )
        )).thenReturn(List.of(deliveryOrder));

        // Then
        gpsRawService.report(command);

        verify(gpsRawRepository, times(1)).saveAll(anyList());
        verify(accountRepository, times(1)).findByUserId(driverUserId);
        verify(deliveryTaskRepository, times(1))
            .findByDriverUserIdAndStatus(driverUserId, DeliveryTaskStatus.IN_PROGRESS);
    }

    @Test
    void when_report_gps_but_account_not_found_then_continue_processing() {
        // Given
        UUID driverUserId = UUID.randomUUID();
        GpsRawCommand command = new GpsRawCommand();
        command.setUserId(driverUserId);
        command.setGpsReportDtos(List.of(GpsReportDto.builder().latitude(11.11).longitude(22.22).build()));
        List<GpsRaw> gpsRaws = List.of(GpsRaw.builder()
            .userId(driverUserId)
            .build());

        // When
        when(reportGpsCommandApplicationMapper.commandToDomains(command.getGpsReportDtos())).thenReturn(gpsRaws);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.empty());
        when(deliveryTaskRepository.findByDriverUserIdAndStatus(driverUserId, DeliveryTaskStatus.IN_PROGRESS))
            .thenReturn(Optional.empty());

        // Then
        gpsRawService.report(command);

        verify(gpsRawRepository, times(1)).saveAll(anyList());
    }

    @Test
    void when_report_gps_but_no_active_delivery_task_then_return() {
        // Given
        UUID driverUserId = UUID.randomUUID();
        GpsRawCommand command = new GpsRawCommand();
        command.setUserId(driverUserId);
        command.setGpsReportDtos(List.of(GpsReportDto.builder().latitude(11.11).longitude(22.22).build()));
        List<GpsRaw> gpsRaws = List.of(GpsRaw.builder()
            .userId(driverUserId)
            .build());

        Account account = Account.builder()
            .userId(driverUserId)
            .userName("TestDriver")
            .build();

        // When
        when(reportGpsCommandApplicationMapper.commandToDomains(command.getGpsReportDtos())).thenReturn(gpsRaws);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(account));
        when(deliveryTaskRepository.findByDriverUserIdAndStatus(driverUserId, DeliveryTaskStatus.IN_PROGRESS))
            .thenReturn(Optional.empty());

        // Then
        gpsRawService.report(command);

        verify(deliveryOrderRepository, never()).findAllByDeliveryTaskIdAndStatusIn(any(), any());
    }

    @Test
    void when_report_gps_but_no_valid_delivery_orders_then_save_without_order_id() {
        // Given
        UUID driverUserId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        GpsRawCommand command = new GpsRawCommand();
        command.setUserId(driverUserId);
        command.setGpsReportDtos(List.of(GpsReportDto.builder().latitude(11.11).longitude(22.22).build()));
        List<GpsRaw> gpsRaws = List.of(GpsRaw.builder()
            .userId(driverUserId)
            .build());

        Account account = Account.builder()
            .userId(driverUserId)
            .userName("TestDriver")
            .build();

        DeliveryTask deliveryTask = DeliveryTask.builder()
            .id(taskId)
            .driverUserId(driverUserId)
            .status(DeliveryTaskStatus.IN_PROGRESS)
            .build();

        // When
        when(reportGpsCommandApplicationMapper.commandToDomains(command.getGpsReportDtos())).thenReturn(gpsRaws);
        when(accountRepository.findByUserId(driverUserId)).thenReturn(Optional.of(account));
        when(deliveryTaskRepository.findByDriverUserIdAndStatus(driverUserId, DeliveryTaskStatus.IN_PROGRESS))
            .thenReturn(Optional.of(deliveryTask));
        when(deliveryOrderRepository.findAllByDeliveryTaskIdAndStatusIn(any(), any()))
            .thenReturn(List.of());

        // Then
        gpsRawService.report(command);

        verify(gpsRawRepository, times(1)).saveAll(anyList());
    }
}