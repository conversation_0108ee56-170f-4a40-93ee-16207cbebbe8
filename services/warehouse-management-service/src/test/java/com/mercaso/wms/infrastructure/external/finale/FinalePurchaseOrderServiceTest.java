package com.mercaso.wms.infrastructure.external.finale;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildVendorDtoList;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.ims.client.dto.VendorDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.PurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ReceivePurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.ims.ImsAdaptor;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlertService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class FinalePurchaseOrderServiceTest {

    @Mock
    private FinaleProductService finaleProductService;

    @Mock
    private PickingTaskRepository pickingTaskRepository;

    @Mock
    private ReceivingTaskRepository receivingTaskRepository;

    @Mock
    private ImsAdaptor imsAdaptor;

    @Mock
    private FinaleConfigProperties finaleConfigProperties;

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private LocationRepository locationRepository;

    @Mock
    private WmsExceptionAlertService wmsExceptionAlertService;

    @InjectMocks
    private FinalePurchaseOrderService finalePurchaseOrderService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createPurchaseOrder_CreatesAndProcesses_with_crv_cost() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        // Mission is the online supplier
        SourceEnum source = SourceEnum.MISSION;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.RECEIVED,
            ReceivingTaskType.ONLINE_RECEIVING);

        // Set received quantity to ensure items are valid for processing
        receivingTasks.getFirst().getReceivingTaskItems().forEach(item -> item.setReceivedQty(5));

        List<ItemCategoryDto> itemCategoryDtos = receivingTasks.getFirst().getReceivingTaskItems().stream()
            .map(item -> createMockItemCategoryDto(item.getItemId(), item.getSkuNumber(), vendorDto,
                new BigDecimal("100.1"), new BigDecimal("1"), 12))
            .toList();

        PurchaseOrderResponse response = createMockPurchaseOrderResponse("1234567");
        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = createMockReceivePurchaseOrderResponse("123456");

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);
        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);
        when(batchRepository.findById(batchId)).thenReturn(batch);

        // Mock location and config for shipment processing
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP_SB");
        when(finaleConfigProperties.getDomain()).thenReturn("test-domain");
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("http://test-facility");
        when(locationRepository.findByName("SHIP_SB")).thenReturn(createMockLocation());
        
        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_CreatesAndProcesses_For_OnlineSupplier() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        // Mission is the online supplier
        SourceEnum source = SourceEnum.MISSION;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.RECEIVED,
            ReceivingTaskType.ONLINE_RECEIVING);

        // Set received quantity to ensure items are valid for processing
        receivingTasks.getFirst().getReceivingTaskItems().forEach(item -> item.setReceivedQty(5));

        List<ItemCategoryDto> itemCategoryDtos = receivingTasks.getFirst().getReceivingTaskItems().stream()
            .map(item -> createMockItemCategoryDto(item.getItemId(), item.getSkuNumber(), vendorDto,
                new BigDecimal("100.1")))
            .toList();

        PurchaseOrderResponse response = createMockPurchaseOrderResponse("1234567");
        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = createMockReceivePurchaseOrderResponse("123456");

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);
        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);
        when(batchRepository.findById(batchId)).thenReturn(batch);

        // Mock location and config for shipment processing
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP_SB");
        when(finaleConfigProperties.getDomain()).thenReturn("test-domain");
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("http://test-facility");
        when(locationRepository.findByName("SHIP_SB")).thenReturn(createMockLocation());
        
        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_CreatesAndProcesses_For_offlineSupplier() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        // Mission is the online supplier
        SourceEnum source = SourceEnum.COSTCO;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1, source, PickingTaskStatus.COMPLETED,
            PickingTaskType.ORDER);

        // Set picked quantity to ensure items are valid for processing
        pickingTasks.getFirst().getPickingTaskItems().forEach(item -> item.setPickedQty(5));

        List<ItemCategoryDto> itemCategoryDtos = pickingTasks.getFirst().getPickingTaskItems().stream()
            .map(item -> createMockItemCategoryDto(item.getItemId(), item.getSkuNumber(), vendorDto,
                new BigDecimal("100.1")))
            .toList();

        PurchaseOrderResponse response = createMockPurchaseOrderResponse("123456");
        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = createMockReceivePurchaseOrderResponse("123456");

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(pickingTasks);
        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);
        when(batchRepository.findById(batchId)).thenReturn(batch);

        // Mock location and config for shipment processing
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP_SB");
        when(finaleConfigProperties.getDomain()).thenReturn("test-domain");
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("http://test-facility");
        when(locationRepository.findByName("SHIP_SB")).thenReturn(createMockLocation());
        
        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_LogsErrorWhenVendorNotFound() {
        SourceEnum source = mock(SourceEnum.class);
        Batch batch = mock(Batch.class);

        when(source.getVendorName()).thenReturn("NonExistentVendor");
        when(imsAdaptor.getVendorByName("NonExistentVendor")).thenReturn(null);

        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verifyNoInteractions(finaleProductService);
    }

    @Test
    void processReceivingTasks_Returns_Null_When_NoTasksExist() {
        UUID batchId = UUID.randomUUID();
        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(List.of());

        PurchaseOrderResponse response = finalePurchaseOrderService.processReceivingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", mock(SourceEnum.class));

        assertNull(response);
    }

    @Test
    void processPickingTasksReturns_Null_When_NoTasksExist() {
        UUID batchId = UUID.randomUUID();
        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(List.of());

        PurchaseOrderResponse response = finalePurchaseOrderService.processPickingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", mock(SourceEnum.class));

        assertNull(response);
    }

    @Test
    void processReceivingTasks_Returns_Null_When_TasksNotCompleted() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.MISSION;
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.RECEIVING,
            ReceivingTaskType.ONLINE_RECEIVING);

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);

        PurchaseOrderResponse response = finalePurchaseOrderService.processReceivingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", source);

        assertNull(response);
    }

    @Test
    void processPickingTasks_Returns_Null_When_TasksNotCompleted() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.COSTCO;
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1, source, PickingTaskStatus.PICKING,
            PickingTaskType.ORDER);

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(pickingTasks);

        PurchaseOrderResponse response = finalePurchaseOrderService.processPickingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", source);

        assertNull(response);
    }

    @Test
    void processReceivingTasks_Returns_Null_When_CanceledTasks() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.MISSION;
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.CANCELED,
            ReceivingTaskType.ONLINE_RECEIVING);

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);

        PurchaseOrderResponse response = finalePurchaseOrderService.processReceivingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", source);

        assertNull(response);
    }

    @Test
    void processPickingTasks_Returns_Null_When_CanceledTasks() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.COSTCO;
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1, source, PickingTaskStatus.CANCELED,
            PickingTaskType.ORDER);

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(pickingTasks);

        PurchaseOrderResponse response = finalePurchaseOrderService.processPickingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", source);

        assertNull(response);
    }

    @Test
    void processReceivingTasks_Returns_Null_When_NoValidItems() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.MISSION;
        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId, 1, source, ReceivingTaskStatus.RECEIVED,
            ReceivingTaskType.ONLINE_RECEIVING);

        // Set received quantity to 0 to simulate no valid items
        receivingTasks.getFirst().getReceivingTaskItems().forEach(item -> item.setReceivedQty(0));

        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(receivingTasks);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(List.of());

        PurchaseOrderResponse response = finalePurchaseOrderService.processReceivingTasks(
            mock(VendorDto.class), batchId, "2023-10-01", source);

        assertNull(response);
    }

    @Test
    void processPickingTasks_Handles_PartiallyCompleted_Status() {
        UUID batchId = UUID.randomUUID();
        SourceEnum source = SourceEnum.COSTCO;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1, source, PickingTaskStatus.PARTIALLY_COMPLETED,
            PickingTaskType.ORDER);

        // Set picked quantity to ensure items are valid for processing
        pickingTasks.getFirst().getPickingTaskItems().forEach(item -> item.setPickedQty(3));

        List<ItemCategoryDto> itemCategoryDtos = pickingTasks.getFirst().getPickingTaskItems().stream()
            .map(item -> createMockItemCategoryDto(item.getItemId(), item.getSkuNumber(), vendorDto,
                new BigDecimal("100.1")))
            .toList();

        PurchaseOrderResponse response = createMockPurchaseOrderResponse("123456");
        ReceivePurchaseOrderResponse receivePurchaseOrderResponse = createMockReceivePurchaseOrderResponse("1234567");

        when(pickingTaskRepository.findByBatchId(batchId)).thenReturn(pickingTasks);
        when(imsAdaptor.getItemsByIds(any())).thenReturn(itemCategoryDtos);
        when(finaleProductService.createPurchaseOrder(any())).thenReturn(response);
        when(finaleProductService.receivePurchaseOrder(any())).thenReturn(receivePurchaseOrderResponse);
        when(finaleProductService.completePurchaseOrder(any())).thenReturn(response);

        // Mock location and config for shipment processing
        when(finaleConfigProperties.getShipSb()).thenReturn("SHIP_SB");
        when(finaleConfigProperties.getDomain()).thenReturn("test-domain");
        when(finaleConfigProperties.getMfcFacilityUrl()).thenReturn("http://test-facility");
        when(locationRepository.findByName("SHIP_SB")).thenReturn(createMockLocation());

        finalePurchaseOrderService.processPickingTasks(vendorDto, batchId, "2023-10-01", source);

        verify(finaleProductService, times(1)).createPurchaseOrder(any());
    }

    @Test
    void createPurchaseOrder_Handles_Null_Response_FromProcessing() {
        UUID batchId = UUID.randomUUID();
        Batch batch = buildBatch(batchId);
        SourceEnum source = SourceEnum.MISSION;
        VendorDto vendorDto = buildVendorDtoList(1, source).getFirst();

        when(imsAdaptor.getVendorByName(anyString())).thenReturn(vendorDto);
        when(receivingTaskRepository.findByBatchId(batchId)).thenReturn(List.of());

        finalePurchaseOrderService.createPurchaseOrder(source, batch);

        verify(batchRepository, times(0)).findById(any());
    }

    private ItemCategoryDto createMockItemCategoryDto(UUID itemId, String skuNumber, VendorDto vendor, BigDecimal cost) {
        return createMockItemCategoryDto(itemId, skuNumber, vendor, cost, null, null);
    }

    private ItemCategoryDto createMockItemCategoryDto(UUID itemId, String skuNumber, VendorDto vendor,
        BigDecimal cost, BigDecimal crv, Integer pack) {
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
        VendorItemDto vendorItemDto = new VendorItemDto();

        vendorItemDto.setVendorId(vendor.getId());
        vendorItemDto.setVendorName(vendor.getVendorName());
        vendorItemDto.setBackupCost(cost);
        vendorItemDto.setItemId(itemId);
        vendorItemDto.setItemSkuNumber(skuNumber);

        itemCategoryDto.setId(itemId);
        itemCategoryDto.setSkuNumber(skuNumber);
        itemCategoryDto.setVendorItemDtos(List.of(vendorItemDto));
        itemCategoryDto.setBackupVendorId(vendor.getId());

        if (crv != null) {
            itemCategoryDto.setCrv(crv);
        }
        if (pack != null) {
            itemCategoryDto.setPack(pack);
        }

        return itemCategoryDto;
    }

    private PurchaseOrderResponse createMockPurchaseOrderResponse(String orderId) {
        PurchaseOrderResponse response = new PurchaseOrderResponse();
        response.setOrderId(orderId);
        response.setOrderItemList(new ArrayList<>());
        return response;
    }

    private ReceivePurchaseOrderResponse createMockReceivePurchaseOrderResponse(String shipmentId) {
        ReceivePurchaseOrderResponse response = new ReceivePurchaseOrderResponse();
        response.setShipmentId(shipmentId);
        return response;
    }

    private Location createMockLocation() {
        return Location.builder()
            .finaleId("test-location-id")
            .name("SHIP_SB")
            .build();
    }

}