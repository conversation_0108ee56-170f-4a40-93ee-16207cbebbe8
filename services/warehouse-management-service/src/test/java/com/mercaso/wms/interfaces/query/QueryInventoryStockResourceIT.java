package com.mercaso.wms.interfaces.query;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystock.Item;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.InventoryStockResourceApi;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryInventoryStockResourceIT extends AbstractIT {

    @Autowired
    private InventoryStockResourceApi inventoryStockResourceApi;

    @Autowired
    private InventoryStockRepository inventoryStockRepository;

    @Autowired
    private PickingTaskRepository pickingTaskRepository;

    private Warehouse warehouse;
    private Location locationCooler;
    private Location locationPhotoStudio;
    private Location locationTobacco;
    private Location locationA;
    private String skuNumber1;
    private String skuNumber2;

    @BeforeEach
    void setUp() {
        warehouse = warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst();

        locationCooler = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name(".COOLER-".concat(RandomStringUtils.randomNumeric(10)))
            .build();
        locationCooler = locationRepository.save(locationCooler);

        locationPhotoStudio = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name("PHOTO-STUDIO-".concat(RandomStringUtils.randomNumeric(10)))
            .build();
        locationPhotoStudio = locationRepository.save(locationPhotoStudio);

        locationTobacco = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name(".TOBACCO-".concat(RandomStringUtils.randomNumeric(10)))
            .build();
        locationTobacco = locationRepository.save(locationTobacco);

        locationA = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name("101-01-A-01-".concat(RandomStringUtils.randomNumeric(10)))
            .build();
        locationA = locationRepository.save(locationA);

        skuNumber1 = "SKU" + RandomStringUtils.randomNumeric(6);
        skuNumber2 = "SKU" + RandomStringUtils.randomNumeric(6);

        createInventoryStock(skuNumber1, locationA);
        createInventoryStock(skuNumber1, locationCooler);
        createInventoryStock(skuNumber1, locationPhotoStudio);
        createInventoryStock(skuNumber2, locationTobacco);
    }

    private void createInventoryStock(String skuNumber, Location location) {
        InventoryStock stock = InventoryStock.builder()
            .warehouse(warehouse)
            .item(Item.builder()
                .skuNumber(skuNumber)
                .title("Test Item " + RandomStringUtils.randomAlphabetic(5))
                .id(UUID.randomUUID())
                .build())
            .location(location)
            .qty(BigDecimal.ONE)
            .reservedQty(BigDecimal.ZERO)
            .availableQty(BigDecimal.ONE)
            .status(InventoryStockStatus.AVAILABLE)
            .build();
        inventoryStockRepository.save(stock);
    }

    @Test
    void findBySkuNumbers_ShouldReturnSortedResults() throws Exception {
        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockResourceApi.findBySkuNumbers(
            warehouse.getId(),
            List.of(skuNumber1, skuNumber2)
        );

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        List<InventoryStockDto> sku1Results = result.get(skuNumber1);
        assertNotNull(sku1Results);
        assertEquals(3, sku1Results.size());
        assertEquals(locationCooler.getName(), sku1Results.get(0).getLocation().getName());
        assertEquals(locationPhotoStudio.getName(), sku1Results.get(1).getLocation().getName());
        assertEquals(locationA.getName(), sku1Results.get(2).getLocation().getName());

        List<InventoryStockDto> sku2Results = result.get(skuNumber2);
        assertNotNull(sku2Results);
        assertEquals(1, sku2Results.size());
        assertEquals(locationTobacco.getName(), sku2Results.getFirst().getLocation().getName());
    }

    @Test
    void when_find_inventory_stock_should_success() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().getFirst();

        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(warehouses.getFirst().getId())
            .locationId(location.getId())
            .itemId(UUID.randomUUID())
            .skuNumber(RandomStringUtils.randomAlphabetic(10))
            .title(RandomStringUtils.randomAlphabetic(10))
            .qty(BigDecimal.ONE)
            .build();

        InventoryStockDto inventoryStockDto = inventoryStockResourceApi.create(command);

        InventoryStockDto inventoryStockDtoFound = inventoryStockResourceApi.findById(inventoryStockDto.getId());

        assertNotNull(inventoryStockDtoFound);
        assertEquals(inventoryStockDto.getId(), inventoryStockDtoFound.getId());
        assertEquals(inventoryStockDto.getWarehouse().getId(), inventoryStockDtoFound.getWarehouse().getId());
        assertEquals(inventoryStockDto.getLocation().getId(), inventoryStockDtoFound.getLocation().getId());
        assertEquals(inventoryStockDto.getItemId(), inventoryStockDtoFound.getItemId());
        assertEquals(inventoryStockDto.getSkuNumber(), inventoryStockDtoFound.getSkuNumber());
        assertEquals(inventoryStockDto.getTitle(), inventoryStockDtoFound.getTitle());
        assertEquals(inventoryStockDto.getQty().setScale(2, RoundingMode.HALF_UP), inventoryStockDtoFound.getQty());
        assertEquals(InventoryStockStatus.AVAILABLE.name(), inventoryStockDtoFound.getStatus());
    }

    @Test
    void findByPickingTaskItemIds_WhenOnlyBinLocation_ShouldReturnBinLocationType() throws Exception {
        // Given
        String skuNumber = "SKU" + RandomStringUtils.randomNumeric(6);
        Location binLocation = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name("BIN-" + RandomStringUtils.randomNumeric(10))
            .build();
        binLocation = locationRepository.save(binLocation);

        createInventoryStockWithQty(skuNumber, binLocation, 30);

        // Create PickingTaskItem first
        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber(skuNumber)
            .title("Test Item")
            .locationName(binLocation.getName())
            .expectQty(10)
            .pickedQty(5)
            .build();

        // Create PickingTask with PickingTaskItem
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .number("TEST-" + RandomStringUtils.randomNumeric(6))
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MDC)
            .type(PickingTaskType.ORDER)
            .pickingTaskItems(List.of(pickingTaskItem))
            .build();
        pickingTask.calculateTotals();
        pickingTask = pickingTaskRepository.save(pickingTask);

        // Get the saved PickingTaskItem from the saved PickingTask
        pickingTaskItem = pickingTask.getPickingTaskItems().getFirst();

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockResourceApi.findByPickingTaskItemIds(
            warehouse.getId(),
            List.of(pickingTaskItem.getId())
        );

        // Then
        assertNotNull(result);
        List<InventoryStockDto> skuResults = result.get(skuNumber);
        assertNotNull(skuResults);
        assertEquals(1, skuResults.size());
        assertEquals("BIN", skuResults.get(0).getLocationType());
        assertEquals(List.of("[BIN]" + binLocation.getName() + "(30)"), skuResults.get(0).getDetail());
        assertEquals(30, skuResults.get(0).getQoh());
    }

    @Test
    void findByPickingTaskItemIds_WhenBinAndStockWithInsufficientBin_ShouldReturnBinPlusStockLocationType() throws Exception {
        // Given
        String skuNumber = "SKU" + RandomStringUtils.randomNumeric(6);
        Location binLocation = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name("BIN-" + RandomStringUtils.randomNumeric(10))
            .build();
        binLocation = locationRepository.save(binLocation);

        Location stockLocation = Location.builder()
            .warehouse(warehouse)
            .type(LocationType.STOCK)
            .name("STOCK-" + RandomStringUtils.randomNumeric(10))
            .build();
        stockLocation = locationRepository.save(stockLocation);

        createInventoryStockWithQty(skuNumber, binLocation, 3); // insufficient
        createInventoryStockWithQty(skuNumber, stockLocation, 20);

        // Create PickingTaskItem first
        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber(skuNumber)
            .title("Test Item")
            .locationName(binLocation.getName())
            .expectQty(10)
            .pickedQty(5) // unpicked: 5
            .build();

        // Create PickingTask with PickingTaskItem
        PickingTask pickingTask = PickingTask.builder()
            .batchId(UUID.randomUUID())
            .number("TEST-" + RandomStringUtils.randomNumeric(6))
            .status(PickingTaskStatus.CREATED)
            .source(SourceEnum.MDC)
            .type(PickingTaskType.ORDER)
            .pickingTaskItems(List.of(pickingTaskItem))
            .build();
        pickingTask.calculateTotals();
        pickingTask = pickingTaskRepository.save(pickingTask);

        // Get the saved PickingTaskItem from the saved PickingTask
        pickingTaskItem = pickingTask.getPickingTaskItems().getFirst();

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockResourceApi.findByPickingTaskItemIds(
            warehouse.getId(),
            List.of(pickingTaskItem.getId())
        );

        // Then
        assertNotNull(result);
        List<InventoryStockDto> skuResults = result.get(skuNumber);
        assertNotNull(skuResults);
        assertEquals(2, skuResults.size());
        assertEquals("BIN+STOCK", skuResults.get(0).getLocationType());
        assertEquals(List.of("[BIN]" + binLocation.getName() + "(3)", "[STOCK]" + stockLocation.getName() + "(20)"),
            skuResults.get(0).getDetail());
        assertEquals(23, skuResults.get(0).getQoh());
        assertEquals("BIN+STOCK", skuResults.get(1).getLocationType());
        assertEquals(List.of("[BIN]" + binLocation.getName() + "(3)", "[STOCK]" + stockLocation.getName() + "(20)"),
            skuResults.get(1).getDetail());
        assertEquals(23, skuResults.get(1).getQoh());
    }

    private void createInventoryStockWithQty(String skuNumber, Location location, int qty) {
        InventoryStock stock = InventoryStock.builder()
            .warehouse(warehouse)
            .item(Item.builder()
                .skuNumber(skuNumber)
                .title("Test Item " + RandomStringUtils.randomAlphabetic(5))
                .id(UUID.randomUUID())
                .build())
            .location(location)
            .qty(BigDecimal.valueOf(qty))
            .reservedQty(BigDecimal.ZERO)
            .availableQty(BigDecimal.valueOf(qty))
            .status(InventoryStockStatus.AVAILABLE)
            .build();
        inventoryStockRepository.save(stock);
    }
}