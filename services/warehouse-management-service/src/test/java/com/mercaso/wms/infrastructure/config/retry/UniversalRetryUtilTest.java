package com.mercaso.wms.infrastructure.config.retry;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.time.Duration;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class UniversalRetryUtilTest {

    @Mock
    private RetryConfigProperties retryConfigProperties;

    private UniversalRetryUtil universalRetryUtil;

    @BeforeEach
    void setUp() {
        universalRetryUtil = new UniversalRetryUtil(retryConfigProperties);
    }

    @Test
    void executeWithRetry_whenRetryDisabled_shouldExecuteOnce() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = createConfig(false, 3, Duration.ofMillis(100));
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);

        // Act
        String result = universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.just("success"));

        // Assert
        assertEquals("success", result);
    }

    @Test
    void executeWithRetry_whenSuccessful_shouldReturnResult() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = createConfig(true, 3, Duration.ofMillis(100));
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);
        // Note: No setupDefaultStrategyConfig() needed for successful case
        
        // Act
        String result = universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.just("success"));

        // Assert
        assertEquals("success", result);
    }

        @Test
    void executeWithRetry_whenNonRetryableError_shouldFailImmediately() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = createConfig(true, 3, Duration.ofMillis(100));
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);
        
        // Setup strategy config for error evaluation
        RetryConfigProperties.StrategyConfig strategyConfig = createDefaultStrategyConfig();
        when(retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR))
            .thenReturn(strategyConfig);
        
        WebClientResponseException ex = WebClientResponseException.create(404, "Not Found", null, null, null);

        // Act & Assert
        assertThrows(WebClientResponseException.class, () -> 
            universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.error(ex)));
    }

        @Test
    void executeWithRetry_whenRetryableError_shouldEventuallyFail() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = createConfig(true, 2, Duration.ofMillis(50));
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);
        
        // Setup strategy config for error evaluation
        RetryConfigProperties.StrategyConfig strategyConfig = createDefaultStrategyConfig();
        when(retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR))
            .thenReturn(strategyConfig);
        
        WebClientResponseException ex = WebClientResponseException.create(500, "Internal Server Error", null, null, null);

        // Act & Assert
        assertThrows(RetryExhaustedException.class, () -> 
            universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.error(ex)));
    }

        @Test
    void executeWithRetry_withCustomConfig_shouldUseProvidedConfig() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = createConfig(true, 1, Duration.ofMillis(50));
        // Note: No setupDefaultStrategyConfig() needed for successful case with custom config
        
        // Act
        String result = universalRetryUtil.executeWithRetry("custom.endpoint", config, () -> Mono.just("success"));

        // Assert
        assertEquals("success", result);
    }

        @Test
    void executeWithRetry_withCustomErrorCodes_shouldRetryOnCustomCodes() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = new RetryConfigProperties.EffectiveRetryConfig(
            true, 2, Duration.ofMillis(50), 1.0, Duration.ofSeconds(1),
            RetryConfigProperties.RetryStrategy.SERVER_ERROR,
            Set.of("404"), // Custom: retry on 404
            Set.of()
        );
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);
        
        // Setup strategy config for error evaluation
        RetryConfigProperties.StrategyConfig strategyConfig = createDefaultStrategyConfig();
        when(retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR))
            .thenReturn(strategyConfig);

        WebClientResponseException ex = WebClientResponseException.create(404, "Not Found", null, null, null);

        // Act & Assert - Should retry and eventually fail with RetryExhaustedException
        assertThrows(RetryExhaustedException.class, () -> 
            universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.error(ex)));
    }

        @Test
    void executeWithRetry_withExcludeErrorCodes_shouldNotRetryOnExcludedCodes() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = new RetryConfigProperties.EffectiveRetryConfig(
            true, 2, Duration.ofMillis(50), 1.0, Duration.ofSeconds(1),
            RetryConfigProperties.RetryStrategy.SERVER_ERROR,
            Set.of(),
            Set.of("500") // Exclude: don't retry on 500
        );
        when(retryConfigProperties.getEffectiveConfig("test.endpoint")).thenReturn(config);
        
        // Setup strategy config for error evaluation
        RetryConfigProperties.StrategyConfig strategyConfig = createDefaultStrategyConfig();
        when(retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR))
            .thenReturn(strategyConfig);

        WebClientResponseException ex = WebClientResponseException.create(500, "Internal Server Error", null, null, null);

        // Act & Assert - Should fail immediately without retry
        assertThrows(WebClientResponseException.class, () -> 
            universalRetryUtil.executeWithRetry("test.endpoint", () -> Mono.error(ex)));
    }

    @Test
    void builder_shouldCreateValidConfig() {
        // Act
        RetryConfigProperties.EffectiveRetryConfig config = UniversalRetryUtil.builder()
            .enabled(true)
            .maxAttempts(5)
            .interval(Duration.ofSeconds(2))
            .backoffMultiplier(1.5)
            .maxInterval(Duration.ofMinutes(1))
            .strategy(RetryConfigProperties.RetryStrategy.RATE_LIMIT)
            .customErrorCodes(Set.of("429", "503"))
            .excludeErrorCodes(Set.of("401"))
            .build();

        // Assert
        assertTrue(config.enabled());
        assertEquals(5, config.maxAttempts());
        assertEquals(Duration.ofSeconds(2), config.interval());
        assertEquals(1.5, config.backoffMultiplier());
        assertEquals(Duration.ofMinutes(1), config.maxInterval());
        assertEquals(RetryConfigProperties.RetryStrategy.RATE_LIMIT, config.strategy());
        assertEquals(Set.of("429", "503"), config.customErrorCodes());
        assertEquals(Set.of("401"), config.excludeErrorCodes());
    }

    @Test
    void builder_withDefaults_shouldCreateValidConfig() {
        // Act
        RetryConfigProperties.EffectiveRetryConfig config = UniversalRetryUtil.builder().build();

        // Assert
        assertTrue(config.enabled());
        assertEquals(3, config.maxAttempts());
        assertEquals(Duration.ofSeconds(1), config.interval());
        assertEquals(2.0, config.backoffMultiplier());
        assertEquals(Duration.ofSeconds(30), config.maxInterval());
        assertEquals(RetryConfigProperties.RetryStrategy.SERVER_ERROR, config.strategy());
        assertTrue(config.customErrorCodes().isEmpty());
        assertTrue(config.excludeErrorCodes().isEmpty());
    }

    private RetryConfigProperties.EffectiveRetryConfig createConfig(boolean enabled, int maxAttempts, Duration interval) {
        return new RetryConfigProperties.EffectiveRetryConfig(
            enabled, maxAttempts, interval, 2.0, Duration.ofSeconds(30),
            RetryConfigProperties.RetryStrategy.SERVER_ERROR,
            Set.of(), Set.of()
        );
    }

        private RetryConfigProperties.StrategyConfig createDefaultStrategyConfig() {
        RetryConfigProperties.StrategyConfig strategyConfig = new RetryConfigProperties.StrategyConfig();
        strategyConfig.setHttpStatusCodes(Set.of("500", "502", "503", "504"));
        strategyConfig.setExceptionTypes(Set.of("ConnectException", "SocketTimeoutException"));
        strategyConfig.setErrorKeywords(Set.of("timeout", "connection"));
        strategyConfig.setRetryOnTimeout(true);
        strategyConfig.setRetryOnConnection(true);
        return strategyConfig;
    }
}
