package com.mercaso.wms.delivery.utils;

import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class DocumentResourceApi extends IntegrationTestRestUtil {

    private static final String QUERY_DOCUMENTS_URL = "/delivery/query/documents/%s/%s";

    public static final String QUERY_ITEM_DOCUMENTS_URL = "/delivery/query/documents/%s/items";

    public DocumentResourceApi(Environment environment) {
        super(environment);
    }

    public List<DeliveryDocumentDto> findDocuments(UUID entityId,
        EntityEnums entityName,
        DeliveryDocumentType deliveryDocumentType) throws Exception {
        if (deliveryDocumentType != null) {
            return getEntityList(String.format(QUERY_DOCUMENTS_URL, entityId, entityName.name()).concat("?documentTypes=").concat(
                deliveryDocumentType.name()), DeliveryDocumentDto.class);
        }
        return getEntityList(String.format(QUERY_DOCUMENTS_URL, entityId, entityName.name()), DeliveryDocumentDto.class);
    }

    public List<DeliveryDocumentDto> findItemDocuments(UUID deliveryOrderId) throws Exception {
        return getEntityList(String.format(QUERY_ITEM_DOCUMENTS_URL, deliveryOrderId), DeliveryDocumentDto.class);
    }
}
