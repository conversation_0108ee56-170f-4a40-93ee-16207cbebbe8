package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.DataServiceUtil.mockGetFinaleProducts;
import static com.mercaso.wms.utils.DataServiceUtil.mockGetShopifyOrders;
import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildReceivingTask;
import static com.mercaso.wms.utils.MockDataUtils.buildShippingOrders;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.ShopifyOrderDto;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.batch.dto.CreateBatchDto;
import com.mercaso.wms.batch.dto.response.Response;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskType;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseStatus;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.repository.batchitems.BatchItemRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.pickingtask.PickingTaskRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.receivingtask.ReceivingTaskRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.shippingorder.ShippingOrderRepositoryImpl;
import com.mercaso.wms.utils.BatchResourceApi;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class BatchResourceIT extends AbstractIT {

    @Autowired
    private BatchResourceApi batchResourceApi;
    @Autowired
    private LocationRepository locationRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private BatchRepository batchRepository;
    @Autowired
    private BatchItemRepositoryImpl batchItemRepository;
    @Autowired
    private PickingTaskRepositoryImpl pickingTaskRepository;
    @Autowired
    private ReceivingTaskRepositoryImpl receivingTaskRepository;
    @Autowired
    private ShippingOrderRepositoryImpl shippingOrderRepository;

    public static final String LOOK_UP = "1-Mercaso Pick Sheet Template_V7.8_lookup.xlsx";

    private static final String FILE_NAME =
        "Mercaso Pick Sheet" + DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14) + ".xlsx";

    @Test
    void when_create_batch_should_success() throws Exception {
        // given
        List<ShopifyOrderDto> shopifyOrderDtos = mockGetShopifyOrders();
        List<FinaleAvailableStockDto> finaleAvailableStockDtos = new ArrayList<>();
        shopifyOrderDtos.stream()
            .flatMap(shopifyOrderDto -> {
                assertNotNull(shopifyOrderDto.getLineItems());
                return shopifyOrderDto.getLineItems().stream();
            })
            .forEach(shopifyOrderLineItemDto -> finaleAvailableStockDtos.add(mockGetFinaleProducts(shopifyOrderLineItemDto.getSku())));

        Warehouse warehouse = warehouseRepository.save(Warehouse.builder()
            .name(RandomStringUtils.randomAlphabetic(5))
            .status(WarehouseStatus.ACTIVE)
            .build());
        locationRepository.save(Location.builder()
            .warehouse(warehouse)
            .type(LocationType.BIN)
            .name(Objects.requireNonNull(Objects.requireNonNull(finaleAvailableStockDtos.getFirst().getStockItemsOnHand())
                .getFirst()
                .getSubLocation()).getName())
            .build());

        try (FileInputStream fis = new FileInputStream(LOOK_UP)) {
            when(documentOperations.downloadDocument(any())).thenReturn(fis.readAllBytes());
        }
        when(documentOperations.uploadDocument(any())).thenReturn(new DocumentResponse("http://localhost:8080/file", FILE_NAME));

        CreateBatchDto createBatchDto = new CreateBatchDto();
        createBatchDto.setFileNames(List.of(LOOK_UP));
        createBatchDto.setTaggedWith(LocalDate.now());

        @SuppressWarnings("rawtypes")
        Response batch = batchResourceApi.createBatch(createBatchDto);

        assertNotNull(batch);
    }

    @Test
    void when_cancel_batch_with_valid_status_then_success() throws Exception {
        Batch batch = buildBatch(UUID.randomUUID());
        batch = batchRepository.save(batch);
        UUID batchId = batch.getId();
        // Given: Create batch items, picking tasks, receiving tasks, and shipping orders
        List<BatchItem> batchItems = buildBatchItems(batchId, 3);
        batchItemRepository.saveAll(batchItems);

        List<PickingTask> pickingTasks = buildPickingTask(batchId, 2,
            SourceEnum.MDC,
            PickingTaskStatus.CREATED,
            com.mercaso.wms.domain.pickingtask.enums.PickingTaskType.ORDER);
        pickingTaskRepository.saveAll(pickingTasks);

        List<ReceivingTask> receivingTasks = buildReceivingTask(batchId,
            2,
            SourceEnum.CORE_MARK,
            ReceivingTaskStatus.CREATED,
            ReceivingTaskType.ONLINE_RECEIVING);
        receivingTaskRepository.saveAll(receivingTasks);

        Warehouse warehouse = warehouseRepository.findByType(WarehouseType.INTERNAL).getFirst();

        List<ShippingOrder> shippingOrders = buildShippingOrders(2, ShippingOrderStatus.OPEN);

        shippingOrders.forEach(so -> {
            so.setStatus(ShippingOrderStatus.IN_PROGRESS);
            so.setBatchId(batchId);
            so.setWarehouse(warehouse);
        });
        shippingOrderRepository.saveAll(shippingOrders);

        // Verify initial state
        assertEquals(3, batchItemRepository.findBatchItemsBy(batchId, "MDC").size());
        assertEquals(2, pickingTaskRepository.findByBatchId(batchId).size());
        assertEquals(2, receivingTaskRepository.findByBatchId(batchId).size());
        assertEquals(2, shippingOrderRepository.findByBatchId(batchId).size());

        // When: Cancel the batch
        Response<String> response = batchResourceApi.cancelBatch(batchId);

        // Then: Verify the response
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("success", response.getMessage());

        // Verify batch status is updated
        Batch cancelledBatch = batchRepository.findById(batchId);
        assertNotNull(cancelledBatch);
        assertEquals(BatchStatus.CANCELED, cancelledBatch.getStatus());

        // Verify all related entities are deleted/updated
        assertEquals(0, batchItemRepository.findBatchItemsBy(batchId, "").size());
        assertEquals(0, pickingTaskRepository.findByBatchId(batchId).size());
        assertEquals(0, receivingTaskRepository.findByBatchId(batchId).size());

        // Verify shipping orders are updated to OPEN status
        List<ShippingOrder> updatedShippingOrders = shippingOrderRepository.findByBatchId(batchId);
        assertEquals(2, updatedShippingOrders.size());
        updatedShippingOrders.forEach(order -> assertEquals(ShippingOrderStatus.OPEN, order.getStatus()));
    }

}