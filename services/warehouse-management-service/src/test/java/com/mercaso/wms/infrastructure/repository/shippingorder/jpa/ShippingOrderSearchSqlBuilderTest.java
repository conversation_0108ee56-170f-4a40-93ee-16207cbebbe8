package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.application.query.SortType;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

class ShippingOrderSearchSqlBuilderTest {

    @Test
    void buildSelectSql_WithBasicQuery_ShouldGenerateValidSql() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .deliveryDate("2025-01-15")
            .breakdownName("Breakdown A")
            .statuses(java.util.List.of("OPEN", "PICKED"))
            .sort(SortType.CREATED_AT_DESC)
            .page(1)
            .pageSize(20)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("SELECT"));
        assertTrue(selectSql.contains("FROM shipping_order so"));
        assertTrue(selectSql.contains("LEFT JOIN location l"));
        assertTrue(selectSql.contains("WHERE so.deleted_at IS NULL"));
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        assertTrue(selectSql.contains("AND so.delivery_date = :deliveryDate"));
        assertTrue(selectSql.contains("AND l.name ILIKE :breakdownName"));
        assertTrue(selectSql.contains("AND so.status IN (:statuses)"));
        assertTrue(selectSql.contains("LIMIT :limit OFFSET :offset"));
    }

    @Test
    void buildCountSql_WithBasicQuery_ShouldGenerateValidSql() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build();

        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .params(params)
            .build();

        // When
        String countSql = sqlBuilder.buildCountSql();

        // Then
        assertNotNull(countSql);
        assertTrue(countSql.contains("SELECT COUNT(DISTINCT so.id)"));
        assertTrue(countSql.contains("FROM shipping_order so"));
        assertTrue(countSql.contains("LEFT JOIN location l"));
        assertTrue(countSql.contains("WHERE so.deleted_at IS NULL"));
        assertTrue(countSql.contains("AND so.order_number = :orderNumber"));
    }

    @Test
    void buildSelectSql_WithNullQuery_ShouldUseDefaultSort() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build(); // sort is null

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("ORDER BY so.created_at DESC"));
    }

    @Test
    void buildSelectSql_WithIncludeHighValueItems_ShouldIncludeHighValueItemsCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .hasHighValueItems(true)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND EXISTS ("));
        assertTrue(selectSql.contains("SELECT 1 FROM shipping_order_items soi_hv"));
        assertTrue(selectSql.contains("WHERE soi_hv.shipping_order_id = so.id"));
        assertTrue(selectSql.contains("AND soi_hv.high_value_item = true"));
        assertTrue(selectSql.contains("AND soi_hv.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithIncludeHighValueItemsFalse_ShouldNotIncludeHighValueItemsCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .hasHighValueItems(false)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        // Should not contain high value items condition when includeHighValueItems is false
        assertTrue(!selectSql.contains("AND EXISTS (") || !selectSql.contains("soi_hv.high_value_item = true"));
    }

    @Test
    void buildSelectSql_ShouldAlwaysIncludeHasHighValueItemsColumn() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("CASE WHEN EXISTS ("));
        assertTrue(selectSql.contains("SELECT 1 FROM shipping_order_items soi_check"));
        assertTrue(selectSql.contains("WHERE soi_check.shipping_order_id = so.id"));
        assertTrue(selectSql.contains("AND soi_check.high_value_item = true"));
        assertTrue(selectSql.contains("AND soi_check.deleted_at IS NULL"));
        assertTrue(selectSql.contains(") THEN true ELSE false END as has_high_value_items"));
    }

    @Test
    void buildCountSql_WithIncludeHighValueItems_ShouldIncludeHighValueItemsCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .hasHighValueItems(true)
            .build();

        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .params(params)
            .build();

        // When
        String countSql = sqlBuilder.buildCountSql();

        // Then
        assertNotNull(countSql);
        assertTrue(countSql.contains("SELECT COUNT(DISTINCT so.id)"));
        assertTrue(countSql.contains("AND EXISTS ("));
        assertTrue(countSql.contains("SELECT 1 FROM shipping_order_items soi_hv"));
        assertTrue(countSql.contains("WHERE soi_hv.shipping_order_id = so.id"));
        assertTrue(countSql.contains("AND soi_hv.high_value_item = true"));
        assertTrue(countSql.contains("AND soi_hv.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithDriverUserIdAndTruckNumber_ShouldIncludeAllConditions() {
        // Given
        java.util.UUID driverUserId = java.util.UUID.randomUUID();
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .driverUserId(driverUserId)
            .truckNumber("TRUCK-001")
            .hasHighValueItems(true)
            .deliveryDate("2025-01-15")
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        assertTrue(selectSql.contains("AND so.driver_user_id = :driverUserId"));
        assertTrue(selectSql.contains("AND so.truck_number ILIKE :truckNumber"));
        assertTrue(selectSql.contains("AND so.delivery_date = :deliveryDate"));
        assertTrue(selectSql.contains("AND EXISTS ("));
        assertTrue(selectSql.contains("soi_hv.high_value_item = true"));
        assertTrue(selectSql.contains("has_high_value_items"));
    }

    @Test
    void buildSelectSql_WithNeedsCheckOrder_ShouldIncludeNeedsCheckOrderCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .qaRequired(true)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND EXISTS ("));
        assertTrue(selectSql.contains("SELECT 1 FROM shipping_order_items soi_check"));
        assertTrue(selectSql.contains("WHERE soi_check.shipping_order_id = so.id"));
        assertTrue(selectSql.contains("AND COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0)"));
        assertTrue(selectSql.contains("AND soi_check.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithNeedsCheckOrderFalse_ShouldNotIncludeNeedsCheckOrderCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .qaRequired(false)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        // Should not contain needs check order condition when needsCheckOrder is false
        assertTrue(selectSql.contains("COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0)"));
    }

    @Test
    void buildCountSql_WithNeedsCheckOrder_ShouldIncludeNeedsCheckOrderCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .qaRequired(true)
            .build();

        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .params(params)
            .build();

        // When
        String countSql = sqlBuilder.buildCountSql();

        // Then
        assertNotNull(countSql);
        assertTrue(countSql.contains("SELECT COUNT(DISTINCT so.id)"));
        assertTrue(countSql.contains("AND EXISTS ("));
        assertTrue(countSql.contains("SELECT 1 FROM shipping_order_items soi_check"));
        assertTrue(countSql.contains("WHERE soi_check.shipping_order_id = so.id"));
        assertTrue(countSql.contains("AND COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0)"));
        assertTrue(countSql.contains("AND soi_check.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithAllConditions_ShouldIncludeAllFilters() {
        // Given
        java.util.UUID driverUserId = java.util.UUID.randomUUID();
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .driverUserId(driverUserId)
            .truckNumber("TRUCK-001")
            .hasHighValueItems(true)
            .qaRequired(true)
            .deliveryDate("2025-01-15")
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        assertTrue(selectSql.contains("AND so.driver_user_id = :driverUserId"));
        assertTrue(selectSql.contains("AND so.truck_number ILIKE :truckNumber"));
        assertTrue(selectSql.contains("AND so.delivery_date = :deliveryDate"));
        // High value items condition
        assertTrue(selectSql.contains("soi_hv.high_value_item = true"));
        // Needs check order condition
        assertTrue(selectSql.contains("COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0)"));
        // has_high_value_items column
        assertTrue(selectSql.contains("has_high_value_items"));
    }

    @Test
    void buildSelectSql_WithPickedNotDelivered_ShouldIncludePickedNotDeliveredCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .pickedNotDelivered(true)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND EXISTS ("));
        assertTrue(selectSql.contains("SELECT 1 FROM shipping_order_items soi_pnd"));
        assertTrue(selectSql.contains("WHERE soi_pnd.shipping_order_id = so.id"));
        assertTrue(selectSql.contains("AND COALESCE(soi_pnd.delivered_qty, 0) < COALESCE(soi_pnd.picked_qty, 0)"));
        assertTrue(selectSql.contains("AND soi_pnd.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithPickedNotDeliveredFalse_ShouldNotIncludePickedNotDeliveredCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .pickedNotDelivered(false)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        // Should not contain picked not delivered condition when pickedNotDelivered is false
        assertTrue(!selectSql.contains("COALESCE(soi_pnd.delivered_qty, 0) < COALESCE(soi_pnd.picked_qty, 0)"));
    }

    @Test
    void buildCountSql_WithPickedNotDelivered_ShouldIncludePickedNotDeliveredCondition() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .pickedNotDelivered(true)
            .build();

        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .params(params)
            .build();

        // When
        String countSql = sqlBuilder.buildCountSql();

        // Then
        assertNotNull(countSql);
        assertTrue(countSql.contains("SELECT COUNT(DISTINCT so.id)"));
        assertTrue(countSql.contains("AND EXISTS ("));
        assertTrue(countSql.contains("SELECT 1 FROM shipping_order_items soi_pnd"));
        assertTrue(countSql.contains("WHERE soi_pnd.shipping_order_id = so.id"));
        assertTrue(countSql.contains("AND COALESCE(soi_pnd.delivered_qty, 0) < COALESCE(soi_pnd.picked_qty, 0)"));
        assertTrue(countSql.contains("AND soi_pnd.deleted_at IS NULL"));
    }

    @Test
    void buildSelectSql_WithAllConditionsIncludingPickedNotDelivered_ShouldIncludeAllFilters() {
        // Given
        java.util.UUID driverUserId = java.util.UUID.randomUUID();
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .driverUserId(driverUserId)
            .truckNumber("TRUCK-001")
            .hasHighValueItems(true)
            .qaRequired(true)
            .pickedNotDelivered(true)
            .deliveryDate("2025-01-15")
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        assertTrue(selectSql.contains("AND so.driver_user_id = :driverUserId"));
        assertTrue(selectSql.contains("AND so.truck_number ILIKE :truckNumber"));
        assertTrue(selectSql.contains("AND so.delivery_date = :deliveryDate"));
        // High value items condition
        assertTrue(selectSql.contains("soi_hv.high_value_item = true"));
        // Needs check order condition
        assertTrue(selectSql.contains("COALESCE(soi_check.fulfilled_qty, 0) != COALESCE(soi_check.picked_qty, 0)"));
        // Picked not delivered condition
        assertTrue(selectSql.contains("COALESCE(soi_pnd.delivered_qty, 0) < COALESCE(soi_pnd.picked_qty, 0)"));
        // has_high_value_items column
        assertTrue(selectSql.contains("has_high_value_items"));
    }

    @Test
    void buildSelectSql_ShouldIncludeDeliveredTotalQtyColumn() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("COALESCE(SUM(COALESCE(soi.delivered_qty, 0)) OVER (PARTITION BY so.id), 0) as delivered_total_qty"));
    }
} 