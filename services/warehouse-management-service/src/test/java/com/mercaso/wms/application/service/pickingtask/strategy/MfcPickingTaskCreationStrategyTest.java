package com.mercaso.wms.application.service.pickingtask.strategy;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MfcPickingTaskCreationStrategyTest {

    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);
    private final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private MfcPickingTaskCreationStrategy strategy;
    private PickingTaskCreationContext context;
    private UUID batchId;
    private Map<UUID, Location> locationMap;

    @BeforeEach
    void setUp() {
        strategy = new MfcPickingTaskCreationStrategy(batchItemQueryService, featureFlagsManager);
        batchId = UUID.randomUUID();
        locationMap = new HashMap<>();
        context = PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(locationMap)
            .pickingTaskRepository(pickingTaskRepository)
            .build();
    }

    @Test
    void getSource_ShouldReturnMFC() {
        assertEquals(SourceEnum.MFC, strategy.getSource());
    }

    @Test
    void extractData_ShouldCallFindByWithCorrectParameters() {
        // Given
        List<BatchItem> expectedItems = buildBatchItems(batchId, 5);
        when(batchItemQueryService.findUnprocessedBy(batchId, SourceEnum.MFC.name())).thenReturn(expectedItems);

        // When
        List<BatchItem> result = strategy.extractData(context);

        // Then
        verify(batchItemQueryService).findUnprocessedBy(batchId, SourceEnum.MFC.name());
        assertEquals(expectedItems, result);
    }

    @Test
    void extractData_WhenNoItemsFound_ShouldReturnEmptyList() {
        // Given
        when(batchItemQueryService.findUnprocessedBy(batchId, SourceEnum.MFC.name())).thenReturn(Collections.emptyList());

        // When
        List<BatchItem> result = strategy.extractData(context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void createAndSave_WhenEmptyItemsList_ShouldReturnEmptyList() {
        // When
        List<PickingTask> result = strategy.createAndSave(Collections.emptyList(), context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void createAndSave_WhenFeatureFlagDisabled_ShouldCreateBasicDepartmentGroupedTasks() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(false);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MFC.name())
                .department("Beverage")
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .expectQty(10)
                .skuNumber("BEV-SKU-001")
                .title("Beverage Item 1")
                .orderNumber("ORD-BEV-001")
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MFC.name())
                .department("Beverage")
                .locationName("A01-02")
                .breakdownName("A01-B02")
                .expectQty(8)
                .skuNumber("BEV-SKU-002")
                .title("Beverage Item 2")
                .orderNumber("ORD-BEV-002")
                .build(),
            BatchItem.builder()
                .id(UUID.randomUUID())
                .batchId(batchId)
                .source(SourceEnum.MFC.name())
                .department("Candy & Snacks")
                .locationName("A02-01")
                .breakdownName("A02-B01")
                .expectQty(5)
                .skuNumber("CANDY-SKU-001")
                .title("Candy Item 1")
                .orderNumber("ORD-CANDY-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Should create one task without breakdown grouping
        assertEquals(SourceEnum.MFC, result.getFirst().getSource());
        assertEquals(PickingTaskType.BATCH, result.getFirst().getType());
        verify(pickingTaskRepository).saveAll(anyList());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_Success() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-01")
                .breakdownName("A-01")
                .expectQty(10)
                .skuNumber("SKU_1")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Beverage Product 1")
                .orderNumber("ORD-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("B01-01")
                .breakdownName("B-01")
                .expectQty(10)
                .skuNumber("SKU_2")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Beverage Product 2")
                .orderNumber("ORD-002")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A02-01")
                .breakdownName("A02-B01")
                .expectQty(10)
                .skuNumber("SKU_3")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Candy Product 1")
                .orderNumber("ORD-003")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 3);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        assertEquals(3, result.size());
        result.forEach(task -> {
            assertEquals(SourceEnum.MFC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_EmptyBatchItems_NoTasksCreated() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        // When
        List<PickingTask> result = strategy.createAndSave(Collections.emptyList(), context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNullBreakdownName_GroupedAsOther() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department("Beverage")
                .locationName("A-01")
                .breakdownName(null)
                .expectQty(10)
                .skuNumber("SKU_1")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Null Breakdown Item 1")
                .orderNumber("ORD-NULL-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("A-02")
                .breakdownName("A-B02")
                .expectQty(10)
                .skuNumber("SKU_2")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Valid Breakdown Item")
                .orderNumber("ORD-VALID-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        // 2 picking tasks should be created: Other's Beverage, A's Beverage
        assertEquals(2, result.size());
        result.forEach(task -> {
            assertEquals(SourceEnum.MFC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNullDepartment_GroupedAsOther() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department(null)
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .expectQty(10)
                .skuNumber("SKU_1")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Null Department Item")
                .orderNumber("ORD-NULL-DEP-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-02")
                .breakdownName("A01-B02")
                .expectQty(10)
                .skuNumber("SKU_2")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Valid Department Item")
                .orderNumber("ORD-VALID-DEP-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 2);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        assertEquals(2, result.size());
        result.forEach(task -> {
            assertEquals(SourceEnum.MFC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNAAndCoolerData_RemovesData() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .expectQty(10)
                .skuNumber("SKU_1")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Valid Item")
                .orderNumber("ORD-VALID-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("NA")
                .breakdownName("A01-B02")
                .expectQty(10)
                .skuNumber("SKU_2")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("NA Item")
                .orderNumber("ORD-NA-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName(".COOLER")
                .breakdownName("COOLER-B01")
                .expectQty(10)
                .skuNumber("SKU_3")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Cooler Item")
                .orderNumber("ORD-COOL-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        // Only 1 picking task should be created because NA and COOLER data are removed
        assertEquals(1, result.size());
        assertEquals(SourceEnum.MFC, result.getFirst().getSource());
        assertEquals(PickingTaskType.BATCH, result.getFirst().getType());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_ComplexGroupingScenario() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        //Create complex test data, including different breakdown aisle and department combinations
        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .expectQty(10)
                .skuNumber("SKU_1")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A01 Beverage 1")
                .orderNumber("ORD-A01-BEV-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-02")
                .breakdownName("A01-B02")
                .expectQty(10)
                .skuNumber("SKU_2")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A01 Beverage 2")
                .orderNumber("ORD-A01-BEV-002")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A01-03")
                .breakdownName("A01-B03")
                .expectQty(10)
                .skuNumber("SKU_3")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A01 Candy")
                .orderNumber("ORD-A01-CANDY-001")
                .build(),
            BatchItem.builder()
                .department("Beverage")
                .locationName("A02-01")
                .breakdownName("A02-B01")
                .expectQty(10)
                .skuNumber("SKU_4")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A02 Beverage")
                .orderNumber("ORD-A02-BEV-001")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A02-02")
                .breakdownName("A02-B02")
                .expectQty(10)
                .skuNumber("SKU_5")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A02 Candy")
                .orderNumber("ORD-A02-CANDY-001")
                .build(),
            BatchItem.builder()
                .department("Tobacco")
                .locationName("A03-01")
                .breakdownName("A03-B01")
                .expectQty(10)
                .skuNumber("SKU_6")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("A03 Tobacco")
                .orderNumber("ORD-A03-TOB-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 5);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        //5 picking tasks should be created:
        // 1. Beverage of A01 (item1, item2)
        // 2. Candy & Snacks of A01 (item3)
        // 3. Beverage of A02 (item4)
        // 4. Candy & Snacks of A02 (item5)
        // 5. Tobacco of A03 (item6)
        assertEquals(5, result.size());
        result.forEach(task -> {
            assertEquals(SourceEnum.MFC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithCandyAndSnacksCategory_ShouldGroupByCategory() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            // Candy & Snacks with different categories
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .category("Candy")
                .expectQty(8)
                .skuNumber("CANDY-SKU-001")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Candy Product 1")
                .orderNumber("ORD-CANDY-001")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A01-02")
                .breakdownName("A01-B02")
                .category("Candy")
                .expectQty(12)
                .skuNumber("CANDY-SKU-002")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Candy Product 2")
                .orderNumber("ORD-CANDY-002")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A01-03")
                .breakdownName("A01-B03")
                .category("Snacks")
                .expectQty(6)
                .skuNumber("SNACKS-SKU-001")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Snacks Product 1")
                .orderNumber("ORD-SNACKS-001")
                .build(),
            BatchItem.builder()
                .department("Candy & Snacks")
                .locationName("A01-04")
                .breakdownName("A01-B04")
                .category(null) // No category
                .expectQty(4)
                .skuNumber("NO-CAT-SKU-001")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("No Category Product")
                .orderNumber("ORD-NOCAT-001")
                .build()
        );

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 3);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.createAndSave(items, context);

        // Then
        assertEquals(3, result.size()); // Should create 3 tasks: Candy, Snacks, NO_CATEGORY
        result.forEach(task -> {
            assertEquals(SourceEnum.MFC, task.getSource());
            assertEquals(PickingTaskType.BATCH, task.getType());
        });
    }

    @Test
    void execute_WhenNoItemsFound_ShouldReturnEmptyList() {
        // Given
        when(batchItemQueryService.findUnprocessedBy(batchId, SourceEnum.MFC.name())).thenReturn(Collections.emptyList());

        // When
        List<PickingTask> result = strategy.execute(context);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void execute_WithValidItems_ShouldReturnCreatedTasks() {
        // Given
        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);

        List<BatchItem> items = Arrays.asList(
            BatchItem.builder()
                .department("Beverage")
                .locationName("A01-01")
                .breakdownName("A01-B01")
                .expectQty(10)
                .skuNumber("TEST-SKU")
                .source(SourceEnum.MFC.name())
                .id(UUID.randomUUID())
                .batchId(batchId)
                .title("Test Item")
                .orderNumber("TEST-ORDER")
                .build()
        );

        when(batchItemQueryService.findUnprocessedBy(batchId, SourceEnum.MFC.name())).thenReturn(items);

        List<PickingTask> builtPickingTasks = buildPickingTask(batchId, 1);
        builtPickingTasks.forEach(task -> {
            task.setSource(SourceEnum.MFC);
            task.setType(PickingTaskType.BATCH);
        });
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(builtPickingTasks);

        // When
        List<PickingTask> result = strategy.execute(context);

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(batchItemQueryService).findUnprocessedBy(batchId, SourceEnum.MFC.name());
        verify(pickingTaskRepository).saveAll(anyList());
        result.forEach(task -> assertEquals(SourceEnum.MFC, task.getSource()));
    }
} 