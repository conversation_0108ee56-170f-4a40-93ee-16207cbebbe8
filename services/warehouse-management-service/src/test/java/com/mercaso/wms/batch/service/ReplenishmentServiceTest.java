package com.mercaso.wms.batch.service;


import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockItemsOnHandDto;
import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;

class ReplenishmentServiceTest {

    private final FinaleProductService finaleProductService = mock(FinaleProductService.class);

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private final ShippingOrderRepository shippingOrderRepository = mock(ShippingOrderRepository.class);

    private final ReplenishmentService replenishmentService = new ReplenishmentService(finaleProductService,
        locationRepository,
        pgAdvisoryLock,
        shippingOrderRepository);

    @Test
    void replenish_WhenNoAvailableStock_ShouldReturnEarly() {
        when(finaleProductService.getAvailableStock(1000)).thenReturn(Collections.emptyList());

        replenishmentService.replenish();

        verify(locationRepository, never()).findAll();
    }

    @Test
    void shouldReturnEmptyWhenLockNotAcquired() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        // When
        ByteArrayOutputStream result = replenishmentService.replenish();

        // Then
        verify(finaleProductService, never()).getAvailableStock(anyInt());
        assertNotNull(result);
    }

    @Test
    void shouldReturnEmptyWhenNoAvailableStock() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getAvailableStock(anyInt())).thenReturn(Collections.emptyList());

        // When
        ByteArrayOutputStream result = replenishmentService.replenish();

        // Then
        verify(locationRepository, never()).findAll();
        assertNotNull(result);
    }

    @Test
    void shouldGenerateReplenishmentReport() {
        // Given
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);

        // Mock available stock
        FinaleAvailableStockItemsOnHandDto itemOnHand = new FinaleAvailableStockItemsOnHandDto();
        FinaleAvailableStockItemsOnHandDto.Location subLocation = new FinaleAvailableStockItemsOnHandDto.Location();
        subLocation.setName("STOCK-01");
        itemOnHand.setSubLocation(subLocation);

        FinaleAvailableStockDto stockDto = new FinaleAvailableStockDto();
        stockDto.setSku("SKU123");
        stockDto.setStockItemsOnHand(List.of(itemOnHand));
        when(finaleProductService.getAvailableStock(anyInt())).thenReturn(List.of(stockDto));

        // Mock locations
        Location location = Location.builder().build();
        location.setName("STOCK-01");
        location.setType(LocationType.STOCK);
        when(locationRepository.findAll()).thenReturn(List.of(location));

        // Mock SKU counts
        SkuCountByDeliveryDate skuCount = new SkuCountByDeliveryDate("SKU123", 5L);
        when(shippingOrderRepository.skuCountByDeliveryDate(anyString()))
            .thenReturn(List.of(skuCount));

        // When
        ByteArrayOutputStream result = replenishmentService.replenish();

        // Then
        assertNotNull(result);
        verify(pgAdvisoryLock).unLock(anyInt());
    }

}