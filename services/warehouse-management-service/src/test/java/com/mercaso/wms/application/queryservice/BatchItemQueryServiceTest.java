package com.mercaso.wms.application.queryservice;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

class BatchItemQueryServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);

    private final BatchItemQueryService batchItemQueryService = new BatchItemQueryService(batchItemRepository);

    @Test
    void when_find_big_beverage_batch_items_by_batch_id_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        when(batchItemRepository.findBatchItemsBy(any(), any(), any(), anyBoolean())).thenReturn(batchItems);

        List<BatchItem> result = batchItemQueryService.findBigBeverageBatchItemsByBatchId(batchItems.get(0).getBatchId(),
            SourceEnum.MFC.name());

        assertEquals(batchItems.size(), result.size());
    }

    @Test
    void when_find_by_batch_id_and_source_then_return_batch_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        when(batchItemRepository.findBatchItemsBy(any(), (String) any())).thenReturn(batchItems);

        List<BatchItem> result = batchItemQueryService.findBy(batchItems.get(0).getBatchId(), SourceEnum.DOWNEY.name());

        assertEquals(batchItems.size(), result.size());
    }

    @Test
    void searchBatchItems_ShouldReturnPageOfBatchItems() {
        // Given
        String source = "MFC";
        String deliveryDate = "2024-03-20";
        Pageable pageable = PageRequest.of(0, 10);

        Page<BatchItem> expectedPage = new PageImpl<>(List.of(BatchItem.builder().build()));

        when(batchItemRepository.searchBatchItems(source, deliveryDate, pageable))
            .thenReturn(expectedPage);

        // When
        Page<BatchItem> result = batchItemQueryService.searchBatchItems(source, deliveryDate, pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result).isEqualTo(expectedPage);
    }


}