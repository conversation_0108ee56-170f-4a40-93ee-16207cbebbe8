package com.mercaso.wms.delivery.utils;

import com.mercaso.wms.delivery.application.command.GpsRawCommand;
import com.mercaso.wms.delivery.application.dto.gps.GpsRawDto;
import com.mercaso.wms.delivery.application.dto.gps.LatestGpsCacheDto;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class GpsRawResourceApi extends IntegrationTestRestUtil {

    private static final String REPORT_GPS_URL = "/delivery/gps-raw/report";

    private static final String GET_GPS_BY_DELIVERY_TASK_ID = "/delivery/query/gps-raw/%s";

    private static final String GET_ALL_LATEST_GPS = "/delivery/query/gps-raw/latest-gps/list";

    public GpsRawResourceApi(Environment environment) {
        super(environment);
    }

    public Void report(GpsRawCommand command) throws Exception {
        return createEntity(REPORT_GPS_URL, SerializationUtils.serialize(command), Void.class);
    }

    public List<GpsRawDto> getGpsByDeliveryTaskId(UUID deliveryTaskId) throws Exception {
        return getEntityList(String.format(GET_GPS_BY_DELIVERY_TASK_ID, deliveryTaskId), GpsRawDto.class);
    }

    public List<LatestGpsCacheDto> findLatestGpsCache() throws Exception {
        return getEntityList(GET_ALL_LATEST_GPS, LatestGpsCacheDto.class);
    }
} 