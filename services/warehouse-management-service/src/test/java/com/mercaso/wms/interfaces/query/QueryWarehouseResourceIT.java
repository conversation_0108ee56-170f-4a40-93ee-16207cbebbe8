package com.mercaso.wms.interfaces.query;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.List;
import org.junit.jupiter.api.Test;

class QueryWarehouseResourceIT extends AbstractIT {

    @Test
    void when_find_by_type_then_return_warehouses() throws Exception {
        List<WarehouseDto> warehouses = warehouseResourceApi.findByType(WarehouseType.EXTERNAL);
        assertEquals(WarehouseType.EXTERNAL, warehouses.getFirst().getType());
    }

    @Test
    void when_find_all_then_return_warehouses() throws Exception {
        List<WarehouseDto> warehouses = warehouseResourceApi.findAll();

        assertTrue(warehouses.size() >= 7);
    }

}