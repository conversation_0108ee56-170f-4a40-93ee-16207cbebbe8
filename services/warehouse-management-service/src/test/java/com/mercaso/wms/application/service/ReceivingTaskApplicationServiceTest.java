package com.mercaso.wms.application.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskDto;
import com.mercaso.wms.application.dto.receivingtask.ReceivingTaskItemDto;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskDtoApplicationMapper;
import com.mercaso.wms.application.mapper.receivingtask.ReceivingTaskItemDtoApplicationMapper;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItemRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class ReceivingTaskApplicationServiceTest {

    private final ReceivingTaskRepository receivingTaskRepository = mock(ReceivingTaskRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final BusinessEventDispatcher businessEventDispatcher = mock(BusinessEventDispatcher.class);
    private final ReceivingTaskDtoApplicationMapper receivingTaskDtoApplicationMapper = mock(ReceivingTaskDtoApplicationMapper.class);
    private final ReceivingTaskItemRepository receivingTaskItemRepository = mock(ReceivingTaskItemRepository.class);
    private final ReceivingTaskItemDtoApplicationMapper receivingTaskItemDtoApplicationMapper = mock(
        ReceivingTaskItemDtoApplicationMapper.class);
    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);
    private final ApplicationEventDispatcher applicationEventDispatcher = mock(ApplicationEventDispatcher.class);
    private final CrossDockTaskItemService crossDockTaskItemService = mock(CrossDockTaskItemService.class);

    private final ReceivingTaskApplicationService receivingTaskApplicationService = new ReceivingTaskApplicationService(
        receivingTaskRepository,
        batchItemQueryService,
        businessEventDispatcher,
        receivingTaskDtoApplicationMapper,
        receivingTaskItemRepository,
        receivingTaskItemDtoApplicationMapper,
        pgAdvisoryLock,
        applicationEventDispatcher,
        crossDockTaskItemService);

    @Test
    void createTask_ShouldCreateReceivingTaskAndDispatchEvent_WhenBatchItemsExist() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = List.of(BatchItem.builder().build());
        ReceivingTask receivingTask = ReceivingTask.builder().build();
        ReceivingTaskDto receivingTaskDto = new ReceivingTaskDto();
        receivingTaskDto.setId(UUID.randomUUID());
        receivingTaskDto.setNumber("RT-001");

        when(batchItemQueryService.findBy(batchId, SourceEnum.VERNON.name())).thenReturn(batchItems);
        when(receivingTaskRepository.save(any(ReceivingTask.class))).thenReturn(receivingTask);
        when(receivingTaskDtoApplicationMapper.domainToDto(receivingTask)).thenReturn(receivingTaskDto);

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.VERNON.name());
        verify(receivingTaskRepository).save(any(ReceivingTask.class));
        verify(receivingTaskDtoApplicationMapper).domainToDto(receivingTask);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void createTask_ShouldNotCreateReceivingTask_WhenNoBatchItemsFound() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        when(batchItemQueryService.findBy(batchId, SourceEnum.VERNON.name())).thenReturn(List.of());

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.VERNON.name());
        verifyNoInteractions(receivingTaskRepository);
        verifyNoInteractions(receivingTaskDtoApplicationMapper);
        verifyNoInteractions(businessEventDispatcher);
    }

    @Test
    void scanReceiveItem_shouldSucceed() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        ReceivingTaskItem mockItem = mock(ReceivingTaskItem.class);
        when(mockItem.getReceivingTaskId()).thenReturn(taskId);
        when(mockItem.getReceivedQty()).thenReturn(0);
        when(mockItem.getExpectQty()).thenReturn(10);

        ReceivingTask mockTask = mock(ReceivingTask.class);
        when(mockTask.getStatus()).thenReturn(ReceivingTaskStatus.RECEIVING);
        when(mockTask.scanReceive(itemId)).thenReturn(mockItem);

        ReceivingTaskItemDto expectedDto = new ReceivingTaskItemDto();

        when(receivingTaskItemRepository.findById(itemId)).thenReturn(mockItem);
        when(receivingTaskRepository.findById(taskId)).thenReturn(mockTask);
        when(receivingTaskRepository.save(mockTask)).thenReturn(mockTask);
        when(receivingTaskItemDtoApplicationMapper.domainToDto(mockItem)).thenReturn(expectedDto);

        // Act
        ReceivingTaskItemDto result = receivingTaskApplicationService.scanReceiveItem(itemId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(receivingTaskItemRepository).findById(itemId);
        verify(receivingTaskRepository).findById(taskId);
        verify(receivingTaskRepository).save(mockTask);
    }

    @Test
    void scanReceiveItem_whenItemNotFound_shouldThrowException() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        when(receivingTaskItemRepository.findById(itemId)).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.scanReceiveItem(itemId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_ITEM_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void scanReceiveItem_whenItemFullyReceived_shouldThrowException() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ReceivingTaskItem mockItem = mock(ReceivingTaskItem.class);
        when(mockItem.getReceivedQty()).thenReturn(10);
        when(mockItem.getExpectQty()).thenReturn(10);
        when(receivingTaskItemRepository.findById(itemId)).thenReturn(mockItem);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.scanReceiveItem(itemId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_ITEM_HAS_RECEIVED.getCode(), exception.getCode());
    }

    @Test
    void startReceiving_shouldSucceed() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask mockTask = mock(ReceivingTask.class);
        when(mockTask.getStatus()).thenReturn(ReceivingTaskStatus.CREATED);
        when(receivingTaskRepository.findById(taskId)).thenReturn(mockTask);
        when(receivingTaskRepository.save(mockTask)).thenReturn(mockTask);

        ReceivingTaskDto mockDto = new ReceivingTaskDto();
        when(receivingTaskDtoApplicationMapper.domainToDto(mockTask)).thenReturn(mockDto);

        // Act
        ReceivingTask result = receivingTaskApplicationService.startReceiving(taskId);

        // Assert
        assertNotNull(result);
        verify(mockTask).start();
        verify(receivingTaskRepository).save(mockTask);
        verify(businessEventDispatcher).dispatch(any());
    }

    @Test
    void startReceiving_whenTaskNotFound_shouldThrowException() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(taskId)).thenReturn(null);

        // Act & Assert
        WmsBusinessException exception = assertThrows(WmsBusinessException.class,
            () -> receivingTaskApplicationService.startReceiving(taskId));
        assertEquals(ErrorCodeEnums.RECEIVING_TASK_NOT_FOUND.getCode(), exception.getCode());
    }

    @Test
    void receive_ShouldSucceed_WhenTaskExists() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.RECEIVING);
        ReceivingTaskDto expectedDto = createReceivingTaskDto(taskId);

        when(receivingTaskRepository.findById(taskId)).thenReturn(task);
        when(receivingTaskRepository.save(any())).thenReturn(task);
        when(receivingTaskDtoApplicationMapper.domainToDto(any())).thenReturn(expectedDto);
        when(businessEventDispatcher.dispatch(any())).thenReturn(DispatchResponseDto.builder()
            .event(new BusinessEventDto())
            .build());

        // Act
        ReceivingTaskDto result = receivingTaskApplicationService.receive(taskId);

        // Assert
        assertThat(result).isEqualTo(expectedDto);

        verify(receivingTaskRepository, times(1)).save(any());
        verify(businessEventDispatcher, times(1)).dispatch(any());
        verify(applicationEventDispatcher, times(1)).publishEvent(any());
    }

    @Test
    void receive_ShouldThrowException_WhenTaskNotFound() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        when(receivingTaskRepository.findById(taskId)).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.receive(taskId))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Receiving task not found");

        verify(receivingTaskRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    @Test
    void receive_ShouldThrowException_WhenTaskAlreadyReceived() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        ReceivingTask task = createReceivingTask(taskId, ReceivingTaskStatus.RECEIVED);
        when(receivingTaskRepository.findById(taskId)).thenReturn(task);

        // Act & Assert
        assertThatThrownBy(() -> receivingTaskApplicationService.receive(taskId))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessageContaining("Receiving task has received");

        verify(receivingTaskRepository, never()).save(any());
        verify(businessEventDispatcher, never()).dispatch(any());
        verify(applicationEventDispatcher, never()).publishEvent(any());
    }

    @Test
    void createTaskForCoreMark_ShouldCreateReceivingTask_WhenBatchItemsExist() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        List<BatchItem> batchItems = List.of(BatchItem.builder().build());
        ReceivingTask receivingTask = ReceivingTask.builder().build();
        ReceivingTaskDto receivingTaskDto = new ReceivingTaskDto();
        receivingTaskDto.setId(UUID.randomUUID());
        receivingTaskDto.setNumber("RT-001");

        when(batchItemQueryService.findBy(batchId, SourceEnum.CORE_MARK.name())).thenReturn(batchItems);
        when(receivingTaskRepository.save(any(ReceivingTask.class))).thenReturn(receivingTask);
        when(receivingTaskDtoApplicationMapper.domainToDto(receivingTask)).thenReturn(receivingTaskDto);

        // Act
        receivingTaskApplicationService.createReceivingTask(batchId);

        // Assert
        verify(batchItemQueryService).findBy(batchId, SourceEnum.CORE_MARK.name());
        verify(receivingTaskRepository).save(any(ReceivingTask.class));
        verify(receivingTaskDtoApplicationMapper).domainToDto(receivingTask);
        verify(businessEventDispatcher).dispatch(any());
    }


    private ReceivingTask createReceivingTask(UUID id, ReceivingTaskStatus status) {
        return ReceivingTask.builder()
            .id(id)
            .status(status)
            .receivingTaskItems(List.of())
            .build();
    }

    private ReceivingTaskDto createReceivingTaskDto(UUID id) {
        return ReceivingTaskDto.builder()
            .id(id)
            .status(ReceivingTaskStatus.RECEIVED)
            .build();
    }
}