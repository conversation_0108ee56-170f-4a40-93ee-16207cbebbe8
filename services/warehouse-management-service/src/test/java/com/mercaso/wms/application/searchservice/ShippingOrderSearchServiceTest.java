package com.mercaso.wms.application.searchservice;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.PickedShippingOrderItemExportDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.infrastructure.repository.shippingorder.jpa.ShippingOrderJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class ShippingOrderSearchServiceTest {

    @Mock
    private ShippingOrderJdbcTemplate shippingOrderJdbcTemplate;

    @Mock
    private ShippingOrderRepository shippingOrderRepository;

    @Mock
    private ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    @InjectMocks
    private ShippingOrderSearchService shippingOrderSearchService;

    @Test
    void searchShippingOrders_WithoutStatus_Success() {
        // Arrange
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("ORDER001")
            .build();
        Pageable pageable = PageRequest.of(0, 10);

        ShippingOrder mockShippingOrder = ShippingOrder.builder()
            .id(UUID.randomUUID())
            .build();
        Page<ShippingOrder> mockPage = new PageImpl<>(List.of(mockShippingOrder));
        ShippingOrderDto mockDto = new ShippingOrderDto();

        when(shippingOrderRepository.findShippingOrderList(any(), any())).thenReturn(mockPage);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(mockDto);

        // Act
        Page<ShippingOrderDto> result = shippingOrderSearchService.searchShippingOrders(query, pageable);

        // Assert
        assertNotNull(result);
        verify(shippingOrderRepository).findShippingOrderList(any(), any());
        verify(shippingOrderDtoApplicationMapper).domainToDto(any());
    }

    @Test
    void searchShippingOrders_WithStatus_Success() {
        // Arrange
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("ORDER001")
            .statuses(List.of("OPEN", "PICKED"))
            .build();
        Pageable pageable = PageRequest.of(0, 10);

        ShippingOrder mockShippingOrder = ShippingOrder.builder()
            .id(UUID.randomUUID())
            .build();
        Page<ShippingOrder> mockPage = new PageImpl<>(List.of(mockShippingOrder));
        ShippingOrderDto mockDto = new ShippingOrderDto();

        when(shippingOrderRepository.findShippingOrderList(any(), any())).thenReturn(mockPage);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(mockDto);

        // Act
        Page<ShippingOrderDto> result = shippingOrderSearchService.searchShippingOrders(query, pageable);

        // Assert
        assertNotNull(result);
        verify(shippingOrderRepository).findShippingOrderList(any(), any());
        verify(shippingOrderDtoApplicationMapper).domainToDto(any());
    }

    @Test
    void searchShippingOrders_WithEmptyStatus_Success() {
        // Arrange
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("ORDER001")
            .statuses(Collections.emptyList())
            .build();
        Pageable pageable = PageRequest.of(0, 10);

        ShippingOrder mockShippingOrder = ShippingOrder.builder()
            .id(UUID.randomUUID())
            .build();
        Page<ShippingOrder> mockPage = new PageImpl<>(List.of(mockShippingOrder));
        ShippingOrderDto mockDto = new ShippingOrderDto();

        when(shippingOrderRepository.findShippingOrderList(any(), any())).thenReturn(mockPage);
        when(shippingOrderDtoApplicationMapper.domainToDto(any())).thenReturn(mockDto);

        // Act
        Page<ShippingOrderDto> result = shippingOrderSearchService.searchShippingOrders(query, pageable);

        // Assert
        assertNotNull(result);
        verify(shippingOrderRepository).findShippingOrderList(any(), any());
        verify(shippingOrderDtoApplicationMapper).domainToDto(any());
    }

    @Test
    void pickedShippingOrderItemsExport_Success() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedShippingOrderItemExportDto> mockDtos = Arrays.asList(
            createMockPickedShippingOrderItemExportDto("ITEM001", "ORDER001", 1),
            createMockPickedShippingOrderItemExportDto("ITEM002", "ORDER002", 2)
        );
        when(shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.pickedShippingOrderItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(shippingOrderJdbcTemplate).fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);
    }

    @Test
    void pickedShippingOrderItemsExport_EmptyData_Success() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedShippingOrderItemExportDto> emptyList = Collections.emptyList();
        when(shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate))
            .thenReturn(emptyList);

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.pickedShippingOrderItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        verify(shippingOrderJdbcTemplate).fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);
    }

    @Test
    void pickedShippingOrderItemsExport_JdbcTemplateThrowsException_ThrowsRuntimeException() {
        // Arrange
        String deliveryDate = "2024-03-20";
        when(shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate))
            .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> shippingOrderSearchService.pickedShippingOrderItemsExport(deliveryDate));

        assertTrue(exception.getMessage().contains("Database error"));
        verify(shippingOrderJdbcTemplate).fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);
    }

    @Test
    void writeTemplate_Success() {
        // Arrange
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<PickedShippingOrderItemExportDto> items = Arrays.asList(
            createMockPickedShippingOrderItemExportDto("ITEM001", "ORDER001", 1)
        );

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.writeTemplate(outputStream, items);

        // Assert
        assertNotNull(result);
    }

    @Test
    void writeTemplate_EmptyList_Success() {
        // Arrange
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<PickedShippingOrderItemExportDto> emptyItems = Collections.emptyList();

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.writeTemplate(outputStream, emptyItems);

        // Assert
        assertNotNull(result);
    }

    @Test
    void pickedShippingOrderItemsExport_WithAllFields() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedShippingOrderItemExportDto> mockDtos = Arrays.asList(
            createCompletePickedShippingOrderItemExportDto()
        );
        when(shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.pickedShippingOrderItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(shippingOrderJdbcTemplate).fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);
    }

    @Test
    void pickedShippingOrderItemsExport_WithNullFields() {
        // Arrange
        String deliveryDate = "2024-03-20";
        List<PickedShippingOrderItemExportDto> mockDtos = Arrays.asList(
            createPickedShippingOrderItemExportDtoWithNulls()
        );
        when(shippingOrderJdbcTemplate.fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate))
            .thenReturn(mockDtos);

        // Act
        ByteArrayOutputStream result = shippingOrderSearchService.pickedShippingOrderItemsExport(deliveryDate);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(shippingOrderJdbcTemplate).fetchPickedShippingOrderItemsByDeliveryDate(deliveryDate);
    }

    private PickedShippingOrderItemExportDto createMockPickedShippingOrderItemExportDto(String itemNumber,
        String orderNumber,
        int line) {
        return PickedShippingOrderItemExportDto.builder()
            .item(itemNumber)
            .orderNumber(orderNumber)
            .line(line)
            .itemDescription("Test Item Description")
            .requestedQty(10)
            .pickedQty(8)
            .fulfilledQty(8)
            .breakdown("TestBreakdown")
            .initialFrom("TestWarehouse")
            .finalFrom("TestSource")
            .build();
    }

    private PickedShippingOrderItemExportDto createCompletePickedShippingOrderItemExportDto() {
        return PickedShippingOrderItemExportDto.builder()
            .item("COMPLETE_ITEM_001")
            .orderNumber("COMPLETE_ORDER_001")
            .line(1)
            .itemDescription("Complete Test Item Description")
            .requestedQty(100)
            .pickedQty(95)
            .fulfilledQty(95)
            .breakdown("CompleteBreakdown")
            .initialFrom("CompleteWarehouse")
            .finalFrom("CompleteSource")
            .build();
    }

    private PickedShippingOrderItemExportDto createPickedShippingOrderItemExportDtoWithNulls() {
        return PickedShippingOrderItemExportDto.builder()
            .item("NULL_TEST_ITEM")
            .orderNumber("NULL_TEST_ORDER")
            .line(1)
            .itemDescription(null)
            .requestedQty(10)
            .pickedQty(null)
            .fulfilledQty(null)
            .filled(null)
            .breakdown(null)
            .initialFrom(null)
            .finalFrom(null)
            .build();
    }
} 