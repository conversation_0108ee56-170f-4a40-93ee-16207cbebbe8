package com.mercaso.wms.utils;

import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class WarehouseResourceApi extends IntegrationTestRestUtil {

    private static final String WAREHOUSE_URL = "/query/warehouses";

    private static final String WAREHOUSE_ALL = "/query/warehouses/all";

    public WarehouseResourceApi(Environment environment) {
        super(environment);
    }

    public List<WarehouseDto> findByType(WarehouseType type) throws Exception {
        return getEntityList(WAREHOUSE_URL + "/" + type.name(), WarehouseDto.class);
    }

    public List<WarehouseDto> findAll() throws Exception {
        return getEntityList(WAREHOUSE_ALL, WarehouseDto.class);
    }
}
