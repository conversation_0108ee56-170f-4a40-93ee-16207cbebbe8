package com.mercaso.wms.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.inventorystock.CreateInventoryStockCommand;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.InventoryStockResourceApi;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class InventoryStockResourceIT extends AbstractIT {

    @Autowired
    InventoryStockResourceApi inventoryStockResourceApi;

    @Test
    void when_create_inventory_stock_should_success() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().getFirst();

        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(warehouses.getFirst().getId())
            .locationId(location.getId())
            .itemId(UUID.randomUUID())
            .skuNumber(RandomStringUtils.randomAlphabetic(10))
            .title(RandomStringUtils.randomAlphabetic(10))
            .qty(BigDecimal.ONE)
            .build();

        InventoryStockDto inventoryStockDto = inventoryStockResourceApi.create(command);

        assertNotNull(inventoryStockDto);
        assertEquals(command.getWarehouseId(), inventoryStockDto.getWarehouse().getId());
        assertEquals(command.getLocationId(), inventoryStockDto.getLocation().getId());
        assertEquals(command.getItemId(), inventoryStockDto.getItemId());
        assertEquals(command.getSkuNumber(), inventoryStockDto.getSkuNumber());
        assertEquals(command.getTitle(), inventoryStockDto.getTitle());
        assertEquals(command.getQty(), inventoryStockDto.getQty());
    }

    @Test
    void when_available_inventory_stock_should_success() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        Location location = locationRepository.findAll().getFirst();

        CreateInventoryStockCommand command = CreateInventoryStockCommand.builder()
            .warehouseId(warehouses.getFirst().getId())
            .locationId(location.getId())
            .itemId(UUID.randomUUID())
            .skuNumber(RandomStringUtils.randomAlphabetic(10))
            .title(RandomStringUtils.randomAlphabetic(10))
            .qty(BigDecimal.ONE)
            .build();

        InventoryStockDto inventoryStockDto = inventoryStockResourceApi.create(command);

        InventoryStockDto unavailableInventoryStockDto = inventoryStockResourceApi.unavailable(inventoryStockDto.getId());

        assertNotNull(unavailableInventoryStockDto);
        assertEquals(inventoryStockDto.getId(), unavailableInventoryStockDto.getId());
        assertEquals(inventoryStockDto.getWarehouse().getId(), unavailableInventoryStockDto.getWarehouse().getId());
        assertEquals(inventoryStockDto.getLocation().getId(), unavailableInventoryStockDto.getLocation().getId());
        assertEquals(inventoryStockDto.getItemId(), unavailableInventoryStockDto.getItemId());
        assertEquals(inventoryStockDto.getSkuNumber(), unavailableInventoryStockDto.getSkuNumber());
        assertEquals(inventoryStockDto.getTitle(), unavailableInventoryStockDto.getTitle());
        assertEquals(inventoryStockDto.getQty().setScale(2, RoundingMode.HALF_UP), unavailableInventoryStockDto.getQty());
        assertEquals(InventoryStockStatus.UNAVAILABLE.name(), unavailableInventoryStockDto.getStatus());

        InventoryStockDto availableInventoryStockDto = inventoryStockResourceApi.available(inventoryStockDto.getId());

        assertNotNull(availableInventoryStockDto);
        assertEquals(inventoryStockDto.getId(), availableInventoryStockDto.getId());
        assertEquals(inventoryStockDto.getWarehouse().getId(), availableInventoryStockDto.getWarehouse().getId());
        assertEquals(inventoryStockDto.getLocation().getId(), availableInventoryStockDto.getLocation().getId());
        assertEquals(inventoryStockDto.getItemId(), availableInventoryStockDto.getItemId());
        assertEquals(inventoryStockDto.getSkuNumber(), availableInventoryStockDto.getSkuNumber());
        assertEquals(inventoryStockDto.getTitle(), availableInventoryStockDto.getTitle());
        assertEquals(inventoryStockDto.getQty().setScale(2, RoundingMode.HALF_UP), availableInventoryStockDto.getQty());
        assertEquals(InventoryStockStatus.AVAILABLE.name(), availableInventoryStockDto.getStatus());
    }

}