package com.mercaso.wms.batch.strategy.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.util.BreakdownNoUtil;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class FullBreakDownSmallPopulateStrategyTest {

        private final FullBreakDownSmallPopulateStrategy fullBreakDownSmallPopulateStrategy = new FullBreakDownSmallPopulateStrategy();

        @Test
        void when_populate_breakdown_template_with_no_more_than_15_then_populate() {
            List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();
            for (int i = 0; i < 10; i++) {
                String orderNumber1 = "M-".concat(RandomStringUtils.randomAlphabetic(5));
                String itemNumber = "JC".concat(RandomStringUtils.randomNumeric(5));
                excelBatchDtos.add(ExcelBatchDto.builder()
                    .orderNumber(orderNumber1)
                    .itemNumber(itemNumber)
                    .pos(BreakdownNoUtil.nextLetterBreakdown("[AA]"))
                    .bigOrder(false)
                    .quantity(1)
                    .build());
            }

            List<BreakdownDto> breakdownDtos = fullBreakDownSmallPopulateStrategy.populateBreakdownTemplate(excelBatchDtos);

            assertEquals(10, breakdownDtos.size());
        }

}