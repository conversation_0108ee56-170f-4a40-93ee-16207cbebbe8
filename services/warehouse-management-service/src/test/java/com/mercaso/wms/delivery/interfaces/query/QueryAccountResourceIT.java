package com.mercaso.wms.delivery.interfaces.query;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.command.account.CreateAccountCommand;
import com.mercaso.wms.delivery.application.dto.account.AccountDto;
import com.mercaso.wms.delivery.domain.account.AccountStatus;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.AddDriverResponse;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Driver;
import com.mercaso.wms.delivery.utils.AccountResourceApi;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryAccountResourceIT extends AbstractIT {

    @Autowired
    private AccountResourceApi accountResourceApi;

    @Test
    void when_find_account_by_id_then_return_accountDto() throws Exception {
        CreateAccountCommand command = CreateAccountCommand.builder()
            .email("test" + RandomStringUtils.randomAlphanumeric(8) + "@example.com")
            .userName(RandomStringUtils.randomAlphabetic(10))
            .warehouseId(UUID.randomUUID())
            .build();

        List<Driver> initialDrivers = new ArrayList<>();
        Driver driver = new Driver();
        driver.setId(UUID.randomUUID().toString());
        driver.setEmail(command.getEmail());
        initialDrivers.add(driver);
        UserDto userDto = new UserDto();
        userDto.setId(UUID.randomUUID());
        when(umsAdaptor.createUser(any(), any())).thenReturn(userDto);
        when(routeManagerAdaptor.addDriver(any())).thenReturn(new AddDriverResponse(UUID.randomUUID().toString()));
        when(routeManagerAdaptor.listDrivers()).thenReturn(initialDrivers);

        // Act
        AccountDto result = accountResourceApi.createAccount(command);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(command.getEmail(), result.getEmail());
        assertEquals(command.getUserName(), result.getUserName());
        assertEquals(AccountStatus.ACTIVE, result.getStatus());

        AccountDto accountDto = accountResourceApi.findById(result.getId());

        assertNotNull(accountDto);
        assertEquals(result.getId(), accountDto.getId());
        assertEquals(result.getEmail(), accountDto.getEmail());
        assertEquals(result.getUserName(), accountDto.getUserName());
        assertEquals(result.getStatus(), accountDto.getStatus());

        when(umsAdaptor.getUserByEmail(any())).thenReturn(List.of(userDto));
        boolean emailExist = accountResourceApi.validateBy(result.getEmail());
        assertTrue(emailExist);

        List<AccountDto> accountDtoList = accountResourceApi.findBy(List.of(result.getId()));
        assertNotNull(accountDtoList);
        assertEquals(1, accountDtoList.size());
    }

}