package com.mercaso.wms.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.BatchItemDto;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.BatchItemJdbcTemplate;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;

class BatchItemsApplicationServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);

    private final BatchItemJdbcTemplate batchItemJdbcTemplate = mock(BatchItemJdbcTemplate.class);

    private final BatchItemApplicationService batchItemsApplicationService = new BatchItemApplicationService(batchItemRepository,
        batchItemJdbcTemplate);

    @Test
    void when_create_batch_items_then_return_batch_items() {
        // given
        List<BatchItem> batchItems = Lists.newArrayList();
        batchItems.add(BatchItem.builder().batchId(UUID.randomUUID()).itemId(UUID.randomUUID()).build());
        // when
        when(batchItemRepository.saveAll(any())).thenReturn(batchItems);
        // then
        List<BatchItemDto> result = batchItemsApplicationService.createBatchItems(batchItems);

        assertNotNull(result);
        assertEquals(batchItems.size(), result.size());
    }

    @Test
    void driverQAExport_WhenSuccess_ShouldReturnOutputStream() {
        // Given
        String deliveryDate = "2024-03-20";
        List<ExcelBatchDto> mockData = Arrays.asList(
            createExcelBatchDto(BatchConstants.TOBACCO_DEPARTMENT),
            createExcelBatchDto(BatchConstants.REFRIGERATED_DEPARTMENT)
        );
        when(batchItemJdbcTemplate.findTobaccoAndRefrigeratedBatchItemsBy(deliveryDate))
            .thenReturn(mockData);

        // When
        ByteArrayOutputStream result = batchItemsApplicationService.driverQAExport(deliveryDate);

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
        verify(batchItemJdbcTemplate).findTobaccoAndRefrigeratedBatchItemsBy(deliveryDate);
    }

    @Test
    void driverQAExport_WhenExceptionOccurs_ShouldReturnEmptyOutputStream() {
        // Given
        String deliveryDate = "2024-03-20";
        when(batchItemJdbcTemplate.findTobaccoAndRefrigeratedBatchItemsBy(deliveryDate))
            .thenThrow(new RuntimeException("Database error"));

        // When
        ByteArrayOutputStream result = batchItemsApplicationService.driverQAExport(deliveryDate);

        // Then
        assertNotNull(result);
    }

    @Test
    void writeTemplate_WithValidData_ShouldWriteToCorrectSheets() {
        // Given
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<ExcelBatchDto> testData = Arrays.asList(
            createExcelBatchDto(BatchConstants.TOBACCO_DEPARTMENT),
            createExcelBatchDto(BatchConstants.REFRIGERATED_DEPARTMENT)
        );

        // When
        ByteArrayOutputStream result = batchItemsApplicationService.writeTemplate(outputStream, testData, "2024-03-20");

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 0);
    }

    private ExcelBatchDto createExcelBatchDto(String department) {
        ExcelBatchDto dto = new ExcelBatchDto();
        dto.setDepartment(department);
        // Set other required fields
        return dto;
    }

}