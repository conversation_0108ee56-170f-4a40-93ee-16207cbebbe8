package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.MockDataUtils.buildItemCategoryDtoList;
import static com.mercaso.wms.utils.MockDataUtils.buildTransferTask;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.transfertask.CreateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.LoadTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.ReceiveTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand;
import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.TransferTaskRepository;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.transfertaskitem.TransferTaskItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class TransferTaskResourceIT extends AbstractIT {

    @Autowired
    private TransferTaskRepository transferTaskRepository;

    @Autowired
    private WarehouseRepository warehouseRepository;

    @Autowired
    private LocationRepository locationRepository;

    @Test
    void getTransferTask_withValidParameters_returnsTransferTask() {
        List<TransferTask> transferTasks = buildTransferTask(1, TransferTaskStatus.DRAFT);

        Warehouse save = warehouseRepository.save(transferTasks.getFirst().getOriginWarehouse());
        Warehouse save1 = warehouseRepository.save(transferTasks.getFirst().getDestinationWarehouse());

        TransferTask first = transferTasks.getFirst();
        first.setOriginWarehouse(save);
        first.setDestinationWarehouse(save1);
        TransferTask saved = transferTaskRepository.save(transferTasks.getFirst());

        TransferTaskDto transferTask = transferTaskResourceApi.getTransferTask(saved.getId());

        assertNotNull(transferTask);
        assertEquals(saved.getId(), transferTask.getId());
        assertEquals(saved.getNumber(), transferTask.getNumber());
        assertEquals(saved.getDeliveryDate(), transferTask.getDeliveryDate());
        assertEquals(saved.getStatus(), transferTask.getStatus());
        assertEquals(saved.getLoaderUserId(), transferTask.getLoaderUserId());
        assertEquals(saved.getLoaderUserName(), transferTask.getLoaderUserName());
        assertEquals(saved.getReceiverUserId(), transferTask.getReceiverUserId());
        assertEquals(saved.getReceiverUserName(), transferTask.getReceiverUserName());
        assertEquals(saved.getOriginWarehouse().getId(), transferTask.getOriginWarehouse().getId());
        assertEquals(saved.getDestinationWarehouse().getId(), transferTask.getDestinationWarehouse().getId());
    }

    @Test
    void createTransferTask_withValidParameters_returnsTransferTask() {
        Warehouse originWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Warehouse destinationWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        UUID itemId = UUID.randomUUID();
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
        itemCategoryDto.setId(itemId);
        itemCategoryDto.setCategory("Test Category");
        itemCategoryDto.setSubCategory("Test Sub Category");
        itemCategoryDto.setBrand("Test Brand");
        itemCategoryDto.setSkuNumber("Test SKU");

        CreateTransferTaskCommand createTransferTaskCommand = CreateTransferTaskCommand.builder()
            .deliveryDate("2021-12-12")
            .originWarehouseId(originWarehouse.getId())
            .destinationWarehouseId(destinationWarehouse.getId())
            .transferTaskItems(List.of(CreateTransferTaskCommand.CreateTransferTaskItemDto.builder()
                .itemId(itemId)
                .pickingTaskItemId(UUID.randomUUID())
                .originLocationId(null)
                .destinationLocationId(null)
                .transferQty(10)
                .build()))
            .build();

        when(imsAdaptor.getItemsByIds(List.of(itemId))).thenReturn(List.of(itemCategoryDto));
        TransferTaskDto transferTask = transferTaskResourceApi.createTransferTask(createTransferTaskCommand);
        assertNotNull(transferTask);
        assertEquals(createTransferTaskCommand.getDeliveryDate(), transferTask.getDeliveryDate());
        assertEquals(originWarehouse.getId(), transferTask.getOriginWarehouse().getId());
        assertEquals(destinationWarehouse.getId(), transferTask.getDestinationWarehouse().getId());
        assertEquals(1, transferTask.getTransferTaskItems().size());
    }

    @Test
    void updateTransferTask_withValidParameters_returnsTransferTask() throws Exception {
        List<TransferTask> transferTasks = buildTransferTask(1, TransferTaskStatus.DRAFT);
        UUID itemId = UUID.randomUUID();

        Warehouse save = warehouseRepository.save(transferTasks.getFirst().getOriginWarehouse());
        Warehouse save1 = warehouseRepository.save(transferTasks.getFirst().getDestinationWarehouse());

        TransferTask first = transferTasks.getFirst();
        first.setTransferTaskItems(List.of());
        first.setOriginWarehouse(save);
        first.setDestinationWarehouse(save1);
        TransferTask saved = transferTaskRepository.save(transferTasks.getFirst());

        UpdateTransferTaskCommand updateTransferTaskCommand = UpdateTransferTaskCommand.builder()
            .transferTaskId(saved.getId())
            .transferTaskItems(List.of(UpdateTransferTaskCommand.UpdateTransferTaskItemDto.builder()
                .itemId(itemId)
                .pickingTaskItemId(UUID.randomUUID())
                .originLocationId(null)
                .destinationLocationId(null)
                .transferQty(10)
                .build()))
            .build();
        List<ItemCategoryDto> itemCategoryDtos = buildItemCategoryDtoList("001");
        itemCategoryDtos.getFirst().setId(itemId);
        when(imsAdaptor.getItemsByIds(List.of(itemId))).thenReturn(itemCategoryDtos);
        TransferTaskDto transferTask = transferTaskResourceApi.updateTransferTask(updateTransferTaskCommand);
        assertNotNull(transferTask);
        assertEquals(updateTransferTaskCommand.getTransferTaskId(), transferTask.getId());
        assertEquals(1, transferTask.getTransferTaskItems().size());
        assertEquals(updateTransferTaskCommand.getTransferTaskItems().getFirst().getItemId(),
            transferTask.getTransferTaskItems().getFirst().getItemId());
        assertEquals(updateTransferTaskCommand.getTransferTaskItems().getFirst().getTransferQty(),
            transferTask.getTransferTaskItems().getFirst().getTransferQty());
        assertEquals(updateTransferTaskCommand.getTransferTaskItems().getFirst().getItemId(),
            transferTask.getTransferTaskItems().getFirst().getItemId());
        assertEquals(updateTransferTaskCommand.getTransferTaskItems().getFirst().getPickingTaskItemId(),
            transferTask.getTransferTaskItems().getFirst().getPickingTaskItemId());
        assertEquals(TransferTaskStatus.DRAFT, transferTask.getStatus());

    }


    @Test
    void loadTransferTask_withValidParameters_returnsTransferTask() throws Exception {
        LoadTransferTaskCommand loadTransferTaskCommand = new LoadTransferTaskCommand();
        loadTransferTaskCommand.setTruckNumber("Test Truck Number");
        Warehouse originWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Warehouse destinationWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        TransferTask transferTask = buildTransferTask(1, TransferTaskStatus.READY_TO_LOAD).getFirst();
        transferTask.setOriginWarehouse(originWarehouse);
        transferTask.setDestinationWarehouse(destinationWarehouse);
        TransferTask saved = transferTaskRepository.save(transferTask);

        TransferTaskDto transferTaskDto = transferTaskResourceApi.loadTransferTask(saved.getId(), loadTransferTaskCommand);

        assertNotNull(transferTaskDto);
        assertEquals(saved.getId(), transferTaskDto.getId());
        assertEquals(saved.getNumber(), transferTaskDto.getNumber());
        assertEquals(saved.getDeliveryDate(), transferTaskDto.getDeliveryDate());
        assertEquals(TransferTaskStatus.IN_TRANSIT, transferTaskDto.getStatus());
        assertEquals(loadTransferTaskCommand.getTruckNumber(), transferTaskDto.getTruckNumber());
        assertNotNull(transferTaskDto.getLoadingAt());
    }

    @Test
    void receiveTransferTask_withValidParameters_returnsTransferTask() throws JsonProcessingException {
        Warehouse originWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Warehouse destinationWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Location originLocation = locationRepository.save(Location.builder()
            .warehouse(originWarehouse)
            .type(LocationType.BIN)
            .name("MFC-Staging")
            .build());
        Location destinationLocation = locationRepository.save(Location.builder()
            .warehouse(destinationWarehouse)
            .type(LocationType.BIN)
            .name("MDC-Staging")
            .build());

        TransferTask transferTask = buildTransferTask(1, TransferTaskStatus.IN_TRANSIT).getFirst();
        transferTask.setOriginWarehouse(originWarehouse);
        transferTask.setDestinationWarehouse(destinationWarehouse);
        TransferTaskItem first = transferTask.getTransferTaskItems().getFirst();
        first.setOriginLocationId(originLocation.getId());
        first.setDestinationLocationId(destinationLocation.getId());
        first.setOriginLocationName(originLocation.getName());
        first.setDestinationLocationName(destinationLocation.getName());

        TransferTask saved = transferTaskRepository.save(transferTask);

        ReceiveTransferTaskCommand receiveTransferTaskCommand = new ReceiveTransferTaskCommand();
        receiveTransferTaskCommand.setDestinationLocationName(destinationLocation.getName());

        TransferTaskDto transferTaskDto = transferTaskResourceApi.receiveTransferTask(saved.getId(), receiveTransferTaskCommand);

        assertNotNull(transferTaskDto);
        assertEquals(saved.getId(), transferTaskDto.getId());
        assertEquals(saved.getNumber(), transferTaskDto.getNumber());
        assertEquals(saved.getDeliveryDate(), transferTaskDto.getDeliveryDate());
        assertEquals(TransferTaskStatus.RECEIVED, transferTaskDto.getStatus());
        assertNotNull(transferTaskDto.getReceivedAt());
    }

    @Test
    void activateTransferTask_withValidParameters_returnsTransferTask() throws JsonProcessingException {
        Warehouse originWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Warehouse destinationWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        TransferTask transferTask = buildTransferTask(1, TransferTaskStatus.DRAFT).getFirst();
        transferTask.setOriginWarehouse(originWarehouse);
        transferTask.setDestinationWarehouse(destinationWarehouse);
        TransferTask saved = transferTaskRepository.save(transferTask);

        TransferTaskDto transferTaskDto = transferTaskResourceApi.activateTransferTask(saved.getId());

        assertNotNull(transferTaskDto);
        assertEquals(saved.getId(), transferTaskDto.getId());
        assertEquals(saved.getNumber(), transferTaskDto.getNumber());
        assertEquals(saved.getDeliveryDate(), transferTaskDto.getDeliveryDate());
        assertEquals(TransferTaskStatus.READY_TO_LOAD, transferTaskDto.getStatus());
    }

    @Test
    void revertToDraft_withValidParameters_returnsTransferTask() throws JsonProcessingException {
        Warehouse originWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        Warehouse destinationWarehouse = warehouseRepository.save(buildWarehouse(UUID.randomUUID()));
        TransferTask transferTask = buildTransferTask(1, TransferTaskStatus.READY_TO_LOAD).getFirst();
        transferTask.setOriginWarehouse(originWarehouse);
        transferTask.setDestinationWarehouse(destinationWarehouse);
        TransferTask saved = transferTaskRepository.save(transferTask);

        TransferTaskDto transferTaskDto = transferTaskResourceApi.revertToDraft(saved.getId());

        assertNotNull(transferTaskDto);
        assertEquals(saved.getId(), transferTaskDto.getId());
        assertEquals(saved.getNumber(), transferTaskDto.getNumber());
        assertEquals(saved.getDeliveryDate(), transferTaskDto.getDeliveryDate());
        assertEquals(TransferTaskStatus.DRAFT, transferTaskDto.getStatus());
    }


}