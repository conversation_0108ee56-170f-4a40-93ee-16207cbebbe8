package com.mercaso.wms.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.CrossDockItemDto;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItem;
import com.mercaso.wms.domain.crossdockitem.CrossDockTaskItemRepository;
import com.mercaso.wms.domain.crossdockitem.enums.CrossDockItemSourceEnum;
import com.mercaso.wms.utils.CrossDockTaskItemResourceApi;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.HttpClientErrorException.UnprocessableEntity;

class CrossDockTaskItemResourceIT extends AbstractIT {

    @Autowired
    private CrossDockTaskItemResourceApi crossDockTaskItemResourceApi;

    @Autowired
    private CrossDockTaskItemRepository crossDockTaskItemRepository;

    @Test
    void when_assignNextSequence_then_returnFirstAvailableItemWithSequence() throws Exception {
        UUID shippingOrderItemId = UUID.randomUUID();

        // Create multiple CrossDockTaskItems with same shippingOrderItemId
        CrossDockTaskItem item1 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-001");
        CrossDockTaskItem item2 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-002");
        CrossDockTaskItem item3 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-003");

        crossDockTaskItemRepository.save(item1);
        crossDockTaskItemRepository.save(item2);
        crossDockTaskItemRepository.save(item3);

        // Call API
        CrossDockItemDto result = crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId);

        // Verify result
        assertNotNull(result);
        assertNotNull(result.getSequence());
        assertEquals("1/3", result.getAssignedSequence());
        assertEquals(shippingOrderItemId, result.getShippingOrderItemId());

        // Call API again to get next item
        CrossDockItemDto result2 = crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId);

        // Verify second result
        assertNotNull(result2);
        assertNotNull(result2.getSequence());
        assertEquals("2/3", result2.getAssignedSequence());
        assertTrue(result2.getSequence().contains("1/3"));
        assertTrue(result2.getSequence().contains("2/3"));
        assertEquals(shippingOrderItemId, result2.getShippingOrderItemId());

        // Call API third time to get last item
        CrossDockItemDto result3 = crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId);

        // Verify third result
        assertNotNull(result3);
        assertNotNull(result3.getSequence());
        assertEquals("3/3", result3.getAssignedSequence());
        assertTrue(result3.getSequence().contains("1/3"));
        assertTrue(result3.getSequence().contains("2/3"));
        assertTrue(result3.getSequence().contains("3/3"));
        assertEquals(shippingOrderItemId, result3.getShippingOrderItemId());

        // Call API fourth time - now should throw 422 (no pending items)
        assertThrows(UnprocessableEntity.class,
            () -> crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId));
    }

    @Test
    void when_assignNextSequence_withNoItems_then_return422() {
        UUID nonExistentShippingOrderItemId = UUID.randomUUID();

        assertThrows(UnprocessableEntity.class,
            () -> crossDockTaskItemResourceApi.assignNextSequence(nonExistentShippingOrderItemId));
    }

    @Test
    void when_assignNextSequence_withAllItemsProcessed_then_return422() {
        UUID shippingOrderItemId = UUID.randomUUID();

        // Create CrossDockTaskItems that already have sequence assigned
        CrossDockTaskItem item1 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-001");
        item1.assignSequence("1/2");
        CrossDockTaskItem item2 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-002");
        item2.assignSequence("2/2");

        crossDockTaskItemRepository.save(item1);
        crossDockTaskItemRepository.save(item2);

        // Call API - expect 422 since all items already have sequence assigned
        assertThrows(UnprocessableEntity.class,
            () -> crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId));
    }

    @Test
    void when_assignNextSequence_withOrderNumberSorting_then_returnItemsInCorrectOrder() throws Exception {
        UUID shippingOrderItemId = UUID.randomUUID();

        // Create CrossDockTaskItems with different order numbers (not in alphabetical order)
        CrossDockTaskItem item1 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-003");
        CrossDockTaskItem item2 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-001");
        CrossDockTaskItem item3 = createCrossDockTaskItem(shippingOrderItemId, "ORDER-002");

        crossDockTaskItemRepository.save(item1);
        crossDockTaskItemRepository.save(item2);
        crossDockTaskItemRepository.save(item3);

        // Call API - should return item with ORDER-001 first (alphabetically first)
        CrossDockItemDto result = crossDockTaskItemResourceApi.assignNextSequence(shippingOrderItemId);

        assertNotNull(result);
        assertEquals("ORDER-001", result.getOrderNumber());
        assertEquals("1/3", result.getAssignedSequence());
    }

    private CrossDockTaskItem createCrossDockTaskItem(UUID shippingOrderItemId, String orderNumber) {
        return CrossDockTaskItem.builder()
            .batchId(UUID.randomUUID())
            .skuNumber("TEST-SKU")
            .title("Test Item")
            .itemId(UUID.randomUUID())
            .taskItemId(UUID.randomUUID())
            .taskType(CrossDockItemSourceEnum.PICKING_TASK)
            .shippingOrderId(UUID.randomUUID())
            .shippingOrderItemId(shippingOrderItemId)
            .pickedQty(1)
            .crossDockedQty(0)
            .breakdownName("A-01")
            .orderNumber(orderNumber)
            .taskNumber("TASK-001")
            .department("Electronics")
            .category("Mobile")
            .build();
    }
}