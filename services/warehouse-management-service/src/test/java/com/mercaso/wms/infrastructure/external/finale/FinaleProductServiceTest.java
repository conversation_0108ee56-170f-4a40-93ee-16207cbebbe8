package com.mercaso.wms.infrastructure.external.finale;


import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.CreatePurchaseOrderDto;
import com.mercaso.wms.infrastructure.external.finale.dto.CreateShipmentDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleTransferShipmentResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.PurchaseOrderResponse;
import com.mercaso.wms.infrastructure.external.finale.dto.ShipmentResponse;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FinaleProductServiceTest {


    @Mock
    private FinaleConfigProperties properties;

    @InjectMocks
    private FinaleProductService finaleProductService;

    @Test
    void createPurchaseOrder_returnsResponse_whenRequestIsSuccessful() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            CreatePurchaseOrderDto createPurchaseOrderDto = new CreatePurchaseOrderDto();
            PurchaseOrderResponse mockResponse = new PurchaseOrderResponse();
            when(properties.getCreatePurchaseOrderUrl()).thenReturn("http://mock-url");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)))
                .thenReturn(mockResponse);

            PurchaseOrderResponse response = finaleProductService.createPurchaseOrder(createPurchaseOrderDto);

            assertNotNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)));
        }
    }

    @Test
    void createPurchaseOrder_returnsNull_whenExceptionOccurs() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            CreatePurchaseOrderDto createPurchaseOrderDto = new CreatePurchaseOrderDto();
            when(properties.getCreatePurchaseOrderUrl()).thenReturn("http://mock-url");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)))
                .thenThrow(new RuntimeException("Mock exception"));

            PurchaseOrderResponse response = finaleProductService.createPurchaseOrder(createPurchaseOrderDto);

            assertNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)));
        }
    }

    @Test
    void createShipment_returnsResponse_whenRequestIsSuccessful() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            CreateShipmentDto createShipmentDto = new CreateShipmentDto();
            FinaleTransferShipmentResponse mockResponse = new FinaleTransferShipmentResponse();
            when(properties.getCreateShipmentUrl()).thenReturn("http://mock-url");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(FinaleTransferShipmentResponse.class)))
                .thenReturn(mockResponse);

            FinaleTransferShipmentResponse response = finaleProductService.createShipment(createShipmentDto);

            assertNotNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(FinaleTransferShipmentResponse.class)));
        }
    }

    @Test
    void createShipment_returnsNull_whenExceptionOccurs() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            CreateShipmentDto createShipmentDto = new CreateShipmentDto();
            when(properties.getCreateShipmentUrl()).thenReturn("http://mock-url");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(FinaleTransferShipmentResponse.class)))
                .thenThrow(new RuntimeException("Mock exception"));

            FinaleTransferShipmentResponse response = finaleProductService.createShipment(createShipmentDto);

            assertNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(FinaleTransferShipmentResponse.class)));
        }
    }

    @Test
    void receiveShipment_returnsResponse_whenRequestIsSuccessful() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            String shipmentId = "12345";
            ShipmentResponse mockResponse = new ShipmentResponse();
            when(properties.getReceiveShipmentUrl()).thenReturn("http://mock-url/%s");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(ShipmentResponse.class)))
                .thenReturn(mockResponse);

            ShipmentResponse response = finaleProductService.receiveShipment(shipmentId);

            assertNotNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(ShipmentResponse.class)));
        }
    }

    @Test
    void receiveShipment_returnsNull_whenExceptionOccurs() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            String shipmentId = "12345";
            when(properties.getReceiveShipmentUrl()).thenReturn("http://mock-url/%s");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(ShipmentResponse.class)))
                .thenThrow(new RuntimeException("Mock exception"));

            ShipmentResponse response = finaleProductService.receiveShipment(shipmentId);

            assertNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(ShipmentResponse.class)));
        }
    }

    @Test
    void completePurchaseOrder_returnsResponse_whenRequestIsSuccessful() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            String shipmentId = "12345";
            PurchaseOrderResponse mockResponse = new PurchaseOrderResponse();
            when(properties.getCompletePurchaseOrderUrl()).thenReturn("http://mock-url/%s");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)))
                .thenReturn(mockResponse);

            PurchaseOrderResponse response = finaleProductService.completePurchaseOrder(shipmentId);

            assertNotNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)));
        }
    }

    @Test
    void completePurchaseOrder_returnsNull_whenExceptionOccurs() {
        try (MockedStatic<HttpClientUtils> mockedStatic = mockStatic(HttpClientUtils.class)) {
            String shipmentId = "12345";
            when(properties.getCompletePurchaseOrderUrl()).thenReturn("http://mock-url/%s");
            mockedStatic.when(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)))
                .thenThrow(new RuntimeException("Mock exception"));

            PurchaseOrderResponse response = finaleProductService.completePurchaseOrder(shipmentId);

            assertNull(response);
            mockedStatic.verify(() -> HttpClientUtils.executePostRequest(anyString(), anyString(), anyMap(), eq(PurchaseOrderResponse.class)));
        }
    }

}