package com.mercaso.wms.infrastructure.schedule;

import static com.mercaso.wms.utils.MockDataUtils.buildBatch;
import static com.mercaso.wms.utils.MockDataUtils.buildWarehouseDto;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.queryservice.WarehouseQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.config.BatchProcessingProperties;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.external.finale.FinalePurchaseOrderService;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class FinalePoCreationSchedulerTest {

    @Mock
    private BatchRepository batchRepository;

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private WarehouseQueryService warehouseQueryService;

    @Mock
    private FinalePurchaseOrderService finalePurchaseOrderService;

    @Mock
    private BatchProcessingProperties batchProcessingProperties;

    @InjectMocks
    private FinalePoCreationScheduler scheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createPurchaseOrders_Does_Nothing_When_No_Lock() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(false);

        scheduler.createPurchaseOrders();

        verifyNoInteractions(batchRepository, warehouseQueryService, finalePurchaseOrderService);
    }

    @Test
    void createPurchaseOrders_Does_Nothing_When_No_Batches_Found() {
        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(Collections.emptyList());

        scheduler.createPurchaseOrders();

        verify(batchRepository, times(1)).findByDeliveryDateRange(any(), any());
        verifyNoInteractions(warehouseQueryService, finalePurchaseOrderService);
    }

    @Test
    void createPurchaseOrders_Processes_Batches_For_Warehouses() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.COSTCO;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        verify(finalePurchaseOrderService, times(1)).createPurchaseOrder(any(SourceEnum.class), eq(batch));
    }

    @Test
    void createPurchaseOrders_With_Specific_DeliveryDate_Uses_Provided_Date() {
        String specificDate = "2024-12-25";
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.MFC;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(2);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(4);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders(specificDate);

        verify(batchRepository, times(1)).findByDeliveryDateRange(any(), any());
        verify(finalePurchaseOrderService, times(1)).createPurchaseOrder(any(SourceEnum.class), eq(batch));
    }

    @Test
    void createPurchaseOrders_With_Different_Configuration_Values() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.JETRO;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(7);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(1);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        verify(batchProcessingProperties, times(2)).getDaysBefore(); // Called twice: once for date range calculation, once for logging
        verify(batchProcessingProperties, times(2)).getDaysAfter(); // Called twice: once for date range calculation, once for logging
        verify(finalePurchaseOrderService, times(1)).createPurchaseOrder(any(SourceEnum.class), eq(batch));
    }

    @Test
    void createPurchaseOrders_With_Feature_Flag_Off_Skips_PO_Creation() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.COSTCO;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(false);

        scheduler.createPurchaseOrders();

        verify(finalePurchaseOrderService, never()).createPurchaseOrder(any(SourceEnum.class), any(Batch.class));
    }

    @Test
    void createPurchaseOrders_With_Existing_PO_Skips_Creation() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.COSTCO;

        Batch batch = buildBatch(batchId);
        // Create finale entities with existing PO
        ArrayNode finaleEntities = JsonNodeFactory.instance.arrayNode();
        ObjectNode existingPO = JsonNodeFactory.instance.objectNode();
        existingPO.put("entityType", "PURCHASE_ORDER");
        existingPO.put("vendorName", sourceEnum.name());
        finaleEntities.add(existingPO);
        batch.setFinaleEntities(finaleEntities);
        
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        verify(finalePurchaseOrderService, never()).createPurchaseOrder(any(SourceEnum.class), any(Batch.class));
    }

    @Test
    void createPurchaseOrders_With_Multiple_Batches_And_Warehouses() {
        UUID batchId1 = UUID.randomUUID();
        UUID batchId2 = UUID.randomUUID();
        UUID warehouseId1 = UUID.randomUUID();
        UUID warehouseId2 = UUID.randomUUID();

        Batch batch1 = buildBatch(batchId1);
        batch1.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        Batch batch2 = buildBatch(batchId2);
        batch2.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        
        WarehouseDto warehouse1 = buildWarehouseDto(warehouseId1, SourceEnum.MFC.name(), WarehouseType.EXTERNAL);
        WarehouseDto warehouse2 = buildWarehouseDto(warehouseId2, SourceEnum.COSTCO.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(3);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(3);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch1, batch2));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse1, warehouse2));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        // Should create PO for each batch-warehouse combination: 2 batches * 2 warehouses = 4 calls
        verify(finalePurchaseOrderService, times(4)).createPurchaseOrder(any(SourceEnum.class), any(Batch.class));
    }

    @Test
    void createPurchaseOrders_With_Zero_Days_Configuration() {
        UUID batchId = UUID.randomUUID();
        UUID warehouseId = UUID.randomUUID();
        SourceEnum sourceEnum = SourceEnum.DOWNEY;

        Batch batch = buildBatch(batchId);
        batch.setFinaleEntities(JsonNodeFactory.instance.arrayNode());
        WarehouseDto warehouse = buildWarehouseDto(warehouseId, sourceEnum.name(), WarehouseType.EXTERNAL);

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(batchProcessingProperties.getDaysBefore()).thenReturn(0);
        when(batchProcessingProperties.getDaysAfter()).thenReturn(0);
        when(batchRepository.findByDeliveryDateRange(any(), any())).thenReturn(List.of(batch));
        when(warehouseQueryService.findByType(WarehouseType.EXTERNAL)).thenReturn(List.of(warehouse));
        when(featureFlagsManager.isFeatureOn(anyString())).thenReturn(true);

        scheduler.createPurchaseOrders();

        verify(batchProcessingProperties, times(2)).getDaysBefore(); // Called twice: once for date range calculation, once for logging
        verify(batchProcessingProperties, times(2)).getDaysAfter(); // Called twice: once for date range calculation, once for logging
        verify(finalePurchaseOrderService, times(1)).createPurchaseOrder(any(SourceEnum.class), eq(batch));
    }

}