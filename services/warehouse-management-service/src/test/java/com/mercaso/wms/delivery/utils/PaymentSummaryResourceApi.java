package com.mercaso.wms.delivery.utils;

import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.dto.view.SearchPaymentSummaryView;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class PaymentSummaryResourceApi extends IntegrationTestRestUtil {

    public PaymentSummaryResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchPaymentSummaryView> searchPaymentSummary(
        String deliveryDate,
        String paymentStatus,
        String orderStatus,
        Boolean issueOrder,
        Integer page,
        Integer pageSize,
        List<SortType> sortTypes) {

        UriComponentsBuilder builder = UriComponentsBuilder.fromPath("/delivery/search/delivery-orders/payment-summary");

        if (deliveryDate != null) {
            builder.queryParam("deliveryDate", deliveryDate);
        }
        if (paymentStatus != null) {
            builder.queryParam("paymentStatus", paymentStatus);
        }
        if (orderStatus != null) {
            builder.queryParam("orderStatus", orderStatus);
        }
        if (issueOrder != null) {
            builder.queryParam("issueOrder", issueOrder);
        }
        if (page != null) {
            builder.queryParam("page", page);
        }
        if (pageSize != null) {
            builder.queryParam("pageSize", pageSize);
        }
        if (sortTypes != null && !sortTypes.isEmpty()) {
            for (SortType sortType : sortTypes) {
                builder.queryParam("sortTypes", sortType.name());
            }
        }

        String url = builder.build().toUriString();
        ResponseEntity<Result> response = getEntity(url, Result.class);
        return response.getBody();
    }

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    private HttpEntity<Object> createHttpEntity() {
        return new HttpEntity<>(createHeaders());
    }
} 