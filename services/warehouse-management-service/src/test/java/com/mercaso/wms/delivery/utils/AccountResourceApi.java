package com.mercaso.wms.delivery.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.command.account.CreateAccountCommand;
import com.mercaso.wms.delivery.application.command.account.UpdateAccountCommand;
import com.mercaso.wms.delivery.application.dto.account.AccountDto;
import com.mercaso.wms.delivery.application.dto.view.SearchAccountView;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class AccountResourceApi extends IntegrationTestRestUtil {

    private static final String ACCOUNTS_URL = "/delivery/accounts";
    private static final String SEARCH_ACCOUNT_URL = "/delivery/search/accounts";
    private static final String PULL_FROM_ROUTE_MANAGER_URL = "/delivery/accounts/pull-from-route-manager";
    private static final String FIND_ACCOUNT_BY_ID_URL = "/delivery/query/accounts/%s";
    private static final String VALIDATE_BY_USER_NAME_EMAIL_URL = "/delivery/query/accounts/validate";
    private static final String FIND_ACCOUNT_BY_IDS_URL = "/delivery/query/accounts?ids=%s";

    public AccountResourceApi(Environment environment) {
        super(environment);
    }

    public AccountDto createAccount(CreateAccountCommand command) {
        return createEntity(ACCOUNTS_URL, command, AccountDto.class);
    }

    public AccountDto updateAccount(UUID id, UpdateAccountCommand command) {
        try {
            return updateEntity(ACCOUNTS_URL + "/" + id, command, AccountDto.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to update account", e);
        }
    }

    public void pullFromRouteManager() {
        try {
            ResponseEntity<Void> response = postEntity(PULL_FROM_ROUTE_MANAGER_URL, null, Void.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to pull from route manager, status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to pull from route manager", e);
        }
    }

    public Result<SearchAccountView> search(
        String userName,
        String email,
        List<String> statuses,
        List<SortType> sortTypes) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_ACCOUNT_URL + "?page=1&pageSize=20");
        if (userName != null) {
            url.append("&userName=").append(userName);
        }
        if (email != null) {
            url.append("&email=").append(email);
        }
        if (CollectionUtils.isNotEmpty(statuses)) {
            url.append("&statuses=").append(String.join(",", statuses));
        }
        if (CollectionUtils.isNotEmpty(sortTypes)) {
            url.append("&sortTypes=").append(sortTypes.stream().map(Enum::name).collect(Collectors.joining(",")));
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public AccountDto findById(UUID id) {
        String url = String.format(FIND_ACCOUNT_BY_ID_URL, id);
        return getEntity(url, AccountDto.class).getBody();
    }

    public boolean validateBy(String email) {
        StringBuilder url = new StringBuilder(VALIDATE_BY_USER_NAME_EMAIL_URL);
        if (email != null) {
            url.append("?email=").append(email);
        }
        return Boolean.TRUE.equals(getEntity(url.toString(), Boolean.class).getBody());
    }

    public List<AccountDto> findBy(List<UUID> ids) throws Exception {
        String url = String.format(FIND_ACCOUNT_BY_IDS_URL, ids.stream().map(UUID::toString).collect(Collectors.joining(",")));
        return getEntityList(url, AccountDto.class);
    }
}
