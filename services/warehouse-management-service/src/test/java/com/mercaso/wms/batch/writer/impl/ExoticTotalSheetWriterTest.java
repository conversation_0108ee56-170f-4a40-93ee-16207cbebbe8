package com.mercaso.wms.batch.writer.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class ExoticTotalSheetWriterTest extends Writer {

    private final ExoticTotalSheetWriter totalSheetWriter = new ExoticTotalSheetWriter();

    @Test
    void when_write_exotic_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> batchDtos = Lists.newArrayList();
        ExcelBatchDto exoticExcelBatchDto = new ExcelBatchDto();
        exoticExcelBatchDto.setItemNumber("exotic");
        exoticExcelBatchDto.setFrom("Location2");
        exoticExcelBatchDto.setQuantity(1);
        exoticExcelBatchDto.setSource(SourceEnum.EXOTIC.name());

        ExcelBatchDto exoticExcelBatchDto1 = new ExcelBatchDto();
        exoticExcelBatchDto1.setItemNumber("exotic1");
        exoticExcelBatchDto1.setFrom("Location2");
        exoticExcelBatchDto1.setQuantity(1);
        exoticExcelBatchDto1.setSource(SourceEnum.EXOTIC.name());

        ExcelBatchDto exoticExcelBatchDto2 = new ExcelBatchDto();
        exoticExcelBatchDto2.setItemNumber("exotic1");
        exoticExcelBatchDto2.setFrom("Location2");
        exoticExcelBatchDto2.setQuantity(1);
        exoticExcelBatchDto2.setSource(SourceEnum.EXOTIC.name());

        batchDtos.add(exoticExcelBatchDto);
        batchDtos.add(exoticExcelBatchDto1);
        batchDtos.add(exoticExcelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.EXOTIC.getValue(), batchDtos));

        writeBatchTemplate(condition, totalSheetWriter);

        List<LinkedHashMap<Integer, String>> exoticTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.EXOTIC_TOTALS.getValue()).doReadSync();

        assertEquals(5, exoticTotals.size());
        assertEquals("1", exoticTotals.get(4).get(6));
        assertEquals("2", exoticTotals.get(3).get(6));
    }

    @Test
    void when_write_empty_tobacco_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> batchDtos = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.EXOTIC.getValue(), batchDtos));

        writeBatchTemplate(condition, totalSheetWriter);

        List<LinkedHashMap<Integer, String>> exoticTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.EXOTIC_TOTALS.getValue()).doReadSync();

        assertEquals(3, exoticTotals.size());
    }
}