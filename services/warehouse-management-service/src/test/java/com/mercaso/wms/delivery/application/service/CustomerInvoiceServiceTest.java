package com.mercaso.wms.delivery.application.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.queryservice.DeliveryDocumentQueryService;
import com.mercaso.wms.delivery.application.queryservice.DeliveryOrderQueryService;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.utils.GenerateInvoiceService;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CustomerInvoiceServiceTest {

    @Mock
    private DeliveryOrderQueryService deliveryOrderQueryService;

    @Mock
    private DeliveryDocumentQueryService deliveryDocumentQueryService;

    @Mock
    private GenerateInvoiceService generateInvoiceService;

    @InjectMocks
    private CustomerInvoiceService customerInvoiceService;

    private UUID deliveryOrderId;
    private DeliveryOrderDto deliveryOrderDto;
    private DeliveryDocumentDto deliveryDocumentDto;

    @BeforeEach
    void setUp() {
        deliveryOrderId = UUID.randomUUID();
        deliveryOrderDto = mock(DeliveryOrderDto.class);
        deliveryDocumentDto = mock(DeliveryDocumentDto.class);
    }

    @Test
    void when_createCustomerInvoice_with_no_existing_invoice_then_return_file_url() throws IOException, InterruptedException {
        // Given
        when(deliveryDocumentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(Collections.emptyList());
        when(deliveryOrderQueryService.findById(deliveryOrderId)).thenReturn(deliveryOrderDto);
        when(generateInvoiceService.generateInvoice(deliveryOrderDto)).thenReturn(deliveryDocumentDto);
        when(deliveryDocumentDto.getFileUrl()).thenReturn("https://example.com/invoice.pdf");

        // When
        String fileUrl = customerInvoiceService.createCustomerInvoice(deliveryOrderId);

        // Then
        assertNotNull(fileUrl);
        assertEquals("https://example.com/invoice.pdf", fileUrl);
        verify(deliveryDocumentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService).findById(deliveryOrderId);
    }

    @Test
    void when_createCustomerInvoice_with_existing_invoice_then_throw_DeliveryBadRequestException() {
        // Given
        DeliveryDocumentDto existingDocument = mock(DeliveryDocumentDto.class);
        when(existingDocument.getDocumentType()).thenReturn(DeliveryDocumentType.INVOICE);
        when(deliveryDocumentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(List.of(existingDocument));

        // When & Then
        DeliveryBadRequestException exception = assertThrows(DeliveryBadRequestException.class, () ->
            customerInvoiceService.createCustomerInvoice(deliveryOrderId)
        );

        assertEquals("Customer invoice already exists for delivery order: " + deliveryOrderId, exception.getMessage());
        verify(deliveryDocumentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService, never()).findById(any());
    }

    @Test
    void when_createCustomerInvoice_with_generation_error_then_throw_DeliveryBusinessException() {
        // Given
        when(deliveryDocumentQueryService.findItemDocumentsBy(deliveryOrderId)).thenReturn(Collections.emptyList());
        when(deliveryOrderQueryService.findById(deliveryOrderId)).thenReturn(deliveryOrderDto);
        try {
            when(generateInvoiceService.generateInvoice(any())).thenThrow(new IOException("Generation error"));
        } catch (IOException | InterruptedException e) {
            // Mock exception handling - this block should not execute during test
        }

        // When & Then
        DeliveryBusinessException exception = assertThrows(DeliveryBusinessException.class, () ->
            customerInvoiceService.createCustomerInvoice(deliveryOrderId)
        );

        assertEquals("Error when generate customer invoice", exception.getMessage());
        verify(deliveryDocumentQueryService).findItemDocumentsBy(deliveryOrderId);
        verify(deliveryOrderQueryService).findById(deliveryOrderId);
    }
}