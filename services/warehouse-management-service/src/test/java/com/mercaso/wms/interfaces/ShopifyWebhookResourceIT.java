package com.mercaso.wms.interfaces;


import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderAdminDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShopifyWebhookResourceIT extends AbstractIT {

    @Autowired
    ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void when_webhook_then_return_void() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertEquals(shopifyOrderDto.getName(), shippingOrder.getOrderNumber());

        shopifyOrderDto.getLineItems().remove(2);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertEquals(9, shippingOrder.getShippingOrderItems().size());

        shopifyOrderDto.getLineItems().getFirst().setCurrentQuantity(5);
        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertEquals(9, shippingOrder.getShippingOrderItems().size());
        assertEquals(5, shippingOrder.getShippingOrderItems().getFirst().getQty());
        assertEquals(5, shippingOrder.getShippingOrderItems().getFirst().getPreValidateQty());
    }

    @Test
    void when_webhook_with_invalid_payload_then_return_use_admin_api() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setId(RandomStringUtils.randomNumeric(11));
        String serialize = SerializationUtils.serialize(shopifyOrderDto);

        when(shopifyAdaptor.getShopifyOrderByAdminApi(any())).thenReturn(new ShopifyOrderAdminDto(shopifyOrderDto));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        shopifyWebhookResourceApi.webhook(serialize.substring(0, serialize.length() - 10));

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertEquals(shopifyOrderDto.getName(), shippingOrder.getOrderNumber());
    }

    @Test
    void when_webhook_with_fraud_order_then_send_slack_message() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setId(RandomStringUtils.randomNumeric(11));
        ShopifyOrderDto.NoteAttributeDto noteAttributeDto = new ShopifyOrderDto.NoteAttributeDto();
        noteAttributeDto.setName("ip");
        noteAttributeDto.setValue("127.0.0.1");
        shopifyOrderDto.setNoteAttributes(List.of(noteAttributeDto));
        String serialize = SerializationUtils.serialize(shopifyOrderDto);
        when(shopifyAdaptor.getShopifyOrderByAdminApi(any())).thenReturn(new ShopifyOrderAdminDto(shopifyOrderDto));
        when(imsAdaptor.getItemsBySkus(any())).thenReturn(List.of());

        shopifyWebhookResourceApi.webhook(serialize);

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertEquals(shopifyOrderDto.getName(), shippingOrder.getOrderNumber());
    }



}