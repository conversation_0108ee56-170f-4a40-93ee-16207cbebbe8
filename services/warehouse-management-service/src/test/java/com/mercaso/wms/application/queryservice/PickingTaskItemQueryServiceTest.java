package com.mercaso.wms.application.queryservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.FailedPickingTaskItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.infrastructure.repository.pickingtaskitem.jpa.PickingTaskItemJdbcTemplate;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

class PickingTaskItemQueryServiceTest {


    private final PickingTaskItemJdbcTemplate jdbcTemplate = mock(PickingTaskItemJdbcTemplate.class);

    private final PickingTaskItemQueryService pickingTaskItemQueryService = new PickingTaskItemQueryService(jdbcTemplate);

    @Test
    void when_find_by_delivery_date_and_picking_task_numbers_then_return_picking_task_items() {
        // given
        String deliveryDate = "2021-10-10";
        List<String> pickingTaskNumbers = List.of("1", "2", "3");
        FailedPickingTaskItemDto failedPickingTaskItemDto = new FailedPickingTaskItemDto();
        failedPickingTaskItemDto.setId(UUID.randomUUID());
        when(jdbcTemplate.searchBy(deliveryDate, pickingTaskNumbers, SourceEnum.MFC.name(), Pageable.ofSize(10))).
            thenReturn(new PageImpl<>(List.of(failedPickingTaskItemDto), Pageable.ofSize(10), 1));

        // when
        Page<FailedPickingTaskItemDto> result = pickingTaskItemQueryService.searchBy(deliveryDate,
            pickingTaskNumbers, SourceEnum.MFC.name(), Pageable.ofSize(10));

        // then
        assertEquals(1, result.getContent().size());
    }

}