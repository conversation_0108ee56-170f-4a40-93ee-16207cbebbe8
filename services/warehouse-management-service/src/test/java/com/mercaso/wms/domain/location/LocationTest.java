package com.mercaso.wms.domain.location;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.domain.location.enums.LocationType;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class LocationTest {

    @Test
    void when_update_location_then_return_location() {
        // given
        String name = RandomStringUtils.randomAlphabetic(10);
        Location location = Location.builder().type(LocationType.BIN).aisleNumber("01").bayNumber("01").name(
            "test").build();

        Location result = location.updateLocation(name, LocationType.BIN, "01", "01");
        // then
        assertEquals(name, result.getName());
    }

}