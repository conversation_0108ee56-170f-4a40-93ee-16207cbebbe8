package com.mercaso.wms.batch.writer;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.wms.application.service.BatchApplicationService;
import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class TemplateWriterServiceTest {

    private final BatchApplicationService batchApplicationService = mock(BatchApplicationService.class);

    private final DocumentOperations documentOperations = mock(DocumentOperations.class);

    private final List<SheetWriter> writers = Lists.newArrayList();

    private final BatchJpaDao batchJpaDao = mock(BatchJpaDao.class);

    private final TemplateWriterService templateWriterService = new TemplateWriterService(documentOperations,
        writers,
        batchJpaDao,
        batchApplicationService);

    @Test
    void when_input_data_then_should_create_a_new_file() throws Exception {
        List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();

        String orderNumber = "M-".concat(RandomStringUtils.randomNumeric(5));
        for (int i = 0; i < 10; i++) {
            ExcelBatchDto excelBatchDto = new ExcelBatchDto();
            excelBatchDto.setOrderNumber(orderNumber);
            excelBatchDto.setFilled(i);
            excelBatchDto.setItemNumber("JC".concat(RandomStringUtils.randomNumeric(5)));
            excelBatchDto.setItemDescription(RandomStringUtils.randomAlphabetic(20));
            excelBatchDto.setQuantity(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            excelBatchDto.setPos(RandomStringUtils.randomAlphabetic(2));
            excelBatchDto.setDepartment(RandomStringUtils.randomAlphabetic(20));
            excelBatchDto.setCategory(RandomStringUtils.randomAlphabetic(20));
            excelBatchDto.setSubCategory(RandomStringUtils.randomAlphabetic(20));
            excelBatchDto.setClazz(RandomStringUtils.randomAlphabetic(20));
            excelBatchDto.setLine(i + 1);
            excelBatchDtos.add(excelBatchDto);
        }

        List<BreakdownDto> bigBreakdownDtos = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            BreakdownDto breakdownDto = new BreakdownDto();
            breakdownDto.setOrderNumber("M-".concat(RandomStringUtils.randomNumeric(5)));
            breakdownDto.setBreakdown(RandomStringUtils.randomAlphanumeric(2));
            breakdownDto.setTotal(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            breakdownDto.setMfcBeverage(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            breakdownDto.setMfcOther(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            bigBreakdownDtos.add(breakdownDto);
        }

        List<BreakdownDto> smallBreakdownDtos = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            BreakdownDto breakdownDto = new BreakdownDto();
            breakdownDto.setOrderNumber("M-".concat(RandomStringUtils.randomNumeric(5)));
            breakdownDto.setBreakdown(RandomStringUtils.randomAlphanumeric(2));
            breakdownDto.setTotal(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            breakdownDto.setMfcBeverage(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            breakdownDto.setMfcOther(Integer.parseInt(RandomStringUtils.randomNumeric(2)));
            smallBreakdownDtos.add(breakdownDto);
        }

        WriteTemplateCondition writeTemplateCondition = WriteTemplateCondition.builder()
            .excelBatchDtos(excelBatchDtos)
            .bigBreakdownDtos(bigBreakdownDtos)
            .smallBreakdownDtos(smallBreakdownDtos)
            .taggedWith("2024-08-01")
            .fileNames(List.of("1-Mercaso Pick Sheet Template_V7.8_1.xlsx"))
            .build();

        byte[] bytes = templateWriterService.writeBatchTemplate(writeTemplateCondition);

        File file = new File("1-Mercaso Pick Sheet Template_V7.8_1.xlsx");
        try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            fileOutputStream.write(bytes);
            assertTrue(file.exists());
        }
    }
}