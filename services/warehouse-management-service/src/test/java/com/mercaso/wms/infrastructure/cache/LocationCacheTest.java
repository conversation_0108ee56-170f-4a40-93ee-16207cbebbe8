package com.mercaso.wms.infrastructure.cache;

import static com.mercaso.wms.builder.DataBuilder.buildLocation;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

class LocationCacheTest {

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final LocationCache locationCache = new LocationCache(locationRepository);

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getLocationMap_shouldReturnEmptyMap_whenNoLocationsExist() {
        when(locationRepository.findAll()).thenReturn(Collections.emptyList());

        Map<UUID, Location> result = locationCache.getLocationMap();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(locationRepository, times(1)).findAll();
    }

    @Test
    void refreshLocationCache_shouldEvictCacheAndReload() {
        List<Location> locations = Arrays.asList(
            buildLocation("Location1", LocationType.STOCK),
            buildLocation("Location2", LocationType.STOCK)
        );
        when(locationRepository.findAll()).thenReturn(locations);

        locationCache.getLocationMap();
        locationCache.refreshLocationCache();

        verify(locationRepository, times(2)).findAll();
    }

}