package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class MissionOrderFormSheetWriterTest extends Writer {

    private final MissionOrderFormSheetWriter missionOrderFormSheetWriter = new MissionOrderFormSheetWriter();

    @Test
    void when_write_mission_order_form_then_fill() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> missions = Lists.newArrayList();
        ExcelBatchDto missionExcelBatchDto = new ExcelBatchDto();
        missionExcelBatchDto.setItemNumber("MI32693");
        missionExcelBatchDto.setFrom("Location2");
        missionExcelBatchDto.setQuantity(1);
        missionExcelBatchDto.setSource(SourceEnum.COSTCO.name());
        missionExcelBatchDto.setFrom("121");
        missionExcelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        missions.add(missionExcelBatchDto);

        condition.setSourceAndListMap(Maps.newHashMap(GeneratedDocNameEnum.MISSION.getValue(), missions));

        writeBatchTemplate(condition, missionOrderFormSheetWriter);

        List<LinkedHashMap<Integer, String>> missionOrderForm =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.MISSION_ORDER_FORM.getValue()).doReadSync();

        assertEquals("1", missionOrderForm.get(5).get(4));
    }

}