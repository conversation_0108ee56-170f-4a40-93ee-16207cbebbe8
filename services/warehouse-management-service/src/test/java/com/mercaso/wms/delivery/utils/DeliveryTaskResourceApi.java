package com.mercaso.wms.delivery.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.wms.delivery.application.command.deliverytask.BuildDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.command.deliverytask.ReassignDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.command.deliverytask.UpdateDeliveryTaskCommand;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.utils.IntegrationTestRestUtil;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * Test utility class for making requests to delivery task related endpoints
 */
@Slf4j
@Component
public class DeliveryTaskResourceApi extends IntegrationTestRestUtil {

    private static final String DELIVERY_TASKS_BUILD = "/delivery/delivery-tasks/build";
    private static final String DELIVERY_TASK_QUERY = "/delivery/query/delivery-tasks";
    private static final String DELIVERY_TASKS_REBUILD = "/delivery/delivery-tasks/{taskId}/rebuild";
    private static final String DELIVERY_TASKS_COMPLETE = "/delivery/delivery-tasks/{taskId}/complete";
    private static final String DELIVERY_TASKS_BATCH_COMPLETE = "/delivery/delivery-tasks/batch-complete";
    private static final String REASSIGN_DELIVERY_TASKS = "/delivery/delivery-tasks/{taskId}/reassign";
    private static final String DELIVERY_TASKS_UPDATE = "/delivery/delivery-tasks/{taskId}";
    private static final String DELIVERY_TASKS_BY_IDS = "/delivery/query/delivery-tasks/by-ids";
    private static final String DELIVERY_TASKS_SYNC_NEW_ROUTES = "/delivery/delivery-tasks/sync-new-routes";

    public DeliveryTaskResourceApi(Environment environment) {
        super(environment);
    }

    /**
     * Sends a request to build delivery tasks for a specific date
     *
     * @param deliveryDate the date to build tasks for
     */
    public void buildDeliveryTasks(LocalDate deliveryDate) {
        BuildDeliveryTaskCommand command = BuildDeliveryTaskCommand.builder()
            .deliveryDate(deliveryDate)
            .build();

        createEntity(DELIVERY_TASKS_BUILD,
            command,
            Void.class);
    }

    /**
     * Sends a request to rebuild a specific delivery task
     *
     * @param taskId the ID of the task to rebuild
     */
    public void rebuildTask(UUID taskId) {
        String url = DELIVERY_TASKS_REBUILD.replace("{taskId}", taskId.toString());
        createEntity(url, null, Void.class);
    }

    /**
     * Retrieves a delivery task by its ID
     *
     * @param deliveryTaskId the ID of the delivery task to retrieve
     * @return the delivery task DTO
     */
    public DeliveryTaskDto findById(UUID deliveryTaskId) {
        ResponseEntity<DeliveryTaskDto> response = getEntity(
            DELIVERY_TASK_QUERY + "/" + deliveryTaskId,
            DeliveryTaskDto.class
        );
        return response.getBody();
    }

    public List<DeliveryTaskDto> findByIds(List<String> ids) throws Exception {
        return getEntityList(DELIVERY_TASKS_BY_IDS.concat("?deliveryTaskIds=").concat(String.join(",", ids)),
            DeliveryTaskDto.class);
    }

    /**
     * Sends a request to complete a delivery task
     *
     * @param taskId the ID of the task to complete
     */
    public void completeTask(UUID taskId) {
        String url = DELIVERY_TASKS_COMPLETE.replace("{taskId}", taskId.toString());
        try {
            updateEntity(url, null, DeliveryTaskDto.class);
        } catch (JsonProcessingException e) {
            log.error("Error completing task with ID {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * Sends a request to complete multiple delivery tasks in batch
     *
     * @param taskIds the list of task IDs to complete
     */
    public void batchCompleteTasks(List<UUID> taskIds) {
        try {
            updateEntity(
                DELIVERY_TASKS_BATCH_COMPLETE,
                taskIds,
                UUID[].class
            );
        } catch (JsonProcessingException e) {
            log.error("Error completing tasks in batch: {}", e.getMessage());
        }
    }

    public DeliveryTaskDto reassignTask(UUID taskId, ReassignDeliveryTaskCommand command) throws JsonProcessingException {
        String url = REASSIGN_DELIVERY_TASKS.replace("{taskId}", taskId.toString());
        return updateEntity(url, command, DeliveryTaskDto.class);
    }

    public DeliveryTaskDto update(UUID deliveryTaskId, UpdateDeliveryTaskCommand command) throws JsonProcessingException {
        String url = DELIVERY_TASKS_UPDATE.replace("{taskId}", deliveryTaskId.toString());
        return updateEntity(url, command, DeliveryTaskDto.class);
    }

    /**
     * Sends a request to sync new routes for a specific date
     *
     * @param command the command containing the delivery date
     */
    public void syncNewRoutes(BuildDeliveryTaskCommand command) {
        createEntity(DELIVERY_TASKS_SYNC_NEW_ROUTES,
            command,
            Void.class);
    }
} 