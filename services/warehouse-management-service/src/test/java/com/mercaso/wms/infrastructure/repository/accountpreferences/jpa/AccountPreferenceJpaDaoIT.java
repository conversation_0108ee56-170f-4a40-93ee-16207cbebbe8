package com.mercaso.wms.infrastructure.repository.accountpreferences.jpa;

import static com.mercaso.wms.utils.MockDataUtils.buildAccountPreference;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.infrastructure.repository.accountpreferences.jpa.dataobject.AccountPreferenceDo;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class AccountPreferenceJpaDaoIT extends AbstractIT {

    @Autowired
    private AccountPreferenceJpaDao accountPreferenceJpaDao;
    @Autowired
    private AccountPreferenceRepository accountPreferenceRepository;

    @Test
    void when_find_by_work_warehouse_name_and_type_then_return_account_preference() {
        // given
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);

        AccountPreference accountPreference = buildAccountPreference(UUID.randomUUID(), warehouses.getLast(), null);

        accountPreferenceRepository.save(accountPreference);

        // when
        List<AccountPreferenceDo> accountPreferenceDos = accountPreferenceJpaDao.findByWorkWarehouseNameAndType(warehouses.getLast()
            .getName(), accountPreference.getTaskType());

        // then
        assertNotNull(accountPreferenceDos);
    }

}