package com.mercaso.wms.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.command.crossdock.BindItemAndTaskCommand;
import com.mercaso.wms.application.command.crossdock.CreateCrossDockTaskCommand;
import com.mercaso.wms.application.dto.CrossDockTaskDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskItemView;
import com.mercaso.wms.application.dto.view.SearchCrossDockTaskView;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class CrossDockTaskResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_CROSS_DOCK_TASKS_URL = "/search/cross-dock-tasks";
    private static final String CREATE_CROSS_DOCK_TASK_URL = "/cross-dock-tasks";
    private static final String BIND_ITEM_AND_TASK_URL = "/cross-dock-tasks/%s";
    private static final String SEARCH_CROSS_DOCK_TASK_ITEMS_URL = "/search/cross-dock-task-items";
    private static final String GET_CROSS_DOCK_TASK_BY_ID_URL = "/query/cross-dock-tasks/%s";

    public CrossDockTaskResourceApi(Environment environment) {
        super(environment);
    }

    public Result<SearchCrossDockTaskView> searchCrossDockTasks(
        String number,
        UUID pickerUserId,
        String deliveryDate) throws Exception {
        return searchCrossDockTasks(number, pickerUserId, deliveryDate, null, null);
    }

    public Result<SearchCrossDockTaskView> searchCrossDockTasks(
        String number,
        UUID pickerUserId,
        String deliveryDate,
        String orderNumber,
        String skuNumber) throws Exception {

        StringBuilder urlBuilder = new StringBuilder(SEARCH_CROSS_DOCK_TASKS_URL).append("?");
        if (StringUtils.isNotBlank(number)) {
            urlBuilder.append("number=").append(number).append("&");
        }
        if (pickerUserId != null) {
            urlBuilder.append("pickerUserId=").append(pickerUserId).append("&");
        }
        if (StringUtils.isNotBlank(deliveryDate)) {
            urlBuilder.append("deliveryDate=").append(deliveryDate).append("&");
        }
        if (StringUtils.isNotBlank(orderNumber)) {
            urlBuilder.append("orderNumber=").append(orderNumber).append("&");
        }
        if (StringUtils.isNotBlank(skuNumber)) {
            urlBuilder.append("skuNumber=").append(skuNumber).append("&");
        }
        String url =
            urlBuilder.toString().endsWith("&") ? urlBuilder.substring(0, urlBuilder.length() - 1) : urlBuilder.toString();

        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public CrossDockTaskDto createCrossDockTask(CreateCrossDockTaskCommand command) {
        return createEntity(CREATE_CROSS_DOCK_TASK_URL, SerializationUtils.serialize(command), CrossDockTaskDto.class);
    }

    public CrossDockTaskDto bindItemAndTask(UUID crossDockTaskId, BindItemAndTaskCommand command) throws Exception {
        String url = String.format(BIND_ITEM_AND_TASK_URL, crossDockTaskId);
        return updateEntity(url, command, CrossDockTaskDto.class);
    }

    public Result<SearchCrossDockTaskItemView> searchCrossDockTaskItems(
        String sku,
        String deliveryDate,
        String pickingTaskItemId,
        String receivingTaskItemId,
        String status,
        String taskNumber,
        String source) throws Exception {
        return searchCrossDockTaskItems(sku,
            deliveryDate,
            pickingTaskItemId,
            receivingTaskItemId,
            status,
            taskNumber,
            source,
            null,
            null,
            null,
            null);
    }

    public Result<SearchCrossDockTaskItemView> searchCrossDockTaskItems(
        String sku,
        String deliveryDate,
        String pickingTaskItemId,
        String receivingTaskItemId,
        String status,
        String taskNumber,
        String source,
        String truckNumber,
        String driver,
        Boolean highValueItem,
        String sortTypes) throws Exception {
        return searchCrossDockTaskItems(sku,
            deliveryDate,
            pickingTaskItemId,
            receivingTaskItemId,
            status,
            taskNumber,
            source,
            truckNumber,
            driver,
            null,
            highValueItem,
            sortTypes);
    }

    public Result<SearchCrossDockTaskItemView> searchCrossDockTaskItems(
        String sku,
        String deliveryDate,
        String pickingTaskItemId,
        String receivingTaskItemId,
        String status,
        String taskNumber,
        String source,
        String truckNumber,
        String driverUserName,
        String driverUserId,
        Boolean highValueItem,
        String sortTypes) throws Exception {
        return searchCrossDockTaskItems(sku,
            deliveryDate,
            pickingTaskItemId,
            receivingTaskItemId,
            status,
            taskNumber,
            source,
            truckNumber,
            driverUserName,
            driverUserId,
            highValueItem,
            sortTypes,
            null);
    }

    public Result<SearchCrossDockTaskItemView> searchCrossDockTaskItems(
        String sku,
        String deliveryDate,
        String pickingTaskItemId,
        String receivingTaskItemId,
        String status,
        String taskNumber,
        String source,
        String truckNumber,
        String driverUserName,
        String driverUserId,
        Boolean highValueItem,
        String sortTypes,
        String orderNumber) throws Exception {

        StringBuilder urlBuilder = new StringBuilder(SEARCH_CROSS_DOCK_TASK_ITEMS_URL).append("?");
        if (StringUtils.isNotBlank(sku)) {
            urlBuilder.append("sku=").append(sku).append("&");
        }
        if (StringUtils.isNotBlank(deliveryDate)) {
            urlBuilder.append("deliveryDate=").append(deliveryDate).append("&");
        }
        if (StringUtils.isNotBlank(pickingTaskItemId)) {
            urlBuilder.append("pickingTaskItemId=").append(pickingTaskItemId).append("&");
        }
        if (StringUtils.isNotBlank(receivingTaskItemId)) {
            urlBuilder.append("receivingTaskItemId=").append(receivingTaskItemId).append("&");
        }
        if (StringUtils.isNotBlank(status)) {
            urlBuilder.append("status=").append(status).append("&");
        }
        if (StringUtils.isNotBlank(taskNumber)) {
            urlBuilder.append("taskNumber=").append(taskNumber).append("&");
        }
        if (StringUtils.isNotBlank(source)) {
            urlBuilder.append("source=").append(source).append("&");
        }
        if (StringUtils.isNotBlank(truckNumber)) {
            urlBuilder.append("truckNumber=").append(truckNumber).append("&");
        }
        if (StringUtils.isNotBlank(driverUserName)) {
            urlBuilder.append("driverUserName=").append(driverUserName).append("&");
        }
        if (StringUtils.isNotBlank(driverUserId)) {
            urlBuilder.append("driverUserId=").append(driverUserId).append("&");
        }
        if (highValueItem != null) {
            urlBuilder.append("highValueItem=").append(highValueItem).append("&");
        }
        if (StringUtils.isNotBlank(sortTypes)) {
            urlBuilder.append("sortTypes=").append(sortTypes).append("&");
        }
        if (StringUtils.isNotBlank(orderNumber)) {
            urlBuilder.append("orderNumber=").append(orderNumber).append("&");
        }
        String url =
            urlBuilder.toString().endsWith("&") ? urlBuilder.substring(0, urlBuilder.length() - 1) : urlBuilder.toString();

        String body = getEntity(url, String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<>() {
        });
    }

    public CrossDockTaskDto getCrossDockTask(UUID id) {
        return getEntity(String.format(GET_CROSS_DOCK_TASK_BY_ID_URL, id), CrossDockTaskDto.class).getBody();
    }

}