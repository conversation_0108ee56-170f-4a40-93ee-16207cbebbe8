package com.mercaso.wms.delivery.interfaces.search;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildAccount;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.delivery.application.dto.view.SearchAccountView;
import com.mercaso.wms.delivery.domain.account.Account;
import com.mercaso.wms.delivery.domain.account.AccountRepository;
import com.mercaso.wms.delivery.infrastructure.repository.account.jpa.AccountJpaDao;
import com.mercaso.wms.delivery.utils.AccountResourceApi;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchAccountResourceIT extends AbstractIT {

    @Autowired
    AccountResourceApi accountResourceApi;
    @Autowired
    AccountRepository accountRepository;
    @Autowired
    AccountJpaDao accountJpaDao;

    @Test
    void when_search_account_then_return_accounts() throws Exception {
        accountJpaDao.deleteAll();
        Account account = buildAccount(RandomStringUtils.randomAlphabetic(20));

        accountRepository.save(account);

        Result<SearchAccountView> searchByUserName = accountResourceApi.search(account.getUserName(),
            null,
            null,
            List.of(SortType.USER_NAME_ASC));

        assertNotNull(searchByUserName);
        assertNotNull(searchByUserName.getData());
        assertEquals(account.getUserName(), searchByUserName.getData().getFirst().getUserName());
        assertEquals(account.getEmail(), searchByUserName.getData().getFirst().getEmail());
        assertEquals(account.getStatus().name(), searchByUserName.getData().getFirst().getStatus());

        Result<SearchAccountView> searchByEmail = accountResourceApi.search(null,
            account.getEmail(),
            null,
            List.of(SortType.USER_NAME_DESC));

        assertNotNull(searchByEmail);
        assertNotNull(searchByEmail.getData());
        assertEquals(account.getUserName(), searchByEmail.getData().getFirst().getUserName());
        assertEquals(account.getEmail(), searchByEmail.getData().getFirst().getEmail());
        assertEquals(account.getStatus().name(), searchByEmail.getData().getFirst().getStatus());

        Result<SearchAccountView> searchByStatus = accountResourceApi.search(null,
            null,
            List.of(account.getStatus().name()),
            null);

        assertNotNull(searchByStatus);
        assertNotNull(searchByStatus.getData());
        assertEquals(account.getUserId(), searchByStatus.getData().getFirst().getUserId());
        assertEquals(account.getUserName(), searchByStatus.getData().getFirst().getUserName());
        assertEquals(account.getEmail(), searchByStatus.getData().getFirst().getEmail());
        assertEquals(account.getStatus().name(), searchByStatus.getData().getFirst().getStatus());

    }

}