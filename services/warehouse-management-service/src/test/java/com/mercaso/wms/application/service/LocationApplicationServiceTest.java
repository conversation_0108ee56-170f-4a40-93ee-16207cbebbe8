package com.mercaso.wms.application.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import com.mercaso.wms.infrastructure.external.finale.FinaleProductService;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleFacilityDto;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class LocationApplicationServiceTest {

    private final FinaleProductService finaleProductService = mock(FinaleProductService.class);

    private final LocationRepository locationRepository = mock(LocationRepository.class);

    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);

    private final PgAdvisoryLock pgAdvisoryLock = mock(PgAdvisoryLock.class);

    private static final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private static final FinaleConfigProperties finaleConfigProperties = mock(FinaleConfigProperties.class);

    private final LocationApplicationService service = new LocationApplicationService(
        finaleProductService,
        locationRepository,
        warehouseRepository,
        pgAdvisoryLock,
        featureFlagsManager,
        finaleConfigProperties
    );

    @BeforeAll
    static void beforeAll() {
        // Arrange
        when(featureFlagsManager.isFeatureOn(FeatureFlagKeys.SYNC_SUBLOCATIONS_FROM_FINALE)).thenReturn(true);
    }

    @Test
    void syncLocations_ShouldDoNothing_WhenNoFinaleData() {
        // Arrange
        when(finaleProductService.getFinaleFacilityData()).thenReturn(Collections.emptyList());

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, never()).saveAll(any());
    }

    @Test
    void syncLocations_ShouldDoNothing_WhenMFCWarehouseNotFound() {
        // Arrange
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto("101-01-A-1", false)
        );
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MFC")).thenReturn(null);

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, never()).saveAll(any());
    }

    @Test
    void syncLocations_ShouldCreateNewLocations_WhenLocationNotExists() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, false)
        );
        Warehouse mfcWarehouse = createWarehouse();

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(new ArrayList<>());

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, times(1)).saveAll(any());
    }

    @Test
    void syncLocations_ShouldCreateStockLocation_WhenShippingDisabled() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, true)
        );
        Warehouse mfcWarehouse = createWarehouse();

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(new ArrayList<>());

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, times(1)).saveAll(any());
    }

    @Test
    void syncLocations_ShouldNotCreateLocation_WhenLocationExists() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, false)
        );
        Warehouse mfcWarehouse = createWarehouse();
        List<Location> existingLocations = List.of(
            Location.builder()
                .name(locationName)
                .type(LocationType.BIN)
                .build()
        );

        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(existingLocations);

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, never()).saveAll(any());
    }

    @Test
    void syncLocations_ShouldNotCreateLocation_WhenLocationExists_and_type_is_STAGING_AREA() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, true)
        );
        Warehouse mfcWarehouse = createWarehouse();
        List<Location> existingLocations = List.of(
            Location.builder()
                .name(locationName)
                .type(LocationType.STAGING_AREA)
                .build()
        );

        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(existingLocations);

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, never()).saveAll(any());
    }

    @Test
    void syncLocations_ShouldNotCreateLocation_WhenLocationExists_but_type_changed_to_disable() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, Boolean.TRUE)
        );
        Warehouse mfcWarehouse = createWarehouse();
        List<Location> existingLocations = List.of(
            Location.builder()
                .name(locationName)
                .type(LocationType.BIN)
                .build()
        );

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(existingLocations);

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, times(1)).update(any());
    }

    @Test
    void syncLocations_ShouldNotCreateLocation_WhenLocationExists_but_type_changed_to_active() {
        // Arrange
        String locationName = "101-01-A-1";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, Boolean.FALSE)
        );
        Warehouse mfcWarehouse = createWarehouse();
        List<Location> existingLocations = List.of(
            Location.builder()
                .name(locationName)
                .type(LocationType.STOCK)
                .build()
        );

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mfcWarehouse);
        when(locationRepository.findAll()).thenReturn(existingLocations);

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, times(1)).update(any());
    }

    @Test
    void syncLocations_ShouldCreateLocation_WhenFacilityNameInSpecialLocationNameList() {
        // Arrange
        String locationName = "PHOTO-STUDIO";
        List<FinaleFacilityDto> facilityData = List.of(
            createFinaleFacilityDto(locationName, false)
        );
        Warehouse mdcWarehouse = createWarehouse();

        when(pgAdvisoryLock.tryLockWithSessionLevel(anyInt())).thenReturn(true);
        when(finaleProductService.getFinaleFacilityData()).thenReturn(facilityData);
        when(warehouseRepository.findByName("MDC")).thenReturn(mdcWarehouse);
        when(locationRepository.findAll()).thenReturn(new ArrayList<>());
        when(finaleConfigProperties.getNeedToBeSyncedLocations()).thenReturn(List.of(locationName));

        // Act
        service.syncLocations();

        // Assert
        verify(locationRepository, times(1)).saveAll(any());
    }

    private FinaleFacilityDto createFinaleFacilityDto(String name, Boolean shippingDisabled) {
        return FinaleFacilityDto.builder()
            .facilityName(name)
            .shippingDisabled(shippingDisabled)
            .build();
    }

    private Warehouse createWarehouse() {
        return Warehouse.builder()
            .id(UUID.randomUUID())
            .name("MFC")
            .build();
    }

}