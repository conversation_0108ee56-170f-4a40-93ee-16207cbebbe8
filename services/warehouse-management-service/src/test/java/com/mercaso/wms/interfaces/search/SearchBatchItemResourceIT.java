package com.mercaso.wms.interfaces.search;


import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.BatchItemDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.infrastructure.repository.batch.jpa.BatchJpaDao;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.BatchItemsJpaDao;
import com.mercaso.wms.infrastructure.repository.batchitems.jpa.mapper.BatchItemDoMapper;
import com.mercaso.wms.utils.BatchResourceApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SearchBatchItemResourceIT extends AbstractIT {

    @Autowired
    private BatchResourceApi batchResourceApi;
    @Autowired
    private BatchItemsJpaDao batchItemsJpaDao;
    @Autowired
    private BatchJpaDao batchJpaDao;
    @Autowired
    private BatchItemDoMapper mapper;


    @Test
    void when_search_batch_items_then_return_batch_items() throws Exception {
        batchJpaDao.deleteAll();
        batchItemsJpaDao.deleteAll();
        // Arrange
        BatchDo batch = new BatchDo();
        batch.setLastModifiedBy("test");
        batch.setTag("2028-08-20");
        BatchDo savedBatch = batchJpaDao.save(batch);
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 2);
        batchItems.forEach(batchItem -> batchItem.setBatchId(savedBatch.getId()));
        batchItemsJpaDao.saveAll(mapper.domainToDos(batchItems));

        Result<BatchItemDto> batchItemDtoResult = batchResourceApi.searchBatchItems(batchItems.getFirst().getSource(),
            batch.getTag());

        // Assert
        assertEquals(2, batchItemDtoResult.getData().size());
    }


}