package com.mercaso.wms.delivery.interfaces.query;

import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildCustomer;
import static com.mercaso.wms.delivery.utils.DeliveryMockDataUtils.buildDeliveryTaskDo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.domain.customer.Customer;
import com.mercaso.wms.delivery.domain.customer.CustomerRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.DeliveryTaskJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.delivery.utils.DeliveryMockDataUtils;
import com.mercaso.wms.delivery.utils.DeliveryTaskResourceApi;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryDeliveryTaskResourceIT extends AbstractIT {

    @Autowired
    private DeliveryTaskResourceApi deliveryTaskResourceApi;

    @Autowired
    private DeliveryTaskJpaDao deliveryTaskJpaDao;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Autowired
    private CustomerRepository customerRepository;

    private UUID savedTaskId;

    @BeforeEach
    void setUp() {
        // Clean up any existing data to avoid conflicts
        deliveryTaskJpaDao.deleteAll();
    }

    @AfterEach
    void tearDown() {
        // Clean up test data
        if (savedTaskId != null) {
            deliveryTaskJpaDao.deleteById(savedTaskId);
        }
    }

    @Test
    void when_find_by_id_with_valid_id_then_returns_delivery_task_with_orders() throws Exception {
        // Prepare test data
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        DeliveryTaskDo taskDo = buildDeliveryTaskDo("TD-TEST-001", deliveryDate, "TRUCK-TEST-001", "TestDriver");
        DeliveryTaskDo savedTask = deliveryTaskJpaDao.saveAndFlush(taskDo); // Use saveAndFlush to ensure data is committed
        savedTaskId = savedTask.getId();

        // Verify task is saved correctly
        Optional<DeliveryTaskDo> foundTask = deliveryTaskJpaDao.findById(savedTaskId);
        assertNotNull(foundTask.orElse(null), "Task should be saved to database");

        Customer customer1 = buildCustomer();
        Customer customer2 = buildCustomer();
        Customer savedCustom1 = customerRepository.save(customer1);
        Customer savedCustom2 = customerRepository.save(customer2);

        // Add related orders to the task
        DeliveryOrder deliveryOrder1 = DeliveryMockDataUtils.buildDeliveryOrder();
        deliveryOrder1.setDeliveryTaskId(savedTask.getId());
        deliveryOrder1.setStatus(DeliveryOrderStatus.ASSIGNED);
        deliveryOrder1.setCustomer(savedCustom1);

        DeliveryOrder deliveryOrder2 = DeliveryMockDataUtils.buildDeliveryOrder();
        deliveryOrder2.setDeliveryTaskId(savedTask.getId());
        deliveryOrder2.setStatus(DeliveryOrderStatus.IN_TRANSIT);
        deliveryOrder2.setCustomer(savedCustom2);

        // Save orders
        deliveryOrderRepository.save(deliveryOrder1);
        deliveryOrderRepository.save(deliveryOrder2);

        // Verify orders are saved with the correct task ID
        List<DeliveryOrder> savedOrders = deliveryOrderRepository.findAllByDeliveryTaskIdIn(List.of(savedTaskId));
        assertNotNull(savedOrders, "Orders should be saved");
        assertEquals(2, savedOrders.size(), "Two orders should be saved");
        savedOrders.forEach(order -> assertEquals(savedTaskId, order.getDeliveryTaskId(),
            "Order should reference the correct task ID"));

        // Call the method being tested
        DeliveryTaskDto result = deliveryTaskResourceApi.findById(savedTaskId);

        // Verify results
        assertNotNull(result, "Result should not be null");
        assertEquals(savedTask.getId(), result.getId());
        assertEquals(savedTask.getNumber(), result.getNumber());
        assertEquals(savedTask.getDeliveryDate(), result.getDeliveryDate());
        assertEquals(savedTask.getStatus(), result.getStatus());
        assertEquals(savedTask.getTruckNumber(), result.getTruckNumber());
        assertEquals(savedTask.getDriverUserName(), result.getDriverUserName());

        // Verify orders were loaded correctly
        assertNotNull(result.getDeliveryOrders(), "Orders should be included");
        assertEquals(2, result.getDeliveryOrders().size(), "Two orders should be included");

        List<DeliveryTaskDto> byIds = deliveryTaskResourceApi.findByIds(List.of(savedTaskId.toString()));

        assertNotNull(byIds);
        assertEquals(1, byIds.size());
        DeliveryTaskDto foundTaskDto = byIds.getFirst();
        assertEquals(savedTask.getId(), foundTaskDto.getId());
        assertEquals(savedTask.getNumber(), foundTaskDto.getNumber());
    }

    @Test
    void when_find_by_id_with_non_existent_id_then_returns_null() {
        // Call the method being tested with a non-existent ID
        UUID nonExistentId = UUID.randomUUID();
        DeliveryTaskDto result = deliveryTaskResourceApi.findById(nonExistentId);

        // Verify results
        assertNull(result);
    }
} 