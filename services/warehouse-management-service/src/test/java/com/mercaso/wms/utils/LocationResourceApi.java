package com.mercaso.wms.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class LocationResourceApi extends IntegrationTestRestUtil {

    private static final String SEARCH_LOCATION_URL = "/search/locations";

    public LocationResourceApi(Environment environment) {
        super(environment);
    }

    public Result<LocationDto> search(String name) throws Exception {
        StringBuilder url = new StringBuilder(SEARCH_LOCATION_URL + "?page=1&pageSize=20");
        if (name != null) {
            url.append("&name=").append(name);
        }
        String body = getEntity(url.toString(), String.class).getBody();
        return SerializationUtils.readValue(body, new TypeReference<Result<LocationDto>>() {
        });
    }

}