package com.mercaso.wms.infrastructure.repository.document;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.wms.domain.document.Document;
import com.mercaso.wms.domain.document.enums.DocumentType;
import com.mercaso.wms.infrastructure.repository.document.jpa.DocumentJpaDao;
import com.mercaso.wms.infrastructure.repository.document.jpa.dataobject.DocumentDo;
import com.mercaso.wms.infrastructure.repository.document.jpa.mapper.DocumentDoMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DocumentRepositoryImplTest {

    @Mock
    private DocumentDoMapper mapper;

    @Mock
    private DocumentJpaDao jpaDao;

    @InjectMocks
    private DocumentRepositoryImpl documentRepository;

    private UUID documentId;
    private UUID entityId;
    private Document document;
    private DocumentDo documentDo;

    @BeforeEach
    void setUp() {
        documentId = UUID.randomUUID();
        entityId = UUID.randomUUID();

        document = Document.builder()
            .id(documentId)
            .entityId(entityId)
            .entityName("SHIPPING_ORDER")
            .documentType(DocumentType.DAMAGED)
            .fileName("test-file.pdf")
            .createdUserName("test-user")
            .build();

        documentDo = new DocumentDo();
        documentDo.setId(documentId);
        documentDo.setEntityId(entityId);
        documentDo.setEntityName("SHIPPING_ORDER");
        documentDo.setDocumentType(DocumentType.DAMAGED);
        documentDo.setFileName("test-file.pdf");
        documentDo.setCreatedUserName("test-user");
    }

    @Test
    void save_Success() {
        // Given
        when(mapper.domainToDo(document)).thenReturn(documentDo);
        when(jpaDao.save(documentDo)).thenReturn(documentDo);
        when(mapper.doToDomain(documentDo)).thenReturn(document);

        // When
        Document result = documentRepository.save(document);

        // Then
        assertNotNull(result);
        assertEquals(document, result);
        verify(mapper).domainToDo(document);
        verify(jpaDao).save(documentDo);
        verify(mapper).doToDomain(documentDo);
    }

    @Test
    void findById_Success() {
        // Given
        when(jpaDao.findById(documentId)).thenReturn(Optional.of(documentDo));
        when(mapper.doToDomain(documentDo)).thenReturn(document);

        // When
        Document result = documentRepository.findById(documentId);

        // Then
        assertNotNull(result);
        assertEquals(document, result);
        verify(jpaDao).findById(documentId);
        verify(mapper).doToDomain(documentDo);
    }

    @Test
    void findById_NotFound_ReturnsNull() {
        // Given
        when(jpaDao.findById(documentId)).thenReturn(Optional.empty());

        // When
        Document result = documentRepository.findById(documentId);

        // Then
        assertNull(result);
        verify(jpaDao).findById(documentId);
        verifyNoInteractions(mapper);
    }

    @Test
    void findByEntityIdAndEntityNameAndDocumentTypes_Success() {
        // Given
        List<DocumentType> documentTypes = List.of(DocumentType.DAMAGED);
        List<DocumentDo> documentDos = List.of(documentDo);
        List<Document> documents = List.of(document);

        when(jpaDao.findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes))
            .thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            entityId, "SHIPPING_ORDER", documentTypes);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(document, result.get(0));
        verify(jpaDao).findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes);
        verify(mapper).doToDomains(documentDos);
    }

    @Test
    void findByEntityIdAndEntityNameAndDocumentTypes_EmptyResult_ReturnsEmptyList() {
        // Given
        List<DocumentType> documentTypes = List.of(DocumentType.DAMAGED);
        List<DocumentDo> documentDos = List.of();
        List<Document> documents = List.of();

        when(jpaDao.findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes))
            .thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            entityId, "SHIPPING_ORDER", documentTypes);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(jpaDao).findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes);
        verify(mapper).doToDomains(documentDos);
    }

    @Test
    void findByEntityIdAndEntityNameAndDocumentTypes_MultipleDocumentTypes_Success() {
        // Given
        List<DocumentType> documentTypes = List.of(DocumentType.DAMAGED);
        List<DocumentDo> documentDos = List.of(documentDo);
        List<Document> documents = List.of(document);

        when(jpaDao.findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes))
            .thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            entityId, "SHIPPING_ORDER", documentTypes);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jpaDao).findByEntityIdAndEntityNameAndDocumentTypes(entityId, "SHIPPING_ORDER", documentTypes);
    }

    @Test
    void findByEntityIds_Success() {
        // Given
        List<UUID> entityIds = List.of(entityId);
        List<DocumentDo> documentDos = List.of(documentDo);
        List<Document> documents = List.of(document);

        when(jpaDao.findByEntityIdIn(entityIds)).thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIds(entityIds);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(document, result.get(0));
        verify(jpaDao).findByEntityIdIn(entityIds);
        verify(mapper).doToDomains(documentDos);
    }

    @Test
    void findByEntityIds_EmptyList_ReturnsEmptyList() {
        // Given
        List<UUID> entityIds = List.of();
        List<DocumentDo> documentDos = List.of();
        List<Document> documents = List.of();

        when(jpaDao.findByEntityIdIn(entityIds)).thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIds(entityIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(jpaDao).findByEntityIdIn(entityIds);
        verify(mapper).doToDomains(documentDos);
    }

    @Test
    void findByEntityIds_MultipleEntityIds_Success() {
        // Given
        UUID entityId2 = UUID.randomUUID();
        List<UUID> entityIds = List.of(entityId, entityId2);
        List<DocumentDo> documentDos = List.of(documentDo);
        List<Document> documents = List.of(document);

        when(jpaDao.findByEntityIdIn(entityIds)).thenReturn(documentDos);
        when(mapper.doToDomains(documentDos)).thenReturn(documents);

        // When
        List<Document> result = documentRepository.findByEntityIds(entityIds);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jpaDao).findByEntityIdIn(entityIds);
    }

    @Test
    void update_ReturnsNull() {
        // When
        Document result = documentRepository.update(document);

        // Then
        assertNull(result);
        verifyNoInteractions(jpaDao, mapper);
    }

    @Test
    void update_WithNullDocument_ReturnsNull() {
        // When
        Document result = documentRepository.update(null);

        // Then
        assertNull(result);
        verifyNoInteractions(jpaDao, mapper);
    }
} 