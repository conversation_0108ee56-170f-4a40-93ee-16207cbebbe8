package com.mercaso.wms.application.queryservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.application.dto.inventory.InventoryStockDto;
import com.mercaso.wms.application.mapper.inventorystock.InventoryStockDtoApplicationMapper;
import com.mercaso.wms.domain.inventorystock.InventoryStock;
import com.mercaso.wms.domain.inventorystock.InventoryStockRepository;
import com.mercaso.wms.domain.inventorystock.Item;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItemRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InventoryStockQueryServiceTest {

    @Mock
    private InventoryStockRepository inventoryStockRepository;

    @Mock
    private InventoryStockDtoApplicationMapper inventoryStockDtoApplicationMapper;

    @Mock
    private PickingTaskItemRepository pickingTaskItemRepository;

    @InjectMocks
    private InventoryStockQueryService inventoryStockQueryService;

    private UUID warehouseId;
    private List<String> skuNumbers;
    private List<InventoryStock> inventoryStocks;
    private WarehouseDto warehouseDto;

    @BeforeEach
    void setUp() {
        warehouseId = UUID.randomUUID();
        skuNumbers = List.of("SKU001", "SKU002");
        inventoryStocks = new ArrayList<>();
        warehouseDto = WarehouseDto.builder().id(warehouseId).build();
    }

    @Test
    void findByWarehouseIdAndSkuNumbers_WhenAllDataValid_ShouldReturnSortedResults() {
        // Given
        inventoryStocks.add(createInventoryStock("SKU001", ".COOLER-01"));
        inventoryStocks.add(createInventoryStock("SKU001", "PHOTO-STUDIO-01"));
        inventoryStocks.add(createInventoryStock("SKU001", "101-01-A-01"));
        inventoryStocks.add(createInventoryStock("SKU002", ".TOBACCO-01"));

        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any()))
            .thenReturn(inventoryStocks);

        mockDtoMapping(inventoryStocks.get(0), createInventoryStockDto(warehouseDto, "SKU001", ".COOLER-01"));
        mockDtoMapping(inventoryStocks.get(1), createInventoryStockDto(warehouseDto, "SKU001", "PHOTO-STUDIO-01"));
        mockDtoMapping(inventoryStocks.get(2), createInventoryStockDto(warehouseDto, "SKU001", "101-01-A-01"));
        mockDtoMapping(inventoryStocks.get(3), createInventoryStockDto(warehouseDto, "SKU002", ".TOBACCO-01"));

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndSkuNumbers(warehouseId,
            skuNumbers);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(3, sku001Results.size());
        assertEquals(".COOLER-01", sku001Results.get(0).getLocation().getName());
        assertEquals("PHOTO-STUDIO-01", sku001Results.get(1).getLocation().getName());
        assertEquals("101-01-A-01", sku001Results.get(2).getLocation().getName());

        List<InventoryStockDto> sku002Results = result.get("SKU002");
        assertNotNull(sku002Results);
        assertEquals(1, sku002Results.size());
        assertEquals(".TOBACCO-01", sku002Results.get(0).getLocation().getName());
    }

    @Test
    void findByWarehouseIdAndSkuNumbers_WhenNullLocation_ShouldPutNullLocationsAtEnd() {
        // Given
        inventoryStocks.add(createInventoryStock("SKU001", ".COOLER-01"));
        inventoryStocks.add(createInventoryStockWithNullLocation("SKU001")); // Null location
        inventoryStocks.add(createInventoryStock("SKU001", "101-01-A-01"));

        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any()))
            .thenReturn(inventoryStocks);

        mockDtoMapping(inventoryStocks.get(0), createInventoryStockDto(warehouseDto, "SKU001", ".COOLER-01"));
        mockDtoMapping(inventoryStocks.get(1), createInventoryStockDtoWithNullLocation(warehouseDto, "SKU001"));
        mockDtoMapping(inventoryStocks.get(2), createInventoryStockDto(warehouseDto, "SKU001", "101-01-A-01"));

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndSkuNumbers(warehouseId,
            skuNumbers);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(3, sku001Results.size());
        assertEquals(".COOLER-01", sku001Results.get(0).getLocation().getName());
        assertEquals("101-01-A-01", sku001Results.get(1).getLocation().getName());
        assertNull(sku001Results.get(2).getLocation()); // Null location should be last
    }

    @Test
    void findByWarehouseIdAndSkuNumbers_WhenNullLocationName_ShouldPutNullNamesAtEnd() {
        // Given
        inventoryStocks.add(createInventoryStock("SKU001", ".COOLER-01"));
        inventoryStocks.add(createInventoryStockWithNullLocationName("SKU001")); // Null location name
        inventoryStocks.add(createInventoryStock("SKU001", "101-01-A-01"));

        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any()))
            .thenReturn(inventoryStocks);

        mockDtoMapping(inventoryStocks.get(0), createInventoryStockDto(warehouseDto, "SKU001", ".COOLER-01"));
        mockDtoMapping(inventoryStocks.get(1), createInventoryStockDtoWithNullLocationName(warehouseDto, "SKU001"));
        mockDtoMapping(inventoryStocks.get(2), createInventoryStockDto(warehouseDto, "SKU001", "101-01-A-01"));

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndSkuNumbers(warehouseId,
            skuNumbers);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(3, sku001Results.size());
        assertEquals(".COOLER-01", sku001Results.get(0).getLocation().getName());
        assertEquals("101-01-A-01", sku001Results.get(1).getLocation().getName());
        assertNull(sku001Results.get(2).getLocation().getName()); // Null name should be last
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenOnlyBinLocation_ShouldReturnBinLocationType() {
        // Given
        UUID pickingTaskItemId1 = UUID.randomUUID();
        UUID pickingTaskItemId2 = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId1, pickingTaskItemId2);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId1, "SKU001", 10, 5), // unpicked: 5
            createPickingTaskItem(pickingTaskItemId2, "SKU001", 8, 3)   // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "BIN-01", LocationType.BIN, 30)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "BIN-01",
            LocationType.BIN,
            30);
        when(inventoryStockDtoApplicationMapper.domainToDto(any())).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(1, sku001Results.size());
        assertEquals("BIN", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(30)"), sku001Results.get(0).getDetail());
        assertEquals(30, sku001Results.get(0).getQoh());
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenOnlyStockLocation_ShouldReturnStockLocationType() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 5) // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "STOCK-01", LocationType.STOCK, 20)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "STOCK-01",
            LocationType.STOCK,
            20);
        when(inventoryStockDtoApplicationMapper.domainToDto(any())).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(1, sku001Results.size());
        assertEquals("STOCK", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[STOCK]STOCK-01(20)"), sku001Results.get(0).getDetail());
        assertEquals(20, sku001Results.get(0).getQoh());
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenBinAndStockWithSufficientBin_ShouldReturnBinLocationType() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 5) // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "BIN-01", LocationType.BIN, 10), // sufficient
            createInventoryStockWithLocationType("SKU001", "STOCK-01", LocationType.STOCK, 20)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto binDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "BIN-01",
            LocationType.BIN,
            10);
        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "STOCK-01",
            LocationType.STOCK,
            20);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(0))).thenReturn(binDto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(1))).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(1, sku001Results.size());
        assertEquals("BIN", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(10)"), sku001Results.get(0).getDetail());
        assertEquals(10, sku001Results.get(0).getQoh());
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenBinAndStockWithInsufficientBin_ShouldReturnBinPlusStockLocationType() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 5) // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "BIN-01", LocationType.BIN, 3), // insufficient
            createInventoryStockWithLocationType("SKU001", "STOCK-01", LocationType.STOCK, 20)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto binDto = createInventoryStockDtoWithLocationType(warehouseDto, "SKU001", "BIN-01", LocationType.BIN, 3);
        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "STOCK-01",
            LocationType.STOCK,
            20);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(0))).thenReturn(binDto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(1))).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(2, sku001Results.size());
        assertEquals("BIN+STOCK", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(3)", "[STOCK]STOCK-01(20)"), sku001Results.get(0).getDetail());
        assertEquals(23, sku001Results.get(0).getQoh());
        assertEquals("BIN+STOCK", sku001Results.get(1).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(3)", "[STOCK]STOCK-01(20)"), sku001Results.get(1).getDetail());
        assertEquals(23, sku001Results.get(1).getQoh());
    }


    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenMixedPositiveAndNegativeQuantities_ShouldFilterNegativeAndCalculateCorrectly() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 5) // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "BIN-01", LocationType.BIN, 8),
            // positive quantity
            createInventoryStockWithLocationType("SKU001", "BIN-02", LocationType.BIN, -3),
            // negative quantity - will be filtered out
            createInventoryStockWithLocationType("SKU001", "STOCK-01", LocationType.STOCK, 15)
            // positive quantity
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto bin1Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "BIN-01",
            LocationType.BIN,
            8);
        InventoryStockDto bin2Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "BIN-02",
            LocationType.BIN,
            -3);
        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "STOCK-01",
            LocationType.STOCK,
            15);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(0))).thenReturn(bin1Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(1))).thenReturn(bin2Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(2))).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(2, sku001Results.size());
        // All should have the same location type and detail - BIN because binQty (8) >= unpickedQty (5)
        assertEquals("BIN", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(8)"), sku001Results.get(0).getDetail());
        assertEquals(8, sku001Results.get(0).getQoh());
        assertEquals("BIN", sku001Results.get(1).getLocationType());
        assertEquals(List.of("[BIN]BIN-01(8)"), sku001Results.get(1).getDetail());
        assertEquals(8, sku001Results.get(1).getQoh());
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenMultipleLocations_ShouldShowAllLocationDetails() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 5) // unpicked: 5
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "bin-10-21-A-2", LocationType.BIN, 3),
            createInventoryStockWithLocationType("SKU001", "bin-10-21-B-1", LocationType.BIN, 2),
            createInventoryStockWithLocationType("SKU001", "stock-106-32-A-2", LocationType.STOCK, 30)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto bin1Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "bin-10-21-A-2",
            LocationType.BIN,
            3);
        InventoryStockDto bin2Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "bin-10-21-B-1",
            LocationType.BIN,
            2);
        InventoryStockDto stockDto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "stock-106-32-A-2",
            LocationType.STOCK,
            30);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(0))).thenReturn(bin1Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(1))).thenReturn(bin2Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(2))).thenReturn(stockDto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(2, sku001Results.size());
        // Should be BIN+STOCK because binQty (5) < unpickedQty (5) - actually binQty (5) == unpickedQty (5), so should be BIN
        assertEquals("BIN", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)", "[BIN]bin-10-21-B-1(2)"), sku001Results.get(0).getDetail());
        assertEquals(5, sku001Results.get(0).getQoh());
        assertEquals("BIN", sku001Results.get(1).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)", "[BIN]bin-10-21-B-1(2)"), sku001Results.get(1).getDetail());
        assertEquals(5, sku001Results.get(1).getQoh());
    }

    @Test
    void findByWarehouseIdAndPickingTaskItemIds_WhenMultipleBinsAndStocks_ShouldShowAllLocationDetails() {
        // Given
        UUID pickingTaskItemId = UUID.randomUUID();
        List<UUID> pickingTaskItemIds = List.of(pickingTaskItemId);

        List<PickingTaskItem> pickingTaskItems = List.of(
            createPickingTaskItem(pickingTaskItemId, "SKU001", 10, 2) // unpicked: 8 (10-2=8)
        );

        List<InventoryStock> inventoryStocks = List.of(
            createInventoryStockWithLocationType("SKU001", "bin-10-21-A-2", LocationType.BIN, 3),
            createInventoryStockWithLocationType("SKU001", "bin-10-21-B-1", LocationType.BIN, 2),
            createInventoryStockWithLocationType("SKU001", "stock-106-32-A-2", LocationType.STOCK, 20),
            createInventoryStockWithLocationType("SKU001", "stock-106-32-B-1", LocationType.STOCK, 15)
        );

        when(pickingTaskItemRepository.findByIds(pickingTaskItemIds)).thenReturn(pickingTaskItems);
        when(inventoryStockRepository.findByWarehouseIdAndSkuNumbers(any(), any())).thenReturn(inventoryStocks);

        InventoryStockDto bin1Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "bin-10-21-A-2",
            LocationType.BIN,
            3);
        InventoryStockDto bin2Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "bin-10-21-B-1",
            LocationType.BIN,
            2);
        InventoryStockDto stock1Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "stock-106-32-A-2",
            LocationType.STOCK,
            20);
        InventoryStockDto stock2Dto = createInventoryStockDtoWithLocationType(warehouseDto,
            "SKU001",
            "stock-106-32-B-1",
            LocationType.STOCK,
            15);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(0))).thenReturn(bin1Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(1))).thenReturn(bin2Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(2))).thenReturn(stock1Dto);
        when(inventoryStockDtoApplicationMapper.domainToDto(inventoryStocks.get(3))).thenReturn(stock2Dto);

        // When
        Map<String, List<InventoryStockDto>> result = inventoryStockQueryService.findByWarehouseIdAndPickingTaskItemIds(
            warehouseId,
            pickingTaskItemIds);

        // Then
        assertNotNull(result);
        List<InventoryStockDto> sku001Results = result.get("SKU001");
        assertNotNull(sku001Results);
        assertEquals(4, sku001Results.size());
        // Should be BIN+STOCK because binQty (5) < unpickedQty (8)
        assertEquals("BIN+STOCK", sku001Results.get(0).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)",
                "[BIN]bin-10-21-B-1(2)",
                "[STOCK]stock-106-32-A-2(20)",
                "[STOCK]stock-106-32-B-1(15)"),
            sku001Results.get(0).getDetail());
        assertEquals(40, sku001Results.get(0).getQoh());
        assertEquals("BIN+STOCK", sku001Results.get(1).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)",
                "[BIN]bin-10-21-B-1(2)",
                "[STOCK]stock-106-32-A-2(20)",
                "[STOCK]stock-106-32-B-1(15)"),
            sku001Results.get(1).getDetail());
        assertEquals(40, sku001Results.get(1).getQoh());
        assertEquals("BIN+STOCK", sku001Results.get(2).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)",
                "[BIN]bin-10-21-B-1(2)",
                "[STOCK]stock-106-32-A-2(20)",
                "[STOCK]stock-106-32-B-1(15)"),
            sku001Results.get(2).getDetail());
        assertEquals(40, sku001Results.get(2).getQoh());
        assertEquals("BIN+STOCK", sku001Results.get(3).getLocationType());
        assertEquals(List.of("[BIN]bin-10-21-A-2(3)",
                "[BIN]bin-10-21-B-1(2)",
                "[STOCK]stock-106-32-A-2(20)",
                "[STOCK]stock-106-32-B-1(15)"),
            sku001Results.get(3).getDetail());
        assertEquals(40, sku001Results.get(3).getQoh());
    }

    private void mockDtoMapping(InventoryStock domain, InventoryStockDto dto) {
        when(inventoryStockDtoApplicationMapper.domainToDto(domain)).thenReturn(dto);
    }

    private InventoryStock createInventoryStock(String skuNumber, String locationName) {
        return InventoryStock.builder()
            .id(UUID.randomUUID())
            .item(Item.builder().skuNumber(skuNumber).build())
            .location(Location.builder().name(locationName).build())
            .qty(BigDecimal.ONE)
            .build();
    }

    private InventoryStock createInventoryStockWithNullLocation(String skuNumber) {
        return InventoryStock.builder()
            .id(UUID.randomUUID())
            .item(Item.builder().skuNumber(skuNumber).build())
            .location(null)
            .qty(BigDecimal.ONE)
            .build();
    }

    private InventoryStock createInventoryStockWithNullLocationName(String skuNumber) {
        return InventoryStock.builder()
            .id(UUID.randomUUID())
            .item(Item.builder().skuNumber(skuNumber).build())
            .location(Location.builder().name(null).build())
            .qty(BigDecimal.ONE)
            .build();
    }

    private InventoryStockDto createInventoryStockDto(WarehouseDto warehouseDto, String skuNumber, String locationName) {
        return InventoryStockDto.builder()
            .id(UUID.randomUUID())
            .warehouse(warehouseDto)
            .skuNumber(skuNumber)
            .location(LocationDto.builder()
                .id(UUID.randomUUID())
                .warehouse(warehouseDto)
                .name(locationName)
                .build())
            .build();
    }

    private InventoryStockDto createInventoryStockDtoWithNullLocation(WarehouseDto warehouseDto, String skuNumber) {
        return InventoryStockDto.builder()
            .id(UUID.randomUUID())
            .warehouse(warehouseDto)
            .skuNumber(skuNumber)
            .location(null)
            .build();
    }

    private InventoryStockDto createInventoryStockDtoWithNullLocationName(WarehouseDto warehouseDto, String skuNumber) {
        return InventoryStockDto.builder()
            .id(UUID.randomUUID())
            .warehouse(warehouseDto)
            .skuNumber(skuNumber)
            .location(LocationDto.builder()
                .id(UUID.randomUUID())
                .warehouse(warehouseDto)
                .name(null)
                .build())
            .build();
    }

    private PickingTaskItem createPickingTaskItem(UUID id, String skuNumber, int expectQty, int pickedQty) {
        return PickingTaskItem.builder()
            .id(id)
            .skuNumber(skuNumber)
            .expectQty(expectQty)
            .pickedQty(pickedQty)
            .build();
    }

    private InventoryStock createInventoryStockWithLocationType(String skuNumber,
        String locationName,
        LocationType locationType,
        int qty) {
        return InventoryStock.builder()
            .id(UUID.randomUUID())
            .item(Item.builder().skuNumber(skuNumber).build())
            .location(Location.builder()
                .name(locationName)
                .type(locationType)
                .build())
            .qty(BigDecimal.valueOf(qty))
            .build();
    }

    private InventoryStockDto createInventoryStockDtoWithLocationType(WarehouseDto warehouseDto,
        String skuNumber,
        String locationName,
        LocationType locationType,
        int qty) {
        return InventoryStockDto.builder()
            .id(UUID.randomUUID())
            .warehouse(warehouseDto)
            .skuNumber(skuNumber)
            .qty(BigDecimal.valueOf(qty))
            .location(LocationDto.builder()
                .id(UUID.randomUUID())
                .warehouse(warehouseDto)
                .name(locationName)
                .type(locationType)
                .build())
            .build();
    }
} 