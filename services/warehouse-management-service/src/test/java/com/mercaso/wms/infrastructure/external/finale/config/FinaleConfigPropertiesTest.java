package com.mercaso.wms.infrastructure.external.finale.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class FinaleConfigPropertiesTest {

    private FinaleConfigProperties finaleConfigProperties;

    @BeforeEach
    void setUp() {
        finaleConfigProperties = new FinaleConfigProperties();
        finaleConfigProperties.setRetryEnabled(true);
        finaleConfigProperties.setMaxRetryAttempts(3);
        finaleConfigProperties.setRetryInterval(Duration.ofSeconds(1));
        finaleConfigProperties.setRetryBackoffMultiplier(2.0);
        finaleConfigProperties.setMaxRetryInterval(Duration.ofSeconds(30));
    }

    @Test
    void getRetrySettings_withDefaultConfig_shouldReturnDefaultValues() {
        // Act
        FinaleConfigProperties.RetrySettings settings = finaleConfigProperties.getRetrySettings("unknown.endpoint");

        // Assert
        assertTrue(settings.enabled());
        assertEquals(3, settings.maxRetryAttempts());
        assertEquals(Duration.ofSeconds(1), settings.retryInterval());
        assertEquals(2.0, settings.retryBackoffMultiplier());
        assertEquals(Duration.ofSeconds(30), settings.maxRetryInterval());
    }

    @Test
    void getRetrySettings_withEndpointOverride_shouldReturnOverriddenValues() {
        // Arrange
        FinaleConfigProperties.RetryConfig override = new FinaleConfigProperties.RetryConfig();
        override.setMaxRetryAttempts(5);
        override.setRetryInterval(Duration.ofSeconds(2));
        override.setRetryBackoffMultiplier(1.5);

        finaleConfigProperties.setEndpointRetries(Map.of("shipment.fill", override));

        // Act
        FinaleConfigProperties.RetrySettings settings = finaleConfigProperties.getRetrySettings("shipment.fill");

        // Assert
        assertTrue(settings.enabled());
        assertEquals(5, settings.maxRetryAttempts());
        assertEquals(Duration.ofSeconds(2), settings.retryInterval());
        assertEquals(1.5, settings.retryBackoffMultiplier());
        assertEquals(Duration.ofSeconds(30), settings.maxRetryInterval()); // fallback to global
    }

    @Test
    void getRetrySettings_withPartialEndpointOverride_shouldMixOverrideAndDefault() {
        // Arrange
        FinaleConfigProperties.RetryConfig partialOverride = new FinaleConfigProperties.RetryConfig();
        partialOverride.setMaxRetryAttempts(2);
        // Other values not set, should fallback to global defaults

        finaleConfigProperties.setEndpointRetries(Map.of("shipment.pack", partialOverride));

        // Act
        FinaleConfigProperties.RetrySettings settings = finaleConfigProperties.getRetrySettings("shipment.pack");

        // Assert
        assertTrue(settings.enabled());
        assertEquals(2, settings.maxRetryAttempts()); // overridden
        assertEquals(Duration.ofSeconds(1), settings.retryInterval()); // fallback to global
        assertEquals(2.0, settings.retryBackoffMultiplier()); // fallback to global
        assertEquals(Duration.ofSeconds(30), settings.maxRetryInterval()); // fallback to global
    }

    @Test
    void getRetrySettings_withDisabledEndpoint_shouldReturnDisabledSettings() {
        // Arrange
        FinaleConfigProperties.RetryConfig disabledConfig = new FinaleConfigProperties.RetryConfig();
        disabledConfig.setEnabled(false);
        disabledConfig.setMaxRetryAttempts(10); // should still be returned even if disabled

        finaleConfigProperties.setEndpointRetries(Map.of("inventory.variance", disabledConfig));

        // Act
        FinaleConfigProperties.RetrySettings settings = finaleConfigProperties.getRetrySettings("inventory.variance");

        // Assert
        assertFalse(settings.enabled()); // overridden to false
        assertEquals(10, settings.maxRetryAttempts()); // overridden value
        assertEquals(Duration.ofSeconds(1), settings.retryInterval()); // fallback to global
        assertEquals(2.0, settings.retryBackoffMultiplier()); // fallback to global
        assertEquals(Duration.ofSeconds(30), settings.maxRetryInterval()); // fallback to global
    }

    @Test
    void getRetrySettings_withGlobalRetryDisabled_shouldStillAllowEndpointOverride() {
        // Arrange
        finaleConfigProperties.setRetryEnabled(false); // global disabled

        FinaleConfigProperties.RetryConfig enabledConfig = new FinaleConfigProperties.RetryConfig();
        enabledConfig.setEnabled(true); // endpoint override to enable
        enabledConfig.setMaxRetryAttempts(1);

        finaleConfigProperties.setEndpointRetries(Map.of("special.endpoint", enabledConfig));

        // Act
        FinaleConfigProperties.RetrySettings settings1 = finaleConfigProperties.getRetrySettings("normal.endpoint");
        FinaleConfigProperties.RetrySettings settings2 = finaleConfigProperties.getRetrySettings("special.endpoint");

        // Assert
        assertFalse(settings1.enabled()); // global setting applies
        assertTrue(settings2.enabled()); // endpoint override applies
        assertEquals(1, settings2.maxRetryAttempts());
    }

    @Test
    void getRetrySettings_withCompleteEndpointOverride_shouldUseAllOverriddenValues() {
        // Arrange
        FinaleConfigProperties.RetryConfig completeOverride = new FinaleConfigProperties.RetryConfig();
        completeOverride.setEnabled(true);
        completeOverride.setMaxRetryAttempts(7);
        completeOverride.setRetryInterval(Duration.ofMillis(500));
        completeOverride.setRetryBackoffMultiplier(3.0);
        completeOverride.setMaxRetryInterval(Duration.ofMinutes(2));

        finaleConfigProperties.setEndpointRetries(Map.of("complete.override", completeOverride));

        // Act
        FinaleConfigProperties.RetrySettings settings = finaleConfigProperties.getRetrySettings("complete.override");

        // Assert
        assertTrue(settings.enabled());
        assertEquals(7, settings.maxRetryAttempts());
        assertEquals(Duration.ofMillis(500), settings.retryInterval());
        assertEquals(3.0, settings.retryBackoffMultiplier());
        assertEquals(Duration.ofMinutes(2), settings.maxRetryInterval());
    }

    @Test
    void retryConfig_defaultValues_shouldBeCorrect() {
        // Arrange & Act
        FinaleConfigProperties.RetryConfig retryConfig = new FinaleConfigProperties.RetryConfig();

        // Assert
        assertTrue(retryConfig.isEnabled()); // default enabled
        assertNull(retryConfig.getMaxRetryAttempts()); // null means fallback to global
        assertNull(retryConfig.getRetryInterval()); // null means fallback to global
        assertNull(retryConfig.getRetryBackoffMultiplier()); // null means fallback to global
        assertNull(retryConfig.getMaxRetryInterval()); // null means fallback to global
    }

    @Test
    void retrySettings_recordProperties_shouldBeImmutable() {
        // Arrange
        FinaleConfigProperties.RetrySettings settings = new FinaleConfigProperties.RetrySettings(
            true,
            2,
            Duration.ofSeconds(1),
            1.5,
            Duration.ofSeconds(10)
        );

        // Assert
        assertTrue(settings.enabled());
        assertEquals(2, settings.maxRetryAttempts());
        assertEquals(Duration.ofSeconds(1), settings.retryInterval());
        assertEquals(1.5, settings.retryBackoffMultiplier());
        assertEquals(Duration.ofSeconds(10), settings.maxRetryInterval());
    }
}
