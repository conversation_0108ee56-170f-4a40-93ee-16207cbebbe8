package com.mercaso.wms.application.service.pickingtask;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class CreatePhotoStudioPickingTaskServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);
    private final LocationRepository locationRepository = mock(LocationRepository.class);
    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = mock(PickingTaskAssignmentConfig.class);
    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);
    private final LocationCache locationCache = mock(LocationCache.class);

    private final CreatePhotoStudioPickingTaskService createPhotoStudioPickingTaskService = new CreatePhotoStudioPickingTaskService(
        batchItemRepository,
        batchItemQueryService,
        pickingTaskRepository,
        locationRepository,
        pickingTaskAssignmentConfig,
        pickingTaskApplicationService,
        locationCache
    );

    @Test
    void createPhotoStudioPickingTask_WhenNoBatchItemsFound_ShouldNotCreateTask() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        when(batchItemQueryService.findByBatchIdAndSourceAndLocationName(batchId, SourceEnum.MDC.name(), "PHOTO-STUDIO"))
            .thenReturn(new ArrayList<>());

        // Act
        createPhotoStudioPickingTaskService.createPickingTask(batchId);

        // Assert
        verify(pickingTaskRepository, never()).save(any());
    }

    @Test
    void createPhotoStudioPickingTask_WhenBatchItemsFound_ShouldCreateTask() {
        // Arrange
        UUID batchId = UUID.randomUUID();
        List<BatchItem> photoStudioBatchItems = new ArrayList<>();
        photoStudioBatchItems.add(BatchItem.builder().id(UUID.randomUUID()).source(SourceEnum.MDC.name()).build());
        photoStudioBatchItems.add(BatchItem.builder().id(UUID.randomUUID()).source(SourceEnum.MDC.name()).build());

        when(batchItemQueryService.findByBatchIdAndSourceAndLocationName(batchId, SourceEnum.MDC.name(), "PHOTO-STUDIO"))
            .thenReturn(photoStudioBatchItems);
        when(pickingTaskRepository.save(any(PickingTask.class)))
            .thenReturn(PickingTask.builder().id(UUID.randomUUID()).source(SourceEnum.MDC).build());

        // Act
        createPhotoStudioPickingTaskService.createPickingTask(batchId);

        // Assert
        verify(pickingTaskRepository).save(any(PickingTask.class));
    }


}