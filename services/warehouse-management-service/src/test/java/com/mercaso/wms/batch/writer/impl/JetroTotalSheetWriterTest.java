package com.mercaso.wms.batch.writer.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.alibaba.excel.EasyExcel;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.LinkedHashMap;
import java.util.List;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.Test;

class JetroTotalSheetWriterTest extends Writer {

    private final JetroTotalSheetWriter jetroTotalSheetWriter = new JetroTotalSheetWriter();

    @Test
    void when_write_jetro_total_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> jetro = Lists.newArrayList();
        ExcelBatchDto jetroExcelBatchDto = new ExcelBatchDto();
        jetroExcelBatchDto.setItemNumber("jetro");
        jetroExcelBatchDto.setFrom("Location2");
        jetroExcelBatchDto.setQuantity(1);
        jetroExcelBatchDto.setSource(SourceEnum.JETRO.name());
        jetroExcelBatchDto.setFrom("121");
        jetroExcelBatchDto.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto jetroExcelBatchDto1 = new ExcelBatchDto();
        jetroExcelBatchDto1.setItemNumber("jetro");
        jetroExcelBatchDto1.setFrom("Location2");
        jetroExcelBatchDto1.setQuantity(1);
        jetroExcelBatchDto1.setSource(SourceEnum.JETRO.name());
        jetroExcelBatchDto1.setFrom("10B");
        jetroExcelBatchDto1.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);

        ExcelBatchDto jetroExcelBatchDto2 = new ExcelBatchDto();
        jetroExcelBatchDto2.setItemNumber("jetro");
        jetroExcelBatchDto2.setFrom("Location2");
        jetroExcelBatchDto2.setQuantity(1);
        jetroExcelBatchDto2.setSource(SourceEnum.JETRO.name());
        jetroExcelBatchDto2.setFrom("10A");
        jetroExcelBatchDto2.setDepartment(BatchConstants.TOBACCO_DEPARTMENT);
        jetro.add(jetroExcelBatchDto);
        jetro.add(jetroExcelBatchDto1);
        jetro.add(jetroExcelBatchDto2);

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.JETRO.name(), jetro));

        writeBatchTemplate(condition, jetroTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> jetroTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.JETRO_TOTALS.getValue()).doReadSync();

        assertEquals(4, jetroTotals.size());
        assertEquals("3", jetroTotals.get(3).get(6));
    }

    @Test
    void when_write_empty_seven_start_then_success() {
        WriteTemplateCondition condition = new WriteTemplateCondition();
        List<ExcelBatchDto> batchDtos = Lists.newArrayList();

        condition.setSourceAndListMap(Maps.newHashMap(SourceEnum.JETRO.name(), batchDtos));

        writeBatchTemplate(condition, jetroTotalSheetWriter);

        List<LinkedHashMap<Integer, String>> jetroTotals =
            EasyExcel.read(fileName).sheet(GeneratedDocNameEnum.JETRO_TOTALS.getValue()).doReadSync();

        assertEquals(3, jetroTotals.size());
    }

}