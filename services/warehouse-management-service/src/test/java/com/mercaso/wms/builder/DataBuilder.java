package com.mercaso.wms.builder;

import com.mercaso.data.client.dto.ShopifyOrderDto;
import com.mercaso.data.client.dto.ShopifyOrderLineItemDto;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.dto.StockDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockDto;
import com.mercaso.wms.infrastructure.external.finale.dto.FinaleAvailableStockItemsOnHandDto;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

public class DataBuilder {

    public static ExcelBatchDto buildBatchDto(String itemNumber, String orderNumber, int quantity) {
        ExcelBatchDto excelBatchDto = new ExcelBatchDto();
        excelBatchDto.setItemNumber(itemNumber);
        excelBatchDto.setOrderNumber(orderNumber);
        excelBatchDto.setQuantity(quantity);
        excelBatchDto.setFrom(SourceEnum.MFC.name());
        return excelBatchDto;
    }

    public static FinaleAvailableStockDto buildFinaleStock(String itemNumber, Integer quantity, String locationName) {
        FinaleAvailableStockDto finaleAvailableStockDto = new FinaleAvailableStockDto();
        finaleAvailableStockDto.setName(itemNumber);
        finaleAvailableStockDto.setSku(itemNumber);
        finaleAvailableStockDto.setMfcQoh(quantity);

        FinaleAvailableStockItemsOnHandDto finaleAvailableStockItemsOnHandDto = new FinaleAvailableStockItemsOnHandDto();
        finaleAvailableStockItemsOnHandDto.setQuantityOnHand(quantity.longValue());
        FinaleAvailableStockItemsOnHandDto.Location location = new FinaleAvailableStockItemsOnHandDto.Location();
        location.setName(locationName);
        finaleAvailableStockItemsOnHandDto.setSubLocation(location);
        finaleAvailableStockDto.setStockItemsOnHand(List.of(finaleAvailableStockItemsOnHandDto));

        return finaleAvailableStockDto;
    }

    public static FinaleAvailableStockItemsOnHandDto buildFinaleStockItemsOnHandDto(Integer quantity, String locationName) {
        FinaleAvailableStockItemsOnHandDto finaleAvailableStockItemsOnHandDto = new FinaleAvailableStockItemsOnHandDto();
        finaleAvailableStockItemsOnHandDto.setQuantityOnHand(quantity.longValue());
        FinaleAvailableStockItemsOnHandDto.Location location = new FinaleAvailableStockItemsOnHandDto.Location();
        location.setName(locationName);
        finaleAvailableStockItemsOnHandDto.setSubLocation(location);
        return finaleAvailableStockItemsOnHandDto;
    }

    public static Location buildLocation(String locationName, LocationType locationType) {
        return Location.builder()
            .id(UUID.randomUUID())
            .name(locationName)
            .type(locationType)
            .build();
    }

    public static LookupDto buildLookupData(String itemNumber, String orderNumber, SourceEnum sourceEnum) {
        return LookupDto.builder()
            .from("from")
            .aisle("aisle")
            .itemNumber(itemNumber)
            .orderNumber(orderNumber)
            .department("department")
            .category("category")
            .subCategory("subCategory")
            .clazz("clazz")
            .sourceEnum(sourceEnum)
            .build();
    }

    public static PopulateCondition buildPopulateCondition(int dataSize) {
        return PopulateCondition.builder()
            .excelBatchDtoList(buildBatchDtoList(dataSize))
            .lookUpData(buildLookUpDataList(SourceEnum.MDC, dataSize))
            .mfcStocks(buildStockDtoList(5))
            .locations(buildLocations(dataSize, LocationType.BIN))
            .build();
    }

    public static List<ExcelBatchDto> buildBatchDtoList(int size) {
        List<ExcelBatchDto> excelBatchDtoList = new ArrayList<>();

        for (int i = 0; i < size; i++) {
            String itemNumber = "item" + i;
            String orderNumber = "order" + i;
            int quantity = i + 1;
            excelBatchDtoList.add(buildBatchDto(itemNumber, orderNumber, quantity));
        }
        return excelBatchDtoList;
    }

    public static List<FinaleAvailableStockDto> buildFinaleStocks(int size) {
        List<FinaleAvailableStockDto> finaleAvailableStockDtoList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String itemNumber = "item" + i;
            int quantity = i + 1;
            String locationName = "00-01-A-" + i;
            FinaleAvailableStockDto finaleAvailableStockDto = buildFinaleStock(itemNumber, quantity, locationName);
            finaleAvailableStockDtoList.add(finaleAvailableStockDto);
        }
        return finaleAvailableStockDtoList;
    }

    public static List<Location> buildLocations(int size, LocationType locationType) {
        List<Location> locations = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String locationName = "00-01-A-" + i;
            locations.add(buildLocation(locationName, locationType));
        }
        return locations;

    }

    public static Map<SourceEnum, List<LookupDto>> buildLookUpDataList(SourceEnum sourceEnum, int size) {
        Map<SourceEnum, List<LookupDto>> lookUpData = new EnumMap<>(SourceEnum.class);
        List<LookupDto> lookupDtoList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String itemNumber = "item" + i;
            String orderNumber = "order" + i;
            lookupDtoList.add(buildLookupData(itemNumber, orderNumber, sourceEnum));
        }
        lookUpData.put(sourceEnum, lookupDtoList);
        return lookUpData;
    }

    public static List<StockDto> buildStockDtoList(int size) {
        List<StockDto> stockDtoList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String itemNumber = "item" + i;
            String locationName = "00-01-A-" + i;
            StockDto stockDto = buildStockDto(itemNumber, locationName, i + 1);
            stockDtoList.add(stockDto);
        }
        return stockDtoList;
    }

    public static StockDto buildStockDto(String itemNumber, String locationName, int quantity) {
        return StockDto.builder()
            .name(locationName)
            .sku(itemNumber)
            .type(LocationType.BIN)
            .totalQty(quantity)
            .subLocation(locationName)
            .source(SourceEnum.MFC)
            .build();
    }

    public static StockDto buildStockDto(String itemNumber, String locationName, int quantity, LocationType locationType) {
        return StockDto.builder()
            .locationId(UUID.randomUUID())
            .name(locationName)
            .sku(itemNumber)
            .type(locationType)
            .totalQty(quantity)
            .remainingQty(quantity)
            .subLocation(locationName)
            .source(SourceEnum.MFC)
            .build();
    }

    public static List<ShopifyOrderDto> buildShopifyOrderDtoList(int size) {
        List<ShopifyOrderDto> shopifyOrderDtoList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String orderNumber = "order" + i;
            ShopifyOrderDto shopifyOrderDto = new ShopifyOrderDto();
            ShopifyOrderLineItemDto shopifyOrderLineItemDto = new ShopifyOrderLineItemDto();
            shopifyOrderLineItemDto.setQuantity(i + 1);
            shopifyOrderLineItemDto.setSku("item" + i);
            shopifyOrderDto.setName(orderNumber);
            shopifyOrderDto.setLineItems(List.of(shopifyOrderLineItemDto));
            shopifyOrderDtoList.add(shopifyOrderDto);
        }
        return shopifyOrderDtoList;
    }

    public static Map<String, Integer> getStockMap(List<StockDto> stockList) {
        var stockMap = new HashMap<String, Integer>();
        if (CollectionUtils.isEmpty(stockList)) {
            return stockMap;
        }
        stockMap.putAll(stockList.stream()
            .collect(Collectors.groupingBy(StockDto::getSku, Collectors.summingInt(StockDto::getTotalQty))));
        return stockMap;
    }

}
