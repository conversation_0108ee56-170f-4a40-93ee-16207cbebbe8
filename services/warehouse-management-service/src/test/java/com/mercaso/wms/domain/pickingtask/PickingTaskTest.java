package com.mercaso.wms.domain.pickingtask;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskTransitionEvents;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.wms.infrastructure.utils.SpringContextUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Mockito;

class PickingTaskTest {

    @SuppressWarnings("unchecked")
    private final StateMachineProcessor<PickingTask, PickingTaskStatus, PickingTaskTransitionEvents> stateMachineProcessor
        = mock(StateMachineProcessor.class);

    @Test
    void when_create_picking_task_then_success() {
        PickingTask pickingTask = PickingTask.builder().build();
        pickingTask.setBatchId(UUID.randomUUID());

        pickingTask.createTask(pickingTask.getBatchId(),
            SourceEnum.MFC,
            buildBatchItems(pickingTask.getBatchId(), 5),
            new HashMap<>());

        assertNotNull(pickingTask);
        assertEquals(5, pickingTask.getPickingTaskItems().size());
    }

    @Test
    void when_update_picking_task_expect_qty_equals_picked_qty_then_error_info_is_null() {
        UUID batchId = UUID.randomUUID();
        List<PickingTask> pickingTasks = buildPickingTask(batchId, 1);
        PickingTask pickingTask = pickingTasks.getFirst();
        PickingTaskItem pickingTaskItem = PickingTaskItem.builder()
            .id(UUID.randomUUID())
            .batchItemId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .skuNumber(RandomStringUtils.randomAlphabetic(10))
            .title(RandomStringUtils.randomAlphabetic(10))
            .locationName(RandomStringUtils.randomAlphabetic(10))
            .prep(RandomStringUtils.randomAlphabetic(10))
            .expectQty(1)
            .pickedQty(1)
            .breakdownName(RandomStringUtils.randomAlphabetic(10))
            .build();
        pickingTask.setPickingTaskItems(List.of(pickingTaskItem));

        UpdatePickingTaskCommand command = new UpdatePickingTaskCommand();
        UpdatePickingTaskCommand.UpdatePickingTaskItemDto updatePickingTaskItemDto = new UpdatePickingTaskCommand.UpdatePickingTaskItemDto();
        updatePickingTaskItemDto.setId(pickingTask.getPickingTaskItems().getFirst().getId());
        updatePickingTaskItemDto.setPickedQty(1);
        updatePickingTaskItemDto.setErrorInfo("test");
        command.setUpdatePickingTaskItemDtos(List.of(updatePickingTaskItemDto));
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            pickingTask.update(command);
            assertNotNull(pickingTask);
            assertEquals(1, pickingTask.getPickingTaskItems().size());
            assertEquals(1, pickingTask.getPickingTaskItems().getFirst().getPickedQty());
            assertNull(pickingTask.getPickingTaskItems().getFirst().getErrorInfo());
        }

    }

    @Test
    void deleteItemsByIds_removesItemsSuccessfully_whenIdsAreValid() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);

            List<PickingTaskItem> items = new ArrayList<>();
            items.add(PickingTaskItem.builder().id(UUID.randomUUID()).build());
            items.add(PickingTaskItem.builder().id(UUID.randomUUID()).build());

            PickingTask pickingTask = PickingTask.builder().pickingTaskItems(items).build();

            UUID idToRemove = pickingTask.getPickingTaskItems().getFirst().getId();
            pickingTask.deleteItemsByIds(List.of(idToRemove));

            assertEquals(1, pickingTask.getPickingTaskItems().size());
            assertTrue(pickingTask.getPickingTaskItems().stream().noneMatch(item -> item.getId().equals(idToRemove)));
        }
    }

    @Test
    void deleteItemsByIds_cancelsTask_whenAllItemsAreRemoved() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);

            List<PickingTaskItem> items = new ArrayList<>();
            items.add(PickingTaskItem.builder().id(UUID.randomUUID()).build());

            PickingTask pickingTask = spy(PickingTask.builder().pickingTaskItems(items).build());

            UUID idToRemove = pickingTask.getPickingTaskItems().getFirst().getId();
            pickingTask.deleteItemsByIds(List.of(idToRemove));

            assertTrue(CollectionUtils.isEmpty(pickingTask.getPickingTaskItems()));
            verify(pickingTask).cancelTask();
        }
    }

    @Test
    void deleteItemsByIds_doesNothing_whenIdsListIsEmpty() {
        PickingTask pickingTask = PickingTask.builder().pickingTaskItems(List.of(
            PickingTaskItem.builder().id(UUID.randomUUID()).build()
        )).build();

        pickingTask.deleteItemsByIds(List.of());

        assertEquals(1, pickingTask.getPickingTaskItems().size());
    }

    @Test
    void calculateTotals_shouldCalculateCorrectly_whenItemsExist() {
        // given
        List<PickingTaskItem> items = List.of(
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(10)
                .pickedQty(5)
                .build(),
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(15)
                .pickedQty(12)
                .build(),
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(8)
                .pickedQty(0)
                .build()
        );

        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(items)
            .build();

        // when
        pickingTask.calculateTotals();

        // then
        assertEquals(33, pickingTask.getTotalQty()); // 10 + 15 + 8
        assertEquals(3, pickingTask.getTotalLines()); // 3 items
        assertEquals(17, pickingTask.getTotalPickedQty()); // 5 + 12 + 0
    }

    @Test
    void calculateTotals_shouldSetZero_whenItemsListIsEmpty() {
        // given
        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(new ArrayList<>())
            .build();

        // when
        pickingTask.calculateTotals();

        // then
        assertEquals(0, pickingTask.getTotalQty());
        assertEquals(0, pickingTask.getTotalLines());
        assertEquals(0, pickingTask.getTotalPickedQty());
    }

    @Test
    void calculateTotals_shouldSetZero_whenItemsListIsNull() {
        // given
        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(null)
            .build();

        // when
        pickingTask.calculateTotals();

        // then
        assertEquals(0, pickingTask.getTotalQty());
        assertEquals(0, pickingTask.getTotalLines());
        assertEquals(0, pickingTask.getTotalPickedQty());
    }

    @Test
    void calculateTotals_shouldHandleNullQuantities() {
        // given
        List<PickingTaskItem> items = List.of(
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(null)
                .pickedQty(null)
                .build(),
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(10)
                .pickedQty(5)
                .build()
        );

        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(items)
            .build();

        // when
        pickingTask.calculateTotals();

        // then
        assertEquals(10, pickingTask.getTotalQty()); // 0 + 10
        assertEquals(2, pickingTask.getTotalLines()); // 2 items
        assertEquals(5, pickingTask.getTotalPickedQty()); // 0 + 5
    }

    @Test
    void createTask_shouldCalculateTotalsAutomatically() {
        // given
        PickingTask pickingTask = PickingTask.builder().build();
        pickingTask.setBatchId(UUID.randomUUID());

        // when
        pickingTask.createTask(pickingTask.getBatchId(),
            SourceEnum.MFC,
            buildBatchItems(pickingTask.getBatchId(), 3),
            new HashMap<>());

        // then
        assertNotNull(pickingTask);
        assertEquals(3, pickingTask.getPickingTaskItems().size());
        assertEquals(3, pickingTask.getTotalLines());
        // totalQty and totalPickedQty will depend on the mock data from buildBatchItems
        assertNotNull(pickingTask.getTotalQty());
        assertNotNull(pickingTask.getTotalPickedQty());
    }

    @Test
    void updatePickingTaskItems_shouldRecalculateTotals() {
        // given
        List<PickingTaskItem> originalItems = List.of(
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(10)
                .pickedQty(5)
                .build()
        );

        List<PickingTaskItem> updatedItems = List.of(
            PickingTaskItem.builder()
                .id(originalItems.getFirst().getId())
                .expectQty(15)
                .pickedQty(12)
                .build()
        );

        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(originalItems)
            .build();

        // when
        pickingTask.updatePickingTaskItems(updatedItems);

        // then
        assertEquals(15, pickingTask.getTotalQty());
        assertEquals(1, pickingTask.getTotalLines());
        assertEquals(12, pickingTask.getTotalPickedQty());
    }

    @Test
    void removePickingTaskItems_shouldRecalculateTotals() {
        // given
        List<PickingTaskItem> items = List.of(
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(10)
                .pickedQty(5)
                .build(),
            PickingTaskItem.builder()
                .id(UUID.randomUUID())
                .expectQty(15)
                .pickedQty(12)
                .build()
        );

        PickingTask pickingTask = PickingTask.builder()
            .pickingTaskItems(new ArrayList<>(items))
            .build();

        // when
        pickingTask.removePickingTaskItems(List.of(items.getFirst()));

        // then
        assertEquals(15, pickingTask.getTotalQty());
        assertEquals(1, pickingTask.getTotalLines());
        assertEquals(12, pickingTask.getTotalPickedQty());
    }

}