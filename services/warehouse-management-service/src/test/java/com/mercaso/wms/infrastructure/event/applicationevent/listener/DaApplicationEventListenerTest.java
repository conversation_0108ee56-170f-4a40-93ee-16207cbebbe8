package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import static com.mercaso.wms.infrastructure.contant.FeatureFlagKeys.CLEAN_SHIP_SB_V1;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.service.InventoryStockApplicationService;
import com.mercaso.wms.application.service.ShippingOrderApplicationService;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredApplicationEvent;
import com.mercaso.wms.delivery.application.event.DeliveryOrderDeliveredPayloadDto;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedApplicationEvent;
import com.mercaso.wms.delivery.application.event.deliverytask.DeliveryTaskReassignedPayloadDto;
import com.mercaso.wms.delivery.application.event.dto.DeliveryTaskReassignedEventDto;
import com.mercaso.wms.delivery.config.DemoDataRefreshConfig;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.delivery.DeliveryAdaptor;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlertService;
import java.lang.reflect.Constructor;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DaApplicationEventListenerTest {

    @Mock
    private InventoryStockApplicationService inventoryStockApplicationService;

    @Mock
    private ShippingOrderApplicationService shippingOrderApplicationService;

    @Mock
    private WmsExceptionAlertService wmsExceptionAlertService;

    @Mock
    private FeatureFlagsManager featureFlagsManager;

    @Mock
    private DeliveryAdaptor deliveryAdaptor;
    @Mock
    private DemoDataRefreshConfig demoDataRefreshConfig;

    @InjectMocks
    private DaApplicationEventListener daApplicationEventListener;

    @Test
    void when_handleDeliveredEvent_with_validAppEvent_then_processSuccessfully() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);
        doNothing().when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));
        doNothing().when(inventoryStockApplicationService).cleanGhostInventory(any(DeliveryOrderDto.class));

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, times(1)).updateDeliveryQty(any(DeliveryOrderDto.class));
        verify(inventoryStockApplicationService, times(1)).cleanGhostInventory(any(DeliveryOrderDto.class));
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_shippingOrderUpdateFailure_then_throwException() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);
        RuntimeException updateException = new RuntimeException("Failed to update shipping order");
        doThrow(updateException).when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));

        // When & Then
        assertThatThrownBy(() -> daApplicationEventListener.handleDeliveredAppEvent(event))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessage("Failed to update delivery quantity")
            .hasCause(updateException);

        verify(shippingOrderApplicationService, times(1)).updateDeliveryQty(any(DeliveryOrderDto.class));
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any(DeliveryOrderDto.class));
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_ghostInventoryCleanupFailure_then_sendAlert() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);
        RuntimeException cleanupException = new RuntimeException("Database connection timeout");

        doNothing().when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));
        doThrow(cleanupException).when(inventoryStockApplicationService).cleanGhostInventory(any(DeliveryOrderDto.class));
        doNothing().when(wmsExceptionAlertService).alertGhostInventoryCleanupFailure(any(), any(), any(), any());

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, times(1)).updateDeliveryQty(any(DeliveryOrderDto.class));
        verify(inventoryStockApplicationService, times(1)).cleanGhostInventory(any(DeliveryOrderDto.class));
        verify(wmsExceptionAlertService, times(1)).alertGhostInventoryCleanupFailure(
            eq(event.getPayload().getDeliveryOrderId()),
            eq("ORD-20250123-001"),
            eq("[{sku=SKU001,qty=2},{sku=SKU002,qty=1}]"),
            eq("Database connection timeout")
        );
    }

    @Test
    void when_handleDeliveredAppEvent_with_ghostInventoryCleanupFailureWithNullMessage_then_sendAlert() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);
        RuntimeException cleanupException = new RuntimeException(); // null message

        doNothing().when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));
        doThrow(cleanupException).when(inventoryStockApplicationService).cleanGhostInventory(any(DeliveryOrderDto.class));
        doNothing().when(wmsExceptionAlertService).alertGhostInventoryCleanupFailure(any(), any(), any(), any());

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(wmsExceptionAlertService, times(1)).alertGhostInventoryCleanupFailure(
            eq(event.getPayload().getDeliveryOrderId()),
            eq("ORD-20250123-001"),
            eq("[{sku=SKU001,qty=2},{sku=SKU002,qty=1}]"),
            eq("RuntimeException")
        );
    }

    @Test
    void when_handleDeliveredAppEvent_with_alertServiceFailure_then_continueProcessing() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);
        RuntimeException cleanupException = new RuntimeException("Cleanup failed");
        RuntimeException alertException = new RuntimeException("Alert service failed");

        doNothing().when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));
        doThrow(cleanupException).when(inventoryStockApplicationService).cleanGhostInventory(any(DeliveryOrderDto.class));
        doThrow(alertException).when(wmsExceptionAlertService).alertGhostInventoryCleanupFailure(any(), any(), any(), any());

        // When - should not throw exception even if alert fails
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, times(1)).updateDeliveryQty(any(DeliveryOrderDto.class));
        verify(inventoryStockApplicationService, times(1)).cleanGhostInventory(any(DeliveryOrderDto.class));
        verify(wmsExceptionAlertService, times(1)).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredEvent_with_nullAppEvent_then_returnEarly() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = null;
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, never()).updateDeliveryQty(any());
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any());
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_nullPayload_then_returnEarly() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = new DeliveryOrderDeliveredApplicationEvent(this, null);
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, never()).updateDeliveryQty(any());
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any());
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_nullDeliveryOrderId_then_returnEarly() {
        // Given
        DeliveryOrderDto deliveryOrderDto = createValidDeliveryOrderDto();
        DeliveryOrderDeliveredPayloadDto payload = DeliveryOrderDeliveredPayloadDto.builder()
            .deliveryOrderId(null) // null delivery order ID
            .data(deliveryOrderDto)
            .build();

        DeliveryOrderDeliveredApplicationEvent event = new DeliveryOrderDeliveredApplicationEvent(this, payload);
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, never()).updateDeliveryQty(any());
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any());
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_nullDeliveryOrderData_then_returnEarly() {
        // Given
        DeliveryOrderDeliveredPayloadDto payload = DeliveryOrderDeliveredPayloadDto.builder()
            .deliveryOrderId(UUID.randomUUID())
            .data(null) // null delivery order data
            .build();

        DeliveryOrderDeliveredApplicationEvent event = new DeliveryOrderDeliveredApplicationEvent(this, payload);
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(shippingOrderApplicationService, never()).updateDeliveryQty(any());
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any());
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveredAppEvent_with_emptyDeliveryItems_then_sendAlertWithNoItems() {
        // Given
        DeliveryOrderDto deliveryOrderDto = DeliveryOrderDto.builder()
            .orderNumber("ORD-20250123-002")
            .deliveryOrderItems(Collections.emptyList()) // empty items
            .build();

        DeliveryOrderDeliveredPayloadDto payload = DeliveryOrderDeliveredPayloadDto.builder()
            .deliveryOrderId(UUID.randomUUID())
            .data(deliveryOrderDto)
            .build();

        DeliveryOrderDeliveredApplicationEvent event = new DeliveryOrderDeliveredApplicationEvent(this, payload);
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(true);

        RuntimeException cleanupException = new RuntimeException("Cleanup failed");

        doNothing().when(shippingOrderApplicationService).updateDeliveryQty(any(DeliveryOrderDto.class));
        doThrow(cleanupException).when(inventoryStockApplicationService).cleanGhostInventory(any(DeliveryOrderDto.class));
        doNothing().when(wmsExceptionAlertService).alertGhostInventoryCleanupFailure(any(), any(), any(), any());

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(wmsExceptionAlertService, times(1)).alertGhostInventoryCleanupFailure(
            eq(event.getPayload().getDeliveryOrderId()),
            eq("ORD-20250123-002"),
            eq("[]"), // empty items should result in empty array string
            eq("Cleanup failed")
        );
    }

    @Test
    void when_handleDeliveredAppEvent_with_featureFlagDisabled_then_returnEarly() {
        // Given
        DeliveryOrderDeliveredApplicationEvent event = createValidApplicationEvent();
        when(featureFlagsManager.isFeatureOn(CLEAN_SHIP_SB_V1)).thenReturn(false);

        // When
        daApplicationEventListener.handleDeliveredAppEvent(event);

        // Then
        verify(featureFlagsManager, times(1)).isFeatureOn(CLEAN_SHIP_SB_V1);
        verify(shippingOrderApplicationService, never()).updateDeliveryQty(any());
        verify(inventoryStockApplicationService, never()).cleanGhostInventory(any());
        verify(wmsExceptionAlertService, never()).alertGhostInventoryCleanupFailure(any(), any(), any(), any());
    }

    private DeliveryOrderDeliveredApplicationEvent createValidApplicationEvent() {
        DeliveryOrderDto deliveryOrderDto = createValidDeliveryOrderDto();

        DeliveryOrderDeliveredPayloadDto payload = DeliveryOrderDeliveredPayloadDto.builder()
            .deliveryOrderId(UUID.randomUUID())
            .data(deliveryOrderDto)
            .build();

        return new DeliveryOrderDeliveredApplicationEvent(this, payload);
    }

    private DeliveryOrderDto createValidDeliveryOrderDto() {
        DeliveryOrderItemDto item1 = DeliveryOrderItemDto.builder()
            .skuNumber("SKU001")
            .deliveredQty(BigDecimal.valueOf(2))
            .build();

        DeliveryOrderItemDto item2 = DeliveryOrderItemDto.builder()
            .skuNumber("SKU002")
            .deliveredQty(BigDecimal.valueOf(1))
            .build();

        return DeliveryOrderDto.builder()
            .orderNumber("ORD-20250123-001")
            .deliveryOrderItems(Arrays.asList(item1, item2))
            .build();
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_validEvent_then_processSuccessfully() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        UUID previousUserId = UUID.randomUUID();
        UUID currentUserId = UUID.randomUUID();
        String currentUserName = "John Doe";

        DeliveryTaskReassignedApplicationEvent event = createValidDeliveryTaskReassignedEvent(
            deliveryTaskId, previousUserId, currentUserId, currentUserName);

        DeliveryTask deliveryTask = createMockDeliveryTask(deliveryTaskId);
        DeliveryOrder deliveryOrder = createMockDeliveryOrder();

        when(deliveryAdaptor.findDeliveryTaskById(deliveryTaskId)).thenReturn(deliveryTask);
        when(deliveryAdaptor.findDeliveryOrderByTaskId(deliveryTaskId)).thenReturn(Collections.singletonList(deliveryOrder));
        doNothing().when(shippingOrderApplicationService).reassignShippingOrders(any(), any(), any(), any());

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, times(1)).findDeliveryTaskById(deliveryTaskId);
        verify(deliveryAdaptor, times(1)).findDeliveryOrderByTaskId(deliveryTaskId);
        verify(shippingOrderApplicationService, times(1)).reassignShippingOrders(
            deliveryTask.getDeliveryDate(), previousUserId, currentUserId, currentUserName);
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_nullEvent_then_returnEarly() {
        // Given
        DeliveryTaskReassignedApplicationEvent event = null;

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, never()).findDeliveryTaskById(any());
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_nullPayload_then_returnEarly() {
        // Given
        DeliveryTaskReassignedApplicationEvent event = createDeliveryTaskReassignedEvent(null);

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, never()).findDeliveryTaskById(any());
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_nullDeliveryTaskId_then_returnEarly() {
        // Given
        DeliveryTaskReassignedEventDto reassignmentData = createReassignmentEventDto(UUID.randomUUID(),
            UUID.randomUUID(),
            "Test User");
        DeliveryTaskReassignedPayloadDto payload = DeliveryTaskReassignedPayloadDto.builder()
            .deliveryTaskId(null)
            .data(reassignmentData)
            .build();

        DeliveryTaskReassignedApplicationEvent event = createDeliveryTaskReassignedEvent(payload);

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, never()).findDeliveryTaskById(any());
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_nullReassignmentData_then_returnEarly() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        DeliveryTaskReassignedPayloadDto payload = DeliveryTaskReassignedPayloadDto.builder()
            .deliveryTaskId(deliveryTaskId)
            .data(null)
            .build();

        DeliveryTaskReassignedApplicationEvent event = createDeliveryTaskReassignedEvent(payload);

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, never()).findDeliveryTaskById(any());
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_nullUserIds_then_returnEarly() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        DeliveryTaskReassignedEventDto reassignmentData = createReassignmentEventDto(null, UUID.randomUUID(), "Test User");
        DeliveryTaskReassignedPayloadDto payload = DeliveryTaskReassignedPayloadDto.builder()
            .deliveryTaskId(deliveryTaskId)
            .data(reassignmentData)
            .build();

        DeliveryTaskReassignedApplicationEvent event = createDeliveryTaskReassignedEvent(payload);

        // When
        daApplicationEventListener.handleDeliveryTaskReassignedEvent(event);

        // Then
        verify(deliveryAdaptor, never()).findDeliveryTaskById(any());
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_deliveryTaskNotFound_then_throwException() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        DeliveryTaskReassignedApplicationEvent event = createValidDeliveryTaskReassignedEvent(
            deliveryTaskId, UUID.randomUUID(), UUID.randomUUID(), "Test User");

        when(deliveryAdaptor.findDeliveryTaskById(deliveryTaskId)).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> daApplicationEventListener.handleDeliveryTaskReassignedEvent(event))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessage("Failed to process delivery task reassignment")
            .hasRootCauseMessage("No delivery task found for ID: " + deliveryTaskId);

        verify(deliveryAdaptor, times(1)).findDeliveryTaskById(deliveryTaskId);
        verify(deliveryAdaptor, never()).findDeliveryOrderByTaskId(any());
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_noDeliveryOrders_then_throwException() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        DeliveryTaskReassignedApplicationEvent event = createValidDeliveryTaskReassignedEvent(
            deliveryTaskId, UUID.randomUUID(), UUID.randomUUID(), "Test User");

        DeliveryTask deliveryTask = createMockDeliveryTask(deliveryTaskId);

        when(deliveryAdaptor.findDeliveryTaskById(deliveryTaskId)).thenReturn(deliveryTask);
        when(deliveryAdaptor.findDeliveryOrderByTaskId(deliveryTaskId)).thenReturn(Collections.emptyList());

        // When & Then
        assertThatThrownBy(() -> daApplicationEventListener.handleDeliveryTaskReassignedEvent(event))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessage("Failed to process delivery task reassignment")
            .hasRootCauseMessage("No delivery orders found for task ID: " + deliveryTaskId);

        verify(deliveryAdaptor, times(1)).findDeliveryTaskById(deliveryTaskId);
        verify(deliveryAdaptor, times(1)).findDeliveryOrderByTaskId(deliveryTaskId);
        verify(shippingOrderApplicationService, never()).reassignShippingOrders(any(), any(), any(), any());
    }

    @Test
    void when_handleDeliveryTaskReassignedEvent_with_reassignmentFailure_then_throwException() {
        // Given
        UUID deliveryTaskId = UUID.randomUUID();
        UUID previousUserId = UUID.randomUUID();
        UUID currentUserId = UUID.randomUUID();
        String currentUserName = "Test User";

        DeliveryTaskReassignedApplicationEvent event = createValidDeliveryTaskReassignedEvent(
            deliveryTaskId, previousUserId, currentUserId, currentUserName);

        DeliveryTask deliveryTask = createMockDeliveryTask(deliveryTaskId);
        DeliveryOrder deliveryOrder = createMockDeliveryOrder();
        RuntimeException reassignmentException = new RuntimeException("Database error during reassignment");

        when(deliveryAdaptor.findDeliveryTaskById(deliveryTaskId)).thenReturn(deliveryTask);
        when(deliveryAdaptor.findDeliveryOrderByTaskId(deliveryTaskId)).thenReturn(Collections.singletonList(deliveryOrder));
        doThrow(reassignmentException).when(shippingOrderApplicationService)
            .reassignShippingOrders(any(), any(), any(), any());

        // When & Then
        assertThatThrownBy(() -> daApplicationEventListener.handleDeliveryTaskReassignedEvent(event))
            .isInstanceOf(WmsBusinessException.class)
            .hasMessage("Failed to process delivery task reassignment")
            .hasCause(reassignmentException);

        verify(deliveryAdaptor, times(1)).findDeliveryTaskById(deliveryTaskId);
        verify(deliveryAdaptor, times(1)).findDeliveryOrderByTaskId(deliveryTaskId);
        verify(shippingOrderApplicationService, times(1)).reassignShippingOrders(
            deliveryTask.getDeliveryDate(), previousUserId, currentUserId, currentUserName);
    }

    private DeliveryTaskReassignedApplicationEvent createValidDeliveryTaskReassignedEvent(
        UUID deliveryTaskId, UUID previousUserId, UUID currentUserId, String currentUserName) {

        DeliveryTaskReassignedEventDto reassignmentData = createReassignmentEventDto(previousUserId,
            currentUserId,
            currentUserName);

        DeliveryTaskReassignedPayloadDto payload = DeliveryTaskReassignedPayloadDto.builder()
            .deliveryTaskId(deliveryTaskId)
            .data(reassignmentData)
            .build();

        return createDeliveryTaskReassignedEvent(payload);
    }

    private DeliveryTaskReassignedEventDto createReassignmentEventDto(UUID previousUserId,
        UUID currentUserId,
        String currentUserName) {
        return DeliveryTaskReassignedEventDto.builder()
            .previousUserId(previousUserId)
            .currentUserId(currentUserId)
            .currentUserName(currentUserName)
            .build();
    }

    private DeliveryTaskReassignedApplicationEvent createDeliveryTaskReassignedEvent(DeliveryTaskReassignedPayloadDto payload) {
        try {
            Constructor<DeliveryTaskReassignedApplicationEvent> constructor =
                DeliveryTaskReassignedApplicationEvent.class.getDeclaredConstructor(Object.class,
                    DeliveryTaskReassignedPayloadDto.class);
            constructor.setAccessible(true);
            return constructor.newInstance(this, payload);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create DeliveryTaskReassignedApplicationEvent", e);
        }
    }

    private DeliveryTask createMockDeliveryTask(UUID deliveryTaskId) {
        return DeliveryTask.builder()
            .id(deliveryTaskId)
            .deliveryDate(LocalDate.now().toString())
            .build();
    }

    private DeliveryOrder createMockDeliveryOrder() {
        return DeliveryOrder.builder()
            .id(UUID.randomUUID())
            .orderNumber("DO-20250123-001")
            .build();
    }
}
