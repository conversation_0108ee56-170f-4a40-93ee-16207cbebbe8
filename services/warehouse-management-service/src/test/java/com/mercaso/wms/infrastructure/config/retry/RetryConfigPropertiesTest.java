package com.mercaso.wms.infrastructure.config.retry;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RetryConfigPropertiesTest {

    private RetryConfigProperties retryConfigProperties;

    @BeforeEach
    void setUp() {
        retryConfigProperties = new RetryConfigProperties();
        retryConfigProperties.setEnabled(true);
        retryConfigProperties.setMaxAttempts(3);
        retryConfigProperties.setInterval(Duration.ofSeconds(1));
        retryConfigProperties.setBackoffMultiplier(2.0);
        retryConfigProperties.setMaxInterval(Duration.ofSeconds(30));
        retryConfigProperties.setDefaultStrategy(RetryConfigProperties.RetryStrategy.SERVER_ERROR);
    }

    @Test
    void getEffectiveConfig_withDefaultConfig_shouldReturnDefaultValues() {
        // Act
        RetryConfigProperties.EffectiveRetryConfig config = retryConfigProperties.getEffectiveConfig("unknown.endpoint");

        // Assert
        assertTrue(config.enabled());
        assertEquals(3, config.maxAttempts());
        assertEquals(Duration.ofSeconds(1), config.interval());
        assertEquals(2.0, config.backoffMultiplier());
        assertEquals(Duration.ofSeconds(30), config.maxInterval());
        assertEquals(RetryConfigProperties.RetryStrategy.SERVER_ERROR, config.strategy());
        assertTrue(config.customErrorCodes().isEmpty());
        assertTrue(config.excludeErrorCodes().isEmpty());
    }

    @Test
    void getEffectiveConfig_withEndpointOverride_shouldReturnOverriddenValues() {
        // Arrange
        RetryConfigProperties.EndpointRetryConfig override = new RetryConfigProperties.EndpointRetryConfig();
        override.setMaxAttempts(5);
        override.setInterval(Duration.ofSeconds(2));
        override.setStrategy(RetryConfigProperties.RetryStrategy.RATE_LIMIT);
        override.setCustomErrorCodes(Set.of("404", "409"));

        retryConfigProperties.setEndpoints(Map.of("shopify.order.create", override));

        // Act
        RetryConfigProperties.EffectiveRetryConfig config = retryConfigProperties.getEffectiveConfig("shopify.order.create");

        // Assert
        assertTrue(config.enabled());
        assertEquals(5, config.maxAttempts());
        assertEquals(Duration.ofSeconds(2), config.interval());
        assertEquals(2.0, config.backoffMultiplier()); // fallback to global
        assertEquals(Duration.ofSeconds(30), config.maxInterval()); // fallback to global
        assertEquals(RetryConfigProperties.RetryStrategy.RATE_LIMIT, config.strategy());
        assertEquals(Set.of("404", "409"), config.customErrorCodes());
        assertTrue(config.excludeErrorCodes().isEmpty());
    }

    @Test
    void getEffectiveConfig_withDisabledEndpoint_shouldReturnDisabledConfig() {
        // Arrange
        RetryConfigProperties.EndpointRetryConfig disabledConfig = new RetryConfigProperties.EndpointRetryConfig();
        disabledConfig.setEnabled(false);
        disabledConfig.setMaxAttempts(10); // should still be returned even if disabled

        retryConfigProperties.setEndpoints(Map.of("finale.inventory.sync", disabledConfig));

        // Act
        RetryConfigProperties.EffectiveRetryConfig config = retryConfigProperties.getEffectiveConfig("finale.inventory.sync");

        // Assert
        assertFalse(config.enabled());
        assertEquals(10, config.maxAttempts());
        assertEquals(Duration.ofSeconds(1), config.interval()); // fallback to global
        assertEquals(RetryConfigProperties.RetryStrategy.SERVER_ERROR, config.strategy()); // fallback to global
    }

    @Test
    void getStrategyConfig_serverError_shouldReturnCorrectConfig() {
        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR);

        // Assert
        assertEquals(Set.of("500", "502", "503", "504"), config.getHttpStatusCodes());
        assertEquals(Set.of("ConnectException", "SocketTimeoutException", "ReadTimeoutException"), config.getExceptionTypes());
        assertEquals(Set.of("timeout", "connection", "network"), config.getErrorKeywords());
        assertTrue(config.isRetryOnTimeout());
        assertTrue(config.isRetryOnConnection());
    }

    @Test
    void getStrategyConfig_rateLimit_shouldReturnCorrectConfig() {
        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.RATE_LIMIT);

        // Assert
        assertEquals(Set.of("429", "500", "502", "503", "504"), config.getHttpStatusCodes());
        assertTrue(config.getErrorKeywords().contains("rate limit"));
        assertTrue(config.getErrorKeywords().contains("quota exceeded"));
        assertTrue(config.isRetryOnTimeout());
        assertTrue(config.isRetryOnConnection());
    }

    @Test
    void getStrategyConfig_comprehensive_shouldReturnCorrectConfig() {
        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.COMPREHENSIVE);

        // Assert
        assertEquals(Set.of("408", "409", "429", "500", "502", "503", "504"), config.getHttpStatusCodes());
        assertTrue(config.getErrorKeywords().contains("timeout"));
        assertTrue(config.getErrorKeywords().contains("rate limit"));
        assertTrue(config.getErrorKeywords().contains("quota"));
        assertTrue(config.isRetryOnTimeout());
        assertTrue(config.isRetryOnConnection());
    }

    @Test
    void getStrategyConfig_networkOnly_shouldReturnCorrectConfig() {
        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.NETWORK_ONLY);

        // Assert
        assertTrue(config.getHttpStatusCodes().isEmpty());
        assertEquals(Set.of("ConnectException", "SocketTimeoutException", "ReadTimeoutException"), config.getExceptionTypes());
        assertEquals(Set.of("timeout", "connection", "network"), config.getErrorKeywords());
        assertTrue(config.isRetryOnTimeout());
        assertTrue(config.isRetryOnConnection());
    }

    @Test
    void getStrategyConfig_custom_shouldReturnEmptyConfig() {
        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.CUSTOM);

        // Assert
        assertTrue(config.getHttpStatusCodes().isEmpty());
        assertTrue(config.getExceptionTypes().isEmpty());
        assertTrue(config.getErrorKeywords().isEmpty());
        assertFalse(config.isRetryOnTimeout());
        assertFalse(config.isRetryOnConnection());
    }

    @Test
    void getStrategyConfig_withCustomStrategyConfig_shouldReturnCustomConfig() {
        // Arrange
        RetryConfigProperties.StrategyConfig customConfig = new RetryConfigProperties.StrategyConfig();
        customConfig.setHttpStatusCodes(Set.of("422", "429"));
        customConfig.setErrorKeywords(Set.of("validation", "rate"));
        customConfig.setRetryOnTimeout(false);

        retryConfigProperties.setStrategies(Map.of(RetryConfigProperties.RetryStrategy.SERVER_ERROR, customConfig));

        // Act
        RetryConfigProperties.StrategyConfig config = retryConfigProperties.getStrategyConfig(RetryConfigProperties.RetryStrategy.SERVER_ERROR);

        // Assert
        assertEquals(Set.of("422", "429"), config.getHttpStatusCodes());
        assertEquals(Set.of("validation", "rate"), config.getErrorKeywords());
        assertFalse(config.isRetryOnTimeout());
    }

    @Test
    void endpointRetryConfig_defaultValues_shouldBeCorrect() {
        // Arrange & Act
        RetryConfigProperties.EndpointRetryConfig config = new RetryConfigProperties.EndpointRetryConfig();

        // Assert
        assertTrue(config.isEnabled());
        assertNull(config.getMaxAttempts());
        assertNull(config.getInterval());
        assertNull(config.getBackoffMultiplier());
        assertNull(config.getMaxInterval());
        assertNull(config.getStrategy());
        assertNull(config.getCustomErrorCodes());
        assertNull(config.getExcludeErrorCodes());
    }

    @Test
    void effectiveRetryConfig_recordProperties_shouldBeImmutable() {
        // Arrange
        RetryConfigProperties.EffectiveRetryConfig config = new RetryConfigProperties.EffectiveRetryConfig(
            true,
            3,
            Duration.ofSeconds(1),
            2.0,
            Duration.ofSeconds(30),
            RetryConfigProperties.RetryStrategy.SERVER_ERROR,
            Set.of("404"),
            Set.of("401")
        );

        // Assert
        assertTrue(config.enabled());
        assertEquals(3, config.maxAttempts());
        assertEquals(Duration.ofSeconds(1), config.interval());
        assertEquals(2.0, config.backoffMultiplier());
        assertEquals(Duration.ofSeconds(30), config.maxInterval());
        assertEquals(RetryConfigProperties.RetryStrategy.SERVER_ERROR, config.strategy());
        assertEquals(Set.of("404"), config.customErrorCodes());
        assertEquals(Set.of("401"), config.excludeErrorCodes());
    }
}
