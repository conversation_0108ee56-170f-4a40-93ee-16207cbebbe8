package com.mercaso.wms.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.dto.Result;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.enums.BatchStatus;
import com.mercaso.wms.infrastructure.repository.batch.BatchRepositoryImpl;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.utils.BatchResourceApi;
import java.time.LocalDate;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class DocRecordsResourceIT extends AbstractIT {

    @Autowired
    BatchResourceApi batchResourceApi;
    @Autowired
    BatchRepositoryImpl batchRepository;

    @Test
    void when_get_batch_should_success() {
        Batch batch = Batch.builder().tag("2024-09-20").status(BatchStatus.IN_PROGRESS).lastModifiedBy("test").build();

        Batch saved = batchRepository.save(batch);

        BatchDo result = batchResourceApi.getBatch(saved.getId());

        assertNotNull(result);
        assertEquals(saved.getTag(), result.getTag());
    }

    @Test
    void when_get_batch_by_page_should_success() throws Exception {
        Batch batch = Batch.builder().tag("2024-09-20").status(BatchStatus.IN_PROGRESS).lastModifiedBy("test").build();

        batchRepository.save(batch);

        Result<BatchDo> batches = batchResourceApi.getBatches(null, null, null);

        assertNotNull(batches);
    }

    @Test
    void when_get_batch_by_delivery_date_should_success() throws Exception {
        Batch batch = Batch.builder()
            .tag(LocalDate.now().plusDays(30).toString())
            .status(BatchStatus.IN_PROGRESS)
            .lastModifiedBy("test")
            .build();

        batchRepository.save(batch);

        Result<BatchDo> batches = batchResourceApi.getBatches(batch.getTag(), null, null);

        assertNotNull(batches);
        batches.getData().forEach(batchDo -> {
            assertEquals(batch.getTag(), batchDo.getTag());
        });

    }

    @Test
    void when_get_batch_by_updated_by_should_success() throws Exception {
        Batch batch = Batch.builder()
            .tag(LocalDate.now().plusDays(30).toString())
            .status(BatchStatus.IN_PROGRESS)
            .lastModifiedBy(UUID.randomUUID().toString())
            .build();

        batchRepository.save(batch);

        Result<BatchDo> batches = batchResourceApi.getBatches(null, null, batch.getLastModifiedBy());

        assertNotNull(batches);
        batches.getData().forEach(batchDo -> {
            assertEquals(batch.getTag(), batchDo.getTag());
        });

    }

}