package com.mercaso.wms.infrastructure.utils;


import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import org.junit.jupiter.api.Test;

class DateUtilsTest {

    @Test
    void laDateTime_returnsCurrentDateTimeInLAZone() {
        String laDateTime = DateUtils.laDateTime();
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("America/Los_Angeles"));
        String expectedDateTime = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        assertEquals(expectedDateTime, laDateTime);
    }

    @Test
    void laDateTime_returnsNonNullString() {
        String laDateTime = DateUtils.laDateTime();
        assertNotNull(laDateTime);
    }

    @Test
    void laDateTime_returnsFormattedString() {
        String laDateTime = DateUtils.laDateTime();
        assertEquals(14, laDateTime.length());
    }

    @Test
    void getDeliveryDateRange_withCustomDays_returnsCorrectRange() {
        LocalDate deliveryDate = LocalDate.of(2024, 9, 5);
        int daysBefore = 2;
        int daysAfter = 4;
        
        LocalDate[] result = DateUtils.getDeliveryDateRange(deliveryDate, daysBefore, daysAfter);
        
        LocalDate expectedStartDate = LocalDate.of(2024, 9, 3); // 2 days before
        LocalDate expectedEndDate = LocalDate.of(2024, 9, 9);   // 4 days after
        
        assertArrayEquals(new LocalDate[]{expectedStartDate, expectedEndDate}, result);
    }

    @Test
    void getDeliveryDateRange_withZeroDays_returnsDeliveryDate() {
        LocalDate deliveryDate = LocalDate.of(2024, 9, 5);
        
        LocalDate[] result = DateUtils.getDeliveryDateRange(deliveryDate, 0, 0);
        
        assertArrayEquals(new LocalDate[]{deliveryDate, deliveryDate}, result);
    }

    @Test
    void getDeliveryDateRange_withAsymmetricDays_returnsCorrectRange() {
        LocalDate deliveryDate = LocalDate.of(2024, 9, 5);
        
        LocalDate[] result = DateUtils.getDeliveryDateRange(deliveryDate, 1, 7);
        
        LocalDate expectedStartDate = LocalDate.of(2024, 9, 4);  // 1 day before
        LocalDate expectedEndDate = LocalDate.of(2024, 9, 12);   // 7 days after
        
        assertArrayEquals(new LocalDate[]{expectedStartDate, expectedEndDate}, result);
    }

    @Test
    @SuppressWarnings("deprecation")
    void getDeliveryDateRange_deprecated_returnsDefault3Days() {
        LocalDate deliveryDate = LocalDate.of(2024, 9, 5);
        
        LocalDate[] result = DateUtils.getDeliveryDateRange(deliveryDate);
        
        LocalDate expectedStartDate = LocalDate.of(2024, 9, 2);  // 3 days before
        LocalDate expectedEndDate = LocalDate.of(2024, 9, 8);    // 3 days after
        
        assertArrayEquals(new LocalDate[]{expectedStartDate, expectedEndDate}, result);
    }

    @Test
    void getNextDeliveryDateRange_withCustomDays_returnsCorrectRange() {
        // This test will use the actual next delivery date, so we test the structure
        LocalDate[] result = DateUtils.getNextDeliveryDateRange(2, 5);
        
        assertNotNull(result);
        assertEquals(2, result.length);
        // Start date should be before end date
        assertEquals(true, result[0].isBefore(result[1]) || result[0].isEqual(result[1]));
    }

    @Test
    @SuppressWarnings("deprecation")
    void getNextDeliveryDateRange_deprecated_returnsDefault3Days() {
        LocalDate[] result = DateUtils.getNextDeliveryDateRange();
        
        assertNotNull(result);
        assertEquals(2, result.length);
        // The range should be 6 days apart (3 before + 3 after)
        long daysBetween = result[1].toEpochDay() - result[0].toEpochDay();
        assertEquals(6, daysBetween);
    }

    @Test
    void getDeliveryDateRange_withLargeDays_handlesCorrectly() {
        LocalDate deliveryDate = LocalDate.of(2024, 6, 15);
        
        LocalDate[] result = DateUtils.getDeliveryDateRange(deliveryDate, 30, 45);
        
        LocalDate expectedStartDate = LocalDate.of(2024, 5, 16);  // 30 days before
        LocalDate expectedEndDate = LocalDate.of(2024, 7, 30);    // 45 days after
        
        assertArrayEquals(new LocalDate[]{expectedStartDate, expectedEndDate}, result);
    }

}