package com.mercaso.wms.interfaces.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.mercaso.wms.application.query.SortType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Sort;

class SortByParseUtilTest {

    @Test
    void getSortField_returnsNull_whenSortFieldIsEmpty() {
        assertNull(SortByParseUtil.getSortField(""));
    }

    @Test
    void getSortField_returnsNull_whenSortFieldIsNull() {
        assertNull(SortByParseUtil.getSortField(null));
    }

    @Test
    void getSortField_returnsNull_whenSortFieldHasNoUnderscore() {
        assertNull(SortByParseUtil.getSortField("name"));
    }

    @Test
    void getSortField_returnsNull_whenFieldPartIsEmpty() {
        assertNull(SortByParseUtil.getSortField("_asc"));
    }

    @Test
    void getSortField_returnsNull_whenOrderPartIsEmpty() {
        assertNull(SortByParseUtil.getSortField("name_"));
    }

    @Test
    void getSortField_returnsSortAsc_whenOrderIsAsc() {
        Sort sort = SortByParseUtil.getSortField("CREATED_AT_ASC");
        assertNotNull(sort);
        assertEquals(sort.getOrderFor("createdAt"), Sort.Order.asc("createdAt"));
    }

    @Test
    void getSortField_returnsSortDesc_whenOrderIsDesc() {
        Sort sort = SortByParseUtil.getSortField("NAME_DESC");
        assertNotNull(sort);
        assertEquals(Sort.Order.desc("name"), sort.getOrderFor("name"));
    }

    @Test
    void getSortField_returnsNull_whenOrderIsInvalid() {
        assertNull(SortByParseUtil.getSortField("NAME_INVALID"));
    }

    @Test
    void getCamelCaseSortFields_withTotalQtySort_returnsCorrectSort() {
        // given
        List<SortType> sortTypes = List.of(SortType.TOTAL_QTY_ASC, SortType.TOTAL_QTY_DESC);

        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK);

        // then
        assertNotNull(sort);
        assertEquals(2, sort.toList().size());
        assertEquals(Sort.Order.asc("pt.totalQty"), sort.toList().get(0));
        assertEquals(Sort.Order.desc("pt.totalQty"), sort.toList().get(1));
    }

    @Test
    void getCamelCaseSortFields_withTotalLinesSort_returnsCorrectSort() {
        // given
        List<SortType> sortTypes = List.of(SortType.TOTAL_LINES_ASC, SortType.TOTAL_LINES_DESC);

        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK);

        // then
        assertNotNull(sort);
        assertEquals(2, sort.toList().size());
        assertEquals(Sort.Order.asc("pt.totalLines"), sort.toList().get(0));
        assertEquals(Sort.Order.desc("pt.totalLines"), sort.toList().get(1));
    }

    @Test
    void getCamelCaseSortFields_withTotalPickedQtySort_returnsCorrectSort() {
        // given
        List<SortType> sortTypes = List.of(SortType.TOTAL_PICKED_QTY_ASC, SortType.TOTAL_PICKED_QTY_DESC);

        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK);

        // then
        assertNotNull(sort);
        assertEquals(2, sort.toList().size());
        assertEquals(Sort.Order.asc("pt.totalPickedQty"), sort.toList().get(0));
        assertEquals(Sort.Order.desc("pt.totalPickedQty"), sort.toList().get(1));
    }

    @Test
    void getCamelCaseSortFields_withMultipleTotalFields_returnsCorrectSort() {
        // given
        List<SortType> sortTypes = List.of(
            SortType.TOTAL_QTY_DESC,
            SortType.TOTAL_LINES_ASC,
            SortType.TOTAL_PICKED_QTY_DESC
        );

        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK);

        // then
        assertNotNull(sort);
        assertEquals(3, sort.toList().size());
        assertEquals(Sort.Order.desc("pt.totalQty"), sort.toList().get(0));
        assertEquals(Sort.Order.asc("pt.totalLines"), sort.toList().get(1));
        assertEquals(Sort.Order.desc("pt.totalPickedQty"), sort.toList().get(2));
    }

    @Test
    void getCamelCaseSortFields_withMixedEntityFields_returnsCorrectSort() {
        // given
        List<SortType> sortTypes = List.of(
            SortType.TOTAL_QTY_ASC,
            SortType.AISLE_NUMBER_DESC // This should map to pti.aisleNumber
        );

        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(sortTypes, EntityEnums.PICKING_TASK);

        // then
        assertNotNull(sort);
        assertEquals(2, sort.toList().size());
        assertEquals(Sort.Order.asc("pt.totalQty"), sort.toList().get(0));
        assertEquals(Sort.Order.desc("pti.aisleNumber"), sort.toList().get(1));
    }

    @Test
    void getCamelCaseSortFields_withEmptyList_returnsNull() {
        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(List.of(), EntityEnums.PICKING_TASK);

        // then
        assertNull(sort);
    }

    @Test
    void getCamelCaseSortFields_withNullList_returnsNull() {
        // when
        Sort sort = SortByParseUtil.getCamelCaseSortFields(null, EntityEnums.PICKING_TASK);

        // then
        assertNull(sort);
    }
}