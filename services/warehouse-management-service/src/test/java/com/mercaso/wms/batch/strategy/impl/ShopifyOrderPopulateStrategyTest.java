package com.mercaso.wms.batch.strategy.impl;

import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.utils.MockDataUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ShopifyOrderPopulateStrategyTest {

    private ShopifyOrderPopulateStrategy strategy;

    @BeforeEach
    void setUp() {
        strategy = new ShopifyOrderPopulateStrategy();
    }

    @Test
    void populateBatchTemplate_withValidOrders_populatesBatchDtos() {
        List<ShippingOrder> shippingOrders = MockDataUtils.buildShippingOrders(1, ShippingOrderStatus.OPEN);
        PopulateCondition condition = PopulateCondition.builder()
            .shippingOrders(shippingOrders)
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(condition);

        assertEquals(2, result.size());
        ExcelBatchDto excelBatchDto = result.getFirst();
        assertEquals(shippingOrders.getFirst().getOrderNumber(), excelBatchDto.getOrderNumber());
        assertEquals(shippingOrders.getFirst().getShippingOrderItems().getFirst().getSkuNumber(), excelBatchDto.getItemNumber());
        assertEquals(10, excelBatchDto.getQuantity());
    }

    @Test
    void populateBatchTemplate_withNoOrders_returnsEmptyList() {
        PopulateCondition condition = PopulateCondition.builder()
            .shippingOrders(Collections.emptyList())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(condition);

        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void populateBatchTemplate_withNullLineItems_returnsEmptyList() {
        List<ShippingOrder> shippingOrders = MockDataUtils.buildShippingOrders(1, ShippingOrderStatus.OPEN);
        shippingOrders.forEach(shippingOrder -> {
            shippingOrder.setShippingOrderItems(null);
        });
        PopulateCondition condition = PopulateCondition.builder()
                .shippingOrders(shippingOrders)
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(condition);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void populateBatchTemplate_withNullQuantity_setsQuantityToZero() {

        List<ShippingOrder> shippingOrders = MockDataUtils.buildShippingOrders(1, ShippingOrderStatus.OPEN);
        shippingOrders.getFirst().getShippingOrderItems().forEach(shippingOrderItem -> {
            shippingOrderItem.setQty(0);
        });
        PopulateCondition condition = PopulateCondition.builder()
            .shippingOrders(shippingOrders)
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(condition);

        assertEquals(0, result.size());
    }
}