package com.mercaso.wms.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doNothing;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;

class AccountPreferenceResourceIT extends AbstractIT {

    @Test
    void when_createAccountPreference_then_accountPreferenceIsCreated() throws Exception {
        List<Warehouse> warehouses = warehouseRepository.findByType(WarehouseType.INTERNAL);
        // given
        CreateAccountPreferenceCommand command = CreateAccountPreferenceCommand.builder()
            .userName("Test User")
            .userId(UUID.randomUUID())
            .status(AccountPreferenceStatus.ACTIVE)
            .workWarehouseId(warehouses.getFirst().getId())
            .isFullTime(true)
            .email(RandomStringUtils.randomAlphabetic(10))
            .dailyWorkHours(8)
            .externalWarehouseId(warehouses.getLast().getId())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment("Test Department")
            .build();

        doNothing().when(umsAdaptor).assignPickerRolesToUser(command.getUserId());

        // when
        AccountPreferenceDto createdAccountPreference = accountPreferenceResourceApi.createAccountPreference(command);

        // then
        assertNotNull(createdAccountPreference);
        assertNotNull(createdAccountPreference.getId());
        assertEquals(command.getUserName(), createdAccountPreference.getUserName());
        assertEquals(command.getStatus().toString(), createdAccountPreference.getStatus());
        assertEquals(command.getWorkWarehouseId(), createdAccountPreference.getWorkWarehouse().getId());
        assertEquals(command.getIsFullTime(), createdAccountPreference.getIsFullTime());
        assertEquals(command.getDailyWorkHours(), createdAccountPreference.getDailyWorkHours());
        assertEquals(command.getExternalWarehouseId(), createdAccountPreference.getExternalWarehouse().getId());
        assertEquals(command.getTaskType(), createdAccountPreference.getTaskType());
        assertEquals(command.getPreferredDepartment(), createdAccountPreference.getPreferredDepartment());

        UpdateAccountPreferenceCommand updateAccountPreferenceCommand = UpdateAccountPreferenceCommand.builder()
            .status(AccountPreferenceStatus.INACTIVE)
            .workWarehouseId(warehouses.getFirst().getId())
            .isFullTime(false)
            .dailyWorkHours(10)
            .externalWarehouseId(warehouses.getLast().getId())
            .taskType(PickingTaskType.ORDER)
            .preferredDepartment("Test Department")
            .build();

        // when
        AccountPreferenceDto updateAccountPreference = accountPreferenceResourceApi.updateAccountPreference(
            createdAccountPreference.getId(),
            updateAccountPreferenceCommand);

        // then
        assertNotNull(updateAccountPreference);
        assertNotNull(updateAccountPreference.getId());
        assertEquals(updateAccountPreferenceCommand.getStatus().toString(), updateAccountPreference.getStatus());
        assertEquals(updateAccountPreferenceCommand.getWorkWarehouseId(), updateAccountPreference.getWorkWarehouse().getId());
        assertEquals(updateAccountPreferenceCommand.getIsFullTime(), updateAccountPreference.getIsFullTime());
        assertEquals(updateAccountPreferenceCommand.getDailyWorkHours(), updateAccountPreference.getDailyWorkHours());
        assertEquals(updateAccountPreferenceCommand.getExternalWarehouseId(),
            updateAccountPreference.getExternalWarehouse().getId());
        assertEquals(updateAccountPreferenceCommand.getTaskType(), updateAccountPreference.getTaskType());
        assertEquals(updateAccountPreferenceCommand.getPreferredDepartment(), updateAccountPreference.getPreferredDepartment());
    }


}