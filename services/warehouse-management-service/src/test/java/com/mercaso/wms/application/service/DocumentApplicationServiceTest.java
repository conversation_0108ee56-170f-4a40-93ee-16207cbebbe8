package com.mercaso.wms.application.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.wms.application.command.document.UploadDocumentCommand;
import com.mercaso.wms.application.dto.document.DocumentDto;
import com.mercaso.wms.application.mapper.document.DocumentDtoApplicationMapper;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.document.Document;
import com.mercaso.wms.domain.document.DocumentRepository;
import com.mercaso.wms.domain.document.enums.DocumentType;
import com.mercaso.wms.infrastructure.external.document.DocumentAdaptor;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DocumentApplicationServiceTest {

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private DocumentAdaptor documentAdaptor;

    @Mock
    private DocumentDtoApplicationMapper documentDtoApplicationMapper;

    @InjectMocks
    private DocumentApplicationService documentApplicationService;

    private UUID entityId;
    private UploadDocumentCommand command;
    private Document document;
    private DocumentDto documentDto;
    private DocumentResponse documentResponse;

    @BeforeEach
    void setUp() {
        entityId = UUID.randomUUID();
        command = UploadDocumentCommand.builder()
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER)
            .documentType(DocumentType.DAMAGED)
            .clientFileIds(List.of("file1"))
            .build();

        document = Document.builder()
            .id(UUID.randomUUID())
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER.name())
            .documentType(DocumentType.DAMAGED)
            .fileName("test-file.pdf")
            .build();

        documentDto = DocumentDto.builder()
            .id(document.getId())
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER.name())
            .documentType(DocumentType.DAMAGED)
            .fileName("test-file.pdf")
            .fileUrl("http://test-url.com/test-file.pdf")
            .build();

        documentResponse = DocumentResponse.builder()
            .name("test-file.pdf")
            .signedUrl("http://test-url.com/test-file.pdf")
            .build();
    }

    @Test
    void uploadDocuments_Success() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "file1", "test.pdf", "application/pdf", "test content".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file1", file);

        when(documentAdaptor.uploadToS3(any(UploadDocumentRequest.class))).thenReturn(documentResponse);
        when(documentRepository.save(any(Document.class))).thenReturn(document);
        when(documentDtoApplicationMapper.domainToDto(document)).thenReturn(documentDto);

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command), files);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(documentDto, result.get(0));
        verify(documentAdaptor).uploadToS3(any(UploadDocumentRequest.class));
        verify(documentRepository).save(any(Document.class));
    }

    @Test
    void uploadDocuments_EmptyFiles_ReturnsEmptyList() {
        // Given
        Map<String, MultipartFile> files = Map.of();

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command), files);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(documentAdaptor, documentRepository);
    }

    @Test
    void uploadDocuments_FileNotFound_ContinuesProcessing() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "file2", "test.pdf", "application/pdf", "test content".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file2", file);

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command), files);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(documentAdaptor, documentRepository);
    }

    @Test
    void uploadDocuments_MultipleFiles_Success() {
        // Given
        MockMultipartFile file1 = new MockMultipartFile(
            "file1", "test1.pdf", "application/pdf", "test content 1".getBytes()
        );
        MockMultipartFile file2 = new MockMultipartFile(
            "file2", "test2.pdf", "application/pdf", "test content 2".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file1", file1, "file2", file2);

        UploadDocumentCommand command1 = UploadDocumentCommand.builder()
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER)
            .documentType(DocumentType.DAMAGED)
            .clientFileIds(List.of("file1"))
            .build();

        UploadDocumentCommand command2 = UploadDocumentCommand.builder()
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER)
            .documentType(DocumentType.DAMAGED)
            .clientFileIds(List.of("file2"))
            .build();

        when(documentAdaptor.uploadToS3(any(UploadDocumentRequest.class))).thenReturn(documentResponse);
        when(documentRepository.save(any(Document.class))).thenReturn(document);
        when(documentDtoApplicationMapper.domainToDto(document)).thenReturn(documentDto);

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command1, command2), files);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(documentAdaptor, times(2)).uploadToS3(any(UploadDocumentRequest.class));
        verify(documentRepository, times(2)).save(any(Document.class));
    }

    @Test
    void uploadDocuments_UploadException_ContinuesProcessing() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "file1", "test.pdf", "application/pdf", "test content".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file1", file);

        when(documentAdaptor.uploadToS3(any(UploadDocumentRequest.class)))
            .thenThrow(new RuntimeException("Upload failed"));

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command), files);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentAdaptor).uploadToS3(any(UploadDocumentRequest.class));
        verifyNoInteractions(documentRepository);
    }

    @Test
    void download_Success() throws IOException {
        // Given
        String documentName = "test-file.pdf";
        byte[] fileContent = "test content".getBytes();
        MockHttpServletResponse response = new MockHttpServletResponse();

        when(documentAdaptor.downloadDocument(documentName)).thenReturn(fileContent);

        // When
        documentApplicationService.download(documentName, response);

        // Then
        assertEquals(200, response.getStatus());
        assertEquals("application/pdf", response.getContentType());
        assertArrayEquals(fileContent, response.getContentAsByteArray());
        verify(documentAdaptor).downloadDocument(documentName);
    }

    @Test
    void download_FileNotFound_Returns404() throws IOException {
        // Given
        String documentName = "test-file.pdf";
        MockHttpServletResponse response = new MockHttpServletResponse();

        when(documentAdaptor.downloadDocument(documentName)).thenReturn(null);

        // When
        documentApplicationService.download(documentName, response);

        // Then
        assertEquals(404, response.getStatus());
        verify(documentAdaptor).downloadDocument(documentName);
    }

    @Test
    void download_EmptyFile_Returns404() throws IOException {
        // Given
        String documentName = "test-file.pdf";
        MockHttpServletResponse response = new MockHttpServletResponse();

        when(documentAdaptor.downloadDocument(documentName)).thenReturn(new byte[0]);

        // When
        documentApplicationService.download(documentName, response);

        // Then
        assertEquals(404, response.getStatus());
        verify(documentAdaptor).downloadDocument(documentName);
    }

    @Test
    void uploadDocuments_MultipleClientFileIds_Success() {
        // Given
        MockMultipartFile file1 = new MockMultipartFile(
            "file1", "test1.pdf", "application/pdf", "test content 1".getBytes()
        );
        MockMultipartFile file2 = new MockMultipartFile(
            "file2", "test2.pdf", "application/pdf", "test content 2".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file1", file1, "file2", file2);

        UploadDocumentCommand commandWithMultipleFiles = UploadDocumentCommand.builder()
            .entityId(entityId)
            .entityName(EntityEnums.SHIPPING_ORDER)
            .documentType(DocumentType.DAMAGED)
            .clientFileIds(List.of("file1", "file2"))
            .build();

        when(documentAdaptor.uploadToS3(any(UploadDocumentRequest.class))).thenReturn(documentResponse);
        when(documentRepository.save(any(Document.class))).thenReturn(document);
        when(documentDtoApplicationMapper.domainToDto(document)).thenReturn(documentDto);

        // When
        var result = documentApplicationService.uploadDocuments(List.of(commandWithMultipleFiles), files);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(documentAdaptor, times(2)).uploadToS3(any(UploadDocumentRequest.class));
        verify(documentRepository, times(2)).save(any(Document.class));
    }

    @Test
    void uploadDocuments_RepositoryException_ContinuesProcessing() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
            "file1", "test.pdf", "application/pdf", "test content".getBytes()
        );
        Map<String, MultipartFile> files = Map.of("file1", file);

        when(documentAdaptor.uploadToS3(any(UploadDocumentRequest.class))).thenReturn(documentResponse);
        when(documentRepository.save(any(Document.class))).thenThrow(new RuntimeException("Database error"));

        // When
        var result = documentApplicationService.uploadDocuments(List.of(command), files);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentAdaptor).uploadToS3(any(UploadDocumentRequest.class));
        verify(documentRepository).save(any(Document.class));
    }
} 