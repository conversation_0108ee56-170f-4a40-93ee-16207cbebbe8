package com.mercaso.wms.application.service.pickingtask;

import static com.mercaso.wms.utils.MockDataUtils.buildBatchItems;
import static com.mercaso.wms.utils.MockDataUtils.buildPickingTask;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.PickingTaskApplicationService;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.batchitem.BatchItemRepository;
import com.mercaso.wms.domain.location.LocationRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class CreateBatchLevelPickingTaskServiceTest {

    private final BatchItemRepository batchItemRepository = mock(BatchItemRepository.class);
    private final BatchItemQueryService batchItemQueryService = mock(BatchItemQueryService.class);
    private final PickingTaskRepository pickingTaskRepository = mock(PickingTaskRepository.class);
    private final LocationRepository locationRepository = mock(LocationRepository.class);
    private final PickingTaskAssignmentConfig pickingTaskAssignmentConfig = mock(PickingTaskAssignmentConfig.class);
    private final PickingTaskApplicationService pickingTaskApplicationService = mock(PickingTaskApplicationService.class);
    private final LocationCache locationCache = mock(LocationCache.class);
    private final FeatureFlagsManager featureFlagsManager = mock(FeatureFlagsManager.class);

    private final CreateBatchLevelPickingTaskService createBatchLevelPickingTask = new CreateBatchLevelPickingTaskService(
        batchItemRepository,
        batchItemQueryService,
        pickingTaskRepository,
        locationRepository,
        pickingTaskAssignmentConfig,
        pickingTaskApplicationService,
        locationCache,
        featureFlagsManager);


    @Test
    void when_batch_created_then_generate_small_order_picking_task() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        when(batchItemQueryService.findSmallBeverageBatchItemsByBatchId(any(), anyString())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(buildPickingTask(batchItems.getFirst().getBatchId(), 5));
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(5, pickingTasks.size());
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_downey_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);

        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(batchItems);
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.DOWNEY));
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.DOWNEY, pickingTask.getSource());
        });
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_Costco_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        batchItems.forEach(batchItem -> batchItem.setSource(SourceEnum.COSTCO.name()));
        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(List.of());
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.COSTCO));
        when(batchItemQueryService.findBy(batchItems.getFirst().getBatchId(), SourceEnum.COSTCO.name())).thenReturn(batchItems);
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.COSTCO, pickingTask.getSource());
        });
    }

    @Test
    void when_batch_created_then_generate_picking_task_with_jetro_items() {
        List<BatchItem> batchItems = buildBatchItems(UUID.randomUUID(), 10);
        for (BatchItem batchItem : batchItems) {
            batchItem.setSource(SourceEnum.JETRO.name());
        }

        when(batchItemQueryService.findBigBeverageBatchItemsByBatchId(any(), any())).thenReturn(List.of());
        when(batchItemQueryService.findBy(any(), anyString())).thenReturn(batchItems);
        List<PickingTask> builtPickingTask = buildPickingTask(batchItems.getFirst().getBatchId(), 10);
        builtPickingTask.forEach(pickingTask -> pickingTask.setSource(SourceEnum.JETRO));
        when(pickingTaskRepository.saveAll(any())).thenReturn(builtPickingTask);

        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchItems.getFirst()
            .getBatchId());

        assertEquals(10, pickingTasks.size());
        pickingTasks.forEach(pickingTask -> {
            assertEquals(2, pickingTask.getPickingTaskItems().size());
            assertEquals(batchItems.getFirst().getBatchId(), pickingTask.getBatchId());
            assertEquals(SourceEnum.JETRO, pickingTask.getSource());
        });
    }

    @Test
    void createMfcBatchPickingTask_Success() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("DEPARTMENT_1")
            .locationName("A01-01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("DEPARTMENT_1")
            .locationName("A01-02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenReturn(List.of(PickingTask.builder().build()));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertEquals(1, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTask_EmptyBatchItems_NoTasksCreated() {
        // Given
        UUID batchId = UUID.randomUUID();

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(Collections.emptyList());

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertTrue(pickingTasks.isEmpty());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_Success() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-01")
            .breakdownName("A-01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("B01-01")
            .breakdownName("B-01")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item3 = BatchItem.builder()
            .department("Candy & Snacks")
            .locationName("A02-01")
            .breakdownName("A02-B01")
            .expectQty(10)
            .skuNumber("SKU_3")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2, item3);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertEquals(3, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_EmptyBatchItems_NoTasksCreated() {
        // Given
        UUID batchId = UUID.randomUUID();

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(Collections.emptyList());

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertTrue(pickingTasks.isEmpty());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNullBreakdownName_GroupedAsOther() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("Beverage")
            .locationName("A-01")
            .breakdownName(null)
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("A-02")
            .breakdownName("A-B02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        // 2 picking tasks should be created: Other's Beverage, A's Beverage
        assertEquals(2, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNullDepartment_GroupedAsOther() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department(null)
            .locationName("A01-01")
            .breakdownName("A01-B01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-02")
            .breakdownName("A01-B02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertEquals(2, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_MultipleBreakdownAisles_CreatesSeparateTasks() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-01")
            .breakdownName("A01-B01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("A02-01")
            .breakdownName("A02-B01")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item3 = BatchItem.builder()
            .department("Candy & Snacks")
            .locationName("A03-01")
            .breakdownName("A03-B01")
            .expectQty(10)
            .skuNumber("SKU_3")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2, item3);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        assertEquals(3, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_WithNAAndCoolerData_RemovesData() {
        // Given
        UUID batchId = UUID.randomUUID();

        BatchItem item1 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-01")
            .breakdownName("A01-B01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("NA")
            .breakdownName("A01-B02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item3 = BatchItem.builder()
            .department("Beverage")
            .locationName(".COOLER")
            .breakdownName("COOLER-B01")
            .expectQty(10)
            .skuNumber("SKU_3")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = new ArrayList<>(Arrays.asList(item1, item2, item3));

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        // Only 1 picking task should be created because NA and COOLER data are removed
        assertEquals(2, pickingTasks.size());
    }

    @Test
    void createMfcBatchPickingTaskByBreakdownAisle_ComplexGroupingScenario() {
        // Given
        UUID batchId = UUID.randomUUID();

        //Create complex test data, including different breakdown aisle and department combinations
        BatchItem item1 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-01")
            .breakdownName("A01-B01")
            .expectQty(10)
            .skuNumber("SKU_1")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item2 = BatchItem.builder()
            .department("Beverage")
            .locationName("A01-02")
            .breakdownName("A01-B02")
            .expectQty(10)
            .skuNumber("SKU_2")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item3 = BatchItem.builder()
            .department("Candy & Snacks")
            .locationName("A01-03")
            .breakdownName("A01-B03")
            .expectQty(10)
            .skuNumber("SKU_3")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item4 = BatchItem.builder()
            .department("Beverage")
            .locationName("A02-01")
            .breakdownName("A02-B01")
            .expectQty(10)
            .skuNumber("SKU_4")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item5 = BatchItem.builder()
            .department("Candy & Snacks")
            .locationName("A02-02")
            .breakdownName("A02-B02")
            .expectQty(10)
            .skuNumber("SKU_5")
            .source(SourceEnum.MFC.name())
            .build();
        BatchItem item6 = BatchItem.builder()
            .department("Tobacco")
            .locationName("A03-01")
            .breakdownName("A03-B01")
            .expectQty(10)
            .skuNumber("SKU_6")
            .source(SourceEnum.MFC.name())
            .build();
        List<BatchItem> mfcBatchItems = Arrays.asList(item1, item2, item3, item4, item5, item6);

        when(featureFlagsManager.isFeatureOn(any())).thenReturn(true);
        when(batchItemQueryService.findBy(batchId, SourceEnum.MFC.name())).thenReturn(mfcBatchItems);
        when(locationCache.getLocationMap()).thenReturn(new HashMap<>());
        when(pickingTaskRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.<List<PickingTask>>getArgument(0));

        // When
        List<PickingTask> pickingTasks = createBatchLevelPickingTask.createPickingTask(batchId);

        // Then
        verify(batchItemQueryService).findBy(batchId, SourceEnum.MFC.name());
        //5 picking tasks should be created:
        // 1. Beverage of A01 (item1, item2)
        // 2. Candy & Snacks of A01 (item3)
        // 3. Beverage of A02 (item4)
        // 4. Candy & Snacks of A02 (item5)
        // 5. Tobacco of A03 (item6)
        assertEquals(5, pickingTasks.size());
    }

}