package com.mercaso.wms.application.queryservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.wms.application.dto.WarehouseDto;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.domain.warehouse.enums.WarehouseType;
import com.mercaso.wms.utils.MockDataUtils;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class WarehouseQueryServiceTest {

    private final WarehouseRepository warehouseRepository = mock(WarehouseRepository.class);

    private final WarehouseQueryService warehouseQueryService = new WarehouseQueryService(warehouseRepository);

    @Test
    void when_find_by_type_then_return_empty() {
        when(warehouseRepository.findByType(any())).thenReturn(Collections.emptyList());

        List<WarehouseDto> warehouseDtos = warehouseQueryService.findByType(WarehouseType.INTERNAL);

        assertEquals(0, warehouseDtos.size());
    }

    @Test
    void when_find_by_type_then_return_warehouse_dtos() {
        when(warehouseRepository.findByType(any())).thenReturn(List.of(MockDataUtils.buildWarehouse(UUID.randomUUID())));

        List<WarehouseDto> warehouseDtos = warehouseQueryService.findByType(WarehouseType.INTERNAL);

        assertNotNull(warehouseDtos);
        assertEquals(1, warehouseDtos.size());
    }

}