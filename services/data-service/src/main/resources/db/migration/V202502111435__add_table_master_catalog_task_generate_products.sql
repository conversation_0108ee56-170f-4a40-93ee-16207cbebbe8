create table if not exists master_catalog_task_generate_products
(
    id                                uuid PRIMARY KEY,
    name                              varchar(256) NOT NULL,
    type                              varchar(64)  NOT NULL,
    raw_data_id                       uuid         NOT NULL,
    upc                               varchar(64)  NOT NULL,
    description                       text         NOT NULL,
    primary_vendor                    varchar(256) NOT NULL,
    potentially_duplicate_raw_data_id uuid         NOT NULL,
    potentially_duplicate_upc         varchar(64)  NOT NULL,
    potentially_duplicate_description text         NOT NULL,
    potentially_duplicate_vendor      varchar(256) NOT NULL,
    status                            integer      NOT NULL,
    group_id                          varchar(64)  NOT NULL,
    created_at                        timestamptz  NOT NULL,
    updated_at                        timestamptz  NOT NULL,
    created_by                        varchar(64)  NOT NULL,
    updated_by                        varchar(64)  NOT NULL
);

comment on column master_catalog_task_generate_products.id is 'Unique identifier of the task';
comment on column master_catalog_task_generate_products.name is 'Name of the task';
comment on column master_catalog_task_generate_products.type is 'Type of the task: remove_duplication, generate_products';
comment on column master_catalog_task_generate_products.raw_data_id is 'Unique identifier of the master catalog raw data';
comment on column master_catalog_task_generate_products.upc is 'UPC of the master catalog raw data';
comment on column master_catalog_task_generate_products.description is 'Description of the master catalog raw data';
comment on column master_catalog_task_generate_products.primary_vendor is 'Primary vendor of the master catalog raw data';
comment on column master_catalog_task_generate_products.potentially_duplicate_raw_data_id is 'Unique identifier of the potentially duplicate master catalog raw data';
comment on column master_catalog_task_generate_products.potentially_duplicate_upc is 'UPC of the potentially duplicate master catalog raw data';
comment on column master_catalog_task_generate_products.potentially_duplicate_description is 'Description of the potentially duplicate master catalog raw data';
comment on column master_catalog_task_generate_products.potentially_duplicate_vendor is 'Vendor of the potentially duplicate master catalog raw data';
comment on column master_catalog_task_generate_products.status is 'Status of the task: 9- remove_duplication_in_progress, 19- remove_duplication_completed, 29- generate_products_in_progress, 39- generate_products_completed';
comment on column master_catalog_task_generate_products.group_id is 'Group identifier of the task';
comment on column master_catalog_task_generate_products.created_at is 'Creation time of the task';
comment on column master_catalog_task_generate_products.updated_at is 'Last update time of the task';
comment on column master_catalog_task_generate_products.created_by is 'User who created the task';
comment on column master_catalog_task_generate_products.updated_by is 'User who last updated the task';

create index if not exists idx_master_catalog_task_generate_products_group_id_status on master_catalog_task_generate_products (group_id, status);
