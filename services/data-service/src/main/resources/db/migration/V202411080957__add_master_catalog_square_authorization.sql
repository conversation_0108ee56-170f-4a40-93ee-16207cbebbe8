create table if not exists master_catalog_square_authorization
(
    id                           uuid primary key,
    state                        uuid                     not null,
    store_id                     uuid                     not null,
    permissions                  varchar[]                not null,
    encrypted_access_token       text,
    access_token_expires_at      timestamp with time zone,
    encrypted_application_id     text,
    encrypted_application_secret text,
    encrypted_refresh_token      text,
    created_at                   timestamp with time zone not null default now(),
    updated_at                   timestamp with time zone not null default now(),
    deleted_at                   timestamp with time zone
);

comment on table master_catalog_square_authorization is 'Square authorization for a store';
comment on column master_catalog_square_authorization.state is 'This is an uuid for verifying its value to help protect against cross-site request forgery. this value will be sent to seller account by us. Use state to find application_id and application_secret when obtain access token.';
comment on column master_catalog_square_authorization.store_id is 'The id of the store table, we use it to relate to the store';
comment on column master_catalog_square_authorization.permissions is 'Permissions that the application is requesting, such as {"INVENTORY_READ", "ITEMS_READ"}';
comment on column master_catalog_square_authorization.encrypted_access_token is 'The access token obtained from Square count, and it is sensitive data, the value is encrypted';
comment on column master_catalog_square_authorization.access_token_expires_at is 'The expiration time of access token, it will be automatically refreshed before it expires';
comment on column master_catalog_square_authorization.encrypted_application_id is 'The application id of Square account, and it is sensitive data, the value is encrypted';
comment on column master_catalog_square_authorization.encrypted_application_secret is 'The application secret of Square account, and it is sensitive data, the value is encrypted';
comment on column master_catalog_square_authorization.encrypted_refresh_token is 'The refresh token of Square account, and it is sensitive data, the value is encrypted';

create index if not exists idx_master_catalog_square_authorization_store_id on master_catalog_square_authorization (store_id);
create index if not exists idx_master_catalog_square_authorization_state on master_catalog_square_authorization (state);