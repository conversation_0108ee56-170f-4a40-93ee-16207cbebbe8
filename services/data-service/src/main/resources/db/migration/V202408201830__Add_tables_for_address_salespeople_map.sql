CREATE TABLE shopify.address_salespeople_map
(
    address                        JSONB PRIMARY KEY,
    formatted_address              TEXT,
    formatted_latitude             DOUBLE PRECISION,
    formatted_longitude            DOUBLE PRECISION,
    google_normalized_address_full JSONB,
    salespeople                    VARCHAR(255)
);

CREATE TABLE shopify.salespeople_territory_map
(
    id                     INTEGER PRIMARY KEY,
    zipcode                VARCHAR(50),
    salesperson_stats      TEXT,
    total_order_count      NUMERIC(10, 1),
    total_sales            TEXT,
    last_order_date        TEXT,
    geometry               TEXT,
    district_y             NUMERIC(10, 1),
    district_salesperson_y VARCHAR(100)
);

CREATE INDEX idx_address_salespeople_map_address ON shopify.address_salespeople_map USING GIN (address jsonb_path_ops);