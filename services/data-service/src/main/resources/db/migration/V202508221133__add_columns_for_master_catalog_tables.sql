alter table mc_batch_job
    add column job_number varchar(64) unique;

alter table mc_task
    add column task_number varchar(64) unique;

update mc_batch_job
set job_number = 'MJ-' || to_char(created_at, 'YYYYMMDD')
where job_number is null;

update mc_task
set task_number = 'MT-' || to_char(created_at, 'YYYYMMDDHH24MISSMS')
    || lpad(floor(random() * 1000)::text, 3, '0')
where task_number is null;

alter table mc_batch_job
    alter column job_number set not null;

alter table mc_task
    alter column task_number set not null;

comment on column mc_batch_job.job_number is 'job business number';
comment on column mc_task.task_number is 'task business number';