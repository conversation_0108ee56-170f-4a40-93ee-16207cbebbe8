alter table master_catalog_task_generate_products
    rename to master_catalog_potentially_duplicate_raw_data;

alter table master_catalog_potentially_duplicate_raw_data
    drop column name,
    drop column type,
    drop column group_id,
    drop column status;

alter table master_catalog_potentially_duplicate_raw_data
    add column if not exists status  varchar(64) not null default 'PENDING',
    add column if not exists task_id uuid        not null;

comment on column master_catalog_potentially_duplicate_raw_data.status is 'Status of the data: PENDING, NOT_DUPLICATE, DUPLICATE';
comment on column master_catalog_potentially_duplicate_raw_data.task_id is 'Task ID';


