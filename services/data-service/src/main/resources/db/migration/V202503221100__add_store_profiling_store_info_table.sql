CREATE TABLE public.store_profiling_store_info
(
    store_id            VARCHAR(50) PRIMARY KEY,
    store_name          VA<PERSON><PERSON><PERSON>(255),
    address_name        VARCHAR(255) NOT NULL,
    latitude            DOUBLE PRECISION,
    longitude           DOUBLE PRECISION,
    zip_code            VA<PERSON>HAR(255),
    is_nrs_store        BOOLEAN,
    is_square_store     BOOLEAN,
    address_name_search tsvector
);

CREATE OR REPLACE FUNCTION store_profiling_address_name_search_trigger() R<PERSON><PERSON><PERSON> trigger AS
$$
BEGIN
    IF NEW.address_name IS DISTINCT FROM OLD.address_name THEN
        NEW.address_name_search := to_tsvector('english', NEW.address_name);
    END IF;
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

CREATE TRIGGER store_profiling_address_name_search_update
    BEFORE INSERT OR UPDATE
    ON public.store_profiling_store_info
    FOR EACH ROW
EXECUTE FUNCTION store_profiling_address_name_search_trigger();

CREATE INDEX store_profiling_address_name_search_idx ON public.store_profiling_store_info USING GIN (address_name_search);

CREATE INDEX store_profiling_address_name_trgm_idx ON public.store_profiling_store_info USING GIN (address_name gin_trgm_ops);