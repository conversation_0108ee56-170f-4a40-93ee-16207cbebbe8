create table if not exists master_catalog_nrs_file_tracking
(
    id         uuid primary key,
    date       varchar(64),
    status     varchar(255),
    type       varchar(64),
    created_at timestamptz default now(),
    updated_at timestamptz default now()
);

comment on table master_catalog_nrs_file_tracking is 'Table to store master_catalog_nrs_task for tracking the status of the task to fetch NRS data';
comment on column master_catalog_nrs_file_tracking.date is 'Date of the nrs data';
comment on column master_catalog_nrs_file_tracking.status is 'Status of the nrs data in specific date';
comment on column master_catalog_nrs_file_tracking.type is 'Type of the nrs data, e.g. geog, fact';

create index if not exists idx_master_catalog_nrs_file_tracking_date_type
    on master_catalog_nrs_file_tracking (date, type);

