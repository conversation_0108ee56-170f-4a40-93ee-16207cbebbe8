ALTER TABLE master_catalog_product_generation_task
    RENAME TO mc_batch_job;

ALTER TABLE mc_batch_job
    drop column if exists name;

comment on column mc_batch_job.id is 'Unique identifier of the job';
comment on column mc_batch_job.status is 'Status of the job: draft, remove_duplication_in_progress, remove_duplication_completed, generate_products_in_progress, generate_products_completed';
comment on column mc_batch_job.completed_at is 'Completion time of the job';
comment on column mc_batch_job.created_at is 'Creation time of the job';
comment on column mc_batch_job.updated_at is 'Last update time of the job';