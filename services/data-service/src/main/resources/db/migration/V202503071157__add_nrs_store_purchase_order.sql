create table if not exists mc_nrs_store_purchase_order (
    id uuid primary key default uuid_generate_v4(),
    nrs_store_id bigint,
    order_created_at timestamptz,
    order_completed_at timestamptz,
    sku varchar(255),
    upc varchar(255),
    sku_package_size bigint,
    upc_type varchar(255),
    sku_quantity bigint,
    created_at timestamptz default now(),
    updated_at timestamptz
);

-- Add comments to key fields
COMMENT ON COLUMN mc_nrs_store_purchase_order.nrs_store_id IS 'NRS store ID, references store information';
COMMENT ON COLUMN mc_nrs_store_purchase_order.order_created_at IS 'Order creation timestamp';
COMMENT ON COLUMN mc_nrs_store_purchase_order.order_completed_at IS 'Order completion timestamp';
COMMENT ON COLUMN mc_nrs_store_purchase_order.sku IS 'Stock Keeping Unit code of the product in Mercaso warehouse';
COMMENT ON COLUMN mc_nrs_store_purchase_order.upc IS 'Universal Product Code';
COMMENT ON COLUMN mc_nrs_store_purchase_order.sku_package_size IS 'Size of the SKU package';
COMMENT ON COLUMN mc_nrs_store_purchase_order.upc_type IS 'Type of UPC, such as "EACH_UPC" or "CASE_UPC"';
COMMENT ON COLUMN mc_nrs_store_purchase_order.sku_quantity IS 'Quantity of SKUs that store purchased';


-- Add index for faster query
CREATE INDEX idx_mc_nrs_store_purchase_order_sku ON mc_nrs_store_purchase_order (sku);
CREATE INDEX idx_mc_nrs_store_purchase_order_upc ON mc_nrs_store_purchase_order (upc);

-- Add composite index for querying by nrs_store_id and order_completed_at
CREATE INDEX idx_mc_nrs_store_purchase_order_store_completed_at ON mc_nrs_store_purchase_order (nrs_store_id, order_completed_at);


