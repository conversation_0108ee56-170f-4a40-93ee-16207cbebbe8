create table if not exists master_catalog_order (
    id uuid primary key,
    state varchar(255) not null,
    created_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    closed_at timestamp with time zone
);

create index if not exists idx_master_catalog_order_state on master_catalog_order (state);

create table if not exists master_catalog_order_line_item (
    id uuid primary key,
    master_catalog_order_id uuid not null,
    master_catalog_raw_data_id uuid not null,
    quantity float not null,
    created_at timestamp with time zone not null default now(),
    updated_at timestamp with time zone not null default now()
);

create index if not exists idx_master_catalog_order_line_item_order_id on master_catalog_order_line_item (master_catalog_order_id);
create index if not exists idx_master_catalog_order_line_item_raw_data_id on master_catalog_order_line_item (master_catalog_raw_data_id);