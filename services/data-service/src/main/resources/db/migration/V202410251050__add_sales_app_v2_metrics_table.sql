CREATE TYPE date_type_enum AS ENUM ('<PERSON>A<PERSON>Y', 'MONTHLY');
CREATE TYPE item_recommendation_type_enum AS ENUM ('UNPURCHASED','PURCHASED');

CREATE TABLE metrics_order_item_recommendation (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_id VARCHAR(255) NOT NULL,
    rank INTEGER NOT NULL,
    sku VARCHAR(100) NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    name VARCHAR(255) NOT NULL,
    department VARCHAR(255) NOT NULL,
    filter_department VARCHAR(255) NOT NULL,
    last_ordered_date TIMESTAMP,
    type item_recommendation_type_enum NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE metrics_order_amount (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_id VARCHAR(255) NOT NULL,
    date_type date_type_enum NOT NULL ,
    date TIMESTAMP NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL,
    total_quantity INTEGER NOT NULL,
    unique_items INTEGER NOT NULL,
    items TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE metrics_order_frequency (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_id VARCHAR(255) NOT NULL,
    date_type date_type_enum NOT NULL,
    date TIMESTAMP NOT NULL,
    frequency INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE metrics_order_discount (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_id VARCHAR(255) NOT NULL,
    date_type date_type_enum NOT NULL,
    date TIMESTAMP NOT NULL,
    discount_order_amount DECIMAL(12, 2) NOT NULL,
    total_order_amount DECIMAL(12, 2) NOT NULL,
    discount_order_count INTEGER NOT NULL,
    total_order_count INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE metrics_order_department_distribution (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_id VARCHAR(255) NOT NULL,
    date_type date_type_enum NOT NULL,
    date TIMESTAMP NOT NULL,
    deps TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Single column indexes for address_id
CREATE INDEX idx_metrics_order_item_recommendation_address_id 
    ON metrics_order_item_recommendation(address_id);

-- Composite indexes for tables with date_type and date
CREATE INDEX idx_metrics_order_amount_composite 
    ON metrics_order_amount(address_id, date_type, date);

CREATE INDEX idx_metrics_order_frequency_composite 
    ON metrics_order_frequency(address_id, date_type, date);

CREATE INDEX idx_metrics_order_discount_composite 
    ON metrics_order_discount(address_id, date_type, date);

CREATE INDEX idx_metrics_order_department_distribution_composite 
    ON metrics_order_department_distribution(address_id, date_type, date);