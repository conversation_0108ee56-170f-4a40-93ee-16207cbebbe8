create table if not exists metrics_territory_target_list
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    address_name  VARCHAR(255) NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    zip_code      VARCHAR(255) NOT NULL,
    label         VARCHAR(255) NOT NULL,
    latitude      double precision,
    longitude     double precision
);

CREATE INDEX idx_metrics_territory_target_list_zip_code ON metrics_territory_target_list (zip_code);