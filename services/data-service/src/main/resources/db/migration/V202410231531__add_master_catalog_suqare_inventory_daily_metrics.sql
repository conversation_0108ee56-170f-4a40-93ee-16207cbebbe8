create table if not exists master_catalog_suqare_inventory_daily_metrics
(
    id                         uuid PRIMARY KEY,
    master_catalog_raw_data_id uuid        NOT NULL,
    store_id                   uuid        NOT NULL,
    quantity                   bigint      NOT NULL,
    in_stock_quantity          bigint      NOT NULL,
    out_stock_quantity         bigint      NOT NULL,
    date                       timestamptz NOT NULL
);
create index if not exists master_catalog_suqare_inventory_daily_metrics_idx on master_catalog_suqare_inventory_daily_metrics (master_catalog_raw_data_id);

create index if not exists master_catalog_suqare_inventory_daily_metrics_store_id on master_catalog_suqare_inventory_daily_metrics (store_id);

create index if not exists master_catalog_suqare_inventory_daily_metrics_date on master_catalog_suqare_inventory_daily_metrics (date);