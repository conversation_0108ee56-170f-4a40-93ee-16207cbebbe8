create table if not exists price_recommendation
(
    id uuid primary key default uuid_generate_v4(),
    store_id varchar(255),
    product_id varchar(255),
    sku varchar(255),
    name varchar(255),
    upc varchar(255),
    department_id varchar(64),
    department varchar(255),
    last_purchase_price varchar(32),
    high_price varchar(32),
    low_price varchar(32),
    avg_price varchar(32),
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now()
);

create index if not exists price_recommendation_store_and_department_idx on price_recommendation (store_id, department_id);

comment on column price_recommendation.store_id is 'Store ID of ecommerce';
comment on column price_recommendation.product_id is 'Product ID of ecommerce';
comment on column price_recommendation.department_id is 'Department ID of ims';
comment on column price_recommendation.high_price is '95% percentile price of transaction of NRS per sku';
comment on column price_recommendation.low_price is '5% percentile price of transaction of NRS per sku';
comment on column price_recommendation.avg_price is '50% percentile price of transaction of NRS per sku';
