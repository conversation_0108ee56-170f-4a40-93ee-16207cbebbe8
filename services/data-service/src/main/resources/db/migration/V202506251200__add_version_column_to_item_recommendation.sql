-- Add version column to item_recommendation table
ALTER TABLE item_recommendation ADD COLUMN version VARCHAR(255) DEFAULT 'V1';

-- Add comment for the new column
COMMENT ON COLUMN item_recommendation.version IS 'The version of the item recommendation, default is V1';

-- Create composite index for store_id and version to optimize the search query
CREATE INDEX IF NOT EXISTS item_recommendation_store_id_version_idx ON item_recommendation (store_id, version);