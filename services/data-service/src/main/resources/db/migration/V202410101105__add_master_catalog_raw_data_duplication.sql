create table if not exists master_catalog_raw_data_duplication
(
    id                uuid primary key,
    upc               varchar(64) not null,
    duplication_group uuid        not null,
    created_at        timestamp   not null default now(),
    updated_at        timestamp   not null default now()
);

create index if not exists raw_data_duplication_upc_idx on master_catalog_raw_data_duplication (upc);
create index if not exists raw_data_duplication_group_idx on master_catalog_raw_data_duplication (duplication_group);