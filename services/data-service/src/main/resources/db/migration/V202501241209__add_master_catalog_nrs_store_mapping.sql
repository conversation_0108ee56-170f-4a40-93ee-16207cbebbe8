create table if not exists master_catalog_nrs_store_mapping
(
    id         uuid primary key,
    store_id   uuid not null,
    nrs_store_id  bigint,
    created_at timestamptz default now(),
    updated_at timestamptz default now()
);

create index if not exists idx_master_catalog_nrs_store_mapping_store_id
    on master_catalog_nrs_store_mapping (store_id);

create index if not exists idx_master_catalog_nrs_store_mapping_nrs_store_id
    on master_catalog_nrs_store_mapping (nrs_store_id);

comment on table master_catalog_nrs_store_mapping is 'Table to store master_catalog_nrs_store_mapping for mapping the store id with nrs store id';
comment on column master_catalog_nrs_store_mapping.store_id is 'Store id of the master catalog';
comment on column master_catalog_nrs_store_mapping.nrs_store_id is 'NRS store id of the store';
