ALTER TABLE master_catalog_replenishment_forecast
RENAME TO replenishment_recommendation;

ALTER TABLE replenishment_recommendation ADD COLUMN product_id VARCHAR(64);
COMMENT ON COLUMN replenishment_recommendation.product_id IS 'The product id of the product that is being replenished';

ALTER TABLE replenishment_recommendation ADD COLUMN last_order_time timestamp with time zone;
COMMENT ON COLUMN replenishment_recommendation.last_order_time IS 'The last time the product was ordered from mercaso';

ALTER TABLE replenishment_recommendation ADD COLUMN model_type VARCHAR(255);
COMMENT ON COLUMN replenishment_recommendation.model_type IS 'The type of model that was used to generate the recommendation, e.g. "MERCAO_ORDER_BASED"';

ALTER TABLE replenishment_recommendation ADD COLUMN reason VARCHAR(255);
COMMENT ON COLUMN replenishment_recommendation.reason IS 'The reason for the recommendation';

ALTER TABLE replenishment_recommendation ADD COLUMN last_order_quantity INTEGER;
COMMENT ON COLUMN replenishment_recommendation.last_order_quantity IS 'The quantity of the product that was ordered from mercaso';

ALTER TABLE replenishment_recommendation ADD COLUMN department_id VARCHAR(64);
COMMENT ON COLUMN replenishment_recommendation.department_id IS 'The department id of the department that the product belongs to';