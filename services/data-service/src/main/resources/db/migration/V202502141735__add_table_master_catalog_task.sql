create table if not exists master_catalog_product_generation_task
(
    id           uuid PRIMARY KEY,
    name         varchar(256) NOT NULL,
    status       varchar(256) NOT NULL,
    completed_at timestamptz,
    created_at   timestamptz  NOT NULL,
    updated_at   timestamptz  NOT NULL
);

comment on column master_catalog_product_generation_task.id is 'Unique identifier of the task';
comment on column master_catalog_product_generation_task.name is 'Name of the task';
comment on column master_catalog_product_generation_task.status is 'Status of the task: draft, remove_duplication_in_progress, remove_duplication_completed, generate_products_in_progress, generate_products_completed';
comment on column master_catalog_product_generation_task.completed_at is 'Completion time of the task';
comment on column master_catalog_product_generation_task.created_at is 'Creation time of the task';
comment on column master_catalog_product_generation_task.updated_at is 'Last update time of the task';