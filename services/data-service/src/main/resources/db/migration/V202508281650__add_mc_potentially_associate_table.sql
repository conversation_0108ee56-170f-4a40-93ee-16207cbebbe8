create table if not exists mc_potentially_associate_product
(
    id                                          uuid         PRIMARY KEY,
    product_id                                  uuid,
    product_name                                varchar(255),
    product_upc                                 varchar(64),
    potentially_associate_product_id            uuid,
    potentially_associate_product_name          varchar(255),
    potentially_associate_product_upc           varchar(64),
    status                                      varchar(64)     NOT NULL,
    task_id                                     uuid            NOT NULL,
    job_id                                      uuid            NOT NULL,
    associated                                  boolean,
    created_at                                  timestamptz     NOT NULL,
    updated_at                                  timestamptz,
    created_by                                  varchar(64)     NOT NULL,
    updated_by                                  varchar(64)
);

comment on column mc_potentially_associate_product.id is 'Unique identifier';
comment on column mc_potentially_associate_product.product_id is 'Unique identifier of the product';
comment on column mc_potentially_associate_product.product_name is 'Name of the product';
comment on column mc_potentially_associate_product.product_upc is 'Upc of the product';
comment on column mc_potentially_associate_product.potentially_associate_product_id is 'Unique identifier of the potentially associate product';
comment on column mc_potentially_associate_product.potentially_associate_product_name is 'Name of the potentially associate product';
comment on column mc_potentially_associate_product.potentially_associate_product_upc is 'Upc of the potentially associate product';
comment on column mc_potentially_associate_product.status is 'status: 1: PENDING_REVIEW, 2: IN_PROCESS, 3: COMPLETED';
comment on column mc_potentially_associate_product.task_id is 'the task id that the record belongs to';
comment on column mc_potentially_associate_product.job_id is 'the job that the record belongs to';
comment on column mc_potentially_associate_product.created_at is 'Creation time of the record';
comment on column mc_potentially_associate_product.updated_at is 'Last update time of the record';
comment on column mc_potentially_associate_product.created_by is 'User who created the record';
comment on column mc_potentially_associate_product.updated_by is 'User who last updated the record';
