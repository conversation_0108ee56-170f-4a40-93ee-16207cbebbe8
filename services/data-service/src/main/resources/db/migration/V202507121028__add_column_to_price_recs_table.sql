alter table price_recommendation
    add column new_item_flag INTEGER,
    add column latest_order BOOLEAN,
    add column ever_green BOOLEAN;

comment
on column price_recommendation.new_item_flag is '1: new item; 0: not new item; indicate if purchasing the item for the first time in the latest order';
comment
on column price_recommendation.latest_order is 'true: latest order; false: not latest order; indicate if the item is in the latest order';
comment
on column price_recommendation.ever_green is 'true: ever green item; false: not ever green item; indicate if the item belong to ever green list';