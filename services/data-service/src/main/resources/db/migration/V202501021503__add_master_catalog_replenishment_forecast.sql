create table if not exists master_catalog_replenishment_forecast
(
    id uuid primary key,
    store_id uuid not null,
    sku varchar(32),
    name varchar(255),
    recommended_quantity numeric not null,
    next_order_time timestamptz,
    metadata jsonb,
    batch_number varchar(255),
    created_at timestamptz default now(),
    updated_at timestamptz default now()
);

comment on table master_catalog_replenishment_forecast is 'Table to store replenishment forecast for master catalog';
comment on column master_catalog_replenishment_forecast.store_id is 'Store id of the item';
comment on column master_catalog_replenishment_forecast.sku is 'SKU of the item';
comment on column master_catalog_replenishment_forecast.name is 'Name of the item';
comment on column master_catalog_replenishment_forecast.recommended_quantity is 'Recommended quantity to order';
comment on column master_catalog_replenishment_forecast.next_order_time is 'Next order time of the item';
comment on column master_catalog_replenishment_forecast.metadata is 'Metadata of the replenishment forecast used to calculate the forecast';
comment on column master_catalog_replenishment_forecast.batch_number is 'Query the recommended prediction data of the same batch. For example：20241201';

create index if not exists idx_master_catalog_replenishment_forecast_store_id on master_catalog_replenishment_forecast (store_id);
create index if not exists idx_master_catalog_replenishment_forecast_batch_number on master_catalog_replenishment_forecast (batch_number);