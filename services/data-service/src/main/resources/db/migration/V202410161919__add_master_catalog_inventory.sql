create table if not exists master_catalog_inventory (
    id uuid primary key,
    state varchar(255) not null,
    quantity int not null,
    master_catalog_raw_data_id uuid not null,
    created_at timestamp not null default now(),
    updated_at timestamp not null default now()
);

create index if not exists master_catalog_inventory_master_catalog_raw_data_id_idx on master_catalog_inventory (master_catalog_raw_data_id);