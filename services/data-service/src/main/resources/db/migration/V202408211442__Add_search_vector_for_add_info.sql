ALTER TABLE public.address_info ADD COLUMN address_name_search tsvector;

UPDATE public.address_info SET address_name_search = to_tsvector('english', address_name);

CREATE INDEX address_name_search_idx ON public.address_info USING GIN (address_name_search);

CREATE OR <PERSON><PERSON>LACE FUNCTION address_name_search_trigger() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
    NEW.address_name_search := to_tsvector('english', NEW.address_name);
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

CREATE TRIGGER address_name_search_update
    BEFORE INSERT OR UPDATE ON public.address_info
    FOR EACH ROW EXECUTE FUNCTION address_name_search_trigger();