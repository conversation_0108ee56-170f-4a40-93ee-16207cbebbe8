create table if not exists master_catalog_square_inventory_change
(
    id                         uuid primary key,
    master_catalog_raw_data_id uuid                     not null,
    type                       varchar(128)             not null,
    from_state                 varchar(255)             not null,
    to_state                   varchar(255)             not null,
    quantity                   bigint                   not null,
    occurred_at                timestamp with time zone not null,
    created_at                 timestamp with time zone not null default now(),
    updated_at                 timestamp with time zone not null default now()
);

create index if not exists idx_master_catalog_square_inventory_change_raw_data_id on master_catalog_square_inventory_change (master_catalog_raw_data_id);