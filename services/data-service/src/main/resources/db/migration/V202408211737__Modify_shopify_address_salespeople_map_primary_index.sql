DROP TABLE shopify.address_salespeople_map;

CREATE TABLE shopify.shipping_addresses
(
    id                             TEXT PRIMARY KEY,
    address                        JSONB,
    formatted_address              TEXT,
    formatted_latitude             DOUBLE PRECISION,
    formatted_longitude            DOUBLE PRECISION,
    google_normalized_address_full JSONB,
    created_at                     TIMESTAMPTZ,
    updated_at                     TIMESTAMPTZ
);

CREATE TABLE shopify.address_salespeople_map
(
    address_id                     TEXT,
    salespeople                    VARCHAR(255),
    address                        JSONB,
    formatted_address              TEXT,
    formatted_latitude             DOUBLE PRECISION,
    formatted_longitude            DOUBLE PRECISION,
    google_normalized_address_full JSONB,
    created_at                     TIMESTAMPTZ,
    updated_at                     TIMESTAMPTZ,
    PRIMARY KEY (address_id, salespeople)
);

CREATE INDEX idx_address_salespeople_map_address_id ON shopify.address_salespeople_map (address_id);
CREATE INDEX idx_address_salespeople_map_salespeople ON shopify.address_salespeople_map (salespeople);