create table if not exists mc_task (
    id uuid primary key,
    status varchar(256),
    "type" varchar(256),
    job_id uuid not null,
    assigned_by varchar(256),
    assigned_to varchar(256),
    created_at timestamptz default now(),
    updated_at timestamptz default now()
);

comment on column mc_task.id is 'Unique identifier of the task';
comment on column mc_task.status is 'Status of the task: PENDING, ASSIGNED, COMPLETED';
comment on column mc_task.type is 'type of the task: DUPLICATION_IN_BATCH, DUPLICATION_WITH_PRODUCT';
comment on column mc_task.job_id is 'the job to which the task belongs';
comment on column mc_task.assigned_by is 'adminer of the task';
comment on column mc_task.assigned_to is 'admin can assign the task to operator';
comment on column mc_task.created_at is 'creation time of the task';
comment on column mc_task.updated_at is 'last update time of the task';