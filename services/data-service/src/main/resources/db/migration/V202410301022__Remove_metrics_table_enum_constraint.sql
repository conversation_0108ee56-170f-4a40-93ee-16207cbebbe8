ALTER TABLE metrics_order_item_recommendation
    DROP CONSTRAINT IF EXISTS item_recommendation_type_enum;

ALTER TABLE metrics_order_amount
    DROP CONSTRAINT IF EXISTS date_type_enum;

ALTER TABLE metrics_order_frequency
    DROP CONSTRAINT IF EXISTS date_type_enum;

ALTER TABLE metrics_order_discount
    DROP CONSTRAINT IF EXISTS date_type_enum;

ALTER TABLE metrics_order_department_distribution
    DROP CONSTRAINT IF EXISTS date_type_enum;


ALTER TABLE metrics_order_item_recommendation
    ALTER COLUMN type TYPE varchar(255) USING type::varchar;

ALTER TABLE metrics_order_amount
    ALTER COLUMN date_type TYPE varchar(255) USING date_type::varchar;

ALTER TABLE metrics_order_frequency
    ALTER COLUMN date_type TYPE varchar(255) USING date_type::varchar;

ALTER TABLE metrics_order_discount
    ALTER COLUMN date_type TYPE varchar(255) USING date_type::varchar;

ALTER TABLE metrics_order_department_distribution
    ALTER COLUMN date_type TYPE varchar(255) USING date_type::varchar;


DROP TYPE IF EXISTS item_recommendation_type_enum;
DROP TYPE IF EXISTS date_type_enum;