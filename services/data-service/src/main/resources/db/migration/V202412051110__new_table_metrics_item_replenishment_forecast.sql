create table if not exists metrics_item_replenishment_forecast
(
    id                   uuid primary key,
    address_id           varchar(64),
    sku                  varchar(32),
    name                 varchar(255),
    order_avg_days       numeric,
    avg_daily_demand     numeric,
    recommended_quantity decimal(10, 2),
    next_order_time timestamptz,
    department           varchar(255),
    created_at           timestamptz default now(),
    updated_at           timestamptz default now()
);

comment on table metrics_item_replenishment_forecast is 'Table to store replenishment forecast for items';
comment on column metrics_item_replenishment_forecast.order_avg_days is 'Total interval days of order time in the past three months / Number of orders in the past six months';
comment on column metrics_item_replenishment_forecast.avg_daily_demand is 'The total number of items ordered in the past three months / Total interval days of order time in the past six months';
comment on column metrics_item_replenishment_forecast.recommended_quantity is 'avg_daily_demand * order_avg_days';
comment on column metrics_item_replenishment_forecast.next_order_time is 'Last order time + order_avg_days';
comment on column metrics_item_replenishment_forecast.department is 'Department of the item';