-- Add price fields to item_recommendation table
ALTER TABLE item_recommendation ADD COLUMN high_price VARCHAR(255);
ALTER TABLE item_recommendation ADD COLUMN low_price VARCHAR(255);
ALTER TABLE item_recommendation ADD COLUMN avg_price VARCHAR(255);

-- Add comments for the new columns
COMMENT ON COLUMN item_recommendation.high_price IS 'The highest price of the recommended item';
COMMENT ON COLUMN item_recommendation.low_price IS 'The lowest price of the recommended item';
COMMENT ON COLUMN item_recommendation.avg_price IS 'The average price of the recommended item';
