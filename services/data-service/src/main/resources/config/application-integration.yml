host:
  db_server: postgres-data-service:5432
spring:
  cloud:
    vault:
      enabled: false
  datasource:
    username: data_user
    password: mercaso
    url: jdbc:postgresql://${host.db_server}/data_service
security:
  enable-method-security: false
  public-paths: /**
otel:
  exporter:
    otlp:
      endpoint: http://localhost:4317
third-party:
  finale:
    graphql-api-url: https://app.finaleinventory.com/mercaso/api/graphql
    token:
square:
  cipher:
    key: LHuWR+c3fJAy7PibZR7kY4PLgbCHJ3rc
