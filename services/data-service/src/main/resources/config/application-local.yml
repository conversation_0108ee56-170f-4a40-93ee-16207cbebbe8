host:
  db_server: localhost:5436
spring:
  cloud:
    vault:
      enabled: false
  datasource:
    username: data_user
    password: mercaso
    url: jdbc:postgresql://${host.db_server}/data_service

otel:
  exporter:
    otlp:
      endpoint: http://localhost:4317

third-party:
  finale:
    graphql-api-url: http://localhost:4318
    token: fake-token
  pinecone:
    apiKey:

square:
  cipher:
    key: LHuWR+c3fJAy7PibZR7kY4PLgbCHJ3rc

mercaso:
  document:
    operations:
      enabled: true
      storage:
        bucket-name: mercaso-application-dev
        square-folder: master-catalog/square/

security:
  enable-method-security: false
  public-paths: /**