package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class UserRestApi extends IntegrationTestRestUtil {

    public UserRestApi(Environment environment) {
        super(environment);
    }

    private static final String USER_ADDRESS_METRICS_V2 = "/metrics/user/v2/addresses";

    public CustomPage<SearchStoreAddressDto> searchUserAddressesV2(String keyword, int pageNumber, int pageSize) {
        Map<String, Object> params = Map.of("keyword", keyword, "pageNumber", pageNumber, "pageSize", pageSize);

        ParameterizedTypeReference<CustomPage<SearchStoreAddressDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(USER_ADDRESS_METRICS_V2, responseType, params).getBody();
    }
}
