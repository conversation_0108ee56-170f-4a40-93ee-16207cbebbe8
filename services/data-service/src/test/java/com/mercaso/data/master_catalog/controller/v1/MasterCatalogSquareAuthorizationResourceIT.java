package com.mercaso.data.master_catalog.controller.v1;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.adaptor.impl.SquareApiAdapterImpl;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.utils.dto.MasterCatalogSquareAuthorizationRequestDtoUtils;
import com.mercaso.data.master_catalog.utils.dto.SquareObtainTokenResponseUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogSquareAuthorizationResourceApiUtils;
import org.springframework.boot.test.mock.mockito.MockBean;

class MasterCatalogSquareAuthorizationResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogSquareAuthorizationResourceApiUtils masterCatalogSquareAuthorizationResourceApiUtils;

    @Autowired
    private MasterCatalogSquareAuthorizationRequestDtoUtils masterCatalogSquareAuthorizationRequestDtoUtils;

    @Autowired
    private MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;

    @Autowired
    private SquareObtainTokenResponseUtils squareObtainTokenResponseUtils;

    @Autowired
    private CipherUtility cipherUtility;

    @MockBean
    private SquareApiAdapter squareApiAdapter;

    @Test
    @DisplayName("Create Square Authorization")
    public void createSquareAuthorization() throws Exception {
        UUID storeId = UUID.randomUUID();
        String applicationId = UUID.randomUUID().toString();
        String applicationSecret = UUID.randomUUID().toString();
        UUID state = UUID.randomUUID();

        masterCatalogSquareAuthorizationResourceApiUtils.createSquareAuthorization(
          masterCatalogSquareAuthorizationRequestDtoUtils.createMasterCatalogSquareAuthorizationRequestDto(storeId, state,
            applicationId, applicationSecret));
    }

    @Test
    @DisplayName("Refresh Square Authorization")
    public void refreshSquareAuthorization() throws Exception {
        String accessToken = UUID.randomUUID().toString();
        Instant accessTokenExpiresAt = Instant.now().plusSeconds(200000);

        MasterCatalogSquareAuthorization masterCatalogSquareAuthorization = new MasterCatalogSquareAuthorization();
        masterCatalogSquareAuthorization.setStoreId(UUID.randomUUID());
        masterCatalogSquareAuthorization.setState(UUID.randomUUID());
        masterCatalogSquareAuthorization.setEncryptedApplicationId(cipherUtility.encrypt(UUID.randomUUID().toString()));
        masterCatalogSquareAuthorization.setEncryptedApplicationSecret(cipherUtility.encrypt(UUID.randomUUID().toString()));
        masterCatalogSquareAuthorization.setPermissions("READ,WRITE");
        masterCatalogSquareAuthorization.setEncryptedRefreshToken(cipherUtility.encrypt(UUID.randomUUID().toString()));
        masterCatalogSquareAuthorization.setAccessTokenExpiresAt(Instant.now().plusSeconds(83400));

        MasterCatalogSquareAuthorization saved = masterCatalogSquareAuthorizationRepository.save(
          masterCatalogSquareAuthorization);

        SquareObtainTokenResponse squareObtainTokenResponse = squareObtainTokenResponseUtils.createSquareObtainTokenResponse(
          accessToken, accessTokenExpiresAt);

        when(squareApiAdapter.obtainTokenByRefreshToken(any(String.class), any(String.class), any(String.class))).thenReturn(
          squareObtainTokenResponse);

        masterCatalogSquareAuthorizationResourceApiUtils.refreshSquareAuthorization();

        masterCatalogSquareAuthorizationRepository.findById(saved.getId()).ifPresent(updated -> {
            Assertions.assertEquals(accessToken,
              cipherUtility.decrypt(updated.getEncryptedAccessToken()));
            Assertions.assertEquals(accessTokenExpiresAt.truncatedTo(ChronoUnit.SECONDS),
              updated.getAccessTokenExpiresAt().truncatedTo(ChronoUnit.SECONDS));
        });
    }
}