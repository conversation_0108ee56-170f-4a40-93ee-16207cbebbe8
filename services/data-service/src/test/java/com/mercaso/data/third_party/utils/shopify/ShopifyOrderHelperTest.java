package com.mercaso.data.third_party.utils.shopify;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.mercaso.data.third_party.dto.shopify.ShopifyOrderDto;
import com.mercaso.data.third_party.dto.shopify.ShopifyOrderLineItemDto;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrderEntity;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersLineItemEntity;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderEntityMock;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderLineItemEntityMock;
import java.util.List;
import org.junit.Test;

public class ShopifyOrderHelperTest {

    @Test
    public void testBuildShopifyOrderDto() {

        ShopifyOrderEntity entity = ShopifyOrderEntityMock.shopifyOrderMock();

        ShopifyOrderDto result = ShopifyOrderHelper.buildShopifyOrderDto(entity);

        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getName(), result.getName());
        assertEquals(entity.getUpdatedAt(), result.getUpdatedAt());
        assertEquals(entity.getCreatedAt(), result.getCreatedAt());
        assertEquals(entity.getAdminGraphqlApiId(), result.getAdminGraphqlApiId());
        assertNotNull(result.getShippingAddress());
        assertEquals(entity.getLineItems().size(),result.getLineItems().size());
    }

    @Test
    public void testBuildShopifyOrderDtoHasLineItems() {

        ShopifyOrderEntity entity = ShopifyOrderEntityMock.shopifyOrderMock();

        ShopifyOrdersLineItemEntity lineItemEntity = ShopifyOrderLineItemEntityMock.ordersLineItemMock();
        entity.setLineItems(List.of(lineItemEntity,lineItemEntity));


        ShopifyOrderDto result = ShopifyOrderHelper.buildShopifyOrderDto(entity);

        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getName(), result.getName());
        assertEquals(entity.getUpdatedAt(), result.getUpdatedAt());
        assertEquals(entity.getCreatedAt(), result.getCreatedAt());
        assertEquals(entity.getAdminGraphqlApiId(), result.getAdminGraphqlApiId());
        assertNotNull(result.getShippingAddress());
        assertEquals(entity.getLineItems().size(),result.getLineItems().size());
        assertEquals(1,result.getLineItems().getFirst().getLineNumber());
        assertEquals(2,result.getLineItems().getLast().getLineNumber());
    }

    @Test
    public void testBuildShopifyOrderLineItemDto() {

        ShopifyOrdersLineItemEntity entity = ShopifyOrderLineItemEntityMock.ordersLineItemMock();

        ShopifyOrderLineItemDto result = ShopifyOrderHelper.buildShopifyOrderLineItemDto(entity);

        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getName(), result.getName());
        assertEquals(entity.getQuantity(), result.getQuantity());
        assertEquals(entity.getSku(), result.getSku());
        assertEquals(entity.getTitle(), result.getTitle());
        assertEquals(entity.getAdminGraphqlApiId(), result.getAdminGraphqlApiId());
        assertEquals(entity.isRefunded(),result.isRefunded());
    }

}
