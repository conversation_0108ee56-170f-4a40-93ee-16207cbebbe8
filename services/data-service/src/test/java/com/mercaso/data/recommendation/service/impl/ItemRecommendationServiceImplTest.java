package com.mercaso.data.recommendation.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.RecommendationReasonDto;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.mapper.ItemRecommendationMapper;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
class ItemRecommendationServiceImplTest {

  @Mock
  private ItemRecommendationRepository itemRecommendationRepository;

  @Mock
  private ItemRecommendationMapper itemRecommendationMapper;

  @InjectMocks
  private ItemRecommendationServiceImpl itemRecommendationService;

  private String storeId;
  private String version;
  private Integer pageNumber;
  private Integer pageSize;
  private Pageable pageable;
  private List<ItemRecommendation> recommendations;
  private Page<ItemRecommendation> recommendationPage;
  private List<ItemRecommendationDto> recommendationDtos;

  @BeforeEach
  void setUp() {
    // Initialize test data
    storeId = "store123";
    version = "V1";
    pageNumber = 0;
    pageSize = 10;
    pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "reasonValue"));

    // Create test entities
    ItemRecommendation recommendation1 = createRecommendation("sku1", "product1", "PURCHASE_RATE",
        "85.5", version);
    ItemRecommendation recommendation2 = createRecommendation("sku2", "product2", "VIEW_COUNT",
        "42.7", version);
    recommendations = Arrays.asList(recommendation1, recommendation2);
    recommendationPage = new PageImpl<>(recommendations, pageable, recommendations.size());

    // Create test DTOs
    ItemRecommendationDto dto1 = createRecommendationDto("sku1", "product1", "PURCHASE_RATE",
        86, version);
    ItemRecommendationDto dto2 = createRecommendationDto("sku2", "product2", "VIEW_COUNT",
        43, version);
    recommendationDtos = Arrays.asList(dto1, dto2);
  }

  @Test
  @DisplayName("Should return pageable response with recommendations when search is called")
  void shouldReturnPageableResponseWithRecommendationsWhenSearchIsCalled() {
    // Given
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, version, null, null, pageNumber,
        pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should return empty response when no recommendations found")
  void shouldReturnEmptyResponseWhenNoRecommendationsFound() {
    // Given
    Page<ItemRecommendation> emptyPage = new PageImpl<>(List.of(), pageable, 0);
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(emptyPage);

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, version, null, null, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(0, response.getData().size());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(0, response.getTotalPages());
    assertEquals(0, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));
  }

  @Test
  @DisplayName("Should filter by departmentId when departmentId is provided")
  void shouldFilterByDepartmentIdWhenDepartmentIdIsProvided() {
    // Given
    String departmentId = "dept123";
    String nonLegacyVersion = "V3"; // Use non-legacy version to enable departmentId filtering
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, nonLegacyVersion, departmentId, null, pageNumber,
        pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should ignore departmentId filter for V1 version")
  void shouldIgnoreDepartmentIdFilterForV1Version() {
    // Given
    String departmentId = "dept123";
    String v1Version = "V1";
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, v1Version, departmentId, null, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));
  }

  @Test
  @DisplayName("Should ignore departmentId filter for V2 version")
  void shouldIgnoreDepartmentIdFilterForV2Version() {
    // Given
    String departmentId = "dept123";
    String v2Version = "V2";
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, v2Version, departmentId, null, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));
  }

  @Test
  @DisplayName("Should use departmentId filter for V3 version when departmentId is provided")
  void shouldUseDepartmentIdFilterForV3VersionWhenDepartmentIdIsProvided() {
    // Given
    String departmentId = "dept123";
    String v3Version = "V3";
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, v3Version, departmentId, null, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));
  }

  @Test
  @DisplayName("Should filter by reason when single reason is provided")
  void shouldFilterByReasonWhenSingleReasonIsProvided() {
    // Given
    List<String> reasons = Arrays.asList("PURCHASE_RATE");
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, version, null, reasons, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should filter by multiple reasons when reasons list is provided")
  void shouldFilterByMultipleReasonsWhenReasonsListIsProvided() {
    // Given
    List<String> reasons = Arrays.asList("PURCHASE_RATE", "VIEW_COUNT", "TRENDING");
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, version, null, reasons, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should not filter by reasons when empty reasons list is provided")
  void shouldNotFilterByReasonsWhenEmptyReasonsListIsProvided() {
    // Given
    List<String> emptyReasons = new ArrayList<>();
    when(itemRecommendationRepository.findAll(any(Specification.class), any(Pageable.class)))
        .thenReturn(recommendationPage);
    when(itemRecommendationMapper.toDto(recommendations.get(0))).thenReturn(recommendationDtos.get(
        0));
    when(itemRecommendationMapper.toDto(recommendations.get(1))).thenReturn(recommendationDtos.get(
        1));

    // When
    PageableResponse<ItemRecommendationDto> response = itemRecommendationService.search(
        storeId, version, null, emptyReasons, pageNumber, pageSize);

    // Then
    assertNotNull(response);
    assertEquals(recommendationDtos, response.getData());
    assertEquals(pageNumber, response.getPageNumber());
    assertEquals(pageSize, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(2, response.getTotalElements());

    // Verify repository was called with specification
    verify(itemRecommendationRepository).findAll(any(Specification.class), eq(pageable));

    // Verify mapper was called for each recommendation
    verify(itemRecommendationMapper).toDto(recommendations.get(0));
    verify(itemRecommendationMapper).toDto(recommendations.get(1));
  }

  @Test
  @DisplayName("Should return distinct departments when getDepartments is called")
  void shouldReturnDistinctDepartmentsWhenGetDepartmentsIsCalled() {
    // Given
    List<Object[]> expectedDepartmentData = Arrays.asList(
        new Object[]{"DEPT001", "Electronics"},
        new Object[]{"DEPT002", "Clothing"},
        new Object[]{"DEPT003", "Food"}
    );
    when(itemRecommendationRepository.findDistinctDepartmentsWithIdByStoreIdAndVersion(storeId,
        version))
            .thenReturn(expectedDepartmentData);

    // When
    List<DepartmentDto> actualDepartments = itemRecommendationService.getDepartments(storeId,
        version);

    // Then
    assertNotNull(actualDepartments);
    assertEquals(3, actualDepartments.size());

    // Verify first department
    DepartmentDto firstDept = actualDepartments.get(0);
    assertEquals("DEPT001", firstDept.id());
    assertEquals("Electronics", firstDept.name());

    // Verify second department
    DepartmentDto secondDept = actualDepartments.get(1);
    assertEquals("DEPT002", secondDept.id());
    assertEquals("Clothing", secondDept.name());

    // Verify third department
    DepartmentDto thirdDept = actualDepartments.get(2);
    assertEquals("DEPT003", thirdDept.id());
    assertEquals("Food", thirdDept.name());

    // Verify repository was called with correct parameters
    verify(itemRecommendationRepository).findDistinctDepartmentsWithIdByStoreIdAndVersion(storeId,
        version);
  }

  @Test
  @DisplayName("Should return empty list when no departments found")
  void shouldReturnEmptyListWhenNoDepartmentsFound() {
    // Given
    List<Object[]> emptyDepartmentData = List.of();
    when(itemRecommendationRepository.findDistinctDepartmentsWithIdByStoreIdAndVersion(storeId,
        version))
            .thenReturn(emptyDepartmentData);

    // When
    List<DepartmentDto> actualDepartments = itemRecommendationService.getDepartments(storeId,
        version);

    // Then
    assertNotNull(actualDepartments);
    assertEquals(0, actualDepartments.size());

    // Verify repository was called with correct parameters
    verify(itemRecommendationRepository).findDistinctDepartmentsWithIdByStoreIdAndVersion(storeId,
        version);
  }

  /**
   * Helper method to create a test ItemRecommendation entity
   */
  private ItemRecommendation createRecommendation(String sku, String productId, String reason,
      String reasonValue, String version) {
    ItemRecommendation recommendation = new ItemRecommendation();
    recommendation.setStoreId(storeId);
    recommendation.setSkuNumber(sku);
    recommendation.setProductId(productId);
    recommendation.setReason(reason);
    recommendation.setReasonValue(reasonValue);
    recommendation.setVersion(version);
    return recommendation;
  }

  /**
   * Helper method to create a test ItemRecommendationRecordDto
   */
  private ItemRecommendationDto createRecommendationDto(String sku, String productId,
      String reasonType, Integer reasonValue, String version) {
    RecommendationReasonDto reason = RecommendationReasonDto.builder()
        .type(reasonType)
        .value(reasonValue)
        .build();

    return ItemRecommendationDto.builder()
        .sku(sku)
        .productId(productId)
        .reason(reason)
        .version(version)
        .highPrice("15.99")
        .lowPrice("12.50")
        .avgPrice("14.25")
        .build();
  }

}