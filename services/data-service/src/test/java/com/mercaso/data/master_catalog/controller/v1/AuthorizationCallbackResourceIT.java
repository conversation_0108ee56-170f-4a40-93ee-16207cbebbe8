package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.listener.SquareInventoryChangeListener.META_DATA_KEY_LOCATION_ID;
import static com.mercaso.data.master_catalog.utils.dto.MasterCatalogLocationDtoUtils.buildMasterCatalogLocationDto;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.mapper.MasterCatalogLocationMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.utils.dto.MasterCatalogSquareAuthorizationRequestDtoUtils;
import com.mercaso.data.master_catalog.utils.dto.SquareObtainTokenResponseUtils;
import com.mercaso.data.master_catalog.utils.resource_utils.AuthorizationCallbackResourceApiUtils;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogSquareAuthorizationResourceApiUtils;
import com.mercaso.data.utils.SerializationUtils;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

class AuthorizationCallbackResourceIT extends AbstractIT {

    @Autowired
    private AuthorizationCallbackResourceApiUtils authorizationCallbackResourceApiUtils;

    @Autowired
    private MasterCatalogSquareAuthorizationResourceApiUtils masterCatalogSquareAuthorizationResourceApiUtils;

    @Autowired
    private MasterCatalogSquareAuthorizationRequestDtoUtils masterCatalogSquareAuthorizationRequestDtoUtils;

    @Autowired
    private SquareObtainTokenResponseUtils squareObtainTokenResponseUtils;

    @Autowired
    private MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;

    @Autowired
    private MasterCatalogLocationRepository masterCatalogLocationRepository;

    @Autowired
    private MasterCatalogLocationMapper masterCatalogLocationMapper;

    @MockBean
    private SquareApiAdapter squareApiAdapter;

    @Test
    void executeSquareCallback() throws Exception {
        // Arrange
        String code = "sq0cgp-zsg8hlgsyaO2UpNeZov_uA";
        UUID state = UUID.randomUUID();
        UUID storeId = UUID.randomUUID();

        when(squareApiAdapter.obtainTokenByAuthorizationCode(any(), any(), any())).thenReturn(
            squareObtainTokenResponseUtils.createSquareObtainTokenResponse());

        masterCatalogSquareAuthorizationResourceApiUtils.createSquareAuthorization(
            masterCatalogSquareAuthorizationRequestDtoUtils.createMasterCatalogSquareAuthorizationRequestDto(
                storeId, state, "applicationId", "applicationSecret"));

        MasterCatalogLocationDto masterCatalogLocationDto1 = buildMasterCatalogLocationDto(storeId);
        MasterCatalogLocationDto masterCatalogLocationDto2 = buildMasterCatalogLocationDto(storeId);
        masterCatalogLocationDto2.setMetadata(SerializationUtils.readTree(
            "{\"locationId\": \"L5NWFG5507VYE\", \"merchantId\": \"ML48WJ0S28K5S\"}"));
        when(squareApiAdapter.listLocations(any())).thenReturn(List.of(masterCatalogLocationDto1, masterCatalogLocationDto2));

        masterCatalogLocationRepository.save(masterCatalogLocationMapper.toEntity(masterCatalogLocationDto2));

        // Act
        String result = authorizationCallbackResourceApiUtils.executeSquareCallback(code, state.toString());
        // Assert
        Assertions.assertEquals("Authorize Success!", result);

        MasterCatalogSquareAuthorization updated = masterCatalogSquareAuthorizationRepository.findByState(state);
        Assertions.assertNotNull(updated.getEncryptedAccessToken());
        Assertions.assertNotNull(updated.getEncryptedRefreshToken());
        Assertions.assertNotNull(updated.getAccessTokenExpiresAt());
        Assertions.assertEquals(state, updated.getState());

        List<MasterCatalogLocation> all = masterCatalogLocationRepository.findAllByStoreId(storeId);
        assert all
            .stream()
            .anyMatch(m -> m.getStoreId().equals(masterCatalogLocationDto1.getStoreId()));
        assert all
            .stream()
            .filter(m -> m.getMetadata()
                .get(META_DATA_KEY_LOCATION_ID)
                .asText()
                .equals(masterCatalogLocationDto2.getMetadata().get(META_DATA_KEY_LOCATION_ID).asText())).count() == 1;
    }
}