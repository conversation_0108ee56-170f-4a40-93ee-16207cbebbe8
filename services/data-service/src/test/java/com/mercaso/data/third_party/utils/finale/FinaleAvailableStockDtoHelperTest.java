package com.mercaso.data.third_party.utils.finale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockDto;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.mock.finale.FinaleAvailableStockEntityMock;
import org.junit.jupiter.api.Test;

class FinaleAvailableStockDtoHelperTest {


    @Test
    void testBuildFinaleAvailableStockDto() {

        FinaleAvailableStockEntity entity = FinaleAvailableStockEntityMock.customerEntityMock();
        FinaleAvailableStockDto result = FinaleAvailableStockDtoHelper.buildFinaleAvailableStockDto(entity);

        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getName(), result.getName());
        assertEquals(entity.getMfcQoh(), result.getMfcQoh());
        assertEquals(entity.getReservationsQoh(), result.getReservationsQoh());
        assertEquals(entity.getShopifyQoh(), result.getShopifyQoh());
        assertEquals(entity.getSku(), result.getSku());
        assertEquals(entity.getStockSublocations(), result.getStockSublocations());
        assertEquals(entity.getRecordLastUpdated(), result.getRecordLastUpdated());

    }

}