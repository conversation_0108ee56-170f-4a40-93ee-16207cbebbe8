package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryChangeRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSuqareInventoryDailyMetricRepository;
import com.mercaso.data.master_catalog.service.impl.SuqareInventoryMetricsServiceImpl;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class SuqareInventoryMetricsServiceTest {

    private MasterCatalogSquareInventoryRepository masterCatalogInventoryRepository;
    private MasterCatalogSquareInventoryChangeRepository masterCatalogSquareInventoryChangeRepository;
    private MasterCatalogSuqareInventoryDailyMetricRepository masterCatalogSuqareInventoryDailyMetricRepository;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private SuqareInventoryMetricsServiceImpl suqareInventoryMetricsService;

    private static final UUID UUID_STRING = UUID.fromString("13085758-c16c-49fd-894c-bcf5932cddec");
    private static final UUID STORE_ID = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        masterCatalogInventoryRepository = mock(MasterCatalogSquareInventoryRepository.class);
        masterCatalogSquareInventoryChangeRepository = mock(MasterCatalogSquareInventoryChangeRepository.class);
        masterCatalogSuqareInventoryDailyMetricRepository = mock(MasterCatalogSuqareInventoryDailyMetricRepository.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);

        suqareInventoryMetricsService = new SuqareInventoryMetricsServiceImpl(
            masterCatalogInventoryRepository,
            masterCatalogSquareInventoryChangeRepository,
            masterCatalogSuqareInventoryDailyMetricRepository,
            masterCatalogRawDataRepository
        );
    }

    @Test
    void calculateDailyMetrics_shouldCalculateAndSaveMetrics() {
        // Arrange
        Integer daysBefore = 7;
        Instant now = Instant.now();
        MasterCatalogSquareInventory latestInventory = createMockInventory(now, 10, UUID_STRING);

        when(masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc())
            .thenReturn(Optional.of(latestInventory));

        when(masterCatalogInventoryRepository.findAllByStateOrderByUpdatedAtDesc(any()))
            .thenReturn(List.of(latestInventory));

        MasterCatalogSquareInventoryChange change = createMockInventoryChange(now.minus(1, ChronoUnit.DAYS),
            UUID_STRING,
            "SOLD",
            2);
        when(masterCatalogSquareInventoryChangeRepository.findAllByOccurredAtBetween(any(), any()))
            .thenReturn(List.of(change));

        MasterCatalogRawData rawData = createMockRawData(UUID_STRING, STORE_ID);
        when(masterCatalogRawDataRepository.findAllById(anyList()))
            .thenReturn(List.of(rawData));

        // Act
        suqareInventoryMetricsService.calculateDailyMetrics(daysBefore);

        // Assert
        ArgumentCaptor<List<MasterCatalogSuqareInventoryDailyMetric>> metricsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSuqareInventoryDailyMetricRepository).saveAll(metricsCaptor.capture());

        List<MasterCatalogSuqareInventoryDailyMetric> savedMetrics = metricsCaptor.getValue();
        assertEquals(7, savedMetrics.size());

        MasterCatalogSuqareInventoryDailyMetric firstMetric = savedMetrics.get(0);
        assertEquals(UUID_STRING, firstMetric.getMasterCatalogRawDataId());
    }

    @Test
    void calculateDailyMetrics_shouldHandleNoInventoryFound() {
        // Arrange
        when(masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc())
            .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(IllegalStateException.class, () -> suqareInventoryMetricsService.calculateDailyMetrics(7));
    }

    @Test
    void calculateDailyMetrics_shouldHandleNoChanges() {
        // Arrange
        Integer daysBefore = 7;
        Instant now = Instant.now();
        MasterCatalogSquareInventory latestInventory = createMockInventory(now, 10, UUID.randomUUID());

        when(masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc())
            .thenReturn(Optional.of(latestInventory));

        when(masterCatalogInventoryRepository.findAllByStateOrderByUpdatedAtDesc(any()))
            .thenReturn(List.of(latestInventory));

        when(masterCatalogSquareInventoryChangeRepository.findAllByOccurredAtBetween(any(), any()))
            .thenReturn(new ArrayList<>());

        MasterCatalogRawData rawData = createMockRawData(latestInventory.getMasterCatalogRawDataId(), UUID.randomUUID());
        when(masterCatalogRawDataRepository.findAllById(anyList()))
            .thenReturn(List.of(rawData));

        // Act
        suqareInventoryMetricsService.calculateDailyMetrics(daysBefore);

        // Assert
        ArgumentCaptor<List<MasterCatalogSuqareInventoryDailyMetric>> metricsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSuqareInventoryDailyMetricRepository).saveAll(metricsCaptor.capture());

        List<MasterCatalogSuqareInventoryDailyMetric> savedMetrics = metricsCaptor.getValue();
        assertEquals(7, savedMetrics.size());

        for (MasterCatalogSuqareInventoryDailyMetric metric : savedMetrics) {
            assertEquals(0, metric.getInStockQuantity());
            assertEquals(0, metric.getOutStockQuantity());
            assertEquals(10, metric.getQuantity());
        }
    }

    @Test
    public void calculateYesterdayMetricsWithOneDayBefore() {
        MasterCatalogSquareInventory latestInventory = createMockInventory(Instant.now(), 10, UUID.randomUUID());

        when(masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc())
            .thenReturn(Optional.of(latestInventory));

        when(masterCatalogInventoryRepository.findAllByStateOrderByUpdatedAtDesc(any()))
            .thenReturn(List.of(latestInventory));

        when(masterCatalogSquareInventoryChangeRepository.findAllByOccurredAtBetween(any(), any()))
            .thenReturn(new ArrayList<>());

        MasterCatalogRawData rawData = createMockRawData(latestInventory.getMasterCatalogRawDataId(), UUID.randomUUID());
        when(masterCatalogRawDataRepository.findAllById(anyList()))
            .thenReturn(List.of(rawData));

        // Act
        suqareInventoryMetricsService.calculateYesterdayMetrics();

        // Assert
        verify(masterCatalogInventoryRepository).findTopByOrderByUpdatedAtDesc();
        verify(masterCatalogInventoryRepository).findAllByStateOrderByUpdatedAtDesc(any());
        verify(masterCatalogSquareInventoryChangeRepository).findAllByOccurredAtBetween(any(), any());
    }

    @Test
    public void initDailyMetrics() {
        MasterCatalogSquareInventory latestInventory = createMockInventory(Instant.now(), 10, UUID.randomUUID());

        when(masterCatalogInventoryRepository.findTopByOrderByUpdatedAtDesc())
            .thenReturn(Optional.of(latestInventory));

        when(masterCatalogInventoryRepository.findAllByStateOrderByUpdatedAtDesc(any()))
            .thenReturn(List.of(latestInventory));

        when(masterCatalogSquareInventoryChangeRepository.findAllByOccurredAtBetween(any(), any()))
            .thenReturn(new ArrayList<>());

        MasterCatalogRawData rawData = createMockRawData(latestInventory.getMasterCatalogRawDataId(), UUID.randomUUID());
        when(masterCatalogRawDataRepository.findAllById(anyList()))
            .thenReturn(List.of(rawData));

        suqareInventoryMetricsService.initDailyMetrics(7);

        verify(masterCatalogSuqareInventoryDailyMetricRepository).deleteAllInBatch();
    }

    private MasterCatalogSquareInventory createMockInventory(Instant updatedAt, int quantity, UUID id) {
        MasterCatalogSquareInventory inventory = new MasterCatalogSquareInventory();
        inventory.setUpdatedAt(updatedAt);
        inventory.setState("IN_STOCK");
        inventory.setQuantity(quantity);
        inventory.setMasterCatalogRawDataId(id);
        return inventory;
    }

    private MasterCatalogSquareInventoryChange createMockInventoryChange(Instant occurredAt, UUID rawDataId, String toState,
        int quantity) {
        MasterCatalogSquareInventoryChange change = new MasterCatalogSquareInventoryChange();
        change.setOccurredAt(occurredAt);
        change.setMasterCatalogRawDataId(rawDataId);
        change.setToState(toState);
        change.setQuantity(quantity);
        return change;
    }

    private MasterCatalogRawData createMockRawData(UUID id, UUID storeId) {
        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(id);
        rawData.setStoreId(storeId);
        return rawData;
    }
}
