package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MasterCatalogPotentiallyAssociateProductResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_POTENTIALLY_ASSOCIATE_PRODUCTS_SEARCH = "/master-catalog/v1/potentially-associate-products/search";

    public MasterCatalogPotentiallyAssociateProductResourceApiUtils(Environment environment) {
        super(environment);
    }

    public CustomPage<MasterCatalogPotentiallyAssociateProductDto> searchPotentiallyAssociateProducts(
            UUID taskId, PotentiallyAssociateProductStatus status, Integer page, Integer pageSize) {

        Map<String, String> params = new java.util.HashMap<>();
        params.put("taskId", taskId.toString());
        params.put("page", page.toString());
        params.put("pageSize", pageSize.toString());
        
        if (status != null) {
            params.put("status", status.name());
        }

        ParameterizedTypeReference<CustomPage<MasterCatalogPotentiallyAssociateProductDto>> responseType = 
                new ParameterizedTypeReference<>() {};

        return getEntityByMap(V1_POTENTIALLY_ASSOCIATE_PRODUCTS_SEARCH, responseType, params).getBody();
    }

    public CustomPage<MasterCatalogPotentiallyAssociateProductDto> searchPotentiallyAssociateProducts(
            UUID taskId, Integer page, Integer pageSize) {
        return searchPotentiallyAssociateProducts(taskId, null, page, pageSize);
    }

    public void updatePotentiallyAssociateProduct(
            UUID id, boolean associated) {
        
        Map<String, String> params = new java.util.HashMap<>();
        params.put("associated", String.valueOf(associated));

        String url = "/master-catalog/v1/potentially-associate-products/" + id;
        try {
            performRequest(url, params, null, HttpMethod.PUT);

        } catch (Exception e) {
            log.error("Error updating potentially association product: {}", e.getMessage(), e);
            throw e;
        }
    }

    public void submitPotentiallyAssociateProducts(List<UUID> potentiallyAssociateProductIds) {
        String url = "/master-catalog/v1/potentially-associate-products/submit";
        try {
            performRequest(url, null, potentiallyAssociateProductIds, HttpMethod.POST);
        } catch (Exception e) {
            log.error("Error submitting potentially association products: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
