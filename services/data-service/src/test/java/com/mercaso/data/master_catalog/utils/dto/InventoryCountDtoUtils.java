package com.mercaso.data.master_catalog.utils.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.mercaso.data.master_catalog.utils.SerializationTestUtils;
import java.util.List;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class InventoryCountDtoUtils {

    public static List<InventoryCountDto> buildInventoryCountDto() {
        return SerializationTestUtils.readValue(getObjects(), new TypeReference<>() {
        });
    }

    private static String getObjects() {
        return """
            [
                    {
                        "catalog_object_id": "2NHYS66EJW56YEKSPRDDRDJT",
                        "catalog_object_type": "ITEM_VARIATION",
                        "state": "IN_STOCK",
                        "location_id": "LQ6C3T6VVG02X",
                        "quantity": "16",
                        "calculated_at": "2024-10-23T11:21:46.109Z"
                    },
                    {
                        "catalog_object_id": "HFNRGCNWD3NGZ7YJDN3D3RJ7",
                        "catalog_object_type": "ITEM_VARIATION",
                        "state": "IN_STOCK",
                        "location_id": "LQ6C3T6VVG02X",
                        "quantity": "234",
                        "calculated_at": "2024-10-23T11:21:46.108Z"
                    },
                    {
                        "catalog_object_id": "K35CR4GWOGBCK7A6POLM5EC7",
                        "catalog_object_type": "ITEM_VARIATION",
                        "state": "IN_STOCK",
                        "location_id": "LQ6C3T6VVG02X",
                        "quantity": "20",
                        "calculated_at": "2024-10-19T08:53:05.814Z"
                    },
                    {
                        "catalog_object_id": "PL5TXMPXW2WATGYVZF3S6ZQD",
                        "catalog_object_type": "ITEM_VARIATION",
                        "state": "IN_STOCK",
                        "location_id": "LQ6C3T6VVG02X",
                        "quantity": "9",
                        "calculated_at": "2024-10-10T03:32:20.941Z"
                    }
                ]
            """;
    }
}
