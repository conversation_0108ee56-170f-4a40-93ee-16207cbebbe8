package com.mercaso.data.third_party.mock.shopify;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.third_party.dto.shopify.ShippingAddressDto;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrderEntity;
import com.mercaso.data.utils.SerializationUtils;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import org.apache.commons.lang3.RandomUtils;

public class ShopifyOrderEntityMock {

    public static ShopifyOrderEntity shopifyOrderMock(){
        ShopifyOrderEntity order = new ShopifyOrderEntity();

        order.setId(RandomUtils.nextLong());
        order.setAdminGraphqlApiId("gid://shopify/Order/450789469");
        order.setAppId(null);
        order.setBrowserIp("0.0.0.0");
        order.setBuyerAcceptsMarketing(false);
        order.setCancelReason(null);
        order.setCancelledAt(null);
        order.setCheckoutId(901414060L);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode emptyJson = mapper.createObjectNode();

        order.setClientDetails(emptyJson);
        order.setClosedAt(null);
        order.setConfirmed(true);
        order.setContactEmail("<EMAIL>");
        order.setCreatedAt(ZonedDateTime.parse("2008-01-10T11:00:00-05:00"));
        order.setCurrency("USD");
        order.setCurrentSubtotalPrice(new BigDecimal("195.67"));
        order.setCurrentSubtotalPriceSet(emptyJson);
        order.setCurrentTotalAdditionalFeesSet(null);
        order.setCurrentTotalDiscounts(new BigDecimal("3.33"));
        order.setCurrentTotalDiscountsSet(emptyJson);
        order.setCurrentTotalDutiesSet(null);
        order.setCurrentTotalPrice(new BigDecimal("199.65"));
        order.setCurrentTotalPriceSet(emptyJson);
        order.setCurrentTotalTax(new BigDecimal("3.98"));
        order.setCurrentTotalTaxSet(emptyJson);
        order.setDeviceId(null);
        order.setDiscountCodes(emptyJson);
        order.setDutiesIncluded(false);
        order.setEmail("<EMAIL>");
        order.setEstimatedTaxes(false);
        order.setFinancialStatus("partially_refunded");
        order.setFulfillmentStatus(null);
        order.setLocationId(null);
        order.setMerchantOfRecordAppId(null);
        order.setName("#1001");
        order.setNote(null);
        order.setNoteAttributes(emptyJson);
        order.setNumber(1);
        order.setOrderNumber(1001);
        order.setOriginalTotalAdditionalFeesSet(null);
        order.setOriginalTotalDutiesSet(null);
        order.setPaymentGatewayNames(emptyJson);
        order.setPhone("+557734881234");
        order.setPoNumber("ABC123");
        order.setPresentmentCurrency("USD");
        order.setProcessedAt(ZonedDateTime.parse("2008-01-10T11:00:00-05:00"));
        order.setSubtotalPrice(new BigDecimal("597.00"));
        order.setSubtotalPriceSet(emptyJson);
        order.setTags("");
        order.setTaxExempt(false);
        order.setTaxLines(emptyJson);
        order.setTaxesIncluded(false);
        order.setTest(false);
        order.setTotalDiscounts(new BigDecimal("10.00"));
        order.setTotalDiscountsSet(emptyJson);
        order.setTotalLineItemsPrice(new BigDecimal("597.00"));
        order.setTotalLineItemsPriceSet(emptyJson);
        order.setTotalOutstanding(new BigDecimal("0.00"));
        order.setTotalPrice(new BigDecimal("598.94"));
        order.setTotalPriceSet(emptyJson);
        order.setTotalShippingPriceSet(emptyJson);
        order.setTotalTax(new BigDecimal("11.94"));
        order.setTotalTaxSet(emptyJson);
        order.setTotalTipReceived(new BigDecimal("0.00"));
        order.setTotalWeight(0);
        order.setUpdatedAt(ZonedDateTime.parse("2008-01-10T11:00:00-05:00"));
        order.setUserId(null);
        order.setBillingAddress(emptyJson);
        order.setCustomer(emptyJson);
        order.setDiscountApplications(emptyJson);
        order.setFulfillments(emptyJson);
        order.setRefunds(emptyJson);
        order.setShippingAddress(SerializationUtils.toTree(buildShippingAddressDto()));
        order.setShippingLines(emptyJson);
        order.setTagList(List.of());
        order.setLineItems(List.of());

        return order;
    }

    private static ShippingAddressDto buildShippingAddressDto() {
        ShippingAddressDto shippingAddress = new ShippingAddressDto();
        shippingAddress.setAddress1("Chestnut Street 92");
        shippingAddress.setAddress2(null);
        shippingAddress.setCity("Louisville");
        shippingAddress.setCompany("John Doe");
        shippingAddress.setCountry("United States");
        shippingAddress.setCountryCode("US");
        shippingAddress.setFirstName("John");
        shippingAddress.setLastName("Doe");
        shippingAddress.setLatitude(45.41634);
        shippingAddress.setLongitude(-75.6868);
        shippingAddress.setPhone("************");
        shippingAddress.setProvince("Kentucky");
        shippingAddress.setProvinceCode("KY");
        shippingAddress.setZip("40202");
        return shippingAddress;
    }
}
