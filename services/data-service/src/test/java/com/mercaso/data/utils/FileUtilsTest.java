package com.mercaso.data.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FileUtilsTest {

    private HttpServer server;
    private final int port = 8000;

    @BeforeAll
    public void startServer() throws IOException {
        server = HttpServer.create(new InetSocketAddress(port), 0);
        server.createContext("/test-download-success.txt", new SuccessHandler());
        server.createContext("/test-download-failure.txt", new FailureHandler());
        server.createContext("/success-file.txt", new SuccessHandler());
        server.createContext("/failure-file.txt", new ServerErrorHandler());
        server.setExecutor(null);
        server.start();
    }

    @AfterAll
    public void stopServer() {
        if (server != null) {
            server.stop(0);
        }
    }

    static class SuccessHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "This is a test content.";
            exchange.getResponseHeaders().add("Content-Type", "text/plain");
            exchange.sendResponseHeaders(200, response.getBytes().length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }

    static class FailureHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "Not Found";
            exchange.sendResponseHeaders(404, response.getBytes().length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }

    static class ServerErrorHandler implements HttpHandler {

        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "Internal Server Error";
            exchange.sendResponseHeaders(500, response.getBytes().length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }

    @Test
    public void testDownloadSuccess() throws IOException {
        // Arrange
        String testPath = "/test-download-success.txt";
        String url = "http://localhost:" + port + testPath;

        // Act
        byte[] result = FileUtils.download(url);

        // Assert
        assertNotNull(result, "Resulting byte array should not be null");
        String resultContent = new String(result, StandardCharsets.UTF_8);
        assertEquals("This is a test content.", resultContent, "Downloaded content should match the expected content");
    }

    @Test
    public void testDownloadFailure() {
        // Arrange
        String testPath = "/test-download-failure.txt";
        String url = "http://localhost:" + port + testPath;

        // Act & Assert
        assertThrows(IOException.class, () -> {
            FileUtils.download(url);
        }, "Expected download to throw IOException for 404 response");
    }

}
