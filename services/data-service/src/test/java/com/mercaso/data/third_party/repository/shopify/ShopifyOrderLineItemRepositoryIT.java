package com.mercaso.data.third_party.repository.shopify;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrderEntity;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersLineItemEntity;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersTagsEntity;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderEntityMock;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderLineItemEntityMock;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderTagsEntityMock;
import jakarta.annotation.PostConstruct;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShopifyOrderLineItemRepositoryIT extends AbstractIT {

    @Autowired
    private ShopifyOrderRepository orderRepository;

    @Autowired
    private ShopifyOrderLineItemRepository lineItemRepository;

    @Autowired
    private ShopifyOrderTagsRepository ordersTagsRepository;

    @PostConstruct
    void init(){
        //init data
        ShopifyOrderEntity entity = ShopifyOrderEntityMock.shopifyOrderMock();
        orderRepository.save(entity);

        ShopifyOrdersLineItemEntity shopifyOrdersLineItemEntity = ShopifyOrderLineItemEntityMock.ordersLineItemMock();
        shopifyOrdersLineItemEntity.setOrder(entity);
        lineItemRepository.save(shopifyOrdersLineItemEntity);

        ShopifyOrdersTagsEntity ordersTags = ShopifyOrderTagsEntityMock.shopifyOrdersTagsMock();
        ordersTags.setOrder(entity);
        ordersTagsRepository.save(ordersTags);
    }

    @Test
    void testFindAll() {
        List<ShopifyOrdersLineItemEntity> resultList = lineItemRepository.findAll();
        Assertions.assertNotEquals(0, resultList.size());
    }

}