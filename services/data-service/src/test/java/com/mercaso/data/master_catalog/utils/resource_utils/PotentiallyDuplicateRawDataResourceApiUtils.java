package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.dto.PotentiallyDuplicateSubmitRequest;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class PotentiallyDuplicateRawDataResourceApiUtils extends IntegrationTestRestUtil {

    private static final String V1_POTENTIALLY_DUPLICATE_RAW_DATA_LIST = "/master-catalog/v1/potentially-duplicate-raw-data/list";
    private static final Logger log = LoggerFactory.getLogger(PotentiallyDuplicateRawDataResourceApiUtils.class);

    public PotentiallyDuplicateRawDataResourceApiUtils(Environment environment) {
        super(environment);
    }

    public CustomPage<MasterCatalogPotentiallyDuplicateRawDataDto> list(String status, String taskId) {
        Map<String, String> params = new java.util.HashMap<>();
        params.put("page", "1");
        params.put("pageSize", "10");
        params.put("taskId", taskId);
        if (status != null) {
            params.put("status", status);
        }

        ParameterizedTypeReference<CustomPage<MasterCatalogPotentiallyDuplicateRawDataDto>> responseType =
            new ParameterizedTypeReference<>() {
            };

        return getEntityByMap(V1_POTENTIALLY_DUPLICATE_RAW_DATA_LIST, responseType, params).getBody();
    }

    public void submit(PotentiallyDuplicateSubmitRequest request) {
        String url = "/master-catalog/v1/potentially-duplicate-raw-data/submit";

        try {
            postEntity(url, request, Void.class);
        } catch (Exception e) {
            log.error("Error submitting potentially duplicate raw data: {}", e.getMessage(), e);
            throw e;
        }
    }

    public void update(UUID id, MasterCatalogPotentiallyDuplicateRawDataUpdateRequest request) {
        String url = "/master-catalog/v1/potentially-duplicate-raw-data/" + id;

        try {
            putEntity(url, request);

        } catch (Exception e) {
            log.error("Error updating potentially duplicate raw data: {}", e.getMessage(), e);
            throw e;
        }
    }
} 