package com.mercaso.data.master_catalog.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class MasterCatalogRawDataDuplicationServiceImplTest {

    private MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogRawDataDuplicationServiceImpl service;

    @BeforeEach
    void setUp() {
        masterCatalogRawDataDuplicationRepository = mock(MasterCatalogRawDataDuplicationRepository.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        service = new MasterCatalogRawDataDuplicationServiceImpl(
            masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataRepository
        );
    }

    /**
     * Test for mergeDuplicationRecords with multiple groups
     */
    @Test
    void testMergeDuplicationRecords_MultipleGroups() {
        // Prepare test data
        UUID group1 = UUID.randomUUID();
        UUID group2 = UUID.randomUUID();
        UUID group3 = UUID.randomUUID();

        // Create test records with different groups
        MasterCatalogRawDataDuplication oldestRecord = createDuplicationRecord(
            "11111111111", group1, Instant.now().minusSeconds(500));
        MasterCatalogRawDataDuplication record1 = createDuplicationRecord(
            "22222222222", group2, Instant.now());
        MasterCatalogRawDataDuplication record2 = createDuplicationRecord(
            "33333333333", group3, Instant.now().plusSeconds(100));

        List<MasterCatalogRawDataDuplication> multiGroupRecords = List.of(oldestRecord, record1, record2);

        // Execute test
        UUID result = service.mergeDuplicationRecords(multiGroupRecords);

        // Verify results
        assertEquals(group1, result, "Should return the duplication group of the oldest record");

        ArgumentCaptor<List<MasterCatalogRawDataDuplication>> captor =
            ArgumentCaptor.forClass(List.class);
        verify(masterCatalogRawDataDuplicationRepository).saveAll(captor.capture());

        List<MasterCatalogRawDataDuplication> savedRecords = captor.getValue();
        assertEquals(2, savedRecords.size(), "Should save two updated records");

        for (MasterCatalogRawDataDuplication record : savedRecords) {
            assertEquals(group1, record.getDuplicationGroup(),
                "All records should be updated to the oldest group");
            assertNotNull(record.getUpdatedAt(), "Updated timestamp should be set");
        }
    }

    /**
     * Test for mergeDuplicationRecords with a single group
     */
    @Test
    void testMergeDuplicationRecords_SingleGroup() {
        // Prepare test data
        UUID group1 = UUID.randomUUID();

        MasterCatalogRawDataDuplication record1 = createDuplicationRecord(
            "1111111111111", group1, Instant.now().minusSeconds(100));
        MasterCatalogRawDataDuplication record2 = createDuplicationRecord(
            "2222222222222", group1, Instant.now());

        List<MasterCatalogRawDataDuplication> singleGroupRecords = List.of(record1, record2);

        // Execute test
        UUID result = service.mergeDuplicationRecords(singleGroupRecords);

        // Verify results
        assertEquals(group1, result, "Should return the single group id");
        verify(masterCatalogRawDataDuplicationRepository, never()).saveAll(any());
    }

    /**
     * Test for mergeDuplicationRecords with an empty list
     */
    @Test
    void testMergeDuplicationRecords_EmptyList() {
        // Execute test
        UUID result = service.mergeDuplicationRecords(Collections.emptyList());

        // Verify results
        Assertions.assertNull(result, "Should return null for empty list");
        verify(masterCatalogRawDataDuplicationRepository, never()).saveAll(any());
    }

    /**
     * Helper method to create a duplication record for testing
     */
    private MasterCatalogRawDataDuplication createDuplicationRecord(String upc, UUID groupId, Instant createdAt) {
        MasterCatalogRawDataDuplication duplication = new MasterCatalogRawDataDuplication();
        duplication.setDuplicationGroup(groupId);
        duplication.setUpc(upc);
        duplication.setCreatedAt(createdAt);
        return duplication;
    }
} 