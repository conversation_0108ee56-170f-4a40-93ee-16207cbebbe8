package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.UUID;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogRawDataDuplicationUtils {

    public static MasterCatalogRawDataDuplication buildMasterCatalogRawDataDuplication(String upc,UUID duplicationGroup) {
        return MasterCatalogRawDataDuplication.builder()
                .id(UUID.randomUUID())
                .upc(upc)
                .duplicationGroup(duplicationGroup)
                .build();
    }
}
