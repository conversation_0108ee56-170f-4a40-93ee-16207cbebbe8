package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationSubmitEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class RemoveDuplicationSubmitEventListenerTest {

    private MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository;
    private MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService;
    private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogTaskRepository masterCatalogTaskRepository;

    private RemoveDuplicationSubmitEventListener listener;

    @BeforeEach
    void setUp() {
        masterCatalogPotentiallyDuplicateRawDataRepository = mock(MasterCatalogPotentiallyDuplicateRawDataRepository.class);
        masterCatalogRawDataDuplicationRepository = mock(MasterCatalogRawDataDuplicationRepository.class);
        masterCatalogRawDataDuplicationService = mock(MasterCatalogRawDataDuplicationService.class);
        masterCatalogBatchJobRepository = mock(MasterCatalogBatchJobRepository.class);
        masterCatalogBatchJobMapper = mock(MasterCatalogBatchJobMapper.class);
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        masterCatalogRawDataRepository= mock(MasterCatalogRawDataRepository.class);
        masterCatalogTaskRepository= mock(MasterCatalogTaskRepository.class);


        listener = new RemoveDuplicationSubmitEventListener(masterCatalogPotentiallyDuplicateRawDataRepository,
            masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataDuplicationService,
            masterCatalogBatchJobRepository,
            masterCatalogBatchJobMapper,
            applicationEventPublisherProvider,
            masterCatalogRawDataRepository,
            masterCatalogTaskRepository
            );
    }

    @Test
    void handleRemoveDuplicationSubmitEvent_shouldSkipWhenUnreviewedDataExists() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationSubmitPayload payload = new RemoveDuplicationSubmitPayload();
        payload.setData(taskDto);

        RemoveDuplicationSubmitEvent event = new RemoveDuplicationSubmitEvent(this, payload);

        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();
        MasterCatalogPotentiallyDuplicateRawData unreviewedData = new MasterCatalogPotentiallyDuplicateRawData();
        unreviewedData.setStatus(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
        dataList.add(unreviewedData);

        MasterCatalogBatchJob task = MasterCatalogBatchJob.builder()
            .id(taskId)
            .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(task);

        when(masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);

        // Act
        listener.handleEvent(event);

        // Assert
        verify(masterCatalogRawDataDuplicationRepository, never()).saveAll(any());
        verify(masterCatalogBatchJobRepository, never()).save(any());
        verify(applicationEventPublisherProvider, never()).publishEventRemoveDuplicationCompleted(any(), any());
    }

    @Test
    void handleRemoveDuplicationSubmitEvent_shouldProcessWhenAllDataReviewed() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        UUID groupId = UUID.randomUUID();

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationSubmitPayload payload = new RemoveDuplicationSubmitPayload();
        payload.setData(taskDto);

        RemoveDuplicationSubmitEvent event = new RemoveDuplicationSubmitEvent(this, payload);

        // Create reviewed data with duplicates
        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();

        MasterCatalogPotentiallyDuplicateRawData reviewedData = new MasterCatalogPotentiallyDuplicateRawData();
        reviewedData.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        reviewedData.setDuplicated(true);
        reviewedData.setRawDataId(rawDataId1);
        reviewedData.setPotentiallyDuplicateRawDataId(rawDataId2);
        reviewedData.setUpc("12345");
        reviewedData.setName("Test Item");
        reviewedData.setPotentiallyDuplicateName("Test Item11");
        reviewedData.setPotentiallyDuplicateUpc("12345");
        dataList.add(reviewedData);

        // Create reviewed data without duplicates
        MasterCatalogPotentiallyDuplicateRawData nonDuplicateData = new MasterCatalogPotentiallyDuplicateRawData();
        nonDuplicateData.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        nonDuplicateData.setDuplicated(false);
        nonDuplicateData.setRawDataId(UUID.randomUUID());
        nonDuplicateData.setPotentiallyDuplicateRawDataId(UUID.randomUUID());
        dataList.add(nonDuplicateData);

        when(masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);

        // Mock task entity
        MasterCatalogBatchJob taskEntity = new MasterCatalogBatchJob();
        taskEntity.setId(taskId);

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(taskEntity);
        when(masterCatalogBatchJobMapper.toDto(taskEntity)).thenReturn(taskDto);

        // Act
        listener.handleEvent(event);

        // Assert
        // Verify duplication records were saved
        verify(masterCatalogRawDataDuplicationRepository).saveAll(any());

        // Verify event was published
        ArgumentCaptor<RemoveDuplicationCompletedPayload> payloadCaptor = ArgumentCaptor.forClass(
            RemoveDuplicationCompletedPayload.class);

        // Verify task status was updated
        ArgumentCaptor<MasterCatalogBatchJob> taskCaptor = ArgumentCaptor.forClass(MasterCatalogBatchJob.class);
        verify(masterCatalogBatchJobRepository).save(taskCaptor.capture());

        MasterCatalogBatchJob savedTask = taskCaptor.getValue();
        assert savedTask.getStatus() == MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_COMPLETED;
    }

    @Test
    void handleRemoveDuplicationSubmitEvent_shouldHandleExceptionGracefully() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationSubmitPayload payload = new RemoveDuplicationSubmitPayload();
        payload.setData(taskDto);

        RemoveDuplicationSubmitEvent event = new RemoveDuplicationSubmitEvent(this, payload);

        // Simulate exception
        when(masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenThrow(new RuntimeException(
            "Test exception"));

        // Act & Assert
        try {
            listener.handleEvent(event);
        } catch (Exception e) {
            // Exception should be rethrown
            assert e.getMessage().equals("Test exception");
        }
    }

    @Test
    void handleRemoveDuplicationSubmitEvent_shouldProcessMultipleDuplicates() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        UUID rawDataId3 = UUID.randomUUID();
        UUID rawDataId4 = UUID.randomUUID();
        UUID groupId1 = UUID.randomUUID();
        UUID groupId2 = UUID.randomUUID();

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationSubmitPayload payload = new RemoveDuplicationSubmitPayload();
        payload.setData(taskDto);

        RemoveDuplicationSubmitEvent event = new RemoveDuplicationSubmitEvent(this, payload);

        // Create multiple reviewed data with duplicates
        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();

        MasterCatalogPotentiallyDuplicateRawData reviewedData1 = new MasterCatalogPotentiallyDuplicateRawData();
        reviewedData1.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        reviewedData1.setDuplicated(true);
        reviewedData1.setRawDataId(rawDataId1);
        reviewedData1.setPotentiallyDuplicateRawDataId(rawDataId2);
        reviewedData1.setUpc("12345");
        reviewedData1.setName("name");
        reviewedData1.setPotentiallyDuplicateName("name123");
        reviewedData1.setPotentiallyDuplicateUpc("12345");
        dataList.add(reviewedData1);

        MasterCatalogPotentiallyDuplicateRawData reviewedData2 = new MasterCatalogPotentiallyDuplicateRawData();
        reviewedData2.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        reviewedData2.setDuplicated(true);
        reviewedData2.setRawDataId(rawDataId3);
        reviewedData2.setPotentiallyDuplicateRawDataId(rawDataId4);
        reviewedData2.setUpc("67890");
        reviewedData2.setName("name789");
        reviewedData2.setPotentiallyDuplicateName("name78910");
        reviewedData2.setPotentiallyDuplicateUpc("67890");
        dataList.add(reviewedData2);

        when(masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);

        when(masterCatalogRawDataDuplicationService.mergeDuplicationRecords(any())).thenReturn(groupId2);

        // Mock task entity
        MasterCatalogBatchJob taskEntity = new MasterCatalogBatchJob();
        taskEntity.setId(taskId);

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(taskEntity);
        when(masterCatalogBatchJobMapper.toDto(taskEntity)).thenReturn(taskDto);

        // Act
        listener.handleEvent(event);

        // Assert
        // Verify duplication records were saved (4 records for 2 pairs)
        ArgumentCaptor<List<MasterCatalogRawDataDuplication>> duplicationCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogRawDataDuplicationRepository).saveAll(duplicationCaptor.capture());

        List<MasterCatalogRawDataDuplication> savedDuplications = duplicationCaptor.getValue();
        assert savedDuplications.size() == 4;

        // Verify task status was updated
        verify(masterCatalogBatchJobRepository).save(any());
    }

    @Test
    void handleRemoveDuplicationSubmitEvent_shouldHandleEmptyDuplicationList() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        RemoveDuplicationSubmitPayload payload = new RemoveDuplicationSubmitPayload();
        payload.setData(taskDto);

        RemoveDuplicationSubmitEvent event = new RemoveDuplicationSubmitEvent(this, payload);

        // Create only non-duplicate data
        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();

        when(masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(List.of());

        // Mock task entity
        MasterCatalogBatchJob taskEntity = new MasterCatalogBatchJob();
        taskEntity.setId(taskId);

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(taskEntity);
        when(masterCatalogBatchJobMapper.toDto(taskEntity)).thenReturn(taskDto);

        // Act
        listener.handleEvent(event);

        // Assert
        // Verify no duplication records were saved
        verify(masterCatalogRawDataDuplicationRepository, never()).saveAll(any());

        // Verify task status was updated
        verify(masterCatalogBatchJobRepository).save(any());
    }
} 

