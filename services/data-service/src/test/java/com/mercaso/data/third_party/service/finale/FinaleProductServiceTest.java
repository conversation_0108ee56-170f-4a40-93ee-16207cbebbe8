package com.mercaso.data.third_party.service.finale;

import static com.mercaso.data.third_party.mock.finale.FinaleProductDataResponseDtoMock.mockFinaleProductDataResponseDto;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.third_party.adaptor.finale.FinaleProductApiAdapter;
import com.mercaso.data.third_party.dto.finale.*;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityPrimaryDataResponseDto.FacilityReorderList;
import com.mercaso.data.third_party.enums.finale.FinaleLocationTypeEnums;
import com.mercaso.data.third_party.mapper.FinaleAvailableStockMapper;
import com.mercaso.data.third_party.mapper.FinaleFacilityMapper;
import com.mercaso.data.third_party.service.finale.impl.FinaleProductServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

import java.util.*;

@ExtendWith(MockitoExtension.class)
class FinaleProductServiceTest {

    @Mock
    private FinaleProductApiAdapter finaleProductApiAdapter;

    @Mock
    private FinaleAvailableStockMapper finaleAvailableStockMapper;

    @Mock
    private FinaleFacilityMapper finaleFacilityMapper;

    @Test
    void getAvailableStock_MFCLocation_Success() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, finaleAvailableStockMapper, null);

        FinaleAvailableStockFilter filter = new FinaleAvailableStockFilter();
        filter.setPageable(new CustomPageable(0, 100));
        filter.setLocationType(FinaleLocationTypeEnums.MFC);
        filter.setLocationName("Facility1");

        FinaleFacilityPrimaryDataResponseDto mockPrimaryResponse = new FinaleFacilityPrimaryDataResponseDto();
        List<FacilityReorderList> facilityList = new ArrayList<>();

        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");
        facilityList.add(facilityReorderList);

        facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility2");
        facilityReorderList.setFacilityUrl("http://facility2.com");
        facilityList.add(facilityReorderList);

        mockPrimaryResponse.setFacilityReorderList(facilityList);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(mockPrimaryResponse));

        FinaleProductDataResponseDto mockResponse = mockFinaleProductDataResponseDto();
        when(finaleProductApiAdapter.fetchProductData(any())).thenReturn(Optional.of(mockResponse));

        FinaleAvailableStockDto mockDto = new FinaleAvailableStockDto();
        when(finaleAvailableStockMapper.toFinaleAvailableStockDto(any())).thenReturn(mockDto);

        Page<FinaleAvailableStockDto> result = finaleProductService.getAvailableStock(filter);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(mockDto, result.getContent().getFirst());

        verify(finaleProductApiAdapter).fetchProductData(any());
        verify(finaleAvailableStockMapper).toFinaleAvailableStockDto(any());
    }

    @Test
    void getAvailableStock_MFCLocation_EmptyResponse() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, finaleAvailableStockMapper, null);

        FinaleAvailableStockFilter filter = new FinaleAvailableStockFilter();
        filter.setPageable(new CustomPageable(0, 100));
        filter.setLocationType(FinaleLocationTypeEnums.MFC);
        filter.setLocationName("Facility1");

        FinaleFacilityPrimaryDataResponseDto mockPrimaryResponse = new FinaleFacilityPrimaryDataResponseDto();
        List<FacilityReorderList> facilityList = new ArrayList<>();

        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");
        facilityList.add(facilityReorderList);

        facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility2");
        facilityReorderList.setFacilityUrl("http://facility2.com");
        facilityList.add(facilityReorderList);

        mockPrimaryResponse.setFacilityReorderList(facilityList);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(mockPrimaryResponse));

        when(finaleProductApiAdapter.fetchProductData(any())).thenReturn(Optional.empty());

        Page<FinaleAvailableStockDto> result = finaleProductService.getAvailableStock(filter);

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter).fetchProductData(any());
    }

    @Test
    void testGetFacilitiesUrl_WithData() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null, finaleProductApiAdapter, null, null);

        FinaleFacilityPrimaryDataResponseDto mockResponse = new FinaleFacilityPrimaryDataResponseDto();
        List<FacilityReorderList> facilityList = new ArrayList<>();

        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");
        facilityList.add(facilityReorderList);

        facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility2");
        facilityReorderList.setFacilityUrl("http://facility2.com");
        facilityList.add(facilityReorderList);

        mockResponse.setFacilityReorderList(facilityList);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(mockResponse));

        Map<String, String> result = finaleProductService.getFacilitiesUrl();

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("http://facility1.com", result.get("Facility1"));
        assertEquals("http://facility2.com", result.get("Facility2"));

        verify(finaleProductApiAdapter).fetchFacilityUrlData();
    }

    @Test
    void testGetFacilitiesUrl_EmptyResponse() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null, finaleProductApiAdapter, null, null);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.empty());

        Map<String, String> result = finaleProductService.getFacilitiesUrl();

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter).fetchFacilityUrlData();
    }

    @Test
    void testGetFacilitiesUrl_EmptyFacilityList() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null, finaleProductApiAdapter, null, null);

        FinaleFacilityPrimaryDataResponseDto mockResponse = new FinaleFacilityPrimaryDataResponseDto();
        mockResponse.setFacilityReorderList(new ArrayList<>());

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(mockResponse));

        Map<String, String> result = finaleProductService.getFacilitiesUrl();

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter).fetchFacilityUrlData();
    }

    @Test
    void testGetFacilitiesUrl_NullFacilityList() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null, finaleProductApiAdapter, null, null);

        FinaleFacilityPrimaryDataResponseDto mockResponse = new FinaleFacilityPrimaryDataResponseDto();
        mockResponse.setFacilityReorderList(null);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(mockResponse));

        Map<String, String> result = finaleProductService.getFacilitiesUrl();

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter).fetchFacilityUrlData();
    }

    @Test
    void testGetFacilities_WithData() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, null, finaleFacilityMapper);

        // Mock getFacilitiesUrl
        FinaleFacilityPrimaryDataResponseDto urlResponse = new FinaleFacilityPrimaryDataResponseDto();

        List<FacilityReorderList> facilityList = new ArrayList<>();
        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");
        facilityList.add(facilityReorderList);

        facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility2");
        facilityReorderList.setFacilityUrl("http://facility2.com");
        facilityList.add(facilityReorderList);

        urlResponse.setFacilityReorderList(facilityList);

        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(urlResponse));

        // Mock fetchFacilityData
        FinaleFacilityDataResponseDto facilityResponse = new FinaleFacilityDataResponseDto();
        when(finaleProductApiAdapter.fetchFacilityData()).thenReturn(Optional.of(facilityResponse));

        // Mock mapper

        FinaleFacilityDto finaleFacilityDto = new FinaleFacilityDto();
        finaleFacilityDto.setParentFacilityUrl("http://facility1.com");
        finaleFacilityDto.setStatusId("FACILITY_ACTIVE");

        FinaleFacilityDto finaleFacilityDto2 = new FinaleFacilityDto();
        finaleFacilityDto2.setParentFacilityUrl("http://facility2.com");
        finaleFacilityDto2.setStatusId("FACILITY_ACTIVE");

        List<FinaleFacilityDto> mappedFacilities = Arrays.asList(
            finaleFacilityDto,
            finaleFacilityDto2
        );
        when(finaleFacilityMapper.toFinaleFacilityDtoList(facilityResponse)).thenReturn(mappedFacilities);

        FinaleFacilityFilter filter = new FinaleFacilityFilter();
        filter.setLocationNames(Arrays.asList("Facility1", "Facility2"));

        Page<FinaleFacilityDto> result = finaleProductService.getFacilities(filter);

        assertNotNull(result);
        assertEquals(2, result.getContent().size());

        verify(finaleProductApiAdapter).fetchFacilityUrlData();
        verify(finaleProductApiAdapter).fetchFacilityData();
        verify(finaleFacilityMapper).toFinaleFacilityDtoList(facilityResponse);
    }

    @Test
    void testGetFacilities_EmptyFacilitiesUrl() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, null, finaleFacilityMapper);

        FinaleFacilityFilter filter = new FinaleFacilityFilter();

        Page<FinaleFacilityDto> result = finaleProductService.getFacilities(filter);

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter,never()).fetchFacilityUrlData();
        verify(finaleProductApiAdapter).fetchFacilityData();
    }

    @Test
    void testGetFacilities_EmptyFacilityData() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, null, finaleFacilityMapper);

        // Mock getFacilitiesUrl
        FinaleFacilityPrimaryDataResponseDto urlResponse = new FinaleFacilityPrimaryDataResponseDto();

        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");

        // Mock fetchFacilityData
        FinaleFacilityDataResponseDto facilityResponse = new FinaleFacilityDataResponseDto();
        when(finaleProductApiAdapter.fetchFacilityData()).thenReturn(Optional.of(facilityResponse));

        FinaleFacilityFilter filter = new FinaleFacilityFilter();

        Page<FinaleFacilityDto> result = finaleProductService.getFacilities(filter);

        assertTrue(result.isEmpty());
        verify(finaleProductApiAdapter,never()).fetchFacilityUrlData();
        verify(finaleProductApiAdapter).fetchFacilityData();
    }

    @Test
    void testGetFacilities_FilteredResults() {
        FinaleProductServiceImpl finaleProductService = new FinaleProductServiceImpl(null,
            finaleProductApiAdapter, null, finaleFacilityMapper);

        // Mock getFacilitiesUrl
        FinaleFacilityPrimaryDataResponseDto urlResponse = new FinaleFacilityPrimaryDataResponseDto();
        List<FacilityReorderList> facilityList = new ArrayList<>();

        FacilityReorderList facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility1");
        facilityReorderList.setFacilityUrl("http://facility1.com");
        facilityList.add(facilityReorderList);

        facilityReorderList = new FacilityReorderList();
        facilityReorderList.setFacilityName("Facility2");
        facilityReorderList.setFacilityUrl("http://facility2.com");
        facilityList.add(facilityReorderList);
        urlResponse.setFacilityReorderList(facilityList);

        urlResponse.setFacilityReorderList(facilityList);
        when(finaleProductApiAdapter.fetchFacilityUrlData()).thenReturn(Optional.of(urlResponse));

        // Mock fetchFacilityData
        FinaleFacilityDataResponseDto facilityResponse = new FinaleFacilityDataResponseDto();
        when(finaleProductApiAdapter.fetchFacilityData()).thenReturn(Optional.of(facilityResponse));

        // Mock mapper
        FinaleFacilityDto finaleFacilityDto = new FinaleFacilityDto();
        finaleFacilityDto.setParentFacilityUrl("http://facility1.com");
        finaleFacilityDto.setStatusId("FACILITY_ACTIVE");

        FinaleFacilityDto finaleFacilityDto2 = new FinaleFacilityDto();
        finaleFacilityDto2.setParentFacilityUrl("http://facility2.com");
        finaleFacilityDto2.setStatusId("FACILITY_INACTIVE");

        List<FinaleFacilityDto> mappedFacilities = Arrays.asList(
            finaleFacilityDto,
            finaleFacilityDto2
        );
        when(finaleFacilityMapper.toFinaleFacilityDtoList(facilityResponse)).thenReturn(mappedFacilities);

        FinaleFacilityFilter filter = new FinaleFacilityFilter();
        filter.setLocationNames(Collections.singletonList("Facility1"));

        Page<FinaleFacilityDto> result = finaleProductService.getFacilities(filter);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());

        verify(finaleProductApiAdapter).fetchFacilityUrlData();
        verify(finaleProductApiAdapter).fetchFacilityData();
        verify(finaleFacilityMapper).toFinaleFacilityDtoList(facilityResponse);
    }
}