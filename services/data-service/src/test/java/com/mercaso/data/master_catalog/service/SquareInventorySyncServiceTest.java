package com.mercaso.data.master_catalog.service;

import static com.mercaso.data.master_catalog.constants.SquareConstants.DAYS_IN_ONE_YEAR;
import static com.mercaso.data.master_catalog.constants.SquareConstants.SQUARE_API_LIMIT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryChangesRequestDto;
import com.mercaso.data.master_catalog.dto.square.BatchRetrieveInventoryCountsRequestDto;
import com.mercaso.data.master_catalog.dto.square.InventoryChangeDto;
import com.mercaso.data.master_catalog.dto.square.InventoryCountDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryChangeRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.impl.SquareInventorySyncServiceImpl;
import com.mercaso.data.utils.SerializationUtils;
import com.squareup.square.models.InventoryAdjustment;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class SquareInventorySyncServiceTest {

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository = mock(
        MasterCatalogSquareVariationMappingRepository.class);
    private final MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository = mock(
        MasterCatalogSquareRawDataRepository.class);
    private final MasterCatalogSquareInventoryRepository masterCatalogSquareInventoryRepository = mock(
        MasterCatalogSquareInventoryRepository.class);
    private final MasterCatalogSquareInventoryChangeRepository masterCatalogSquareInventoryChangeRepository = mock(
        MasterCatalogSquareInventoryChangeRepository.class);

    private final SquareInventorySyncServiceImpl inventoryService = new SquareInventorySyncServiceImpl(
        squareApiAdapter,
        variationMappingRepository,
        masterCatalogSquareRawDataRepository,
        masterCatalogSquareInventoryRepository,
        masterCatalogSquareInventoryChangeRepository
    );

    private UUID storeId;
    private List<String> presentAtLocationIds;
    private SquareDataSyncRequest request = new SquareDataSyncRequest();

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        presentAtLocationIds = Arrays.asList(UUID.randomUUID().toString(), UUID.randomUUID().toString());
        request.setStoreId(storeId);
    }

    @Test
    void testSyncInventory_NoInventoryCounts() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        MasterCatalogSquareInventory existingInventory = mock(MasterCatalogSquareInventory.class);
        Instant existingUpdatedAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existingInventory.getUpdatedAt()).thenReturn(existingUpdatedAt);
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.of(existingInventory));

        // Mock fetchInventoryCounts to return an empty list
        List<InventoryCountDto> inventoryCountList = Collections.emptyList();
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(existingUpdatedAt.toString(), capturedRequest.getUpdatedAfter());

        // Verify saveRawInventoryData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(any(MasterCatalogSquareRawData.class));

        // Verify saveInventory was not called
        verify(masterCatalogSquareInventoryRepository, never()).saveAll(any(List.class));
    }

    @Test
    void testSyncInventory_NoExistingInventory() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return a list
        InventoryCountDto inventoryCountDto = mock(InventoryCountDto.class);
        when(inventoryCountDto.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto.getQuantity()).thenReturn("20");
        when(inventoryCountDto.getState()).thenReturn("AVAILABLE");
        List<InventoryCountDto> inventoryCountList = List.of(inventoryCountDto);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        UUID masterCatalogRawDataId = UUID.randomUUID();
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId);
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(List.of(variationMapping));

        // Mock masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn to return empty
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(any()))
            .thenReturn(Collections.emptyList());

        // Mock saveRawInventoryData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(DAYS_IN_ONE_YEAR,
            ChronoUnit.DAYS.between(Instant.parse(capturedRequest.getUpdatedAfter()), Instant.now()),
            1);

        // Verify saveRawInventoryData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryCountList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Verify masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn was called
        ArgumentCaptor<List<UUID>> rawDataIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).findAllByMasterCatalogRawDataIdIn(rawDataIdsCaptor.capture());
        Collection<UUID> capturedRawDataIds = rawDataIdsCaptor.getValue();
        assertEquals(Collections.singletonList(masterCatalogRawDataId), capturedRawDataIds);

        // Verify saveInventory was called
        ArgumentCaptor<List<MasterCatalogSquareInventory>> inventoryListCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).saveAll(inventoryListCaptor.capture());
        List<MasterCatalogSquareInventory> savedInventories = inventoryListCaptor.getValue();
        assertEquals(1, savedInventories.size());
        MasterCatalogSquareInventory savedInventory = savedInventories.getFirst();
        assertEquals(masterCatalogRawDataId, savedInventory.getMasterCatalogRawDataId());
        assertEquals(Integer.valueOf("20"), savedInventory.getQuantity());
        assertEquals("AVAILABLE", savedInventory.getState());
        assertEquals(String.valueOf(sourceId), savedInventory.getSourceId());
        // Cannot assert exact time, but ensure it's set
        assertTrue(savedInventory.getUpdatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncInventory_NoExistingInventoryAndNoInventoryCounts() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return an empty list
        List<InventoryCountDto> inventoryCountList = Collections.emptyList();
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(DAYS_IN_ONE_YEAR,
            ChronoUnit.DAYS.between(Instant.parse(capturedRequest.getUpdatedAfter()), Instant.now()),
            1);

        // Verify saveRawInventoryData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(any(MasterCatalogSquareRawData.class));

        // Verify saveInventory was not called
        verify(masterCatalogSquareInventoryRepository, never()).saveAll(any(List.class));
    }

    @Test
    void testSyncInventory_WithMultipleInventoryCounts() {
        // Create fixed UUIDs for masterCatalogRawDataIds
        UUID masterCatalogRawDataId1 = UUID.randomUUID();
        UUID masterCatalogRawDataId2 = UUID.randomUUID();

        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return multiple InventoryCountDto
        InventoryCountDto inventoryCountDto1 = mock(InventoryCountDto.class);
        when(inventoryCountDto1.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto1.getQuantity()).thenReturn("10");
        when(inventoryCountDto1.getState()).thenReturn("IN_STOCK");

        InventoryCountDto inventoryCountDto2 = mock(InventoryCountDto.class);
        when(inventoryCountDto2.getCatalogObjectId()).thenReturn("variation456");
        when(inventoryCountDto2.getQuantity()).thenReturn("5");
        when(inventoryCountDto2.getState()).thenReturn("OUT_OF_STOCK");

        List<InventoryCountDto> inventoryCountList = Arrays.asList(inventoryCountDto1, inventoryCountDto2);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping1 = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId1);

        MasterCatalogSquareVariationMapping variationMapping2 = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping2.getVariationId()).thenReturn("variation456");
        when(variationMapping2.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId2);

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(Arrays.asList("variation123", "variation456"),
            storeId)).thenReturn(Arrays.asList(variationMapping1, variationMapping2));

        // Mock masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn to return empty
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(Arrays.asList(
            variationMapping1.getMasterCatalogRawDataId(),
            variationMapping2.getMasterCatalogRawDataId()
        ))).thenReturn(Collections.emptyList());

        // Mock saving raw inventory data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(DAYS_IN_ONE_YEAR,
            ChronoUnit.DAYS.between(Instant.parse(capturedRequest.getUpdatedAfter()), Instant.now()),
            1);

        // Verify saveRawInventoryData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryCountList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation123", "variation456"), capturedVariationIds);

        // Verify masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn was called
        ArgumentCaptor<List<UUID>> rawDataIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).findAllByMasterCatalogRawDataIdIn(rawDataIdsCaptor.capture());
        Collection<UUID> capturedRawDataIds = rawDataIdsCaptor.getValue();

        // Compare as Sets to ignore order
        Set<UUID> expectedRawDataIds = new HashSet<>(Arrays.asList(masterCatalogRawDataId1, masterCatalogRawDataId2));
        Set<UUID> actualRawDataIds = new HashSet<>(capturedRawDataIds);
        assertEquals(expectedRawDataIds, actualRawDataIds);

        // Verify inventories were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareInventory>> inventoryListCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).saveAll(inventoryListCaptor.capture());
        List<MasterCatalogSquareInventory> savedInventories = inventoryListCaptor.getValue();
        assertEquals(2, savedInventories.size());

        MasterCatalogSquareInventory savedInventory1 = savedInventories.get(0);
        MasterCatalogSquareInventory savedInventory2 = savedInventories.get(1);

        assertEquals(masterCatalogRawDataId1, savedInventory1.getMasterCatalogRawDataId());
        assertEquals(Integer.valueOf("10"), savedInventory1.getQuantity());
        assertEquals("IN_STOCK", savedInventory1.getState());
        assertEquals(sourceId.toString(), savedInventory1.getSourceId());

        assertEquals(masterCatalogRawDataId2, savedInventory2.getMasterCatalogRawDataId());
        assertEquals(Integer.valueOf("5"), savedInventory2.getQuantity());
        assertEquals("OUT_OF_STOCK", savedInventory2.getState());
        assertEquals(sourceId.toString(), savedInventory2.getSourceId());

        // Ensure updatedAt is set
        assertTrue(savedInventory1.getUpdatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
        assertTrue(savedInventory2.getUpdatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncInventory_WithUnmappedVariationIds() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return a list with some unmapped variationIds
        InventoryCountDto inventoryCountDto1 = mock(InventoryCountDto.class);
        when(inventoryCountDto1.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto1.getQuantity()).thenReturn("5");
        when(inventoryCountDto1.getState()).thenReturn("AVAILABLE");

        InventoryCountDto inventoryCountDto2 = mock(InventoryCountDto.class);
        when(inventoryCountDto2.getCatalogObjectId()).thenReturn("variationUnmapped");
        when(inventoryCountDto2.getQuantity()).thenReturn("0");
        when(inventoryCountDto2.getState()).thenReturn("OUT_OF_STOCK");

        List<InventoryCountDto> inventoryCountList = Arrays.asList(inventoryCountDto1, inventoryCountDto2);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variationMappingRepository.findAllByVariationIdIn to return only one mapping
        MasterCatalogSquareVariationMapping variationMapping1 = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping1.getVariationId()).thenReturn("variation123");
        when(variationMapping1.getMasterCatalogRawDataId()).thenReturn(UUID.randomUUID());

        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(Arrays.asList("variation123", "variationUnmapped"),
            storeId)).thenReturn(Collections.singletonList(variationMapping1));

        // Mock masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(Arrays.asList(
            variationMapping1.getMasterCatalogRawDataId()
        ))).thenReturn(Collections.emptyList());

        // Mock saveRawInventoryData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(DAYS_IN_ONE_YEAR,
            ChronoUnit.DAYS.between(Instant.parse(capturedRequest.getUpdatedAfter()), Instant.now()),
            1);

        // Verify saveRawInventoryData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryCountList), savedRawData.getData());

        // Verify variationMappingRepository.findAllByVariationIdIn was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Arrays.asList("variation123", "variationUnmapped"), capturedVariationIds);

        // Verify masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn was called
        ArgumentCaptor<List<UUID>> rawDataIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).findAllByMasterCatalogRawDataIdIn(rawDataIdsCaptor.capture());
        Collection<UUID> capturedRawDataIds = rawDataIdsCaptor.getValue();
        assertEquals(Collections.singletonList(variationMapping1.getMasterCatalogRawDataId()), capturedRawDataIds);

        // Verify saveInventory was called only for mapped variation
        ArgumentCaptor<List<MasterCatalogSquareInventory>> inventoryListCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1)).saveAll(inventoryListCaptor.capture());
        List<MasterCatalogSquareInventory> savedInventories = inventoryListCaptor.getValue();
        assertEquals(1, savedInventories.size());

        MasterCatalogSquareInventory savedInventory = savedInventories.get(0);
        assertEquals(variationMapping1.getMasterCatalogRawDataId(), savedInventory.getMasterCatalogRawDataId());
        assertEquals(Integer.valueOf("5"), savedInventory.getQuantity());
        assertEquals("AVAILABLE", savedInventory.getState());
        assertEquals(String.valueOf(sourceId), savedInventory.getSourceId());

        // Ensure updatedAt is set
        assertEquals(true, savedInventory.getUpdatedAt().isAfter(Instant.parse("2024-01-01T00:00:00Z")));
    }

    @Test
    void testSyncInventory_WithExceptionDuringSaveRawInventoryData() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return a list
        InventoryCountDto inventoryCountDto = mock(InventoryCountDto.class);
        when(inventoryCountDto.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto.getQuantity()).thenReturn("10");
        when(inventoryCountDto.getState()).thenReturn("IN_STOCK");
        List<InventoryCountDto> inventoryCountList = Arrays.asList(inventoryCountDto);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(UUID.randomUUID());
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(Arrays.asList(variationMapping));

        // Mock masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn to return empty
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(any()))
            .thenReturn(Collections.emptyList());

        // Mock saveRawInventoryData to throw an exception
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenThrow(new RuntimeException("Database error"));

        // Execute the method and expect an exception
        try {
            inventoryService.syncInventory(request, presentAtLocationIds);
        } catch (RuntimeException e) {
            // Expected exception
            assertEquals("Database error", e.getMessage());
        }

        // Verify fetchInventoryCounts was called
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryCounts(eq(storeId),
            any(BatchRetrieveInventoryCountsRequestDto.class));

        // Verify saveRawInventoryData was called
        verify(masterCatalogSquareRawDataRepository, times(1)).save(any(MasterCatalogSquareRawData.class));

        // Verify that masterCatalogSquareInventoryRepository was interacted as expected
        verify(masterCatalogSquareInventoryRepository, times(1)).findTopByStoreIdAndOrderByUpdatedAtDesc(storeId);
    }

    @Test
    void testSyncInventory_WithExistingInventoryUpdate() {
        // Create a fixed UUID for masterCatalogRawDataId
        UUID masterCatalogRawDataId = UUID.randomUUID();

        // Mock existing inventory with the fixed masterCatalogRawDataId
        MasterCatalogSquareInventory existingInventory = mock(MasterCatalogSquareInventory.class);
        Instant existingUpdatedAt = Instant.parse("2024-01-01T00:00:00Z");
        when(existingInventory.getUpdatedAt()).thenReturn(existingUpdatedAt);
        when(existingInventory.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId);
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.of(existingInventory));
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(any()))
            .thenReturn(Collections.singletonList(existingInventory));

        // Mock inventory count DTO
        InventoryCountDto inventoryCountDto = mock(InventoryCountDto.class);
        when(inventoryCountDto.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto.getQuantity()).thenReturn("15");
        when(inventoryCountDto.getState()).thenReturn("LOW_STOCK");
        List<InventoryCountDto> inventoryCountList = Collections.singletonList(inventoryCountDto);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variation mapping with the same masterCatalogRawDataId
        MasterCatalogSquareVariationMapping variationMapping = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId);
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(Collections.singletonList(variationMapping));

        // Mock saving raw inventory data
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any()))
            .thenReturn(rawData);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify fetchInventoryCounts was called correctly
        ArgumentCaptor<BatchRetrieveInventoryCountsRequestDto> requestCaptor =
            ArgumentCaptor.forClass(BatchRetrieveInventoryCountsRequestDto.class);
        verify(squareApiAdapter, times(1))
            .batchRetrieveInventoryCounts(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryCountsRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(existingUpdatedAt.toString(), capturedRequest.getUpdatedAfter());

        // Verify raw data was saved correctly
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor =
            ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryCountList), savedRawData.getData());

        // Verify variation mappings were fetched correctly
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1))
            .findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(), eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Verify existing inventories were fetched correctly
        ArgumentCaptor<List<UUID>> rawDataIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1))
            .findAllByMasterCatalogRawDataIdIn(rawDataIdsCaptor.capture());
        Collection<UUID> capturedRawDataIds = rawDataIdsCaptor.getValue();
        assertEquals(Collections.singletonList(masterCatalogRawDataId), capturedRawDataIds);

        // Verify inventories were saved correctly
        ArgumentCaptor<List<MasterCatalogSquareInventory>> inventoryListCaptor =
            ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryRepository, times(1))
            .saveAll(inventoryListCaptor.capture());
        List<MasterCatalogSquareInventory> savedInventories = inventoryListCaptor.getValue();
        assertEquals(1, savedInventories.size());

        MasterCatalogSquareInventory savedInventory = savedInventories.get(0);
        assertEquals(masterCatalogRawDataId, savedInventory.getMasterCatalogRawDataId());
    }

    @Test
    void testBuildCreateOrUpdateInventory_WithException() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return a list
        InventoryCountDto inventoryCountDto = mock(InventoryCountDto.class);
        when(inventoryCountDto.getCatalogObjectId()).thenReturn("variation123");
        when(inventoryCountDto.getQuantity()).thenReturn("10");
        when(inventoryCountDto.getState()).thenReturn("IN_STOCK");
        List<InventoryCountDto> inventoryCountList = Arrays.asList(inventoryCountDto);
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(inventoryCountList);

        // Mock variationMappingRepository.findAllByVariationIdIn
        MasterCatalogSquareVariationMapping variationMapping = mock(MasterCatalogSquareVariationMapping.class);
        when(variationMapping.getVariationId()).thenReturn("variation123");
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(UUID.randomUUID());
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(any(), any()))
            .thenReturn(Arrays.asList(variationMapping));

        // Mock masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn to return empty
        when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(any()))
            .thenReturn(Collections.emptyList());

        // Mock saveRawInventoryData to return a UUID
        UUID sourceId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(sourceId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Mock saveInventory to throw an exception
        when(masterCatalogSquareInventoryRepository.saveAll(any()))
            .thenThrow(new RuntimeException("Database save error"));

        // Execute the method and expect an exception
        try {
            inventoryService.syncInventory(request, presentAtLocationIds);
        } catch (RuntimeException e) {
            assertEquals("Database save error", e.getMessage());
        }

        // Verify saveInventory was called
        verify(masterCatalogSquareInventoryRepository, times(1)).saveAll(any(List.class));
    }

    @Test
    void testSyncInventory_WithNullInventoryCountList() {
        // Mock retrieveLatestSyncTimeForInventory to return a fixed Instant
        when(masterCatalogSquareInventoryRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryCounts to return null
        when(squareApiAdapter.batchRetrieveInventoryCounts(eq(storeId), any(BatchRetrieveInventoryCountsRequestDto.class)))
            .thenReturn(null);

        // Execute the method
        inventoryService.syncInventory(request, presentAtLocationIds);

        // Verify that saveRawInventoryData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(any(MasterCatalogSquareRawData.class));

        // Verify that saveInventory was not called
        verify(masterCatalogSquareInventoryRepository, never()).saveAll(any(List.class));

        // Verify no interactions with variationMappingRepository
        verifyNoInteractions(variationMappingRepository);
    }

    /**
     * Test syncInventoryChanges when no inventory changes are found.
     */
    @Test
    void testSyncInventoryChanges_NoInventoryChanges() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return an empty Optional
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.empty());

        // Mock fetchInventoryChanges to return an empty list
        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenReturn(Collections.emptyList());

        // Execute the method
        inventoryService.syncInventoryChanges(request, presentAtLocationIds);

        // Verify fetchInventoryChanges was called
        ArgumentCaptor<BatchRetrieveInventoryChangesRequestDto> requestCaptor = ArgumentCaptor.forClass(
            BatchRetrieveInventoryChangesRequestDto.class);
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryChanges(eq(storeId), requestCaptor.capture());
        BatchRetrieveInventoryChangesRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(SQUARE_API_LIMIT, capturedRequest.getLimit());
        assertEquals(presentAtLocationIds, capturedRequest.getLocationIds());
        assertEquals(List.of("ADJUSTMENT"), capturedRequest.getTypes());
        assertNotNull(capturedRequest.getUpdatedAfter());

        // Verify saveRawInventoryChangesData was not called
        verify(masterCatalogSquareRawDataRepository, never()).save(any(MasterCatalogSquareRawData.class));

        // Verify findThenBuildVariationIdToRawDataIdMap was not called
        verify(variationMappingRepository, never()).findAllByVariationIdInAndStoreIdIs(any(), any());

        // Verify saveInventoryChanges was not called
        verify(masterCatalogSquareInventoryChangeRepository, never()).saveAll(any());
    }

    /**
     * Test syncInventoryChanges successfully saves inventory changes.
     */
    @Test
    void testSyncInventoryChanges_SuccessfulSave() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return a fixed Instant
        MasterCatalogSquareInventoryChange existingChange = mock(MasterCatalogSquareInventoryChange.class);
        when(existingChange.getUpdatedAt()).thenReturn(Instant.parse("2023-01-01T00:00:00Z"));
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.of(existingChange));

        // Mock fetchInventoryChanges to return a list with one InventoryChangeDto
        InventoryAdjustment adjustment = new InventoryAdjustment.Builder()
            .catalogObjectId("variation123")
            .occurredAt("2024-04-27T10:15:30Z")
            .quantity("5")
            .fromState("IN_STOCK")
            .toState("LOW_STOCK")
            .build();

        InventoryChangeDto changeDto = new InventoryChangeDto();
        changeDto.setAdjustment(adjustment);

        List<InventoryChangeDto> inventoryChanges = List.of(changeDto);

        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenReturn(inventoryChanges);

        // Mock saveRawInventoryChangesData to return a UUID
        UUID rawDataId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(rawDataId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Mock variationMappingRepository.findAllByVariationIdIn to return a mapping
        MasterCatalogSquareVariationMapping variationMapping = mock(MasterCatalogSquareVariationMapping.class);
        UUID masterCatalogRawDataId = UUID.randomUUID();
        when(variationMapping.getVariationId()).thenReturn("variation123");
        when(variationMapping.getMasterCatalogRawDataId()).thenReturn(masterCatalogRawDataId);
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(anyList(), eq(storeId)))
            .thenReturn(List.of(variationMapping));

        // Execute the method
        inventoryService.syncInventoryChanges(request, presentAtLocationIds);

        // Verify saveRawInventoryChangesData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryChanges), savedRawData.getData());

        // Verify findThenBuildVariationIdToRawDataIdMap was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variation123"), capturedVariationIds);

        // Capture the argument passed to saveAll
        ArgumentCaptor<List<MasterCatalogSquareInventoryChange>> inventoryChangeCaptor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogSquareInventoryChangeRepository, times(1)).saveAll(inventoryChangeCaptor.capture());
        List<MasterCatalogSquareInventoryChange> savedChanges = inventoryChangeCaptor.getValue();
        assertEquals(1, savedChanges.size());
        MasterCatalogSquareInventoryChange savedChange = savedChanges.get(0);
        assertEquals(masterCatalogRawDataId, savedChange.getMasterCatalogRawDataId());
        assertEquals(5, savedChange.getQuantity());
        assertEquals("IN_STOCK", savedChange.getFromState());
        assertEquals("LOW_STOCK", savedChange.getToState());
        assertEquals(rawDataId.toString(), savedChange.getSourceId());
        assertNotNull(savedChange.getUpdatedAt());
        assertEquals(Instant.parse("2024-04-27T10:15:30Z"), savedChange.getOccurredAt());
    }

    /**
     * Test syncInventoryChanges with unmapped variation IDs.
     */
    @Test
    void testSyncInventoryChanges_WithUnmappedVariationIds() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return a fixed Instant
        MasterCatalogSquareInventoryChange existingChange = mock(MasterCatalogSquareInventoryChange.class);
        when(existingChange.getUpdatedAt()).thenReturn(Instant.parse("2023-01-01T00:00:00Z"));
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.of(existingChange));

        // Mock fetchInventoryChanges to return a list with one InventoryChangeDto
        InventoryAdjustment adjustment = new InventoryAdjustment.Builder()
            .catalogObjectId("variationUnmapped")
            .occurredAt("2024-04-27T10:15:30Z")
            .quantity("5")
            .fromState("IN_STOCK")
            .toState("LOW_STOCK")
            .build();

        InventoryChangeDto changeDto = new InventoryChangeDto();
        changeDto.setAdjustment(adjustment);

        List<InventoryChangeDto> inventoryChanges = List.of(changeDto);

        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenReturn(inventoryChanges);

        // Mock saveRawInventoryChangesData to return a UUID
        UUID rawDataId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(rawDataId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Mock variationMappingRepository.findAllByVariationIdIn to return an empty list (unmapped)
        when(variationMappingRepository.findAllByVariationIdInAndStoreIdIs(anyList(), eq(storeId)))
            .thenReturn(Collections.emptyList());

        // Execute the method
        inventoryService.syncInventoryChanges(request, presentAtLocationIds);

        // Verify saveRawInventoryChangesData was called
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryChanges), savedRawData.getData());

        // Verify findThenBuildVariationIdToRawDataIdMap was called
        ArgumentCaptor<List<String>> variationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(variationMappingRepository, times(1)).findAllByVariationIdInAndStoreIdIs(variationIdsCaptor.capture(),
            eq(storeId));
        List<String> capturedVariationIds = variationIdsCaptor.getValue();
        assertEquals(Collections.singletonList("variationUnmapped"), capturedVariationIds);

        // Verify that no inventory changes were saved due to unmapped variation IDs
        verify(masterCatalogSquareInventoryChangeRepository, never()).saveAll(any());
    }

    /**
     * Test syncInventoryChanges when API call throws an ApiException.
     */
    @Test
    void testSyncInventoryChanges_WithApiException() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return a fixed Instant
        MasterCatalogSquareInventoryChange existingChange = mock(MasterCatalogSquareInventoryChange.class);
        when(existingChange.getUpdatedAt()).thenReturn(Instant.parse("2023-01-01T00:00:00Z"));
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.of(existingChange));

        // Mock fetchInventoryChanges to throw an ApiException
        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenThrow(new RuntimeException("API error"));

        // Execute the method and expect a RuntimeException
        assertThrows(RuntimeException.class, () -> {
            inventoryService.syncInventoryChanges(request, presentAtLocationIds);
        });
    }

    /**
     * Test syncInventoryChanges when response contains errors.
     */
    @Test
    void testSyncInventoryChanges_WithErrorInResponse() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return a fixed Instant
        MasterCatalogSquareInventoryChange existingChange = mock(MasterCatalogSquareInventoryChange.class);
        when(existingChange.getUpdatedAt()).thenReturn(Instant.parse("2023-01-01T00:00:00Z"));
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.of(existingChange));

        // Mock fetchInventoryChanges to return a list with one InventoryChangeDto with null adjustment (simulating an error)
        InventoryChangeDto changeDtoWithNullAdjustment = new InventoryChangeDto();
        changeDtoWithNullAdjustment.setAdjustment(null);

        List<InventoryChangeDto> inventoryChanges = List.of(changeDtoWithNullAdjustment);

        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenReturn(inventoryChanges);

        // Mock saveRawInventoryChangesData to return a UUID
        UUID rawDataId = UUID.randomUUID();
        MasterCatalogSquareRawData rawData = mock(MasterCatalogSquareRawData.class);
        when(rawData.getId()).thenReturn(rawDataId);
        when(masterCatalogSquareRawDataRepository.save(any(MasterCatalogSquareRawData.class)))
            .thenReturn(rawData);

        // Execute the method
        inventoryService.syncInventoryChanges(request, presentAtLocationIds);

        // Verify fetchInventoryChanges was called
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryChanges(eq(storeId),
            any(BatchRetrieveInventoryChangesRequestDto.class));

        // Verify saveRawInventoryChangesData was called with the inventoryChanges list
        ArgumentCaptor<MasterCatalogSquareRawData> rawDataCaptor = ArgumentCaptor.forClass(MasterCatalogSquareRawData.class);
        verify(masterCatalogSquareRawDataRepository, times(1)).save(rawDataCaptor.capture());
        MasterCatalogSquareRawData savedRawData = rawDataCaptor.getValue();
        assertEquals(SerializationUtils.toTree(inventoryChanges), savedRawData.getData());

    }

    /**
     * Test syncInventoryChanges when fetchInventoryChanges returns null.
     */
    @Test
    void testSyncInventoryChanges_WithNullInventoryChanges() {
        // Mock retrieveLatestSyncTimeForInventoryChanges to return a fixed Instant
        MasterCatalogSquareInventoryChange existingChange = mock(MasterCatalogSquareInventoryChange.class);
        when(existingChange.getUpdatedAt()).thenReturn(Instant.parse("2023-01-01T00:00:00Z"));
        when(masterCatalogSquareInventoryChangeRepository.findTopByStoreIdAndOrderByUpdatedAtDesc(eq(storeId)))
            .thenReturn(Optional.of(existingChange));

        // Mock fetchInventoryChanges to return null
        when(squareApiAdapter.batchRetrieveInventoryChanges(eq(storeId), any(BatchRetrieveInventoryChangesRequestDto.class)))
            .thenReturn(null);

        // Execute the method
        inventoryService.syncInventoryChanges(request, presentAtLocationIds);

        // Verify fetchInventoryChanges was called
        verify(squareApiAdapter, times(1)).batchRetrieveInventoryChanges(eq(storeId),
            any(BatchRetrieveInventoryChangesRequestDto.class));
    }
}