package com.mercaso.data.recommendation.utils.resource_utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.Map;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class ReplenishmentRecommendationResourceApiUtils extends IntegrationTestRestUtil {

  private static final String V1_SEARCH_BY_STORE_ID = "/recommendation/v1/replenishment-recommendations/departments";
  private static final String V1_SEARCH_BY_STORE_AND_DEPARTMENT_ID = "/recommendation/v1/replenishment-recommendations/search";

  public ReplenishmentRecommendationResourceApiUtils(Environment environment) {
    super(environment);
  }

  public List<DepartmentDto> searchByStoreId(String storeId) throws Exception {
    return getEntityList(V1_SEARCH_BY_STORE_ID + "?storeId=" + storeId,
        null,
        DepartmentDto.class);
  }

  public PageableResponse<ReplenishmentRecommendationDto> searchByStoreIdAndDepartmentId(String storeId,String departmentId) throws JsonProcessingException {

    Map<String, String> params = new java.util.HashMap<>(Map.of());
    params.put("pageNumber", "0");
    params.put("pageSize", "20");
    params.put("storeId", storeId);
    params.put("departmentId", departmentId);

    ResponseEntity<String> response = performRequest(
        V1_SEARCH_BY_STORE_AND_DEPARTMENT_ID,
        params,
        null,
        HttpMethod.GET);

    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            ReplenishmentRecommendationDto.class));
  }
}
