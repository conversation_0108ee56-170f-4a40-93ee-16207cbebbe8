package com.mercaso.data.third_party.controller.finale;


import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockFilter;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityFilter;
import com.mercaso.data.third_party.utils.controller_utils.FinaleProductRestApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;

class FinaleProductControllerIT extends AbstractIT {

    @Autowired
    protected FinaleProductRestApi finaleProductRestApi;

    @Test
    void testGetProducts() {

        FinaleAvailableStockFilter filter = new FinaleAvailableStockFilter();
        filter.setPageable(new CustomPageable(1, 10));
        ResponseEntity<CustomPage> products = finaleProductRestApi.getProducts(filter);
        assertNotNull(products);
    }

    @Test
    void testGetFacilities() {

        FinaleFacilityFilter filter = new FinaleFacilityFilter();
        ResponseEntity<CustomPage> products = finaleProductRestApi.getFacilities(filter);
        assertNotNull(products);
    }
}
