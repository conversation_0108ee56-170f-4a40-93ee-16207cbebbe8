package com.mercaso.data.metrics.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.service.MetricsStoreService;
import com.mercaso.data.metrics.utils.controller_utils.UserRestApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

class UserControllerIT extends AbstractIT {

    @Autowired
    private UserRestApi userRestApi;

    @MockBean
    private MetricsStoreService metricsStoreService;

    @Test
    void testSearchUserAddressesV2() {

        String addressId = UUID.randomUUID().toString();
        List<SearchStoreAddressDto> searchStoreAddressDtoList = List.of(new SearchStoreAddressDto(addressId, "address"));
        Page<SearchStoreAddressDto> userAddressDtoPage = new PageImpl<>(searchStoreAddressDtoList,
            PageRequest.of(1, 20),
            searchStoreAddressDtoList.size());

        when(metricsStoreService.searchStoreAddressesV2ForSalesApp(any())).thenReturn(userAddressDtoPage);

        CustomPage<SearchStoreAddressDto> result = userRestApi.searchUserAddressesV2("keyword", 1, 20);

        assertNotNull(result);
        assertEquals(searchStoreAddressDtoList.size(), result.getData().size());
        assertEquals(searchStoreAddressDtoList.getFirst().getId(), result.getData().getFirst().getId());
        assertEquals(searchStoreAddressDtoList.getFirst().getAddressName(), result.getData().getFirst().getAddressName());
    }
}
