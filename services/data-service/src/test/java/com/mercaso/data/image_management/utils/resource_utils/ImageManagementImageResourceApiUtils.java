package com.mercaso.data.image_management.utils.resource_utils;

import com.mercaso.data.image_management.dto.Result;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import org.springframework.core.ParameterizedTypeReference;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.http.ResponseEntity;
import java.util.HashMap;
import java.util.Map;

@Component
public class ImageManagementImageResourceApiUtils extends IntegrationTestRestUtil {

  private static final String V1_IMAGE_MANAGEMENT_ITEM_UPLOAD = "/v1/image-management/image/upload";
  private static final String V1_IMAGE_MANAGEMENT_ITEM_SEARCH = "/v1/image-management/image/search";

  public ImageManagementImageResourceApiUtils(Environment environment) {
    super(environment);
  }

  public Result<?> upload(String shotAt, String fileName,
      ImageTypeEnum imageTypeEnum, File file) throws IOException {

    byte[] fileContent = Files.readAllBytes(file.toPath());
    ByteArrayResource fileResource = new ByteArrayResource(fileContent) {
      @Override
      public String getFilename() {
        return file.getName();
      }
    };
    MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
    body.add("shotAt", shotAt);
    body.add("fileName", fileName);
    body.add("imageType", imageTypeEnum.name());
    body.add("batchId", "test-batch-123");
    body.add("file", fileResource);

    return uploadFile(V1_IMAGE_MANAGEMENT_ITEM_UPLOAD, body, Result.class);
  }

  public CustomPage<ImageManagementItemImageDto> searchImages(ImageSearchRequestDto request) {
    Map<String, Object> params = new HashMap<>();
    if (request.getSku() != null) {
      params.put("sku", request.getSku());
    }
    if (request.getUpc() != null) {
      params.put("upc", request.getUpc());
    }
    if (request.getImageType() != null) {
      params.put("imageType", request.getImageType().name());
    }
    if (request.getImageAngle() != null) {
      params.put("imageAngle", request.getImageAngle());
    }
    if (request.getIsPrimary() != null) {
      params.put("isPrimary", request.getIsPrimary());
    }
    if (request.getIsEach() != null) {
      params.put("isEach", request.getIsEach());
    }
    if (request.getPage() != null) {
      params.put("page", request.getPage());
    }
    if (request.getPageSize() != null) {
      params.put("pageSize", request.getPageSize());
    }
    if (request.getSortBy() != null) {
      params.put("sortBy", request.getSortBy());
    }
    if (request.getSortDirection() != null) {
      params.put("sortDirection", request.getSortDirection());
    }
    ResponseEntity<CustomPage<ImageManagementItemImageDto>> response = getEntityByMap(
        V1_IMAGE_MANAGEMENT_ITEM_SEARCH,
        new ParameterizedTypeReference<CustomPage<ImageManagementItemImageDto>>() {
        },
        params
    );
    return response.getBody();
  }
}
