package com.mercaso.data.third_party.adapter.finale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.mercaso.data.third_party.adaptor.finale.impl.FinaleProductApiAdapterImpl;
import com.mercaso.data.third_party.config.FinaleConfigProperties;
import com.mercaso.data.third_party.dto.finale.FinaleProductDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityDataResponseDto;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityPrimaryDataResponseDto;
import com.mercaso.data.utils.HttpClientUtils;
import com.mercaso.data.utils.SerializationUtils;
import java.io.IOException;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FinaleProductApiAdapterImplTest {

    @Mock
    private FinaleConfigProperties properties;

    @InjectMocks
    private FinaleProductApiAdapterImpl adapter;

    @Test
    void fetchProductData_Success() throws IOException {
        when(properties.getGraphqlApiUrl()).thenReturn("https://api.example.com/graphql");
        when(properties.getToken()).thenReturn("token");

        FinaleProductDataResponseDto mockResponse = new FinaleProductDataResponseDto();
        // Set up mockResponse as needed

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class);
            MockedStatic<SerializationUtils> mockedSerializationUtils = mockStatic(SerializationUtils.class)) {

            mockedSerializationUtils.when(() -> SerializationUtils.serialize(any()))
                .thenReturn("serialized-request-body");

            mockedHttpClientUtils.when(() -> HttpClientUtils.executePostRequest(
                    anyString(), anyString(), anyMap(), eq(FinaleProductDataResponseDto.class)))
                .thenReturn(mockResponse);

            Optional<FinaleProductDataResponseDto> result = adapter.fetchProductData(anyString());

            assertTrue(result.isPresent());
            assertEquals(mockResponse, result.get());

            mockedHttpClientUtils.verify(() -> HttpClientUtils.executePostRequest(
                eq("https://api.example.com/graphql"),
                eq("serialized-request-body"),
                argThat(headers ->
                    headers.get("Content-Type").equals("application/json") &&
                        headers.get("Authorization").equals("Basic token")
                ),
                eq(FinaleProductDataResponseDto.class)
            ));
        }
    }

    @Test
    void fetchProductData_HttpClientException() throws IOException {
        when(properties.getGraphqlApiUrl()).thenReturn("https://api.example.com/graphql");
        when(properties.getToken()).thenReturn("token");

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class);
            MockedStatic<SerializationUtils> mockedSerializationUtils = mockStatic(SerializationUtils.class)) {

            mockedSerializationUtils.when(() -> SerializationUtils.serialize(any()))
                .thenReturn("serialized-request-body");

            mockedHttpClientUtils.when(() -> HttpClientUtils.executePostRequest(
                    anyString(), anyString(), anyMap(), eq(FinaleProductDataResponseDto.class)))
                .thenThrow(new IOException("HTTP request failed"));

            Optional<FinaleProductDataResponseDto> result = adapter.fetchProductData(anyString());

            assertFalse(result.isPresent());
        }
    }

    @Test
    void fetchFacilityData_Success() throws IOException {
        when(properties.getFacilityApiUrl()).thenReturn("https://api.example.com/facility");
        when(properties.getToken()).thenReturn("token");

        FinaleFacilityDataResponseDto mockResponse = new FinaleFacilityDataResponseDto();
        // Set up mockResponse as needed

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class)) {
            mockedHttpClientUtils.when(() -> HttpClientUtils.executeGetRequest(
                    anyString(), anyMap(), eq(FinaleFacilityDataResponseDto.class)))
                .thenReturn(mockResponse);

            Optional<FinaleFacilityDataResponseDto> result = adapter.fetchFacilityData();

            assertTrue(result.isPresent());
            assertEquals(mockResponse, result.get());

            mockedHttpClientUtils.verify(() -> HttpClientUtils.executeGetRequest(
                eq("https://api.example.com/facility"),
                argThat(headers ->
                    headers.get("Content-Type").equals("application/json") &&
                        headers.get("Authorization").equals("Basic token")
                ),
                eq(FinaleFacilityDataResponseDto.class)
            ));
        }
    }

    @Test
    void fetchFacilityData_HttpClientException() throws IOException {
        when(properties.getFacilityApiUrl()).thenReturn("https://api.example.com/facility");
        when(properties.getToken()).thenReturn("token");

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class)) {
            mockedHttpClientUtils.when(() -> HttpClientUtils.executeGetRequest(
                    anyString(), anyMap(), eq(FinaleFacilityDataResponseDto.class)))
                .thenThrow(new IOException("HTTP request failed"));

            Optional<FinaleFacilityDataResponseDto> result = adapter.fetchFacilityData();

            assertFalse(result.isPresent());
        }
    }

    @Test
    void fetchFacilityConfigData_Success() throws IOException {
        when(properties.getFinalePrimaryApiUrl()).thenReturn("https://api.example.com/customization/PRIMARY");
        when(properties.getToken()).thenReturn("token");

        FinaleFacilityPrimaryDataResponseDto mockResponse = new FinaleFacilityPrimaryDataResponseDto();
        // Set up mockResponse as needed

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class)) {
            mockedHttpClientUtils.when(() -> HttpClientUtils.executeGetRequest(
                    anyString(), anyMap(), eq(FinaleFacilityPrimaryDataResponseDto.class)))
                .thenReturn(mockResponse);

            Optional<FinaleFacilityPrimaryDataResponseDto> result = adapter.fetchFacilityUrlData();

            assertTrue(result.isPresent());
            assertEquals(mockResponse, result.get());

            mockedHttpClientUtils.verify(() -> HttpClientUtils.executeGetRequest(
                eq("https://api.example.com/customization/PRIMARY"),
                argThat(headers ->
                    headers.get("Content-Type").equals("application/json") &&
                        headers.get("Authorization").equals("Basic token")
                ),
                eq(FinaleFacilityPrimaryDataResponseDto.class)
            ));
        }
    }

    @Test
    void fetchFacilityConfigData_HttpClientException() throws IOException {
        when(properties.getFinalePrimaryApiUrl()).thenReturn("https://api.example.com/customization/PRIMARY");
        when(properties.getToken()).thenReturn("token");

        try (MockedStatic<HttpClientUtils> mockedHttpClientUtils = mockStatic(HttpClientUtils.class)) {
            mockedHttpClientUtils.when(() -> HttpClientUtils.executeGetRequest(
                    anyString(), anyMap(), eq(FinaleFacilityPrimaryDataResponseDto.class)))
                .thenThrow(new IOException("HTTP request failed"));

            Optional<FinaleFacilityPrimaryDataResponseDto> result = adapter.fetchFacilityUrlData();

            assertFalse(result.isPresent());
        }
    }
}