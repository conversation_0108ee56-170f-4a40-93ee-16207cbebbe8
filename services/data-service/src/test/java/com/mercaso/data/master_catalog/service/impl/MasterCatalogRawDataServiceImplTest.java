package com.mercaso.data.master_catalog.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.mapper.MasterCatalogRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class MasterCatalogRawDataServiceImplTest {

    private MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogRawDataMapper masterCatalogRawDataMapper;
    private MasterCatalogImageRepository masterCatalogImageRepository;
    private ExternalApiAdapter externalApiAdapter;
    private S3OperationAdapter s3OperationAdapter;
    private ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private MasterCatalogRawDataServiceImpl service;

    @BeforeEach
    void setUp() {
        masterCatalogRawDataDuplicationRepository = mock(MasterCatalogRawDataDuplicationRepository.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        masterCatalogRawDataMapper = mock(MasterCatalogRawDataMapper.class);
        masterCatalogImageRepository = mock(MasterCatalogImageRepository.class);
        externalApiAdapter = mock(ExternalApiAdapter.class);
        s3OperationAdapter = mock(S3OperationAdapter.class);
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);

        service = new MasterCatalogRawDataServiceImpl(
            masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataRepository,
            masterCatalogRawDataMapper,
            masterCatalogImageRepository,
            externalApiAdapter,
            s3OperationAdapter,
            applicationEventPublisherProvider
        );
    }

    @Test
    void testSyncRawDataByUpc_WithOtherRecords_ShouldSyncAndPublishEvents() {
        // Given
        String upc = "123456789012";
        UUID sourceId = UUID.randomUUID();
        UUID targetId1 = UUID.randomUUID();
        UUID targetId2 = UUID.randomUUID();

        MasterCatalogRawData sourceData = createRawData(sourceId, upc, "Source Product");
        sourceData.setBrand("Source Brand");
        sourceData.setPrice(BigDecimal.valueOf(10.99));

        MasterCatalogRawData targetData1 = createRawData(targetId1, upc, "Target Product 1");
        targetData1.setBrand("Target Brand 1");
        targetData1.setPrice(BigDecimal.valueOf(5.99));

        MasterCatalogRawData targetData2 = createRawData(targetId2, upc, "Target Product 2");
        targetData2.setBrand("Target Brand 2");
        targetData2.setPrice(BigDecimal.valueOf(7.99));

        List<MasterCatalogRawData> rawDataList = Arrays.asList(sourceData, targetData1, targetData2);

        when(masterCatalogRawDataRepository.findAllByUpc(upc)).thenReturn(rawDataList);
        when(masterCatalogRawDataRepository.save(any(MasterCatalogRawData.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));
        when(masterCatalogImageRepository.findByMasterCatalogRawDataId(any(UUID.class)))
            .thenReturn(Collections.emptyList());

        // When
        service.syncMasterCatalogRawData(sourceData);

        // Then
        verify(masterCatalogRawDataRepository).findAllByUpc(upc);
        verify(masterCatalogRawDataRepository, times(2)).save(any(MasterCatalogRawData.class));
        verify(applicationEventPublisherProvider, times(2)).publishMasterCatalogRawDataSyncedEvent(any(), any(), any());
    }

    @Test
    void testSyncRawDataByUpc_WithImages_ShouldSyncImages() {
        // Given
        String upc = "123456789012";
        UUID sourceId = UUID.randomUUID();
        UUID targetId = UUID.randomUUID();

        MasterCatalogRawData sourceData = createRawData(sourceId, upc, "Source Product");
        MasterCatalogImage sourceImage1 = createImage("source/image1.jpg", true);
        MasterCatalogImage sourceImage2 = createImage("source/image2.jpg", false);
        sourceData.setImages(Arrays.asList(sourceImage1, sourceImage2));

        MasterCatalogRawData targetData = createRawData(targetId, upc, "Target Product");

        when(masterCatalogRawDataRepository.findAllByUpc(upc))
            .thenReturn(Arrays.asList(sourceData, targetData));
        when(masterCatalogRawDataRepository.save(any(MasterCatalogRawData.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));
        when(masterCatalogImageRepository.findByMasterCatalogRawDataId(any(UUID.class)))
            .thenReturn(Collections.emptyList());

        // When
        service.syncMasterCatalogRawData(sourceData);

        // Then
        verify(masterCatalogImageRepository).deleteByMasterCatalogRawDataId(targetId);
        verify(masterCatalogImageRepository).saveAll(anyList());
    }

    private MasterCatalogRawData createRawData(UUID id, String upc, String name) {
        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(id);
        rawData.setUpc(upc);
        rawData.setName(name);
        rawData.setDescription("Test Description");
        rawData.setBrand("Test Brand");
        rawData.setPackageSize(100);
        rawData.setPackageType(PackageType.PACK);
        rawData.setDepartment("Test Department");
        rawData.setCategory("Test Category");
        rawData.setSubCategory("Test SubCategory");
        rawData.setClazz("Test Class");
        rawData.setPrimaryVendor("Test Vendor");
        rawData.setPrice(BigDecimal.valueOf(9.99));
        rawData.setStatus("COMPLETED");
        return rawData;
    }

    private MasterCatalogImage createImage(String imagePath, boolean primaryImage) {
        return MasterCatalogImage.builder()
            .imagePath(imagePath)
            .primaryImage(primaryImage)
            .build();
    }
}
