package com.mercaso.data.recommendation.controller;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.repository.ReplenishmentRecommendationRepository;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.recommendation.utils.resource_utils.ReplenishmentRecommendationResourceApiUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ReplenishmentRecommendationResourceIT extends AbstractIT {

  @Autowired
  private ReplenishmentRecommendationRepository replenishmentRecommendationRepository;
  @Autowired
  private ReplenishmentRecommendationResourceApiUtils replenishmentRecommendationResourceApiUtils;

  @Test
  public void getDepartments_success() throws Exception {
    String storeId1 = String.valueOf(UUID.randomUUID());

    String departmentId1 = UUID.randomUUID().toString();
    String departmentId2 = UUID.randomUUID().toString();

    String batchNumber1 = Instant.now().minusSeconds(40).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber2 = Instant.now().minusSeconds(30).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber3 = Instant.now().minusSeconds(20).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber4 = Instant.now().minusSeconds(10).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

    ReplenishmentRecommendation recommendation1 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId1), batchNumber1);
    ReplenishmentRecommendation recommendation2 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId2), batchNumber2);
    ReplenishmentRecommendation recommendation3 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId1), batchNumber3);
    ReplenishmentRecommendation recommendation4 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId2), batchNumber4);

    replenishmentRecommendationRepository.saveAll(
        List.of(recommendation1, recommendation2, recommendation3, recommendation4));

    List<DepartmentDto> departments = replenishmentRecommendationResourceApiUtils.searchByStoreId(storeId1);

    assert !departments.isEmpty();
    departments.forEach(department -> {
      assert department.id().equals(UUID.fromString(departmentId2).toString());
    });
  }

  @Test
  public void searchReplenishmentRecommendations_success() throws Exception {
    String storeId1 = UUID.randomUUID().toString();

    String departmentId1 = UUID.randomUUID().toString();
    String departmentId2 = UUID.randomUUID().toString();
    String departmentId3 = UUID.randomUUID().toString();
    String departmentId4 = UUID.randomUUID().toString();

    String batchNumber1 = Instant.now().minusSeconds(40).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber2 = Instant.now().minusSeconds(30).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber3 = Instant.now().minusSeconds(20).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    String batchNumber4 = Instant.now().minusSeconds(10).atZone(ZoneId.systemDefault()).format(
        DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

    ReplenishmentRecommendation recommendation1 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId1), batchNumber1);
    ReplenishmentRecommendation recommendation2 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId2), batchNumber2);
    ReplenishmentRecommendation recommendation3 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId3), batchNumber3);
    ReplenishmentRecommendation recommendation4 = buildRecommendationData(UUID.fromString(storeId1),
        UUID.fromString(departmentId4), batchNumber4);

    replenishmentRecommendationRepository.saveAll(
        List.of(recommendation1, recommendation2, recommendation3, recommendation4));

    PageableResponse<ReplenishmentRecommendationDto> recommendations = replenishmentRecommendationResourceApiUtils.searchByStoreIdAndDepartmentId(
        storeId1, departmentId1);
    PageableResponse<ReplenishmentRecommendationDto> recommendationAll = replenishmentRecommendationResourceApiUtils.searchByStoreIdAndDepartmentId(
        storeId1, null);

    assert recommendations.getData().isEmpty();
    assert !recommendationAll.getData().isEmpty();

    ReplenishmentRecommendationDto recommendation = recommendationAll.getData().get(0);
    assert recommendation.getStoreId().equals(UUID.fromString(storeId1)) && recommendation.getDepartmentId()
        .equals(UUID.fromString(departmentId4));

  }

  @Test
  public void getDepartments_whenNoDataExists_shouldReturnEmptyList() throws Exception {
    // Use a random store ID that doesn't have any data
    String storeIdWithNoData = UUID.randomUUID().toString();

    List<DepartmentDto> departments = replenishmentRecommendationResourceApiUtils.searchByStoreId(storeIdWithNoData);

    // Should return empty list when no data exists for the store
    assert departments.isEmpty();
  }

  @Test
  public void searchReplenishmentRecommendations_whenNoDataExists_shouldReturnEmptyResult() throws Exception {
    // Use a random store ID that doesn't have any data
    String storeIdWithNoData = UUID.randomUUID().toString();

    PageableResponse<ReplenishmentRecommendationDto> recommendations = replenishmentRecommendationResourceApiUtils.searchByStoreIdAndDepartmentId(
        storeIdWithNoData, null);

    // Should return empty result when no data exists for the store
    assert recommendations.getData().isEmpty();
    assert recommendations.getTotalElements() == 0;
    assert recommendations.getTotalPages() == 0;
  }

  private ReplenishmentRecommendation buildRecommendationData(UUID storeId, UUID departmentId, String batchNumber) {
    return ReplenishmentRecommendation.builder()
        .storeId(storeId)
        .productId("Test Product")
        .sku(Instant.now().toString())
        .upc(String.valueOf(System.currentTimeMillis()))
        .recommendedQuantity(new BigDecimal(Instant.now().getEpochSecond()))
        .departmentId(departmentId)
        .departmentName("Test Department Name")
        .batchNumber(batchNumber)
        .recentOrderCount(1)
        .nextOrderTime(Instant.now().plusSeconds(60))
        .build();
  }
}
