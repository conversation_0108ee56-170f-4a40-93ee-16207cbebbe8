//package com.mercaso.data.image_management.repository;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//
//import com.mercaso.data.AbstractIT;
//import com.mercaso.data.image_management.entity.ImageManagementImage;
//import com.mercaso.data.image_management.entity.ImageManagementItemImage;
//import com.mercaso.data.image_management.enums.ImageTypeEnum;
//import java.time.Instant;
//import java.util.UUID;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Pageable;
//
//public class ImageManagementImageRepositoryIT extends AbstractIT {
//
//    @Autowired
//    private ImageManagementItemImageRepository imageManagementItemImageRepository;
//
//    @Autowired
//    private ImageManagementImageRepository imageManagementImageRepository;
//
//    @Test
//    public void searchImagesWithValidParametersSuccess() {
//        UUID imageId = UUID.randomUUID();
//        String sku = "TEST-SKU-1";
//        String upc = "UPC-1";
//
//        ImageManagementImage image = ImageManagementImage.builder()
//            .id(imageId)
//            .fileName("test-image.jpg")
//            .filePath("/test/path/test-image.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image);
//
//        ImageManagementItemImage itemImage = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId)
//            .sku(sku)
//            .upc(upc)
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(true)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage);
//
//        Pageable pageable = PageRequest.of(0, 10);
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            sku, upc, ImageTypeEnum.RAW.name(), null, null, null, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku.equals(item.getSku()) && upc.equals(item.getUpc())));
//
//        ImageManagementItemImage foundItem = result.getContent().stream()
//            .filter(item -> sku.equals(item.getSku()) && upc.equals(item.getUpc()))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(foundItem);
//        assertEquals(sku, foundItem.getSku());
//        assertEquals(upc, foundItem.getUpc());
//    }
//
//    @Test
//    public void searchImagesWithDefaultParametersDefaults() {
//        UUID imageId = UUID.randomUUID();
//        String sku = "TEST-SKU-1";
//
//        ImageManagementImage image = ImageManagementImage.builder()
//            .id(imageId)
//            .fileName("test-image.jpg")
//            .filePath("/test/path/test-image.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image);
//
//        ImageManagementItemImage itemImage = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId)
//            .sku(sku)
//            .upc("123456789")
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(true)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage);
//
//        Pageable pageable = PageRequest.of(0, 20); // default page size
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            sku, null, null, null, null, null, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku.equals(item.getSku())));
//
//        ImageManagementItemImage foundItem = result.getContent().stream()
//            .filter(item -> sku.equals(item.getSku()))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(foundItem);
//        assertEquals(sku, foundItem.getSku());
//    }
//
//    @Test
//    public void searchImagesWithPrimaryImageFilterFilteredResults() {
//        UUID imageId = UUID.randomUUID();
//        String sku = "TEST-SKU-1";
//
//        ImageManagementImage image = ImageManagementImage.builder()
//            .id(imageId)
//            .fileName("test-image.jpg")
//            .filePath("/test/path/test-image.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image);
//
//        ImageManagementItemImage itemImage = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId)
//            .sku(sku)
//            .upc("123456789")
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(true)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage);
//
//        Pageable pageable = PageRequest.of(0, 10);
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            sku, null, null, null, true, null, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku.equals(item.getSku()) && item.getIsPrimary()));
//
//        ImageManagementItemImage foundItem = result.getContent().stream()
//            .filter(item -> sku.equals(item.getSku()))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(foundItem);
//        assertTrue(foundItem.getIsPrimary());
//    }
//
//    @Test
//    public void searchImagesWithEachFlagFilterResults() {
//        UUID imageId = UUID.randomUUID();
//        String sku = "TEST-SKU-1";
//
//        ImageManagementImage image = ImageManagementImage.builder()
//            .id(imageId)
//            .fileName("test-image.jpg")
//            .filePath("/test/path/test-image.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image);
//
//        ImageManagementItemImage itemImage = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId)
//            .sku(sku)
//            .upc("123456789")
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(false)
//            .eachFlag(true)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage);
//
//        Pageable pageable = PageRequest.of(0, 10);
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            sku, null, null, null, null, true, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku.equals(item.getSku()) && item.getEachFlag()));
//
//        ImageManagementItemImage foundItem = result.getContent().stream()
//            .filter(item -> sku.equals(item.getSku()))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(foundItem);
//        assertTrue(foundItem.getEachFlag());
//    }
//
//    @Test
//    public void searchImagesWithSortingSorting() {
//        UUID imageId1 = UUID.randomUUID();
//        UUID imageId2 = UUID.randomUUID();
//        String sku1 = "TEST-SKU-1";
//        String sku2 = "TEST-SKU-2";
//
//        ImageManagementImage image1 = ImageManagementImage.builder()
//            .id(imageId1)
//            .fileName("test-image1.jpg")
//            .filePath("/test/path/test-image1.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image1);
//
//        ImageManagementImage image2 = ImageManagementImage.builder()
//            .id(imageId2)
//            .fileName("test-image2.jpg")
//            .filePath("/test/path/test-image2.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image2);
//
//        ImageManagementItemImage itemImage1 = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId1)
//            .sku(sku1)
//            .upc("123456789")
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(false)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage1);
//
//        ImageManagementItemImage itemImage2 = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId2)
//            .sku(sku2)
//            .upc("987654321")
//            .imageAngel("Top_1")
//            .imageType(ImageTypeEnum.EDIT.name())
//            .isPrimary(false)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage2);
//
//        Pageable pageable = PageRequest.of(0, 10);
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            null, null, null, null, null, null, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku1.equals(item.getSku())));
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku2.equals(item.getSku())));
//    }
//
//    @Test
//    public void searchImagesRepositoryTestWithSkuFilter() {
//        UUID imageId = UUID.randomUUID();
//        String sku = "TEST-SKU-1";
//
//        ImageManagementImage image = ImageManagementImage.builder()
//            .id(imageId)
//            .fileName("test-image.jpg")
//            .filePath("/test/path/test-image.jpg")
//            .fileSize(1024L)
//            .mimeType("image/jpeg")
//            .shotAt(Instant.now())
//            .createdBy("test-user")
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementImageRepository.save(image);
//
//        ImageManagementItemImage itemImage = ImageManagementItemImage.builder()
//            .id(UUID.randomUUID())
//            .imageId(imageId)
//            .sku(sku)
//            .upc("123456789")
//            .imageAngel("Front_2")
//            .imageType(ImageTypeEnum.RAW.name())
//            .isPrimary(true)
//            .eachFlag(false)
//            .createdAt(Instant.now())
//            .updatedAt(Instant.now())
//            .build();
//        imageManagementItemImageRepository.save(itemImage);
//
//        Pageable pageable = PageRequest.of(0, 10);
//        Page<ImageManagementItemImage> result = imageManagementItemImageRepository.searchImages(
//            sku, null, null, null, null, null, pageable
//        );
//
//        assertNotNull(result);
//        assertTrue(result.getContent().stream()
//            .anyMatch(item -> sku.equals(item.getSku())));
//
//        ImageManagementItemImage foundItem = result.getContent().stream()
//            .filter(item -> sku.equals(item.getSku()))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(foundItem);
//        assertEquals(sku, foundItem.getSku());
//    }
//}