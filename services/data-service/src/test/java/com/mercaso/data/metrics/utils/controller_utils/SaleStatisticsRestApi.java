package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.metrics.dto.MetricsItemReplenishmentForecastDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SaleStatisticsRestApi extends IntegrationTestRestUtil {

    private static final String ITEM_REPLENISHMENT_FORECAST_PATH = "/sale-statistics/item-replenishment-forecast";

    public SaleStatisticsRestApi(Environment environment) {
        super(environment);
    }

    public List<MetricsItemReplenishmentForecastDto> getItemReplenishmentForecast(String addressId, String departmentName)
        throws Exception {

        return getEntityList(
            ITEM_REPLENISHMENT_FORECAST_PATH + "?addressId=" + addressId + "&departmentName=" + departmentName,
            null,
            MetricsItemReplenishmentForecastDto.class);
    }
}
