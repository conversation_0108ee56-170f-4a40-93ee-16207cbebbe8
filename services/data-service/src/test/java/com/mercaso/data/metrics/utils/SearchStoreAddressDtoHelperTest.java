package com.mercaso.data.metrics.utils;

import static com.mercaso.data.metrics.mock.AddressInfoEntityMock.addressInfoEntityMock;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.entity.AddressInfoEntity;
import org.junit.jupiter.api.Test;

class SearchStoreAddressDtoHelperTest {

    @Test
    void testBuildStoreAddressDto() {
        // Arrange
        AddressInfoEntity entity = addressInfoEntityMock();
        // Act
        SearchStoreAddressDto result = StoreAddressDtoHelper.buildStoreAddressDto(entity);
        // Assert
        assertNotNull(result);
        assertEquals(entity.getAddressId(), result.getId());
        assertEquals(entity.getAddressName(), result.getAddressName());
    }

}
