package com.mercaso.data.metrics.repository;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import com.mercaso.data.metrics.mock.MetricsStoreProfilingStoreInfoEntityMock;
import com.mercaso.data.metrics.utils.LuceneTokenizer;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

class StoreProfilingStoreInfoRepositoryIT extends AbstractIT {

    @Autowired
    private StoreProfilingStoreInfoRepository storeProfilingStoreInfoRepository;


    @Test
    void testFindBySearchQuery() {

        String storeId = UUID.randomUUID().toString();
        String addressName1 = "address1 Line one";
        String storeId2 = UUID.randomUUID().toString();
        String addressName2 = "address1 Line two";
        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity.setStoreId(storeId);
        storeProfilingAddressInfoEntity.setAddressName(addressName1);

        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity2 = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity2.setStoreId(storeId2);
        storeProfilingAddressInfoEntity2.setAddressName(addressName2);

        storeProfilingStoreInfoRepository.saveAll(List.of(storeProfilingAddressInfoEntity2,
            storeProfilingAddressInfoEntity));

        String query = LuceneTokenizer.convertKeywordToTsQuery("one");
        Pageable pageable = PageRequest.of(0, 10);
        Page<StoreProfilingStoreInfoEntity> resultPage = storeProfilingStoreInfoRepository.findBySearchQuery(query,
            pageable);

        assertTrue(resultPage.getTotalElements() >= 1);
        assert resultPage.get()
            .toList()
            .stream()
            .anyMatch(addressInfoEntity1 -> addressInfoEntity1.getStoreId().equals(storeId));

        String query2 = LuceneTokenizer.convertKeywordToTsQuery("address1");
        Pageable pageable2 = PageRequest.of(0, 10);
        Page<StoreProfilingStoreInfoEntity> resultPage2 = storeProfilingStoreInfoRepository.findBySearchQuery(query2,
            pageable2);

        assertTrue(resultPage2.getTotalElements() >= 2);
    }

    @Test
    void testFindBySearchQueryDistinct() {

        String storeId = UUID.randomUUID().toString();
        String addressName = "address2 Line three";
        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity.setStoreId(storeId);
        storeProfilingAddressInfoEntity.setAddressName(addressName);

        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity2 = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity2.setStoreId(storeId);
        storeProfilingAddressInfoEntity2.setAddressName(addressName);

        storeProfilingStoreInfoRepository.saveAll(List.of(storeProfilingAddressInfoEntity2,
            storeProfilingAddressInfoEntity));

        String query = LuceneTokenizer.convertKeywordToTsQuery("three");
        Pageable pageable = PageRequest.of(0, 10);
        Page<StoreProfilingStoreInfoEntity> resultPage = storeProfilingStoreInfoRepository.findBySearchQuery(query,
            pageable);

        assertTrue(resultPage.getTotalElements() >= 1);
        assert resultPage.get()
            .toList()
            .stream()
            .anyMatch(addressInfoEntity1 -> addressInfoEntity1.getStoreId().equals(storeId));

        String query2 = LuceneTokenizer.convertKeywordToTsQuery("address2");
        Pageable pageable2 = PageRequest.of(0, 10);
        Page<StoreProfilingStoreInfoEntity> resultPage2 = storeProfilingStoreInfoRepository.findBySearchQuery(query2,
            pageable2);

        assert resultPage2.getTotalElements() >= 1;
    }

    @Test
    void testQueryAll() {

        String storeId1 = UUID.randomUUID().toString();
        String addressName1 = "address3 Line Four";
        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity.setStoreId(storeId1);
        storeProfilingAddressInfoEntity.setAddressName(addressName1);

        String storeId2 = UUID.randomUUID().toString();
        String addressName2 = "address4 Line Five";
        StoreProfilingStoreInfoEntity storeProfilingAddressInfoEntity2 = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeProfilingAddressInfoEntity2.setStoreId(storeId2);
        storeProfilingAddressInfoEntity2.setAddressName(addressName2);

        storeProfilingStoreInfoRepository.saveAll(List.of(storeProfilingAddressInfoEntity2,
            storeProfilingAddressInfoEntity));

        Pageable pageable = PageRequest.of(0, 10);
        Page<StoreProfilingStoreInfoEntity> resultPage = storeProfilingStoreInfoRepository.queryAll(pageable);

        assertNotNull(resultPage);
    }
}
