package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogSquareOrderLineItemUtils.buildMasterCatalogSquareOrderLineItem;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogSquareOrderUtils.buildMasterCatalogSquareOrder;
import static com.mercaso.data.master_catalog.utils.entity.StoreEntityUtils.buildStore;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrderLineItem;
import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSuqareInventoryDailyMetricRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.DashboardResourceApiUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

class DashboardResourceIT extends AbstractIT {

    @Autowired
    private DashboardResourceApiUtils dashboardResourceApiUtils;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogSuqareInventoryDailyMetricRepository dailyMetricRepository;

    @Autowired
    private MasterCatalogSquareOrderRepository masterCatalogOrderRepository;

    @Autowired
    private MasterCatalogSquareOrderLineItemRepository masterCatalogOrderLineItemRepository;

    private Store store;
    private MasterCatalogRawData rawData;

    @BeforeEach
    void setUp() {
        System.out.println("Setting up test data...");
        
        store = storeRepository.saveAndFlush(buildStore("Mercaso"));
        System.out.println("Store created with ID: " + store.getId());

        MasterCatalogRawData masterCatalogRawData = buildMasterCatalogRawData(store.getId());
        rawData = masterCatalogRawDataRepository.saveAndFlush(masterCatalogRawData);
        System.out.println("Raw data created with ID: " + rawData.getId() + ", UPC: " + rawData.getUpc());
        
        // Verify data was saved
        boolean storeExists = storeRepository.existsById(store.getId());
        boolean rawDataExists = masterCatalogRawDataRepository.existsById(rawData.getId());
        System.out.println("After save - Store exists: " + storeExists + ", Raw data exists: " + rawDataExists);

        MasterCatalogSquareOrder masterCatalogSquareOrder1 = buildMasterCatalogSquareOrder();
        MasterCatalogSquareOrder savedMasterCatalogSquareOrder1 = masterCatalogOrderRepository.save(masterCatalogSquareOrder1);
        MasterCatalogSquareOrder masterCatalogSquareOrder2 = buildMasterCatalogSquareOrder();
        MasterCatalogSquareOrder savedMasterCatalogSquareOrder2 = masterCatalogOrderRepository.save(masterCatalogSquareOrder2);
        MasterCatalogSquareOrder masterCatalogSquareOrder3 = buildMasterCatalogSquareOrder();
        masterCatalogSquareOrder3.setCreatedAt(Instant.now().minus(1, ChronoUnit.DAYS));
        MasterCatalogSquareOrder savedMasterCatalogSquareOrder3 = masterCatalogOrderRepository.save(masterCatalogSquareOrder3);

        MasterCatalogSquareOrderLineItem masterCatalogOrderLineItem1 = buildMasterCatalogSquareOrderLineItem(
            savedMasterCatalogSquareOrder1.getId(),
            rawData.getId());
        MasterCatalogSquareOrderLineItem masterCatalogOrderLineItem2 = buildMasterCatalogSquareOrderLineItem(
            savedMasterCatalogSquareOrder2.getId(),
            rawData.getId());
        MasterCatalogSquareOrderLineItem masterCatalogOrderLineItem3 = buildMasterCatalogSquareOrderLineItem(
            savedMasterCatalogSquareOrder3.getId(),
            rawData.getId());

        masterCatalogOrderLineItemRepository.saveAll(List.of(masterCatalogOrderLineItem1,
            masterCatalogOrderLineItem2,
            masterCatalogOrderLineItem3));

        Instant now = Instant.now().truncatedTo(ChronoUnit.DAYS);
        for (int i = 0; i < 7; i++) {
            MasterCatalogSuqareInventoryDailyMetric dailyMetric = new MasterCatalogSuqareInventoryDailyMetric();
            dailyMetric.setMasterCatalogRawDataId(rawData.getId());
            dailyMetric.setStoreId(store.getId());
            dailyMetric.setQuantity(100 - i * 10);
            dailyMetric.setInStockQuantity(80 - i * 8);
            dailyMetric.setOutStockQuantity(20 - i * 2);
            dailyMetric.setDate(now.minus(i, ChronoUnit.DAYS));
            dailyMetricRepository.save(dailyMetric);
        }
    }

//    @Test
//    void shouldGetInventoryReplenishmentTrend() throws Exception {
//        MasterCatalogSuqareInventoryDailyMetric dailyMetricZero = new MasterCatalogSuqareInventoryDailyMetric();
//        dailyMetricZero.setMasterCatalogRawDataId(rawData.getId());
//        dailyMetricZero.setStoreId(store.getId());
//        dailyMetricZero.setQuantity(0);
//        dailyMetricZero.setInStockQuantity(0);
//        dailyMetricZero.setOutStockQuantity(10);
//        dailyMetricZero.setDate(Instant.now().minus(15, ChronoUnit.DAYS));
//        dailyMetricRepository.save(dailyMetricZero);
//
//        List<InventoryAndReplenishmentTrendDto> result = dashboardResourceApiUtils.getInventoryReplenishmentTrend(store.getId()
//                .toString(),
//            rawData.getUpc());
//
//        Assertions.assertEquals(30, result.size());
//        assertEquals(0, result.get(15).getQuantity());
//    }

    @Test
    void orderTrend() throws Exception {

        List<OrderTrendDto> result = dashboardResourceApiUtils.getOrderTrend(store.getId().toString(),
            rawData.getUpc());

        Assertions.assertFalse(result.isEmpty());
        assertEquals(0, result.get(15).getQuantity());
    }
}
