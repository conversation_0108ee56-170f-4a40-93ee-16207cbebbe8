package com.mercaso.data.master_catalog.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.exception.SquareAuthorizationNotFoundException;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import com.mercaso.data.master_catalog.service.MasterCatalogSquareAuthorizationService;
import com.squareup.square.SquareClient;
import org.junit.jupiter.api.Test;

import java.util.UUID;

class SquareClientServiceImplTest {

    private final MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository = mock(
      MasterCatalogSquareAuthorizationRepository.class);
    private final CipherUtility cipherUtility = mock(CipherUtility.class);
    private final SquareClientServiceImpl squareClientService = new SquareClientServiceImpl(
      masterCatalogSquareAuthorizationRepository,
      cipherUtility);
    private final UUID storeId = UUID.randomUUID();

    private static final String ENCRYPTED_TOKEN = "encrypted_token";
    private static final String DECRYPTED_TOKEN = "decrypted_token";

    @Test
    void getSquareClient_Success() {
        // Arrange
        MasterCatalogSquareAuthorization masterCatalogSquareAuthorization = new MasterCatalogSquareAuthorization();
        masterCatalogSquareAuthorization.setEncryptedAccessToken(ENCRYPTED_TOKEN);

        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(masterCatalogSquareAuthorization);
        when(cipherUtility.decrypt(ENCRYPTED_TOKEN)).thenReturn(DECRYPTED_TOKEN);

        // Act
        SquareClient result = squareClientService.getSquareClient(storeId);

        // Assert
        assertNotNull(result);
        verify(masterCatalogSquareAuthorizationRepository).findByStoreId(storeId);
        verify(cipherUtility).decrypt(ENCRYPTED_TOKEN);
    }

    @Test
    void getSquareClient_NullStoreId_ThrowsNullPointerException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> squareClientService.getSquareClient(null));
    }

    @Test
    void getSquareClient_AuthorizationNotFound_ThrowsException() {
        // Arrange
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(null);

        // Act & Assert
        assertThrows(SquareAuthorizationNotFoundException.class,
          () -> squareClientService.getSquareClient(storeId));
    }

    @Test
    void getDefaultSquareClient_Success() {
        // Act
        SquareClient result = squareClientService.getDefaultSquareClient();

        // Assert
        assertNotNull(result);
        verifyNoInteractions(masterCatalogSquareAuthorizationRepository, cipherUtility);
    }
} 