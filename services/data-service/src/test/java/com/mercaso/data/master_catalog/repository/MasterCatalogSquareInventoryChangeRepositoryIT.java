package com.mercaso.data.master_catalog.repository;

import static com.mercaso.data.master_catalog.enums.InventoryStates.IN_STOCK;
import static com.mercaso.data.master_catalog.enums.InventoryStates.SOLD;
import static com.mercaso.data.master_catalog.enums.square.InventoryChangeTypes.ADJUSTMENT;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventoryChange;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class MasterCatalogSquareInventoryChangeRepositoryIT extends AbstractIT {

    @Autowired
    private MasterCatalogSquareInventoryChangeRepository repository;
    @Autowired
    private MasterCatalogRawDataRepository rawDataRepository;

    @Test
    void testFindTopByStoreIdAndOrderByUpdatedAtDesc() {
        UUID storeId = UUID.randomUUID();
        MasterCatalogRawData masterCatalogRawData1 = buildMasterCatalogRawData(storeId);
        masterCatalogRawData1.setUpdatedAt(Instant.now().minusSeconds(10));
        MasterCatalogRawData saved1 = rawDataRepository.save(masterCatalogRawData1);

        MasterCatalogRawData masterCatalogRawData2 = buildMasterCatalogRawData(storeId);
        masterCatalogRawData2.setUpdatedAt(Instant.now().plusSeconds(5));
        MasterCatalogRawData saved2 = rawDataRepository.save(masterCatalogRawData2);

        MasterCatalogRawData masterCatalogRawData3 = buildMasterCatalogRawData(storeId);
        masterCatalogRawData3.setUpdatedAt(Instant.now().plusSeconds(15));
        MasterCatalogRawData saved3 = rawDataRepository.save(masterCatalogRawData2);

        repository.save(MasterCatalogSquareInventoryChange.builder()
            .masterCatalogRawDataId(saved1.getId())
            .fromState(IN_STOCK.name())
            .toState(SOLD.name())
            .type(ADJUSTMENT.name())
            .quantity(10)
            .occurredAt(Instant.now())
            .build());
        repository.save(MasterCatalogSquareInventoryChange.builder()
            .masterCatalogRawDataId(saved2.getId())
            .type(ADJUSTMENT.name())
            .fromState(IN_STOCK.name())
            .quantity(10)
            .toState(SOLD.name())
            .occurredAt(Instant.now())
            .build());
        repository.save(MasterCatalogSquareInventoryChange.builder()
            .masterCatalogRawDataId(saved3.getId())
            .type(ADJUSTMENT.name())
            .fromState(IN_STOCK.name())
            .quantity(10)
            .toState(SOLD.name())
            .occurredAt(Instant.now())
            .build());

        Optional<MasterCatalogSquareInventoryChange> result = repository.findTopByStoreIdAndOrderByUpdatedAtDesc(storeId);
        assert result.isPresent();
        assertEquals(saved3.getId(), result.get().getMasterCatalogRawDataId());
    }
}
