package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.dto.SearchCatalogObjectsResponseDtoUtils.buildSearchCatalogObjectsResponseDto;
import static com.mercaso.data.master_catalog.utils.entity.StoreEntityUtils.buildStore;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.data.master_catalog.dto.square.SearchCatalogObjectsResponseDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.SquareDataSyncResourceApiUtils;
import com.squareup.square.models.CatalogItem;
import com.squareup.square.models.CatalogObject;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageRequest;

@Slf4j
class SquareDataSyncResourceIT extends AbstractIT {

    @Autowired
    private SquareDataSyncResourceApiUtils squareDataSyncResourceApiUtils;
    @Autowired
    private StoreRepository storeRepository;
    @Autowired
    private MasterCatalogRawDataRepository rawDataRepository;
    @Autowired
    private MasterCatalogSquareVariationMappingRepository masterCatalogSquareVariationMappingRepository;
    @Autowired
    private MasterCatalogSquareRawDataRepository masterCatalogSquareRawDataRepository;

    @MockBean
    private SquareApiAdapter squareApiAdapter;
    @MockBean
    private S3OperationAdapter s3OperationAdapter;
    @MockBean
    private MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository;


    @Test
    void testSyncData() {

        Store store = buildStore("Mercaso");
        storeRepository.save(store);

        when(masterCatalogSquareAuthorizationRepository.findByStoreId(any())).thenReturn(
            MasterCatalogSquareAuthorization.builder()
                .storeId(store.getId())
                .build());

        SearchCatalogObjectsResponseDto searchCatalogObjectsResponseDto = buildSearchCatalogObjectsResponseDto();
        when(squareApiAdapter.searchCatalogObjects(any(), any())).thenReturn(searchCatalogObjectsResponseDto);

        DocumentResponseDto documentResponseDto = DocumentResponseDto.builder()
            .name("Test")
            .signedUrl("https://test.com/image.jpe")
            .build();
        when(s3OperationAdapter.upload(any())).thenReturn(documentResponseDto);

        squareDataSyncResourceApiUtils.syncSquareData();

        List<String> upcs = searchCatalogObjectsResponseDto.getObjects()
            .stream()
            .map(CatalogObject::getItemData)
            .toList()
            .stream()
            .map(CatalogItem::getVariations)
            .toList()
            .stream()
            .flatMap(java.util.Collection::stream)
            .toList()
            .stream()
            .map(variation -> variation.getItemVariationData().getUpc())
            .filter(upc -> upc != null && !upc.isEmpty())
            .toList();
        log.info("testSyncData UPC: {}", upcs);
        // Assert
        PageRequest pageRequest = PageRequest.of(1, 1);
        assert !rawDataRepository.findByUpcIn(upcs, pageRequest).getContent().isEmpty();
    }
}
