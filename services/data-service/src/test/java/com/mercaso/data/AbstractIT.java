package com.mercaso.data;

import com.mercaso.data.config.SecurityContextUtilStaticFilter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Profile("integration")
@EnableTransactionManagement
@Slf4j
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
    SecurityContextUtilStaticFilter.class
})
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
// @EmbeddedKafka(count = 3)
@AutoConfigureMockMvc
public abstract class AbstractIT {

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    // Store resource IDs created by each test method, isolated by thread
    private static final ThreadLocal<Map<String, Set<String>>> testResourceIds = 
        ThreadLocal.withInitial(HashMap::new);
    
    // Custom cleanup strategy mapping: table name -> custom cleanup SQL template
    // Only used for tables that need special cleanup logic
    private static final Map<String, String> CUSTOM_CLEANUP_STRATEGIES = new ConcurrentHashMap<>();
    
    // Cache for entity class to table name mapping
    private static final Map<Class<?>, String> ENTITY_TABLE_CACHE = new ConcurrentHashMap<>();
    
    /**
     * Extract table name from JPA entity class using @Table annotation
     * 
     * @param entityClass the JPA entity class
     * @return the table name, or null if not found
     */
    private String getTableNameFromEntity(Class<?> entityClass) {
        return ENTITY_TABLE_CACHE.computeIfAbsent(entityClass, clazz -> {
            // Check if class has @Entity annotation
            if (!clazz.isAnnotationPresent(Entity.class)) {
                log.warn("Class {} is not a JPA entity (missing @Entity annotation)", clazz.getSimpleName());
                return null;
            }
            
            // Check for @Table annotation
            Table tableAnnotation = clazz.getAnnotation(Table.class);
            if (tableAnnotation != null && !tableAnnotation.name().isEmpty()) {
                String tableName = tableAnnotation.name();
                log.debug("Found table name '{}' from @Table annotation on class {}", tableName, clazz.getSimpleName());
                return tableName;
            }
            
            // Fallback: use class name converted to snake_case
            String tableName = convertToSnakeCase(clazz.getSimpleName());
            log.debug("Using snake_case conversion '{}' for entity class {}", tableName, clazz.getSimpleName());
            return tableName;
        });
    }
    
    /**
     * Convert CamelCase to snake_case
     * 
     * @param camelCase the CamelCase string
     * @return the snake_case string
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * Generate default cleanup SQL for a table
     * 
     * @param tableName the database table name
     * @return the DELETE SQL template with ? as ID placeholder
     */
    private String generateDefaultCleanupSql(String tableName) {
        return String.format("DELETE FROM %s WHERE id = ?", tableName);
    }
    
    /**
     * Get cleanup SQL for a table (custom or auto-generated)
     * 
     * @param tableName the database table name
     * @return the DELETE SQL template
     */
    private String getCleanupSql(String tableName) {
        // Check for custom cleanup strategy first
        String customSql = CUSTOM_CLEANUP_STRATEGIES.get(tableName);
        if (customSql != null) {
            log.debug("Using custom cleanup SQL for table: {}", tableName);
            return customSql;
        }
        
        // Generate default cleanup SQL
        String defaultSql = generateDefaultCleanupSql(tableName);
        log.debug("Using auto-generated cleanup SQL for table {}: {}", tableName, defaultSql);
        return defaultSql;
    }
    
    /**
     * Extract ID value from an entity object
     * 
     * @param entity the entity object
     * @return the ID value as string, or null if not found
     */
    private String extractEntityId(Object entity) {
        if (entity == null) {
            return null;
        }
        
        Class<?> entityClass = entity.getClass();
        
        // Try common ID field names
        String[] idFieldNames = {"id", "getId"};
        
        for (String fieldName : idFieldNames) {
            try {
                if (fieldName.startsWith("get")) {
                    // Try getter method
                    var method = entityClass.getMethod(fieldName);
                    Object idValue = method.invoke(entity);
                    if (idValue != null) {
                        return idValue.toString();
                    }
                } else {
                    // Try field access
                    Field field = entityClass.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object idValue = field.get(entity);
                    if (idValue != null) {
                        return idValue.toString();
                    }
                }
            } catch (Exception e) {
                // Continue trying other methods
                log.debug("Failed to extract ID using {}: {}", fieldName, e.getMessage());
            }
        }
        
        log.warn("Could not extract ID from entity of type {}", entityClass.getSimpleName());
        return null;
    }
    
    // ========================================
    // Core Registration Methods (Simplified)
    // ========================================
    
    /**
     * Register resource IDs for cleanup by entity class
     * 
     * @param entityClass the JPA entity class
     * @param resourceIds single ID or collection of resource IDs to be cleaned up
     */
    protected void registerForCleanup(Class<?> entityClass, Object resourceIds) {
        String tableName = getTableNameFromEntity(entityClass);
        if (tableName == null) {
            log.warn("Could not determine table name for entity class: {}", entityClass.getSimpleName());
            return;
        }
        
        registerForCleanup(tableName, resourceIds);
    }
    
    /**
     * Register resource IDs for cleanup by table name
     * 
     * @param tableName the database table name
     * @param resourceIds single ID (String) or collection of resource IDs to be cleaned up
     */
    protected void registerForCleanup(String tableName, Object resourceIds) {
        if (resourceIds == null || tableName == null) {
            log.warn("Cannot register null resource for cleanup: table={}, ids={}", tableName, resourceIds);
            return;
        }
        
        Map<String, Set<String>> resources = testResourceIds.get();
        Set<String> tableResources = resources.computeIfAbsent(tableName, k -> new HashSet<>());
        
        if (resourceIds instanceof String) {
            // Single ID
            tableResources.add((String) resourceIds);
            log.debug("Registered resource for cleanup: table={}, id={}", tableName, resourceIds);
        } else if (resourceIds instanceof Collection) {
            // Collection of IDs
            Collection<?> ids = (Collection<?>) resourceIds;
            for (Object id : ids) {
                if (id != null) {
                    tableResources.add(id.toString());
                }
            }
            log.debug("Registered {} resources for cleanup in table: {}", ids.size(), tableName);
        } else {
            log.warn("Unsupported resource ID type: {}. Expected String or Collection.", resourceIds.getClass());
        }
    }
    
    /**
     * Register entities for cleanup (extracts table name and ID automatically)
     * 
     * @param entities single entity or collection of JPA entity objects
     */
    protected void registerForCleanup(Object entities) {
        if (entities == null) {
            log.warn("Cannot register null entity for cleanup");
            return;
        }
        
        if (entities instanceof Collection) {
            // Collection of entities
            Collection<?> entityCollection = (Collection<?>) entities;
            for (Object entity : entityCollection) {
                registerSingleEntityForCleanup(entity);
            }
            log.debug("Registered {} entities for cleanup", entityCollection.size());
        } else {
            // Single entity
            registerSingleEntityForCleanup(entities);
        }
    }
    
    /**
     * Register a single entity for cleanup
     * 
     * @param entity the JPA entity object
     */
    private void registerSingleEntityForCleanup(Object entity) {
        if (entity == null) {
            return;
        }
        
        Class<?> entityClass = entity.getClass();
        String tableName = getTableNameFromEntity(entityClass);
        String entityId = extractEntityId(entity);
        
        if (tableName == null) {
            log.warn("Could not determine table name for entity class: {}", entityClass.getSimpleName());
            return;
        }
        
        if (entityId == null) {
            log.warn("Could not extract ID from entity of class: {}", entityClass.getSimpleName());
            return;
        }
        
        registerForCleanup(tableName, entityId);
        log.debug("Registered entity for cleanup: class={}, table={}, id={}", 
            entityClass.getSimpleName(), tableName, entityId);
    }
    
    // ========================================
    // Utility Methods
    // ========================================
    
    /**
     * Add a custom cleanup strategy for a specific table
     * Use this only when the default "DELETE FROM table_name WHERE id = ?" is not sufficient
     * 
     * @param tableName the database table name
     * @param deleteSql the DELETE SQL template with ? as ID placeholder
     */
    protected void addCustomCleanupStrategy(String tableName, String deleteSql) {
        CUSTOM_CLEANUP_STRATEGIES.put(tableName, deleteSql);
        log.debug("Added custom cleanup strategy for table: {}", tableName);
    }
    
    /**
     * Add a custom cleanup strategy for an entity class
     * 
     * @param entityClass the JPA entity class
     * @param deleteSql the DELETE SQL template with ? as ID placeholder
     */
    protected void addCustomCleanupStrategy(Class<?> entityClass, String deleteSql) {
        String tableName = getTableNameFromEntity(entityClass);
        if (tableName == null) {
            log.warn("Could not determine table name for entity class: {}", entityClass.getSimpleName());
            return;
        }
        
        addCustomCleanupStrategy(tableName, deleteSql);
    }
    
    /**
     * Manually cleanup resources for a specific table
     * 
     * @param tableName the database table name
     */
    protected void cleanupResourcesForTable(String tableName) {
        Map<String, Set<String>> resources = testResourceIds.get();
        Set<String> resourceIds = resources.get(tableName);
        
        if (resourceIds == null || resourceIds.isEmpty()) {
            return;
        }
        
        String cleanupSql = getCleanupSql(tableName);
        
        int deletedCount = 0;
        for (String resourceId : resourceIds) {
            try {
                int affected = jdbcTemplate.update(cleanupSql, resourceId);
                if (affected > 0) {
                    deletedCount++;
                    log.debug("Deleted resource: table={}, id={}", tableName, resourceId);
                }
            } catch (Exception e) {
                log.warn("Failed to delete resource: table={}, id={}, error={}", 
                    tableName, resourceId, e.getMessage());
            }
        }
        
        if (deletedCount > 0) {
            log.info("Cleaned up {} resources from table: {}", deletedCount, tableName);
        }
        
        // Clear processed resource IDs
        resourceIds.clear();
    }
    
    /**
     * Manually cleanup resources for an entity class
     * 
     * @param entityClass the JPA entity class
     */
    protected void cleanupResourcesForEntity(Class<?> entityClass) {
        String tableName = getTableNameFromEntity(entityClass);
        if (tableName == null) {
            log.warn("Could not determine table name for entity class: {}", entityClass.getSimpleName());
            return;
        }
        
        cleanupResourcesForTable(tableName);
    }
    
    /**
     * Get statistics of currently registered resources
     * 
     * @return map of table name to resource count
     */
    protected Map<String, Integer> getResourceStats() {
        Map<String, Set<String>> resources = testResourceIds.get();
        Map<String, Integer> stats = new HashMap<>();
        
        for (Map.Entry<String, Set<String>> entry : resources.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().size());
        }
        
        return stats;
    }
    
    /**
     * Check if a resource is registered for cleanup
     * 
     * @param tableName the database table name
     * @param resourceId the resource ID
     * @return true if the resource is registered for cleanup
     */
    protected boolean isResourceRegistered(String tableName, String resourceId) {
        Map<String, Set<String>> resources = testResourceIds.get();
        Set<String> tableResources = resources.get(tableName);
        return tableResources != null && tableResources.contains(resourceId);
    }
    
    /**
     * Cleanup registered resources after each test method execution
     */
    @AfterEach
    void cleanupTestResources() {
        Map<String, Set<String>> resources = testResourceIds.get();
        
        if (resources.isEmpty()) {
            log.debug("No manual cleanup needed - transaction rollback will handle database changes");
            return;
        }
        
        log.info("Starting manual cleanup for registered resources...");
        int totalCleaned = 0;
        
        for (String tableName : resources.keySet()) {
            int beforeSize = resources.get(tableName).size();
            cleanupResourcesForTable(tableName);
            totalCleaned += beforeSize;
        }
        
        // Clear ThreadLocal to prevent memory leaks
        testResourceIds.remove();
        
        if (totalCleaned > 0) {
            log.info("Manual cleanup completed. Total resources cleaned: {}", totalCleaned);
        }
    }
}
