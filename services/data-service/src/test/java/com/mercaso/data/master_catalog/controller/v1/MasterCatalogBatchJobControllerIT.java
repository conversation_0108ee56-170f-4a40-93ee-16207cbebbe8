package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.http.HttpStatus.OK;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogBatchJobResourceApiUtils;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;

@Slf4j
class MasterCatalogBatchJobControllerIT extends AbstractIT {

    @Autowired
    private MasterCatalogBatchJobResourceApiUtils masterCatalogBatchJobResourceApiUtils;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

    @MockBean
    private ExternalApiAdapter externalApiAdapter;

    private List<MasterCatalogRawData> testData;

    @BeforeEach
    void setUp() {
        testData = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            MasterCatalogRawData rawData = buildMasterCatalogRawData(UUID.randomUUID());
            rawData.setStatus(RawDataStatus.DRAFT.name());
            testData.add(rawData);
        }
        testData = masterCatalogRawDataRepository.saveAll(testData);
    }

    @Test
    void startProcessing() {
        ResponseEntity<Void> response = masterCatalogBatchJobResourceApiUtils.startProcessing();

        assertEquals(OK, response.getStatusCode());
    }

    @Test
    void search() {
        // Arrange
        String jobNumber = UUID.randomUUID().toString();
        MasterCatalogBatchJob draftJob = MasterCatalogBatchJob.builder()
            .jobNumber(jobNumber)
            .status(MasterCatalogBatchJobStatus.DRAFT)
            .completedAt(LocalDateTime.now().minusDays(1).toInstant(ZoneOffset.UTC))
            .build();
        MasterCatalogBatchJob inProgressJob = MasterCatalogBatchJob.builder()
            .jobNumber(UUID.randomUUID().toString())
            .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
            .completedAt(LocalDateTime.now().minusDays(5).toInstant(ZoneOffset.UTC))
            .build();
        
        masterCatalogBatchJobRepository.save(draftJob);
        masterCatalogBatchJobRepository.save(inProgressJob);

        // Act
        CustomPage<MasterCatalogBatchJobListDto> result = masterCatalogBatchJobResourceApiUtils.search(null, null, null,
            LocalDateTime.now().minusDays(2).toInstant(ZoneOffset.UTC), LocalDateTime.now().toInstant(ZoneOffset.UTC),
            1, 10);

        // Assert
        assertEquals(1, result.getTotalCount());
        assertEquals(jobNumber, result.getData().get(0).getJobNumber());
        assertEquals(MasterCatalogBatchJobStatus.DRAFT, result.getData().get(0).getStatus());
    }
}