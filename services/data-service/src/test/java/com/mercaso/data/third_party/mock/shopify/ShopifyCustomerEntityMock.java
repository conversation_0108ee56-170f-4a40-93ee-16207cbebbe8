package com.mercaso.data.third_party.mock.shopify;

import com.mercaso.data.third_party.entity.shopify.customer.ShopifyCustomerAddressEntity;
import com.mercaso.data.third_party.entity.shopify.customer.ShopifyCustomerEntity;
import com.mercaso.data.third_party.entity.shopify.customer.ShopifyCustomerMarketingConsentEntity;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;

public class ShopifyCustomerEntityMock {

    public static ShopifyCustomerEntity customerEntityMock(){
        ShopifyCustomerEntity customerEntity = new ShopifyCustomerEntity();
        customerEntity.setId(1073339474L);
        customerEntity.setEmail("<EMAIL>");
        customerEntity.setCreatedAt(ZonedDateTime.parse("2024-07-02T11:24:34-04:00"));
        customerEntity.setUpdatedAt(ZonedDateTime.parse("2024-07-02T11:24:34-04:00"));
        customerEntity.setFirstName("Steve");
        customerEntity.setLastName("Lastnameson");
        customerEntity.setOrdersCount(0);
        customerEntity.setState("disabled");
        customerEntity.setTotalSpent(new BigDecimal("0.00"));
        customerEntity.setLastOrderId(null);
        customerEntity.setNote(null);
        customerEntity.setVerifiedEmail(true);
        customerEntity.setMultipassIdentifier(null);
        customerEntity.setTaxExempt(false);
        customerEntity.setTags("");
        customerEntity.setLastOrderName(null);
        customerEntity.setCurrency("USD");
        customerEntity.setPhone("+15142546011");

        ShopifyCustomerAddressEntity address = getShopifyCustomerAddressEntity();
        customerEntity.setAddresses(Collections.singletonList(address));

        customerEntity.setTaxExemptions(Collections.emptyList());

        ShopifyCustomerMarketingConsentEntity emailConsent = new ShopifyCustomerMarketingConsentEntity();
        emailConsent.setState("not_subscribed");
        emailConsent.setOptInLevel("single_opt_in");
        emailConsent.setConsentUpdatedAt(null);
        emailConsent.setConsentCollectedFrom(null);
        customerEntity.setEmailMarketingConsent(emailConsent);

        ShopifyCustomerMarketingConsentEntity smsConsent = new ShopifyCustomerMarketingConsentEntity();
        smsConsent.setState("not_subscribed");
        smsConsent.setOptInLevel("single_opt_in");
        smsConsent.setConsentUpdatedAt(null);
        smsConsent.setConsentCollectedFrom("OTHER");
        customerEntity.setSmsMarketingConsent(smsConsent);

        customerEntity.setAdminGraphqlApiId("gid://shopify/Customer/1073339474");
        customerEntity.setDefaultAddress(address);

        return customerEntity;
    }

    private static ShopifyCustomerAddressEntity getShopifyCustomerAddressEntity() {
        ShopifyCustomerAddressEntity address = new ShopifyCustomerAddressEntity();
        address.setId(1053317307L);
        address.setCustomerId(1073339474L);
        address.setFirstName("Mother");
        address.setLastName("Lastnameson");
        address.setCompany(null);
        address.setAddress1("123 Oak St");
        address.setAddress2(null);
        address.setCity("Ottawa");
        address.setProvince("Ontario");
        address.setCountry("Canada");
        address.setZip("123 ABC");
        address.setPhone("555-1212");
        address.setName("Mother Lastnameson");
        address.setProvinceCode("ON");
        address.setCountryCode("CA");
        address.setCountryName("Canada");
        address.setDefaultAddress(true);
        return address;
    }
}