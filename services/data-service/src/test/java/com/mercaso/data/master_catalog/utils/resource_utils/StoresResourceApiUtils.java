package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.master_catalog.dto.StoreDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class StoresResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_STORES = "/master-catalog/v1/stores";

    public StoresResourceApiUtils(Environment environment) {
        super(environment);
    }

    // Get stores
    public List<StoreDto> getStores() throws Exception {
        return getEntityList(V1_STORES, StoreDto.class);
    }
}
