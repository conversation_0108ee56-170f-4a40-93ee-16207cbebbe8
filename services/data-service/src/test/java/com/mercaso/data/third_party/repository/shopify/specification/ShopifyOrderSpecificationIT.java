package com.mercaso.data.third_party.repository.shopify.specification;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.third_party.dto.shopify.ShopifyOrderFilter;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrderEntity;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersTagsEntity;
import com.mercaso.data.third_party.enums.shopify.ShopifyOrderStatusEnums;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderEntityMock;
import com.mercaso.data.third_party.mock.shopify.ShopifyOrderTagsEntityMock;
import com.mercaso.data.third_party.repository.shopify.ShopifyOrderRepository;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

class ShopifyOrderSpecificationIT extends AbstractIT {

    @Autowired
    private ShopifyOrderRepository shopifyOrderRepository;


    @Test
    void testTagsFilter() {

        Long tag = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);

        ShopifyOrderEntity shopifyOrderEntity = ShopifyOrderEntityMock.shopifyOrderMock();
        ShopifyOrdersTagsEntity ordersTags = ShopifyOrderTagsEntityMock.shopifyOrdersTagsMock();
        ordersTags.setTag(tag.toString());
        ordersTags.setOrder(shopifyOrderEntity);
        shopifyOrderEntity.setTagList(List.of(ordersTags));

        shopifyOrderRepository.save(shopifyOrderEntity);

        ShopifyOrderFilter shopifyOrderFilter = new ShopifyOrderFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        shopifyOrderFilter.setPageable(customPageable);
        shopifyOrderFilter.setTags(List.of(tag.toString()));
        Specification<ShopifyOrderEntity> shopifyOrderEntitySpecification = ShopifyOrderSpecification.hasStatusAtAndTags(
            shopifyOrderFilter.getStatus(), shopifyOrderFilter.getTags());

        Page<ShopifyOrderEntity> result = shopifyOrderRepository.findAll(shopifyOrderEntitySpecification,
            shopifyOrderFilter.getPageable());

        List<ShopifyOrderEntity> content = result.getContent();
        ShopifyOrderEntity first = content.getFirst();

        assertEquals(1, content.size());
        assertEquals(shopifyOrderEntity.getId(), first.getId());
    }

    @Test
    void testCanceledStatusFilter() {

        Long tag = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);

        ShopifyOrderEntity shopifyOrderEntity = ShopifyOrderEntityMock.shopifyOrderMock();
        shopifyOrderEntity.setCancelledAt(ZonedDateTime.now());
        ShopifyOrdersTagsEntity ordersTags = ShopifyOrderTagsEntityMock.shopifyOrdersTagsMock();
        ordersTags.setTag(tag.toString());
        ordersTags.setOrder(shopifyOrderEntity);
        shopifyOrderEntity.setTagList(List.of(ordersTags));

        shopifyOrderRepository.save(shopifyOrderEntity);

        ShopifyOrderFilter shopifyOrderFilter = new ShopifyOrderFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        shopifyOrderFilter.setPageable(customPageable);
        shopifyOrderFilter.setTags(List.of(tag.toString()));
        shopifyOrderFilter.setStatus(ShopifyOrderStatusEnums.CANCELLED);
        Specification<ShopifyOrderEntity> shopifyOrderEntitySpecification = ShopifyOrderSpecification.hasStatusAtAndTags(
            shopifyOrderFilter.getStatus(), shopifyOrderFilter.getTags());

        Page<ShopifyOrderEntity> result = shopifyOrderRepository.findAll(shopifyOrderEntitySpecification,
            shopifyOrderFilter.getPageable());

        List<ShopifyOrderEntity> content = result.getContent();
        ShopifyOrderEntity first = content.getFirst();

        assertEquals(1, content.size());
        assertEquals(shopifyOrderEntity.getId(), first.getId());
    }

    @Test
    void testShippedStatusFilter() {

        Long tag = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);

        ShopifyOrderEntity shopifyOrderEntity = ShopifyOrderEntityMock.shopifyOrderMock();
        shopifyOrderEntity.setFulfillmentStatus("fulfilled");
        ShopifyOrdersTagsEntity ordersTags = ShopifyOrderTagsEntityMock.shopifyOrdersTagsMock();
        ordersTags.setTag(tag.toString());
        ordersTags.setOrder(shopifyOrderEntity);
        shopifyOrderEntity.setTagList(List.of(ordersTags));

        shopifyOrderRepository.save(shopifyOrderEntity);

        ShopifyOrderFilter shopifyOrderFilter = new ShopifyOrderFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        shopifyOrderFilter.setPageable(customPageable);
        shopifyOrderFilter.setTags(List.of(tag.toString()));
        shopifyOrderFilter.setStatus(ShopifyOrderStatusEnums.SHIPPED);
        Specification<ShopifyOrderEntity> shopifyOrderEntitySpecification = ShopifyOrderSpecification.hasStatusAtAndTags(
            shopifyOrderFilter.getStatus(), shopifyOrderFilter.getTags());

        Page<ShopifyOrderEntity> result = shopifyOrderRepository.findAll(shopifyOrderEntitySpecification,
            shopifyOrderFilter.getPageable());

        List<ShopifyOrderEntity> content = result.getContent();
        ShopifyOrderEntity first = content.getFirst();

        assertEquals(1, content.size());
        assertEquals(shopifyOrderEntity.getId(), first.getId());
    }

    @Test
    void testOpenStatusFilter() {

        Long tag = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);

        ShopifyOrderEntity shopifyOrderEntity = ShopifyOrderEntityMock.shopifyOrderMock();
        ShopifyOrdersTagsEntity ordersTags = ShopifyOrderTagsEntityMock.shopifyOrdersTagsMock();
        ordersTags.setTag(tag.toString());
        ordersTags.setOrder(shopifyOrderEntity);
        shopifyOrderEntity.setTagList(List.of(ordersTags));

        shopifyOrderRepository.save(shopifyOrderEntity);

        ShopifyOrderFilter shopifyOrderFilter = new ShopifyOrderFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        shopifyOrderFilter.setPageable(customPageable);
        shopifyOrderFilter.setTags(List.of(tag.toString()));
        shopifyOrderFilter.setStatus(ShopifyOrderStatusEnums.OPEN);
        Specification<ShopifyOrderEntity> shopifyOrderEntitySpecification = ShopifyOrderSpecification.hasStatusAtAndTags(
            shopifyOrderFilter.getStatus(), shopifyOrderFilter.getTags());

        Page<ShopifyOrderEntity> result = shopifyOrderRepository.findAll(shopifyOrderEntitySpecification,
            shopifyOrderFilter.getPageable());

        List<ShopifyOrderEntity> content = result.getContent();
        ShopifyOrderEntity first = content.getFirst();

        assertEquals(1, content.size());
        assertEquals(shopifyOrderEntity.getId(), first.getId());
    }
}
