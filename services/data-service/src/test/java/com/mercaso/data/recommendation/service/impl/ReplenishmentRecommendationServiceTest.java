package com.mercaso.data.recommendation.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.adaptor.WmsClientAdaptor;
import com.mercaso.data.master_catalog.dto.ReplenishmentForecastFileExportResult;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.ReplenishmentRecommendationDto;
import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareInventory;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.enums.PackageType;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.ReplenishmentRecommendationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareInventoryRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository.SquareOrderLineItemInfo;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.recommendation.mapper.ReplenishmentRecommendationMapper;
import com.mercaso.ims.client.dto.ItemDto;
import com.mercaso.ims.client.dto.VendorItemDto;
import com.mercaso.wms.client.dto.InventoryStockDto;
import com.mercaso.wms.client.dto.ResultInventoryStockDto;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

@SuppressWarnings("unchecked")
class ReplenishmentRecommendationServiceTest {

  private ReplenishmentRecommendationMapper replenishmentRecommendationMapper;
  private ReplenishmentRecommendationRepository forecastRepository;
  private StoreRepository storeRepository;
  private MasterCatalogSquareOrderRepository masterCatalogSquareOrderRepository;
  private MasterCatalogSquareOrderLineItemRepository masterCatalogSquareOrderLineItemRepository;
  private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
  private MasterCatalogSquareInventoryRepository masterCatalogSquareInventoryRepository;
  private WmsClientAdaptor wmsClientAdaptor;
  private ImsClientAdaptor imsClientAdaptor;
  private ReplenishmentRecommendationCsvExporterFactory csvExporterFactory;
  private ReplenishmentRecommendationService service;
  private static final String ACTIVE_SOURCE_STATUS = "ACTIVE";

  private UUID storeId;
  private Store store;
  private ReplenishmentRecommendation forecast;
  private static final String BATCH_NUMBER = "20240315";

  @BeforeEach
  void setUp() {
    forecastRepository = mock(ReplenishmentRecommendationRepository.class);
    storeRepository = mock(StoreRepository.class);
    masterCatalogSquareOrderRepository = mock(MasterCatalogSquareOrderRepository.class);
    masterCatalogSquareOrderLineItemRepository = mock(MasterCatalogSquareOrderLineItemRepository.class);
    masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
    masterCatalogSquareInventoryRepository = mock(MasterCatalogSquareInventoryRepository.class);
    wmsClientAdaptor = mock(WmsClientAdaptor.class);
    imsClientAdaptor = mock(ImsClientAdaptor.class);
    csvExporterFactory = mock(ReplenishmentRecommendationCsvExporterFactory.class);
    replenishmentRecommendationMapper = mock(ReplenishmentRecommendationMapper.class);

    service = new ReplenishmentRecommendationService(forecastRepository, storeRepository,
        masterCatalogSquareOrderRepository, masterCatalogSquareOrderLineItemRepository,
        masterCatalogRawDataRepository, masterCatalogSquareInventoryRepository, wmsClientAdaptor,
        imsClientAdaptor, csvExporterFactory, replenishmentRecommendationMapper);

    storeId = UUID.randomUUID();
    store = new Store();
    store.setId(storeId);
    store.setName("Test Store");
    store.setIntegrationType("SQUARE");

    forecast = new ReplenishmentRecommendation();
    forecast.setId(UUID.randomUUID());
    forecast.setStoreId(storeId);
    forecast.setSku("SKU123");
    forecast.setRecommendedQuantity(BigDecimal.TEN);
  }

  @Test
  void exportDataToCSV_Success() {
    // Given
    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(BATCH_NUMBER);
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));
    when(forecastRepository.findByStoreIdAndBatchNumber(storeId, BATCH_NUMBER))
        .thenReturn(Optional.of(Arrays.asList(forecast)));
    when(csvExporterFactory.getExporter(anyString())).thenReturn(new DefaultReplenishmentRecommendationCsvExporter());

    // When
    ReplenishmentForecastFileExportResult result = service.exportDataToCSV(storeId);

    // Then
    assertNotNull(result);
    assertEquals("Test Store_20240315.csv", result.getFileName());
    assertTrue(result.getContent().length > 0);

    // Verify CSV content
    String csvContent = new String(result.getContent());
    assertTrue(
        csvContent.contains("\"Batch Number\",\"ID\",\"Customer ID\",\"Quantity\",\"SKU\",\"Product Name\""));
    assertTrue(csvContent.contains(forecast.getId().toString()));
    assertTrue(csvContent.contains(forecast.getSku()));
    assertTrue(csvContent.contains(forecast.getRecommendedQuantity().toString()));

    verify(forecastRepository).findLatestBatchNumberByStoreId(storeId);
    verify(storeRepository).findById(storeId);
    verify(forecastRepository).findByStoreIdAndBatchNumber(storeId, BATCH_NUMBER);
  }

  @Test
  void exportDataToCSV_StoreNotFound() {
    // Given
    when(storeRepository.findById(storeId)).thenReturn(Optional.empty());

    // When & Then
    assertThrows(IllegalArgumentException.class, () -> service.exportDataToCSV(storeId));
    verify(storeRepository).findById(storeId);
  }

  @Test
  void exportForecastDataToCSV_NoData() {
    // Given
    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(BATCH_NUMBER);
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));
    when(forecastRepository.findByStoreIdAndBatchNumber(storeId, BATCH_NUMBER)).thenReturn(Optional.empty());
    when(csvExporterFactory.getExporter(anyString())).thenReturn(new DefaultReplenishmentRecommendationCsvExporter());

    // When
    ReplenishmentForecastFileExportResult result = service.exportDataToCSV(storeId);

    // Then
    assertNotNull(result);
    assertEquals("Test Store_20240315.csv", result.getFileName());
    assertTrue(result.getContent().length > 0);

    // Verify CSV content contains only header
    String csvContent = new String(result.getContent());
    assertTrue(
        csvContent.contains("\"Batch Number\",\"ID\",\"Customer ID\",\"Quantity\",\"SKU\",\"Product Name\""));
    assertEquals(1, csvContent.split("\n").length); // Header row only

    verify(forecastRepository).findLatestBatchNumberByStoreId(storeId);
    verify(storeRepository).findById(storeId);
    verify(forecastRepository).findByStoreIdAndBatchNumber(storeId, BATCH_NUMBER);
  }

  @Test
  void exportDataToCSV_NoBatchNumber() {
    // Given
    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(null);
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));
    when(forecastRepository.findByStoreIdAndBatchNumber(storeId, null)).thenReturn(Optional.of(List.of()));
    when(csvExporterFactory.getExporter(anyString())).thenReturn(new DefaultReplenishmentRecommendationCsvExporter());

    // When
    ReplenishmentForecastFileExportResult result = service.exportDataToCSV(storeId);

    // Then
    assertNotNull(result);
    assertEquals("Test Store_none.csv", result.getFileName());
    assertTrue(result.getContent().length > 0);

    // Verify CSV content contains only header
    String csvContent = new String(result.getContent());
    assertTrue(
        csvContent.contains("\"Batch Number\",\"ID\",\"Customer ID\",\"Quantity\",\"SKU\",\"Product Name\""));
    assertEquals(1, csvContent.split("\n").length); // Header row only

    verify(forecastRepository).findLatestBatchNumberByStoreId(storeId);
    verify(storeRepository).findById(storeId);
    verify(forecastRepository).findByStoreIdAndBatchNumber(storeId, null);
  }

  @Test
  void generateReplenishment_Success() {
    // Given
    UUID mercasoStoreId = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    String batchNumber = "20240315";
    String upc = "123456789";
    String sku = "SKU123";

    // Mock store
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));

    // Mock batch number generation
    try {
      Method generateBatchNumberMethod = ReplenishmentRecommendationService.class.getDeclaredMethod(
          "generateBatchNumber");
      generateBatchNumberMethod.setAccessible(true);
      when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber);
    } catch (Exception e) {
      fail("Failed to mock generateBatchNumber method", e);
    }

    // Mock store items
    MasterCatalogRawData storeItem = new MasterCatalogRawData();
    UUID rawDataId = UUID.randomUUID();
    storeItem.setId(rawDataId);
    storeItem.setUpc(upc);
    storeItem.setStoreId(storeId);
    List<MasterCatalogRawData> storeItems = List.of(storeItem);
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(storeItems);

    // Mock Mercaso SKU numbers
    when(masterCatalogRawDataRepository.findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(sku));

    // Mock Mercaso items
    MasterCatalogRawData mercasoItem = new MasterCatalogRawData();
    mercasoItem.setSkuNumber(sku);
    mercasoItem.setUpc(upc);
    mercasoItem.setName("Test Product");
    mercasoItem.setPackageSize(10);
    mercasoItem.setPackageType(PackageType.PACK);
    when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(mercasoItem));

    // Mock inventory
    MasterCatalogSquareInventory inventory = new MasterCatalogSquareInventory();
    inventory.setMasterCatalogRawDataId(rawDataId);
    inventory.setQuantity(5);
    when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
        .thenReturn(List.of(inventory));

    // Mock order line items
    SquareOrderLineItemInfo lineItem = mock(SquareOrderLineItemInfo.class);
    when(lineItem.getQuantity()).thenReturn(20);
    when(lineItem.getOrderCreatedAt()).thenReturn(Instant.now().minus(7, ChronoUnit.DAYS));
    when(masterCatalogSquareOrderLineItemRepository.findAllByRawDataIdInAndOrderCreatedAfter(
        anyList(), any(Instant.class))).thenReturn(List.of(lineItem));

    when(imsClientAdaptor.searchItemDetailBySku(anyString())).thenReturn(Optional.empty());

    // Mock WMS Inventory
    ResultInventoryStockDto wmsInventory = new ResultInventoryStockDto();
    InventoryStockDto wmsInventoryData = new InventoryStockDto();
    wmsInventoryData.setSkuNumber(sku);
    wmsInventoryData.setAvailableQty(new BigDecimal(10));
    wmsInventoryData.setStatus("AVAILABLE");
    wmsInventory.setData(List.of(wmsInventoryData));
    when(wmsClientAdaptor.searchInventoryStockBySku(anyString())).thenReturn(Optional.of(wmsInventory));

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    ArgumentCaptor<List<ReplenishmentRecommendation>> forecastCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(forecastRepository).saveAll(forecastCaptor.capture());

    List<ReplenishmentRecommendation> savedForecasts = forecastCaptor.getValue();
    assertNotNull(savedForecasts);
    assertFalse(savedForecasts.isEmpty());

    ReplenishmentRecommendation savedForecast = savedForecasts.get(0);
    assertEquals(storeId, savedForecast.getStoreId());
    assertEquals(sku, savedForecast.getSku());
    assertEquals(upc, savedForecast.getUpc());
    assertEquals("Test Product", savedForecast.getName());
    assertTrue(savedForecast.getRecommendedQuantity().compareTo(BigDecimal.ZERO) > 0);
  }

  @Test
  void generateReplenishment_backupVendorItemAvailability() {
    // Given
    UUID mercasoStoreId = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    String batchNumber = "20240315";
    String upc = "123456789";
    String sku = "SKU123";

    // Mock store
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));

    // Mock batch number generation
    try {
      Method generateBatchNumberMethod = ReplenishmentRecommendationService.class.getDeclaredMethod(
          "generateBatchNumber");
      generateBatchNumberMethod.setAccessible(true);
      when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber);
    } catch (Exception e) {
      fail("Failed to mock generateBatchNumber method", e);
    }

    // Mock store items
    MasterCatalogRawData storeItem = new MasterCatalogRawData();
    UUID rawDataId = UUID.randomUUID();
    storeItem.setId(rawDataId);
    storeItem.setUpc(upc);
    storeItem.setStoreId(storeId);
    List<MasterCatalogRawData> storeItems = List.of(storeItem);
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(storeItems);

    // Mock Mercaso SKU numbers
    when(masterCatalogRawDataRepository.findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(sku));

    // Mock Mercaso items
    MasterCatalogRawData mercasoItem = new MasterCatalogRawData();
    mercasoItem.setSkuNumber(sku);
    mercasoItem.setUpc(upc);
    mercasoItem.setName("Test Product");
    mercasoItem.setPackageSize(10);
    mercasoItem.setPackageType(PackageType.PACK);
    when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(mercasoItem));

    // Mock inventory
    MasterCatalogSquareInventory inventory = new MasterCatalogSquareInventory();
    inventory.setMasterCatalogRawDataId(rawDataId);
    inventory.setQuantity(5);
    when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
        .thenReturn(List.of(inventory));

    // Mock order line items
    SquareOrderLineItemInfo lineItem = mock(SquareOrderLineItemInfo.class);
    when(lineItem.getQuantity()).thenReturn(20);
    when(lineItem.getOrderCreatedAt()).thenReturn(Instant.now().minus(7, ChronoUnit.DAYS));
    when(masterCatalogSquareOrderLineItemRepository.findAllByRawDataIdInAndOrderCreatedAfter(
        anyList(), any(Instant.class))).thenReturn(List.of(lineItem));

    // Mock IMS Inventory
    ItemDto imsInventory = new ItemDto();
    VendorItemDto imsVendorItem = new VendorItemDto();
    imsVendorItem.setAvailability(true);
    imsInventory.setBackupVendorItem(imsVendorItem);
    when(imsClientAdaptor.searchItemDetailBySku(anyString())).thenReturn(Optional.of(imsInventory));

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    ArgumentCaptor<List<ReplenishmentRecommendation>> forecastCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(forecastRepository).saveAll(forecastCaptor.capture());

    List<ReplenishmentRecommendation> savedForecasts = forecastCaptor.getValue();
    assertNotNull(savedForecasts);
    assertFalse(savedForecasts.isEmpty());

    ReplenishmentRecommendation savedForecast = savedForecasts.get(0);
    assertEquals(storeId, savedForecast.getStoreId());
    assertEquals(sku, savedForecast.getSku());
    assertEquals(upc, savedForecast.getUpc());
    assertEquals("Test Product", savedForecast.getName());
    assertTrue(savedForecast.getRecommendedQuantity().compareTo(BigDecimal.ZERO) > 0);
  }


  @Test
  void generateReplenishment_currentInventoryGreaterThanRecommendedQuantity() {
    // Given
    UUID mercasoStoreId = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    String batchNumber = "20240315";
    String upc = "123456789";
    String sku = "SKU123";

    // Mock store
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));

    // Mock batch number generation
    try {
      Method generateBatchNumberMethod = ReplenishmentRecommendationService.class.getDeclaredMethod(
          "generateBatchNumber");
      generateBatchNumberMethod.setAccessible(true);
      when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber);
    } catch (Exception e) {
      fail("Failed to mock generateBatchNumber method", e);
    }

    // Mock store items
    MasterCatalogRawData storeItem = new MasterCatalogRawData();
    UUID rawDataId = UUID.randomUUID();
    storeItem.setId(rawDataId);
    storeItem.setUpc(upc);
    storeItem.setStoreId(storeId);
    List<MasterCatalogRawData> storeItems = List.of(storeItem);
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(storeItems);

    // Mock Mercaso SKU numbers
    when(masterCatalogRawDataRepository.findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(sku));

    // Mock Mercaso items
    MasterCatalogRawData mercasoItem = new MasterCatalogRawData();
    mercasoItem.setSkuNumber(sku);
    mercasoItem.setUpc(upc);
    mercasoItem.setName("Test Product");
    mercasoItem.setPackageSize(10);
    mercasoItem.setPackageType(PackageType.PACK);
    when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(mercasoItem));

    // Mock inventory
    MasterCatalogSquareInventory inventory = new MasterCatalogSquareInventory();
    inventory.setMasterCatalogRawDataId(rawDataId);
    inventory.setQuantity(5);
    when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
        .thenReturn(List.of(inventory));

    // Mock order line items
    SquareOrderLineItemInfo lineItem = mock(SquareOrderLineItemInfo.class);
    when(lineItem.getQuantity()).thenReturn(2);
    when(lineItem.getOrderCreatedAt()).thenReturn(Instant.now().minus(7, ChronoUnit.DAYS));
    when(masterCatalogSquareOrderLineItemRepository.findAllByRawDataIdInAndOrderCreatedAfter(
        anyList(), any(Instant.class))).thenReturn(List.of(lineItem));

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    ArgumentCaptor<List<ReplenishmentRecommendation>> forecastCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(forecastRepository).saveAll(forecastCaptor.capture());

    List<ReplenishmentRecommendation> savedForecasts = forecastCaptor.getValue();
    assertNotNull(savedForecasts);
    assertFalse(savedForecasts.isEmpty());

    ReplenishmentRecommendation savedForecast = savedForecasts.get(0);
    assertEquals(storeId, savedForecast.getStoreId());
    assertEquals(sku, savedForecast.getSku());
    assertEquals(upc, savedForecast.getUpc());
    assertEquals("Test Product", savedForecast.getName());
    assertSame(BigDecimal.ZERO, savedForecast.getRecommendedQuantity());
  }

  @Test
  void generateReplenishment_NoStoreItemsFound() {
    // Given
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(Collections.emptyList());

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    verify(forecastRepository, never()).saveAll(anyList());
  }

  @Test
  void generateReplenishment_NoMercasoSkuNumbersFound() {
    // Given
    UUID mercasoStoreId = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    String upc = "123456789";

    // Mock store
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));

    // Mock store items
    MasterCatalogRawData storeItem = new MasterCatalogRawData();
    storeItem.setId(UUID.randomUUID());
    storeItem.setUpc(upc);
    storeItem.setStoreId(storeId);
    List<MasterCatalogRawData> storeItems = List.of(storeItem);
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(storeItems);

    // Mock empty Mercaso SKU numbers
    when(masterCatalogRawDataRepository.findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(Collections.emptyList());

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    verify(forecastRepository, never()).saveAll(anyList());
  }

  @Test
  void generateReplenishment_NoSalesHistory() {
    // Given
    UUID mercasoStoreId = UUID.fromString("b350491e-e238-4caf-b328-cf2438e057d8");
    String batchNumber = "20240315";
    String upc = "123456789";
    String sku = "SKU123";

    // Mock store
    when(storeRepository.findById(storeId)).thenReturn(Optional.of(store));

    // Mock batch number generation
    try {
      Method generateBatchNumberMethod = ReplenishmentRecommendationService.class.getDeclaredMethod(
          "generateBatchNumber");
      generateBatchNumberMethod.setAccessible(true);
      when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber);
    } catch (Exception e) {
      fail("Failed to mock generateBatchNumber method", e);
    }

    // Mock store items
    MasterCatalogRawData storeItem = new MasterCatalogRawData();
    UUID rawDataId = UUID.randomUUID();
    storeItem.setId(rawDataId);
    storeItem.setUpc(upc);
    storeItem.setStoreId(storeId);
    List<MasterCatalogRawData> storeItems = List.of(storeItem);
    when(masterCatalogRawDataRepository.findAllByStoreId(storeId)).thenReturn(storeItems);

    // Mock Mercaso SKU numbers
    when(masterCatalogRawDataRepository.findAllSkuNumbersByStoreIdAndSourceStatusAndUpcIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(sku));

    // Mock Mercaso items
    MasterCatalogRawData mercasoItem = new MasterCatalogRawData();
    mercasoItem.setSkuNumber(sku);
    mercasoItem.setUpc(upc);
    mercasoItem.setName("Test Product");
    mercasoItem.setPackageSize(10);
    mercasoItem.setPackageType(PackageType.PACK);
    when(masterCatalogRawDataRepository.findAllByStoreIdAndSourceStatusAndSkuNumberIn(
        eq(mercasoStoreId), eq(ACTIVE_SOURCE_STATUS), anyList())).thenReturn(List.of(mercasoItem));

    // Mock inventory
    MasterCatalogSquareInventory inventory = new MasterCatalogSquareInventory();
    inventory.setMasterCatalogRawDataId(rawDataId);
    inventory.setQuantity(0);
    when(masterCatalogSquareInventoryRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
        .thenReturn(List.of(inventory));

    when(imsClientAdaptor.searchItemDetailBySku(anyString())).thenReturn(Optional.empty());
    // Mock WMS Inventory
    ResultInventoryStockDto wmsInventory = new ResultInventoryStockDto();
    InventoryStockDto wmsInventoryData = new InventoryStockDto();
    wmsInventoryData.setSkuNumber(sku);
    wmsInventoryData.setAvailableQty(new BigDecimal(10));
    wmsInventoryData.setStatus("AVAILABLE");
    wmsInventory.setData(List.of(wmsInventoryData));
    when(wmsClientAdaptor.searchInventoryStockBySku(anyString())).thenReturn(Optional.of(wmsInventory));

    // Mock empty order line items (no sales history)
    when(masterCatalogSquareOrderLineItemRepository.findAllByRawDataIdInAndOrderCreatedAfter(
        anyList(), any(Instant.class))).thenReturn(Collections.emptyList());

    // When
    service.generateReplenishment(List.of(storeId));

    // Then
    ArgumentCaptor<List<ReplenishmentRecommendation>> forecastCaptor =
        ArgumentCaptor.forClass(List.class);
    verify(forecastRepository).saveAll(forecastCaptor.capture());

    List<ReplenishmentRecommendation> savedForecasts = forecastCaptor.getValue();
    assertNotNull(savedForecasts);
    assertFalse(savedForecasts.isEmpty());

    ReplenishmentRecommendation savedForecast = savedForecasts.get(0);
    assertEquals(storeId, savedForecast.getStoreId());
    assertEquals(sku, savedForecast.getSku());
    assertEquals(upc, savedForecast.getUpc());
    assertEquals("Test Product", savedForecast.getName());
    // For no sales history and 0 inventory, recommended quantity should be 1 if available inventory > 0
    assertEquals(new BigDecimal(1), savedForecast.getRecommendedQuantity());
  }

  @Test
  public void searchReplenishmentRecommendation_WhenNoDepartmentId_ShouldBeSuccess() {
    UUID storeId = UUID.randomUUID();
    String batchNumber1 = "20250606093633";
    String batchNumber2 = "20250606093635";
    Pageable pageable = PageRequest.of(0, 10);

    String sku1 = UUID.randomUUID().toString();
    String sku2 = UUID.randomUUID().toString();
    ReplenishmentRecommendation recommendation1 = ReplenishmentRecommendation.builder()
        .storeId(storeId).batchNumber(batchNumber1).sku(sku1).build();
    ReplenishmentRecommendation recommendation2 = ReplenishmentRecommendation.builder()
        .storeId(storeId).batchNumber(batchNumber2).sku(sku2).build();

    ReplenishmentRecommendationDto recommendationDto1 = ReplenishmentRecommendationDto.builder()
        .storeId(storeId).batchNumber(batchNumber1).sku(sku1).build();
    ReplenishmentRecommendationDto recommendationDto2 = ReplenishmentRecommendationDto.builder()
        .storeId(storeId).batchNumber(batchNumber2).sku(sku2).build();

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber2);
    when(replenishmentRecommendationMapper.toDto(recommendation1)).thenReturn(recommendationDto1);
    when(replenishmentRecommendationMapper.toDto(recommendation2)).thenReturn(recommendationDto2);

    when(forecastRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(
        new PageImpl<>(List.of(recommendation2), pageable, 1));

    PageableResponse<ReplenishmentRecommendationDto> response = service.searchReplenishmentRecommendation(
        storeId, null, pageable);

    verify(replenishmentRecommendationMapper).toDto(recommendation2);

    assertNotNull(response);
    assertEquals(0, response.getPageNumber());
    assertEquals(recommendationDto2, response.getData().get(0));
    assertEquals(10, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(1, response.getTotalElements());
  }

  @Test
  public void searchReplenishmentRecommendation_WithDepartmentId_ShouldBeSuccess() {
    UUID storeId = UUID.randomUUID();
    String batchNumber1 = "20250606093633";
    String batchNumber2 = "20250606093635";

    UUID departmentId1 = UUID.randomUUID();
    UUID departmentId2 = UUID.randomUUID();
    Pageable pageable = PageRequest.of(0, 10);

    String sku1 = UUID.randomUUID().toString();
    String sku2 = UUID.randomUUID().toString();
    ReplenishmentRecommendation recommendation1 = ReplenishmentRecommendation.builder()
        .storeId(storeId).batchNumber(batchNumber1).departmentId(departmentId1).sku(sku1).build();
    ReplenishmentRecommendation recommendation2 = ReplenishmentRecommendation.builder()
        .storeId(storeId).batchNumber(batchNumber2).departmentId(departmentId2).sku(sku2).build();

    ReplenishmentRecommendationDto recommendationDto1 = ReplenishmentRecommendationDto.builder()
        .storeId(storeId).batchNumber(batchNumber1).departmentId(departmentId1).sku(sku1).build();
    ReplenishmentRecommendationDto recommendationDto2 = ReplenishmentRecommendationDto.builder()
        .storeId(storeId).batchNumber(batchNumber2).departmentId(departmentId2).sku(sku2).build();

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber2);
    when(replenishmentRecommendationMapper.toDto(recommendation1)).thenReturn(recommendationDto1);
    when(replenishmentRecommendationMapper.toDto(recommendation2)).thenReturn(recommendationDto2);

    when(forecastRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(
        new PageImpl<>(List.of(recommendation2), pageable, 1));

    PageableResponse<ReplenishmentRecommendationDto> response = service.searchReplenishmentRecommendation(
        storeId, null, pageable);

    verify(replenishmentRecommendationMapper).toDto(recommendation2);

    assertNotNull(response);
    assertEquals(1, response.getData().size());
    assertEquals(0, response.getPageNumber());
    assertEquals(recommendationDto2, response.getData().get(0));
    assertEquals(departmentId2, response.getData().get(0).getDepartmentId());
    assertEquals(10, response.getPageSize());
    assertEquals(1, response.getTotalPages());
    assertEquals(1, response.getTotalElements());
  }

  @Test
  void searchReplenishmentRecommendation_WhenBatchNumberNotFound_ShouldReturnEmptyResult() {

    UUID storeId = UUID.randomUUID();
    Pageable pageable = PageRequest.of(0, 10);

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(null);

    PageableResponse<ReplenishmentRecommendationDto> response = service.searchReplenishmentRecommendation(
        storeId, null, pageable);

    assertNotNull(response);
    assertTrue(response.getData().isEmpty());
    assertEquals(0, response.getPageNumber());
    assertEquals(10, response.getPageSize());
    assertEquals(0, response.getTotalPages());
    assertEquals(0, response.getTotalElements());
  }

  @Test
  void searchReplenishmentRecommendation_WhenBatchNumberIsEmpty_ShouldReturnEmptyResult() {

    UUID storeId = UUID.randomUUID();
    Pageable pageable = PageRequest.of(0, 10);

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn("");

    PageableResponse<ReplenishmentRecommendationDto> response = service.searchReplenishmentRecommendation(
        storeId, null, pageable);

    assertNotNull(response);
    assertTrue(response.getData().isEmpty());
    assertEquals(0, response.getPageNumber());
    assertEquals(10, response.getPageSize());
    assertEquals(0, response.getTotalPages());
    assertEquals(0, response.getTotalElements());
  }

  @Test
  void searchDepartment_WhenBatchNumberIsEmpty_ShouldReturnEmptyList() {
    UUID storeId = UUID.randomUUID();

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn("");

    List<DepartmentDto> result = service.searchDepartment(storeId);

    assertTrue(result.isEmpty());
  }

  @Test
  void searchDepartment_WithDuplicateData_ShouldReturnDistinctList() {

    UUID storeId = UUID.randomUUID();
    String batchNumber = "20250606093635";
    String departmentId1 = UUID.randomUUID().toString();
    String departmentId2 = UUID.randomUUID().toString();

    ReplenishmentRecommendation rec1 = buildRecommendation(departmentId1, "TEST_DEPARTMENT_1");
    ReplenishmentRecommendation rec2 = buildRecommendation(departmentId1, "TEST_DEPARTMENT_1");
    ReplenishmentRecommendation rec3 = buildRecommendation(departmentId2, "TEST_DEPARTMENT_2");

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(batchNumber);
    when(forecastRepository.findByStoreIdAndBatchNumber(storeId, batchNumber))
        .thenReturn(Optional.of(List.of(rec1, rec2, rec3)));

    List<DepartmentDto> result = service.searchDepartment(storeId);

    assertEquals(2, result.size());
    assertTrue(result.stream().anyMatch(d -> d.id().equals(departmentId1)));
    assertTrue(result.stream().anyMatch(d -> d.id().equals(departmentId2)));
  }

  @Test
  void searchDepartment_WhenBatchNumberNotFound_ShouldReturnEmptyList() {
    UUID storeId = UUID.randomUUID();

    when(forecastRepository.findLatestBatchNumberByStoreId(storeId)).thenReturn(null);

    List<DepartmentDto> result = service.searchDepartment(storeId);

    assertTrue(result.isEmpty());
    // Due to early return when batch number is null, findByStoreIdAndBatchNumber should not be called
    verify(forecastRepository, never()).findByStoreIdAndBatchNumber(any(), any());
  }

  private ReplenishmentRecommendation buildRecommendation(String deptId, String deptName) {
    return ReplenishmentRecommendation.builder()
        .departmentId(UUID.fromString(deptId))
        .departmentName(deptName)
        .nextOrderTime(Instant.now().plus(1, ChronoUnit.DAYS))
        .build();
  }
}
