package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.metrics.dto.MetricsOrderAmountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderDepartmentDistributionDto;
import com.mercaso.data.metrics.dto.MetricsOrderDiscountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderFrequencyDto;
import com.mercaso.data.metrics.dto.MetricsOrderItemRecommendationDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class OrderRestApi extends IntegrationTestRestUtil {

    public OrderRestApi(Environment environment) {
        super(environment);
    }


    private static final String UNPURCHASED_ITEMS_RECOMMENDATION = "/metrics/order/unpurchased-items-recommendation";


    public List<MetricsOrderItemRecommendationDto> getUnpurchasedItemsRecommendation(String addressId, String filterDepartment) {

        Map<String, Object> params = Map.of("addressId", addressId, "departmentName", filterDepartment);

        ParameterizedTypeReference<List<MetricsOrderItemRecommendationDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(UNPURCHASED_ITEMS_RECOMMENDATION, responseType, params).getBody();
    }

    private static final String PURCHASED_ITEMS_RECOMMENDATION = "/metrics/order/purchased-items-recommendation";


    public List<MetricsOrderItemRecommendationDto> getPurchasedItemsRecommendation(String addressId, String filterDepartment) {

        Map<String, Object> params = Map.of("addressId", addressId, "departmentName", filterDepartment);

        ParameterizedTypeReference<List<MetricsOrderItemRecommendationDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(PURCHASED_ITEMS_RECOMMENDATION, responseType, params).getBody();
    }

    private static final String ORDER_AMOUNT_SUMMARY = "/metrics/order/amount-summary";

    public List<MetricsOrderAmountSummaryDto> getOrderAmountSummary(
            String departmentName, String addressId, String timeAggType, Integer timeLength) {
        
        Map<String, Object> params = Map.of(
            "departmentName", departmentName,
            "addressId", addressId,
            "timeAggType", timeAggType,
            "timeLength", timeLength
        );

        ParameterizedTypeReference<List<MetricsOrderAmountSummaryDto>> responseType =
            new ParameterizedTypeReference<>() {};

        return getEntityByMap(ORDER_AMOUNT_SUMMARY, responseType, params).getBody();
    }

    private static final String ORDER_FREQUENCY_V2 = "/metrics/order/v2/frequency";

    public List<MetricsOrderFrequencyDto> getOrderFrequencyV2(String addressId, String timeAggType, Integer timeLength) {
        
        Map<String, Object> params = Map.of(
            "addressId", addressId,
            "timeAggType", timeAggType,
            "timeLength", timeLength
        );

        ParameterizedTypeReference<List<MetricsOrderFrequencyDto>> responseType = 
            new ParameterizedTypeReference<>() {};

        return getEntityByMap(ORDER_FREQUENCY_V2, responseType, params).getBody();
    }


    private static final String ORDER_DEPARTMENT_DISTRIBUTION = "/metrics/order/department-distribution";

    public List<MetricsOrderDepartmentDistributionDto> getOrderDepartmentDistribution(String addressId, String timeAggType, Integer timeLength) {

        Map<String, Object> params = Map.of(
            "addressId", addressId,
            "timeAggType", timeAggType,
            "timeLength", timeLength
        );

        ParameterizedTypeReference<List<MetricsOrderDepartmentDistributionDto>> responseType =
            new ParameterizedTypeReference<>() {};

        return getEntityByMap(ORDER_DEPARTMENT_DISTRIBUTION, responseType, params).getBody();
    }

    private static final String ORDER_DISCOUNT_SUMMARY_METRICS = "/metrics/order/discount-summary";


    public MetricsOrderDiscountSummaryDto getOrderDiscountSummary(String addressId, String timeAggType, Integer timeLength) {

        Map<String, Object> params = Map.of(
            "addressId", addressId,
            "timeAggType", timeAggType,
            "timeLength", timeLength
        );

        ParameterizedTypeReference<MetricsOrderDiscountSummaryDto> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(ORDER_DISCOUNT_SUMMARY_METRICS, responseType, params).getBody();
    }
}
