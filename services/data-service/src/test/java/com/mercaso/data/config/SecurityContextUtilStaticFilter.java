package com.mercaso.data.config;

import static org.mockito.Mockito.mockStatic;

import com.google.common.collect.Lists;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import org.mockito.MockedStatic;
import org.springframework.boot.test.context.TestComponent;
import org.springframework.web.filter.OncePerRequestFilter;

@TestComponent
public class SecurityContextUtilStaticFilter extends OncePerRequestFilter {

  public static final String MOCKED_USER_ID = "ebeea0e3-a9c5-4f9b-88f8-c9719ee7db98";

  private final List<String> mockedRoles = Lists.newArrayList("admin_master_catalog");

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
      FilterChain filterChain) throws ServletException, IOException {
    try (MockedStatic<SecurityContextUtil> securityContextUtilMockedStatic = mockStatic(
        SecurityContextUtil.class)) {
      securityContextUtilMockedStatic.when(SecurityContextUtil::getMercasoRoles)
          .thenReturn(mockedRoles);
      securityContextUtilMockedStatic.when(SecurityContextUtil::getLoginUserId)
          .thenReturn(MOCKED_USER_ID);
      // Additional method when

      filterChain.doFilter(request, response);
    }
  }
}
