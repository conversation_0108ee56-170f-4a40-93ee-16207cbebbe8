package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class SuqareInventoryMetricsResourceApiUtils extends IntegrationTestRestUtil {

    private static final String V1_INIT_DAILY_METRICS = "/master-catalog/v1/square-inventory-metrics/daily-metrics/init";
    private static final String V1_DAILY_METRICS = "/master-catalog/v1/square-inventory-metrics/daily-metrics";

    public SuqareInventoryMetricsResourceApiUtils(Environment environment) {
        super(environment);
    }

    public ResponseEntity<Void> initDailyMetrics(Integer daysBefore) {
        return postEntity(V1_INIT_DAILY_METRICS + "?daysBefore=" + daysBefore, null, Void.class);
    }

    public void calculateYesterdayMetrics() {
        postEntity(V1_DAILY_METRICS, null, null);
    }

}
