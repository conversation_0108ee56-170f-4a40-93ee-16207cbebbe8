package com.mercaso.data.metrics.mapper;

import com.mercaso.data.metrics.dto.StoreProfilingStoreContinuousMetricsDto;
import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class StoreProfilingStoreContinuousMetricsDtoMapperTest {

    private final StoreProfilingStoreContinuousMetricsDtoMapper mapper = Mappers.getMapper(StoreProfilingStoreContinuousMetricsDtoMapper.class);

    @Test
    void toDto_ShouldMapEntityToDto() {
        // Given
        StoreProfilingStoreContinuousMetricsEntity entity = createTestEntity();

        // When
        StoreProfilingStoreContinuousMetricsDto dto = mapper.toDto(entity);

        // Then
        assertNotNull(dto);
        assertEquals(entity.getStoreId(), dto.getStoreId());
        assertEquals(entity.getMetricCategory(), dto.getMetricCategory());
        assertEquals(entity.getMetricName(), dto.getMetricName());
        assertEquals(entity.getMetricDesc(), dto.getMetricDesc());
        assertEquals(entity.getMetricValue(), dto.getMetricValue());
        assertEquals(entity.getMetricDate(), dto.getMetricDate());
        assertEquals(entity.getMetricDateType(), dto.getMetricDateType());
    }

    @Test
    void toEntity_ShouldMapDtoToEntity() {
        // Given
        StoreProfilingStoreContinuousMetricsDto dto = createTestDto();

        // When
        StoreProfilingStoreContinuousMetricsEntity entity = mapper.toEntity(dto);

        // Then
        assertNotNull(entity);
        assertEquals(dto.getStoreId(), entity.getStoreId());
        assertEquals(dto.getMetricCategory(), entity.getMetricCategory());
        assertEquals(dto.getMetricName(), entity.getMetricName());
        assertEquals(dto.getMetricDesc(), entity.getMetricDesc());
        assertEquals(dto.getMetricValue(), entity.getMetricValue());
        assertEquals(dto.getMetricDate(), entity.getMetricDate());
        assertEquals(dto.getMetricDateType(), entity.getMetricDateType());
    }

    @Test
    void toDtoList_ShouldMapEntityListToDtoList() {
        // Given
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntity(),
            createTestEntity()
        );

        // When
        List<StoreProfilingStoreContinuousMetricsDto> dtos = mapper.toDtoList(entities);

        // Then
        assertNotNull(dtos);
        assertEquals(2, dtos.size());
        assertNotNull(dtos.get(0));
        assertNotNull(dtos.get(1));
    }

    private StoreProfilingStoreContinuousMetricsEntity createTestEntity() {
        StoreProfilingStoreContinuousMetricsEntity entity = new StoreProfilingStoreContinuousMetricsEntity();
        entity.setId(1L);
        entity.setStoreId("test-store");
        entity.setMetricCategory("test-category");
        entity.setMetricName("test-metric");
        entity.setMetricDesc("test description");
        entity.setMetricValue(new BigDecimal("100.50"));
        entity.setMetricDate(LocalDateTime.of(2024, 1, 1, 12, 0));
        entity.setMetricDateType("daily");
        return entity;
    }

    private StoreProfilingStoreContinuousMetricsDto createTestDto() {
        StoreProfilingStoreContinuousMetricsDto dto = new StoreProfilingStoreContinuousMetricsDto();
        dto.setStoreId("test-store");
        dto.setMetricCategory("test-category");
        dto.setMetricName("test-metric");
        dto.setMetricDesc("test description");
        dto.setMetricValue(new BigDecimal("100.50"));
        dto.setMetricDate(LocalDateTime.of(2024, 1, 1, 12, 0));
        dto.setMetricDateType("daily");
        return dto;
    }
} 