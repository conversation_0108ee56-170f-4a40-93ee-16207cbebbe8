package com.mercaso.data.swagger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.mercaso.data.AbstractIT;
import java.io.BufferedWriter;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

public class SwaggerIT extends AbstractIT {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void createSwaggerJson() throws Exception {
        MvcResult mvcResult = this.mockMvc.perform(get("/v3/api-docs")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        MockHttpServletResponse response = mvcResult.getResponse();

        assertEquals(200, response.getStatus());

        String swaggerJson = response.getContentAsString();

        String outputFile = System.getenv("SWAGGER_OUTPUT_FILE");
        if (outputFile == null || outputFile.isEmpty()) {
            return;
        }

        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(outputFile), StandardCharsets.UTF_8)) {
            writer.write(swaggerJson);
        }

        assertTrue(new File(outputFile).exists());
    }

}
