package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.MetricsOrderDiscountEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MetricsOrderDiscountEntityMock {

    public static MetricsOrderDiscountEntity metricsOrderDiscountEntityMock(String addressId, String dateType,
        Integer timeLength) {
        MetricsOrderDiscountEntity metricsOrderDiscountEntity = new MetricsOrderDiscountEntity();

        metricsOrderDiscountEntity.setAddressId(addressId);
        metricsOrderDiscountEntity.setDateType(dateType);
        metricsOrderDiscountEntity.setDateLength(timeLength);
        metricsOrderDiscountEntity.setCreatedAt(LocalDateTime.now());

        metricsOrderDiscountEntity.setDiscountOrderAmount(BigDecimal.valueOf(20));
        metricsOrderDiscountEntity.setTotalOrderAmount(BigDecimal.valueOf(100));
        metricsOrderDiscountEntity.setDiscountOrderCount(1);
        metricsOrderDiscountEntity.setTotalOrderCount(10);

        return metricsOrderDiscountEntity;
    }

}
