package com.mercaso.data.third_party.utils.controller_utils;


import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.third_party.dto.shopify.ShopifyOrderFilter;
import com.mercaso.data.third_party.dto.shopify.ShopifyProductFilter;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class ShopifyRestApi extends IntegrationTestRestUtil {

    private static final String CREATE_TEST_ORDER_URL = "/shopify/orders";

    private static final String CREATE_TEST_PRODUCT_URL = "/shopify/products";

    public ShopifyRestApi(Environment environment) {
        super(environment);
    }

    public ResponseEntity<CustomPage> getOrders(ShopifyOrderFilter filter) {
        return postEntity(CREATE_TEST_ORDER_URL, filter, CustomPage.class);
    }

    public ResponseEntity<CustomPage> getProducts(ShopifyProductFilter filter) {
        return postEntity(CREATE_TEST_PRODUCT_URL, filter, CustomPage.class);
    }
}
