package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogProductUtils {

    public static MasterCatalogProduct buildMasterCatalogProduct(UUID id) {
        MasterCatalogProduct product = new MasterCatalogProduct();
        product.setId(id);
        product.setUpc(String.valueOf(Instant.now().getEpochSecond()));
        product.setName("Test Product");
        product.setDescription("Test Description");
        product.setMasterCatalogRawDataId(UUID.randomUUID());
        return product;
    }
}
