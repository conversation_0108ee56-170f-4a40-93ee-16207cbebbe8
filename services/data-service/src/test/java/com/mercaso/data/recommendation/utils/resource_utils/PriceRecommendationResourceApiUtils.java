package com.mercaso.data.recommendation.utils.resource_utils;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class PriceRecommendationResourceApiUtils extends IntegrationTestRestUtil {

  public static final String PRICE_RECOMMENDATIONS_V1_BASE = "/recommendation/v1/price-recommendations";
  public static final String PRICE_RECOMMENDATIONS_V1_SEARCH =
      PRICE_RECOMMENDATIONS_V1_BASE + "/search";
  public static final String PRICE_RECOMMENDATIONS_V1_DEPARTMENTS =
      PRICE_RECOMMENDATIONS_V1_BASE + "/departments";
  public static final String PRICE_RECOMMENDATIONS_V1_EVERGREEN_DEPARTMENTS =
      PRICE_RECOMMENDATIONS_V1_BASE + "/evergreen/departments";
  public static final String PRICE_RECOMMENDATIONS_V1_SEARCH_ALL =
      PRICE_RECOMMENDATIONS_V1_BASE + "/all/search";
  public static final String PRICE_RECOMMENDATIONS_V1_EVERGREEN =
      PRICE_RECOMMENDATIONS_V1_BASE + "/evergreen/search";

  public PriceRecommendationResourceApiUtils(Environment environment) {
    super(environment);
  }

  public PageableResponse<PriceRecommendationDto> searchPriceRecommendations(String storeId,
      String departmentId, String searchText, Boolean latestOrder,
      Integer pageNumber,
      Integer pageSize) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("storeId", storeId);
    params.put("latestOrder", String.valueOf(latestOrder));

    if (departmentId != null) {
      params.put("departmentId", departmentId);
    }

    if (searchText != null) {
      params.put("searchText", searchText);
    }

    if (pageNumber != null) {
      params.put("pageNumber", pageNumber.toString());
    }

    if (pageSize != null) {
      params.put("pageSize", pageSize.toString());
    }

    ResponseEntity<String> response = performRequest(
        PRICE_RECOMMENDATIONS_V1_SEARCH,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            PriceRecommendationDto.class));
  }

  public PageableResponse<PriceRecommendationDto> searchEverGreenRecommendations(String departmentId) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("pageNumber", "0");
    params.put("pageSize", "10");
    params.put("departmentId", departmentId);


    ResponseEntity<String> response = performRequest(
        PRICE_RECOMMENDATIONS_V1_EVERGREEN,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            PriceRecommendationDto.class));
  }

  public List<DepartmentDto> searchDepartments(String storeId) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("storeId", storeId);

    return getEntityList(
        PRICE_RECOMMENDATIONS_V1_DEPARTMENTS,
        params,
        DepartmentDto.class);
  }

  public List<DepartmentDto> searchEverGreenDepartments() throws Exception {
    return getEntityList(
        PRICE_RECOMMENDATIONS_V1_EVERGREEN_DEPARTMENTS,
        null,
        DepartmentDto.class);
  }

  public PageableResponse<PriceRecommendationDto> searchAll(String storeId, String searchText) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("pageNumber", "0");
    params.put("pageSize", "10");
    params.put("storeId", storeId);
    params.put("searchText", searchText);

    ResponseEntity<String> response = performRequest(
        PRICE_RECOMMENDATIONS_V1_SEARCH_ALL,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            PriceRecommendationDto.class));
  }
}
