package com.mercaso.data.metrics.repository;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.entity.AddressInfoEntity;
import com.mercaso.data.metrics.mock.AddressInfoEntityMock;
import com.mercaso.data.metrics.utils.LuceneTokenizer;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

class AddressInfoRepositoryIT extends AbstractIT {

    @Autowired
    private AddressInfoRepository addressInfoRepository;


    @Test
    void testFindBySearchQuery() {

        String addressId1 = UUID.randomUUID().toString();
        String addressName1 = "address1 Line one";
        String addressId2 = UUID.randomUUID().toString();
        String addressName2 = "address1 Line two";
        AddressInfoEntity addressInfoEntity = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity.setAddressId(addressId1);
        addressInfoEntity.setAddressName(addressName1);

        AddressInfoEntity addressInfoEntity2 = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity2.setAddressId(addressId2);
        addressInfoEntity2.setAddressName(addressName2);

        addressInfoRepository.saveAll(List.of(addressInfoEntity2, addressInfoEntity));

        String query = LuceneTokenizer.convertKeywordToTsQuery("one");
        Pageable pageable = PageRequest.of(0, 10);
        Page<AddressInfoEntity> resultPage = addressInfoRepository.findBySearchQuery(query, pageable);

        assertTrue(resultPage.getTotalElements() >= 1);
        assert resultPage.get()
            .toList()
            .stream()
            .anyMatch(addressInfoEntity1 -> addressInfoEntity1.getAddressId().equals(addressId1));

        String query2 = LuceneTokenizer.convertKeywordToTsQuery("address1");
        Pageable pageable2 = PageRequest.of(0, 10);
        Page<AddressInfoEntity> resultPage2 = addressInfoRepository.findBySearchQuery(query2, pageable2);

        assertTrue(resultPage2.getTotalElements() >= 2);
    }

    @Test
    void testFindBySearchQueryDistinct() {

        String addressId1 = UUID.randomUUID().toString();
        String addressName1 = "address2 Line three";
        AddressInfoEntity addressInfoEntity = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity.setAddressId(addressId1);
        addressInfoEntity.setAddressName(addressName1);

        AddressInfoEntity addressInfoEntity2 = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity2.setAddressId(addressId1);
        addressInfoEntity2.setAddressName(addressName1);

        addressInfoRepository.saveAll(List.of(addressInfoEntity2, addressInfoEntity));

        String query = LuceneTokenizer.convertKeywordToTsQuery("three");
        Pageable pageable = PageRequest.of(0, 10);
        Page<AddressInfoEntity> resultPage = addressInfoRepository.findBySearchQuery(query, pageable);

        assertTrue(resultPage.getTotalElements() >= 1);
        assert resultPage.get()
            .toList()
            .stream()
            .anyMatch(addressInfoEntity1 -> addressInfoEntity1.getAddressId().equals(addressId1));

        String query2 = LuceneTokenizer.convertKeywordToTsQuery("address2");
        Pageable pageable2 = PageRequest.of(0, 10);
        Page<AddressInfoEntity> resultPage2 = addressInfoRepository.findBySearchQuery(query2, pageable2);

        assert resultPage2.getTotalElements() >= 1;
    }

    @Test
    void testQueryAll() {

        String addressId1 = UUID.randomUUID().toString();
        String addressName1 = "address3 Line Four";
        AddressInfoEntity addressInfoEntity = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity.setAddressId(addressId1);
        addressInfoEntity.setAddressName(addressName1);

        String addressId2 = UUID.randomUUID().toString();
        String addressName2 = "address4 Line Five";
        AddressInfoEntity addressInfoEntity2 = AddressInfoEntityMock.addressInfoEntityMock();
        addressInfoEntity2.setAddressId(addressId2);
        addressInfoEntity2.setAddressName(addressName2);

        addressInfoRepository.saveAll(List.of(addressInfoEntity2, addressInfoEntity));

        Pageable pageable = PageRequest.of(0, 10);
        Page<AddressInfoEntity> resultPage = addressInfoRepository.queryAll(pageable);

        assertNotNull(resultPage);
    }
}
