package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.OrderSkuQuantitySummaryEntity;

public class OrderSkuQuantitySummaryEntityMock {

    public static OrderSkuQuantitySummaryEntity orderSkuQuantitySummaryEntityMock(String quantityType) {
        OrderSkuQuantitySummaryEntity entity = new OrderSkuQuantitySummaryEntity();
        entity.setId("1");
        entity.setSku("sku");
        entity.setQuantity(1L);
        entity.setQuantityType(quantityType);
        return entity;
    }

}
