package com.mercaso.data.recommendation.utils.resource_utils;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.utils.IntegrationTestRestUtil;

@Component
public class ItemRecommendationResourceApiUtils extends IntegrationTestRestUtil {

  private static final String RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_SEARCH = "/recommendation/v1/item-recommendations/search";
  private static final String RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_DEPARTMENTS = "/recommendation/v1/item-recommendations/departments";
  private final ObjectMapper objectMapper = new ObjectMapper();

  public ItemRecommendationResourceApiUtils(Environment environment) {
    super(environment);
  }

  public PageableResponse<ItemRecommendationDto> searchItemRecommendations(String storeId,
      Integer pageNumber,
      Integer pageSize) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("storeId", storeId);

    if (pageNumber != null) {
      params.put("pageNumber", pageNumber.toString());
    }

    if (pageSize != null) {
      params.put("pageSize", pageSize.toString());
    }

    ResponseEntity<String> response = performRequest(
        RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_SEARCH,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            ItemRecommendationDto.class));
  }

  public PageableResponse<ItemRecommendationDto> searchItemRecommendationsWithReason(
      String storeId, String reason, Integer pageNumber, Integer pageSize) throws Exception {
    return searchItemRecommendationsWithReasons(storeId, reason != null ? List.of(reason) : null,
        pageNumber, pageSize);
  }

  public PageableResponse<ItemRecommendationDto> searchItemRecommendationsWithReasons(
      String storeId, List<String> reasons, Integer pageNumber, Integer pageSize) throws Exception {

    // Use Map to handle parameters including List<String> reasons
    Map<String, Object> params = new HashMap<>();
    params.put("storeId", storeId);

    // Handle List<String> reasons parameter properly for HTTP query parameters
    // The getEntityByMap method will handle Collection values correctly
    if (reasons != null && !reasons.isEmpty()) {
      params.put("reasons", reasons);
    }

    // Add pagination parameters
    if (pageNumber != null) {
      params.put("pageNumber", pageNumber.toString());
    }

    if (pageSize != null) {
      params.put("pageSize", pageSize.toString());
    }

    ParameterizedTypeReference<PageableResponse<ItemRecommendationDto>> responseType = new ParameterizedTypeReference<PageableResponse<ItemRecommendationDto>>() {
    };

    ResponseEntity<PageableResponse<ItemRecommendationDto>> response = getEntityByMap(
        RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_SEARCH, responseType, params);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return response.getBody();
  }

  public PageableResponse<ItemRecommendationDto> searchItemRecommendationsWithVersionAndDepartmentId(
      String storeId, String version, String departmentId, Integer pageNumber,
      Integer pageSize) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("storeId", storeId);

    if (version != null) {
      params.put("version", version);
    }

    if (departmentId != null) {
      params.put("departmentId", departmentId);
    }

    if (pageNumber != null) {
      params.put("pageNumber", pageNumber.toString());
    }

    if (pageSize != null) {
      params.put("pageSize", pageSize.toString());
    }

    ResponseEntity<String> response = performRequest(
        RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_SEARCH,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(),
        objectMapper.getTypeFactory().constructParametricType(PageableResponse.class,
            ItemRecommendationDto.class));
  }

  public List<DepartmentDto> getDepartments(String storeId, String version) throws Exception {
    Map<String, String> params = new HashMap<>();

    if (storeId != null) {
      params.put("storeId", storeId);
    }

    if (version != null) {
      params.put("version", version);
    }

    ResponseEntity<String> response = performRequest(
        RECOMMENDATION_V1_ITEM_RECOMMENDATIONS_DEPARTMENTS,
        params,
        null,
        HttpMethod.GET);

    assertTrue(response.getStatusCode().is2xxSuccessful());
    return objectMapper.readValue(response.getBody(), new TypeReference<List<DepartmentDto>>() {
    });
  }
}
