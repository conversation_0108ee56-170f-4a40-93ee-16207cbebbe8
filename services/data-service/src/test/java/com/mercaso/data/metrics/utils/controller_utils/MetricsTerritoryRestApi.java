package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.MetricsTerritoryAlertDto;
import com.mercaso.data.metrics.dto.MetricsTerritorySalespersonDto;
import com.mercaso.data.metrics.enums.AlertType;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class MetricsTerritoryRestApi extends IntegrationTestRestUtil {

    private static final String SALES_PERSONS = "/metrics/territory/salesperson";

    private static final String ALERT = "/metrics/territory/alert";

    public MetricsTerritoryRestApi(Environment environment) {
        super(environment);
    }

    public CustomPage<MetricsTerritorySalespersonDto> getSalesperson() {

        Map<String, Object> params = Map.of();

        ParameterizedTypeReference<CustomPage<MetricsTerritorySalespersonDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(SALES_PERSONS, responseType, params).getBody();
    }

    public CustomPage<MetricsTerritoryAlertDto> getAlert(AlertType alertType,List<String> zipCodes) {

        Map<String, Object> params = Map.of("alertType",alertType.name(),"zipCodes",zipCodes);

        ParameterizedTypeReference<CustomPage<MetricsTerritoryAlertDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(ALERT, responseType, params).getBody();
    }
}
