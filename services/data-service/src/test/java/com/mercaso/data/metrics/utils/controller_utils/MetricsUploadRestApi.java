package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Component
public class MetricsUploadRestApi extends IntegrationTestRestUtil {

    public MetricsUploadRestApi(Environment environment) {
        super(environment);
    }

    private static final String METRICS_UPLOAD_DOCUMENT = "/metrics/document/v1/upload/ANY";


    public void uploadDocument() {
        byte[] byteArray = "test content".getBytes();
        ByteArrayResource resource = new ByteArrayResource(byteArray) {
            @Override
            public String getFilename() {
                return "test.txt";
            }
        };

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", resource);

        postEntity(METRICS_UPLOAD_DOCUMENT, body, null, MediaType.MULTIPART_FORM_DATA);
    }

}
