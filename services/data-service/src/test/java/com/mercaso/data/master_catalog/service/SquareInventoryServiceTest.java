package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareVariationMappingRepository;
import com.mercaso.data.master_catalog.service.impl.SquareInventoryServiceImpl;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

class SquareInventoryServiceTest {

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);
    private final MasterCatalogSquareVariationMappingRepository variationMappingRepository =
        mock(MasterCatalogSquareVariationMappingRepository.class);
    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository =
        mock(MasterCatalogRawDataRepository.class);
    private final MasterCatalogLocationRepository masterCatalogLocationRepository =
        mock(MasterCatalogLocationRepository.class);

    private final SquareInventoryServiceImpl squareInventoryService = new SquareInventoryServiceImpl(
        squareApiAdapter,
        variationMappingRepository,
        masterCatalogRawDataRepository,
        masterCatalogLocationRepository
    );

    private UUID storeId;
    private MockMultipartFile validFile;

    @BeforeEach
    void setUp() throws IOException {
        storeId = UUID.randomUUID();

        // Create a real Excel workbook
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Inventory");

        // Create header row
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("VariationId");
        headerRow.createCell(1).setCellValue("Quantity");
        headerRow.createCell(2).setCellValue("LocationId");

        // Create data row
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("VAR001");
        dataRow.createCell(1).setCellValue("10");
        dataRow.createCell(2).setCellValue("LOC1");

        // Convert workbook to bytes
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] bytes = bos.toByteArray();

        validFile = new MockMultipartFile(
            "inventory.xlsx",
            "inventory.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            bytes
        );

        workbook.close();
        bos.close();
    }

    @Test
    void testBatchChangeInventory_WithValidFile_Success() throws IOException {
        // Act
        squareInventoryService.batchChangeInventory(storeId, validFile);

        // Assert - verify the process completed without exceptions
        // Note: actual processing is done in InventoryAdjustmentListener
    }

    @Test
    void testBatchChangeInventory_WithIOException_ThrowsException() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getInputStream()).thenThrow(new IOException("Failed to read file"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            squareInventoryService.batchChangeInventory(storeId, mockFile);
        });

        assertEquals("Failed to process inventory adjustment file", exception.getMessage());
    }

    @Test
    void testBatchChangeInventory_WithNullFile_ThrowsException() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            squareInventoryService.batchChangeInventory(storeId, null);
        });
    }

    @Test
    void testBatchChangeInventory_WithEmptyFile_ThrowsException() {
        // Arrange
        MockMultipartFile emptyFile = new MockMultipartFile(
            "empty.xlsx",
            "empty.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            new byte[0]
        );

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            squareInventoryService.batchChangeInventory(storeId, emptyFile);
        });

        assertEquals("Convert excel format exception.You can try specifying the 'excelType' yourself",
            exception.getMessage());
    }
}