package com.mercaso.data.metrics.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.entity.AddressInfoEntity;
import com.mercaso.data.metrics.mock.AddressInfoEntityMock;
import com.mercaso.data.metrics.repository.AddressInfoRepository;
import com.mercaso.data.metrics.service.impl.MetricsStoreServiceImpl;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class MetricsStoreServiceTest {

    private final AddressInfoRepository addressInfoRepository = Mockito.mock(AddressInfoRepository.class);

    private final MetricsStoreService metricsStoreService = new MetricsStoreServiceImpl(addressInfoRepository);

    @Test
    void searchStoreAddressesV2_EmptyKeyword_ReturnsAllAddressesForSalesApp() {

        AddressInfoEntity entity = AddressInfoEntityMock.addressInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<AddressInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(addressInfoRepository.queryAll(any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = metricsStoreService.searchStoreAddressesV2ForSalesApp(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

    @Test
    void searchStoreAddressesV2_WithKeyword_ReturnsMatchingAddressesForSalesApp() {
        AddressInfoEntity entity = AddressInfoEntityMock.addressInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("test");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<AddressInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(addressInfoRepository.findBySearchQuery(anyString(), any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = metricsStoreService.searchStoreAddressesV2ForSalesApp(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

}
