package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogProductsResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_PRODUCTS_SEARCH = "/master-catalog/v1/products/search";
    public static final String V1_ASSOCIATED_PRODUCTS_SEARCH = "/master-catalog/v1/products/association/search";
    public static final String V1_ASSOCIATED_PRODUCTS_SEARCH_BY_IMAGE = "/master-catalog/v1/products/association/search-by-image";

    public MasterCatalogProductsResourceApiUtils(Environment environment) {
        super(environment);
    }

    public CustomPage<MasterCatalogProductDto> searchAssociatedProducts(String upc) {

        Map<String, String> params = new java.util.HashMap<>(Map.of());
        params.put("page", "1");
        params.put("pageSize", "2");
        params.put("upc", upc);

        ParameterizedTypeReference<CustomPage<MasterCatalogProductDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(V1_ASSOCIATED_PRODUCTS_SEARCH, responseType, params).getBody();
    }

    public CustomPage<MasterCatalogProductDto> searchProducts(String upc, String name) {

        Map<String, String> params = new java.util.HashMap<>(Map.of());
        params.put("page", "1");
        params.put("pageSize", "2");
        params.put("upc", upc);
        params.put("name", name);

        ParameterizedTypeReference<CustomPage<MasterCatalogProductDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(V1_PRODUCTS_SEARCH, responseType, params).getBody();
    }
}
