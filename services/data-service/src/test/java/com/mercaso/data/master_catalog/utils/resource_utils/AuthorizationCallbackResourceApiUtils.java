package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class AuthorizationCallbackResourceApiUtils extends IntegrationTestRestUtil {

    private static final String V1_OAUTH_CALLBACK_SQUARE = "/v1/oauth/callback/square";

    public AuthorizationCallbackResourceApiUtils(Environment environment) {
        super(environment);
    }

    public String executeSquareCallback(String code, String state) {
        return getForObject(V1_OAUTH_CALLBACK_SQUARE + "?code=" + code + "&state=" + state, String.class);
    }

}
