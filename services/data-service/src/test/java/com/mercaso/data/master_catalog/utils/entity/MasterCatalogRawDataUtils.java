package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogRawDataUtils {

    public static MasterCatalogRawData  buildMasterCatalogRawData(UUID storeId) {
        return MasterCatalogRawData.builder()
            .id(UUID.randomUUID())
            .storeId(storeId).upc(String.valueOf(Instant.now().getEpochSecond()))
            .name("Test Product")
            .description("Test Description")
            .primaryVendor("Test Vendor")
            .status("DRAFT")
            .upc("1111111111")
            .build();
    }

    public static MasterCatalogRawData  buildMasterCatalogRawData(String upc) {
        return MasterCatalogRawData.builder()
                .storeId(UUID.randomUUID())
                .upc(upc)
                .name("Test Product")
                .description("Test Description")
                .primaryVendor("Test Vendor")
                .status("DRAFT")
                .build();
    }
}
