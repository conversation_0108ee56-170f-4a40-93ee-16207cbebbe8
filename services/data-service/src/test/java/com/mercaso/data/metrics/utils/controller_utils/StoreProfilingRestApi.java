package com.mercaso.data.metrics.utils.controller_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class StoreProfilingRestApi extends IntegrationTestRestUtil {

    public StoreProfilingRestApi(Environment environment) {
        super(environment);
    }

    private static final String STORE_PROFILING_ADDRESS = "/metrics/store-profiling/v1/addresses";

    public CustomPage<SearchStoreAddressDto> searchStoreProfilingAddresses(String keyword, int pageNumber, int pageSize) {
        Map<String, Object> params = Map.of("keyword", keyword, "pageNumber", pageNumber, "pageSize", pageSize);

        ParameterizedTypeReference<CustomPage<SearchStoreAddressDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(STORE_PROFILING_ADDRESS, responseType, params).getBody();
    }


    private static final String STORE_PROFILING = "/metrics/store-profiling/v1/addresses";

    public StoreProfilingDto getStoreProfilingByStoreId(String storeId) {
        String requestUrl = STORE_PROFILING + "/" + storeId;
        return getEntity(requestUrl, StoreProfilingDto.class).getBody();
    }
}
