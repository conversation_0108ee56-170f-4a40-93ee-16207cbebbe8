package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import java.time.Instant;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogSquareOrderUtils {

    public static MasterCatalogSquareOrder buildMasterCatalogSquareOrder() {
        MasterCatalogSquareOrder masterCatalogSquareOrder = new MasterCatalogSquareOrder();
        masterCatalogSquareOrder.setState("COMPLETED");
        masterCatalogSquareOrder.setCreatedAt(Instant.now());
        masterCatalogSquareOrder.setUpdatedAt(Instant.now());
        return masterCatalogSquareOrder;

    }

}
