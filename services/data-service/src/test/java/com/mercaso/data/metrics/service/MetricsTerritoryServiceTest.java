package com.mercaso.data.metrics.service;

import static org.mockito.ArgumentMatchers.any;

import com.mercaso.data.metrics.dto.MetricsTerritoryAlertAggregatedDto;
import com.mercaso.data.metrics.dto.MetricsTerritoryAlertDto;
import com.mercaso.data.metrics.dto.MetricsTerritorySalespersonDto;
import com.mercaso.data.metrics.entity.MetricsTerritorySalespersonEntity;
import com.mercaso.data.metrics.entity.AddressInfoEntity;
import com.mercaso.data.metrics.entity.MetricsTerritoryDecliningOrderFrequencyEntity;
import com.mercaso.data.metrics.entity.MetricsTerritoryDecliningAOVEntity;
import com.mercaso.data.metrics.entity.MetricsTerritoryStoreSaveAlertEntity;
import com.mercaso.data.metrics.entity.MetricsTerritoryTargetListEntity;
import com.mercaso.data.metrics.enums.AlertType;
import com.mercaso.data.metrics.mapper.MetricsTerritorySalespersonDtoMapper;
import com.mercaso.data.metrics.repository.AddressInfoRepository;
import com.mercaso.data.metrics.repository.MetricsTerritoryDecliningOrderFrequencyRepository;
import com.mercaso.data.metrics.repository.MetricsTerritorySalespersonRepository;
import com.mercaso.data.metrics.repository.MetricsTerritoryDecliningAOVEntityRepository;
import com.mercaso.data.metrics.repository.MetricsTerritoryStoreSaveAlertRepository;
import com.mercaso.data.metrics.repository.MetricsTerritoryTargetListRepository;
import com.mercaso.data.metrics.service.impl.MetricsTerritoryServiceImpl;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

class MetricsTerritoryServiceTest {

    private final MetricsTerritorySalespersonRepository metricsTerritorySalespersonRepository = Mockito.mock(
        MetricsTerritorySalespersonRepository.class);

    private final MetricsTerritorySalespersonDtoMapper metricsTerritorySalespersonDtoMapper = Mockito.mock(
        MetricsTerritorySalespersonDtoMapper.class);

    private final AddressInfoRepository addressInfoRepository = Mockito.mock(AddressInfoRepository.class);

    private final MetricsTerritoryDecliningOrderFrequencyRepository metricsTerritoryDecliningOrderFrequencyRepository = Mockito.mock(
        MetricsTerritoryDecliningOrderFrequencyRepository.class);

    private final MetricsTerritoryDecliningAOVEntityRepository metricsTerritoryDecliningAOVEntityRepository = 
        Mockito.mock(MetricsTerritoryDecliningAOVEntityRepository.class);

    private final MetricsTerritoryStoreSaveAlertRepository metricsTerritoryStoreSaveAlertRepository = Mockito.mock(
        MetricsTerritoryStoreSaveAlertRepository.class);

    private final MetricsTerritoryTargetListRepository metricsTerritoryTargetListRepository = Mockito.mock(
        MetricsTerritoryTargetListRepository.class);

    private final MetricsTerritoryService metricsTerritoryService = new MetricsTerritoryServiceImpl(
        metricsTerritorySalespersonRepository,
        metricsTerritorySalespersonDtoMapper, addressInfoRepository, metricsTerritoryDecliningOrderFrequencyRepository, metricsTerritoryDecliningAOVEntityRepository, metricsTerritoryStoreSaveAlertRepository, metricsTerritoryTargetListRepository);

    @Test
    void testGetSalespersons() {
        // Setup test data
        List<MetricsTerritorySalespersonEntity> metricsTerritorySalespersonEntities = new ArrayList<>();

        MetricsTerritorySalespersonEntity metricsTerritorySalespersonEntity = new MetricsTerritorySalespersonEntity();
        metricsTerritorySalespersonEntity.setSalespersonName("John Doe");
        metricsTerritorySalespersonEntity.setZipCodeList(Arrays.asList("12345", "67890"));
        metricsTerritorySalespersonEntities.add(metricsTerritorySalespersonEntity);

        PageImpl<MetricsTerritorySalespersonEntity> page = new PageImpl<>(metricsTerritorySalespersonEntities);
        Mockito.when(metricsTerritorySalespersonRepository.findAll(Pageable.unpaged())).thenReturn(page);

        // Mock DTO mapper
        Mockito.when(metricsTerritorySalespersonDtoMapper.toDto(any()))
            .thenReturn(new MetricsTerritorySalespersonDto("John Doe", Arrays.asList("12345", "67890")));

        // Mock address info repository
        List<AddressInfoEntity> addressInfoEntities = new ArrayList<>();
        AddressInfoEntity address1 = new AddressInfoEntity();
        address1.setZipCode("12345");
        address1.setAddressId("addr-1");
        
        AddressInfoEntity address2 = new AddressInfoEntity();
        address2.setZipCode("67890");
        address2.setAddressId("addr-2");
        
        addressInfoEntities.add(address1);
        addressInfoEntities.add(address2);
        
        Mockito.when(addressInfoRepository.findAll()).thenReturn(addressInfoEntities);

        // Mock alert repositories
        Mockito.when(metricsTerritoryDecliningAOVEntityRepository.findDistinctAddressIdBy())
            .thenReturn(Arrays.asList("addr-1"));
        
        Mockito.when(metricsTerritoryDecliningOrderFrequencyRepository.findDistinctAddressIdBy())
            .thenReturn(Arrays.asList("addr-2"));
        
        Mockito.when(metricsTerritoryStoreSaveAlertRepository.findDistinctAddressIdBy())
            .thenReturn(Arrays.asList("addr-1", "addr-2"));

        // Execute method under test
        Page<MetricsTerritorySalespersonDto> metricsTerritorySalespersonDtos = metricsTerritoryService.getSalespersons();

        // Verify results
        Assertions.assertEquals(1, metricsTerritorySalespersonDtos.getTotalElements());
        Assertions.assertEquals("John Doe", metricsTerritorySalespersonDtos.getContent().getFirst().getSalespersonName());
        Assertions.assertEquals(Arrays.asList("12345", "67890"),
            metricsTerritorySalespersonDtos.getContent().getFirst().getZipCodeList());
        
        // Verify all repository methods were called
        Mockito.verify(addressInfoRepository).findAll();
        Mockito.verify(metricsTerritoryDecliningAOVEntityRepository).findDistinctAddressIdBy();
        Mockito.verify(metricsTerritoryDecliningOrderFrequencyRepository).findDistinctAddressIdBy();
        Mockito.verify(metricsTerritoryStoreSaveAlertRepository).findDistinctAddressIdBy();
    }


    @Test
    void testGetAlert_EmptyZipCodes() {
        // Given
        List<String> emptyZipCodes = new ArrayList<>();
        
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(AlertType.DECLINING_AOV, emptyZipCodes);
        
        // Then
        Assertions.assertTrue(result.isEmpty());
        Assertions.assertEquals(0, result.getTotalElements());
    }
    
    @Test
    void testGetAlert_NoAddressInfoFound() {
        // Given
        List<String> zipCodes = Arrays.asList("12345", "67890");
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(new ArrayList<>());
            
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(AlertType.DECLINING_AOV, zipCodes);
        
        // Then
        Assertions.assertTrue(result.isEmpty());
        Assertions.assertEquals(0, result.getTotalElements());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
    }
    
    @Test
    void testGetAlert_InvalidAlertType() {
        // Given
        List<String> zipCodes = List.of("12345");
        
        // When & Then
        Assertions.assertThrows(IllegalArgumentException.class, 
            () -> metricsTerritoryService.getAlert(null, zipCodes),
            "Unsupported alert type: null");

        // No need to verify repository call as it won't be reached
        Mockito.verifyNoInteractions(addressInfoRepository);
    }

    @Test
    void testGetAlert_DecliningOrderFrequency() {
        // Given
        List<String> zipCodes = List.of("12345");
        String addressId = UUID.randomUUID().toString();
        
        // Mock AddressInfo lookup
        AddressInfoEntity addressInfo = new AddressInfoEntity();
        addressInfo.setId(1L);
        addressInfo.setAddressId(addressId);
        addressInfo.setZipCode("12345");
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(List.of(addressInfo));
        
        // Mock declining order frequency data
        MetricsTerritoryDecliningOrderFrequencyEntity decliningFrequency = new MetricsTerritoryDecliningOrderFrequencyEntity();
        decliningFrequency.setAddressId(addressId);
        decliningFrequency.setAlertDate(LocalDate.now());
        decliningFrequency.setDecreasePct(0.155);
        
        Page<MetricsTerritoryDecliningOrderFrequencyEntity> decliningFrequencyPage = 
            new PageImpl<>(List.of(decliningFrequency));
            
        Mockito.when(metricsTerritoryDecliningOrderFrequencyRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(decliningFrequencyPage);
            
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(
            AlertType.DECLINING_ORDER_FREQUENCY, zipCodes);
        
        // Then
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(1, result.getTotalElements());
        
        MetricsTerritoryAlertDto alertDto = result.getContent().getFirst();
        Assertions.assertEquals(addressId, alertDto.getAddressId());
        Assertions.assertEquals(16, alertDto.getAlertValue());
        Assertions.assertNotNull(alertDto.getAlertDate());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
        Mockito.verify(metricsTerritoryDecliningOrderFrequencyRepository)
            .findByAddressIdIn(any(), any(Pageable.class));
    }

    @Test
    void testGetAlert_DecliningAOV() {
        // Given
        List<String> zipCodes = List.of("12345");
        
        // Mock AddressInfo lookup
        AddressInfoEntity addressInfo = new AddressInfoEntity();
        addressInfo.setId(1L);
        addressInfo.setAddressId("addr-123");
        addressInfo.setZipCode("12345");
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(List.of(addressInfo));
        
        // Mock declining AOV data
        MetricsTerritoryDecliningAOVEntity decliningAOV = new MetricsTerritoryDecliningAOVEntity();
        decliningAOV.setAddressId("addr-123");
        decliningAOV.setAlertDate(LocalDate.now());
        decliningAOV.setDepartment("All Department");
        decliningAOV.setDecreasePct(BigDecimal.valueOf(0.15));
        
        Page<MetricsTerritoryDecliningAOVEntity> decliningAOVPage = 
            new PageImpl<>(List.of(decliningAOV));
            
        Mockito.when(metricsTerritoryDecliningAOVEntityRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(decliningAOVPage);
            
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(
            AlertType.DECLINING_AOV, zipCodes);
        
        // Then
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(1, result.getTotalElements());
        
        MetricsTerritoryAlertDto alertDto = result.getContent().getFirst();
        Assertions.assertEquals("addr-123", alertDto.getAddressId());
        Assertions.assertEquals("in All Department", alertDto.getAlertDetail());
        Assertions.assertNotNull(alertDto.getAlertDate());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
        Mockito.verify(metricsTerritoryDecliningAOVEntityRepository)
            .findByAddressIdIn(any(), any(Pageable.class));
    }

    @Test
    void testGetAlert_StoreInTargetList() {
        // Given
        List<String> zipCodes = List.of("12345");
        
        // Mock AddressInfo lookup
        AddressInfoEntity addressInfo = new AddressInfoEntity();
        addressInfo.setId(1L);
        addressInfo.setAddressId("addr-123");
        addressInfo.setZipCode("12345");
        addressInfo.setAddressName("Test Store");
        
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(List.of(addressInfo));
        
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(
            AlertType.STORE_IN_TARGET_LIST, zipCodes);
        
        // Then
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(1, result.getTotalElements());
        
        MetricsTerritoryAlertDto alertDto = result.getContent().getFirst();
        Assertions.assertEquals("addr-123", alertDto.getAddressId());
        Assertions.assertNull(alertDto.getAlertDate());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
    }

    @Test
    void testGetAlert_StoreInSaveCategory() {
        // Given
        List<String> zipCodes = List.of("12345");
        String addressId = "addr-123";
        
        // Mock AddressInfo lookup
        AddressInfoEntity addressInfo = new AddressInfoEntity();
        addressInfo.setId(1L);
        addressInfo.setAddressId(addressId);
        addressInfo.setZipCode("12345");
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(List.of(addressInfo));
        
        // Mock store save category data
        MetricsTerritoryStoreSaveAlertEntity storeSaveAlert = new MetricsTerritoryStoreSaveAlertEntity();
        storeSaveAlert.setAddressId(addressId);
        storeSaveAlert.setAlertDate(LocalDate.now());
        storeSaveAlert.setDaysUntilChurn(30);
        
        Page<MetricsTerritoryStoreSaveAlertEntity> storeSaveAlertPage = 
            new PageImpl<>(List.of(storeSaveAlert));
            
        Mockito.when(metricsTerritoryStoreSaveAlertRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(storeSaveAlertPage);
            
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(
            AlertType.STORE_IN_SAVE_CATEGORY, zipCodes);
        
        // Then
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(1, result.getTotalElements());
        
        MetricsTerritoryAlertDto alertDto = result.getContent().getFirst();
        Assertions.assertEquals(addressId, alertDto.getAddressId());
        Assertions.assertEquals(30, alertDto.getAlertValue());
        Assertions.assertNotNull(alertDto.getAlertDate());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
        Mockito.verify(metricsTerritoryStoreSaveAlertRepository)
            .findByAddressIdIn(any(), any(Pageable.class));
    }

    @Test
    void testGetAlert_StoreNearingChurnCategory() {
        // Given
        List<String> zipCodes = List.of("12345");
        String addressId = "addr-123";
        
        // Mock AddressInfo lookup
        AddressInfoEntity addressInfo = new AddressInfoEntity();
        addressInfo.setId(1L);
        addressInfo.setAddressId(addressId);
        addressInfo.setZipCode("12345");
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(List.of(addressInfo));
        
        // Create two entities - one with NEARING_CHURN status and one with different status
        MetricsTerritoryStoreSaveAlertEntity nearingChurnAlert = new MetricsTerritoryStoreSaveAlertEntity();
        nearingChurnAlert.setAddressId(addressId);
        nearingChurnAlert.setAlertDate(LocalDate.now());
        nearingChurnAlert.setDaysUntilChurn(15);
        nearingChurnAlert.setStoreStatus("NEARING_CHURN");
        
        MetricsTerritoryStoreSaveAlertEntity otherStatusAlert = new MetricsTerritoryStoreSaveAlertEntity();
        otherStatusAlert.setAddressId(addressId);
        otherStatusAlert.setAlertDate(LocalDate.now());
        otherStatusAlert.setDaysUntilChurn(30);
        otherStatusAlert.setStoreStatus("ACTIVE");
        
        // Return both entities from the repository
        Page<MetricsTerritoryStoreSaveAlertEntity> storeSaveAlertPage = 
            new PageImpl<>(List.of(nearingChurnAlert, otherStatusAlert));
            
        Mockito.when(metricsTerritoryStoreSaveAlertRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(storeSaveAlertPage);
            
        // When
        Page<MetricsTerritoryAlertDto> result = metricsTerritoryService.getAlert(
            AlertType.STORE_NEARING_CHURN_CATEGORY, zipCodes);
        
        // Then
        Assertions.assertFalse(result.isEmpty());
        // Should only have one result after filtering
        Assertions.assertEquals(1, result.getTotalElements());
        
        MetricsTerritoryAlertDto alertDto = result.getContent().getFirst();
        Assertions.assertEquals(addressId, alertDto.getAddressId());
        // Should have the days until churn from the NEARING_CHURN entity
        Assertions.assertEquals(15, alertDto.getAlertValue());
        Assertions.assertNotNull(alertDto.getAlertDate());
        
        // Verify
        Mockito.verify(addressInfoRepository).findByZipCodeIn(zipCodes);
        Mockito.verify(metricsTerritoryStoreSaveAlertRepository)
            .findByAddressIdIn(any(), any(Pageable.class));
    }

    @Test
    void testGetAggregatedAlert_EmptyZipCodes() {
        // Given
        List<String> emptyZipCodes = new ArrayList<>();
        
        // When
        MetricsTerritoryAlertAggregatedDto result = metricsTerritoryService.getAggregatedAlert(emptyZipCodes);
        
        // Then
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.getChurnAlerts().isEmpty());
        Assertions.assertTrue(result.getSaveAlerts().isEmpty());
        Assertions.assertTrue(result.getFrequencyAlerts().isEmpty());
        Assertions.assertTrue(result.getAovAlerts().isEmpty());
        Assertions.assertTrue(result.getTargetAlerts().isEmpty());
        
        // Verify no repository interactions
        Mockito.verifyNoInteractions(addressInfoRepository);
    }
    
    @Test
    void testGetAggregatedAlert_Success() {
        // Given
        List<String> zipCodes = List.of("12345");
        String addressId1 = "addr-123";
        String addressId2 = "addr-456";
        String addressId3 = "addr-789";
        
        // Mock AddressInfo lookup for all alert types
        AddressInfoEntity addressInfo1 = new AddressInfoEntity();
        addressInfo1.setAddressId(addressId1);
        addressInfo1.setZipCode("12345");
        addressInfo1.setAddressName("Store 1");
        
        AddressInfoEntity addressInfo2 = new AddressInfoEntity();
        addressInfo2.setAddressId(addressId2);
        addressInfo2.setZipCode("12345");
        addressInfo2.setAddressName("Store 2");
        
        AddressInfoEntity addressInfo3 = new AddressInfoEntity();
        addressInfo3.setAddressId(addressId3);
        addressInfo3.setZipCode("12345");
        addressInfo3.setAddressName("Store 3");
        
        List<AddressInfoEntity> addressInfos = List.of(addressInfo1, addressInfo2, addressInfo3);
        
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(addressInfos);
        
        // Mock STORE_NEARING_CHURN_CATEGORY alerts
        MetricsTerritoryStoreSaveAlertEntity nearingChurnAlert = new MetricsTerritoryStoreSaveAlertEntity();
        nearingChurnAlert.setAddressId(addressId1);
        nearingChurnAlert.setAlertDate(LocalDate.now());
        nearingChurnAlert.setDaysUntilChurn(15);
        nearingChurnAlert.setStoreStatus("NEARING_CHURN");
        
        // Mock STORE_IN_SAVE_CATEGORY alerts
        MetricsTerritoryStoreSaveAlertEntity saveAlert = new MetricsTerritoryStoreSaveAlertEntity();
        saveAlert.setAddressId(addressId2);
        saveAlert.setAlertDate(LocalDate.now());
        saveAlert.setDaysUntilChurn(30);
        
        Page<MetricsTerritoryStoreSaveAlertEntity> savePage = 
            new PageImpl<>(List.of(saveAlert,nearingChurnAlert));
        
        // Mock DECLINING_ORDER_FREQUENCY alerts
        MetricsTerritoryDecliningOrderFrequencyEntity frequencyAlert = new MetricsTerritoryDecliningOrderFrequencyEntity();
        frequencyAlert.setAddressId(addressId3);
        frequencyAlert.setAlertDate(LocalDate.now());
        frequencyAlert.setDecreasePct(0.25);
        
        Page<MetricsTerritoryDecliningOrderFrequencyEntity> frequencyPage = 
            new PageImpl<>(List.of(frequencyAlert));
        
        // Mock DECLINING_AOV alerts
        MetricsTerritoryDecliningAOVEntity aovAlert = new MetricsTerritoryDecliningAOVEntity();
        aovAlert.setAddressId(addressId1); // Same as nearingChurn to test priority
        aovAlert.setAlertDate(LocalDate.now());
        aovAlert.setDepartment("All Department");
        aovAlert.setDecreasePct(BigDecimal.valueOf(0.15));
        
        Page<MetricsTerritoryDecliningAOVEntity> aovPage = 
            new PageImpl<>(List.of(aovAlert));
        
        // Setup repository mocks for different alert types
        // For STORE_NEARING_CHURN_CATEGORY
        Mockito.when(metricsTerritoryStoreSaveAlertRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(savePage);
        
        // For DECLINING_ORDER_FREQUENCY
        Mockito.when(metricsTerritoryDecliningOrderFrequencyRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(frequencyPage);
        
        // For DECLINING_AOV
        Mockito.when(metricsTerritoryDecliningAOVEntityRepository.findByAddressIdIn(
            any(), any(Pageable.class)))
            .thenReturn(aovPage);
        
        // When
        MetricsTerritoryAlertAggregatedDto result = metricsTerritoryService.getAggregatedAlert(zipCodes);
        
        // Then
        Assertions.assertNotNull(result);
        
        // Check churn alerts (highest priority)
        Assertions.assertEquals(1, result.getChurnAlerts().size());
        Assertions.assertEquals(addressId1, result.getChurnAlerts().getFirst().getAddressId());
        
        // Check save alerts
        Assertions.assertEquals(1, result.getSaveAlerts().size());
        Assertions.assertEquals(addressId2, result.getSaveAlerts().getFirst().getAddressId());
        
        // Check frequency alerts
        Assertions.assertEquals(1, result.getFrequencyAlerts().size());
        Assertions.assertEquals(addressId3, result.getFrequencyAlerts().getFirst().getAddressId());
        
        // Check AOV alerts - should be empty because addressId1 was already processed with higher priority
        Assertions.assertEquals(0, result.getAovAlerts().size());
    }

    @Test
    void testGetAggregatedAlert_WithTargetList() {
        // Given
        List<String> zipCodes = List.of("12345");
        
        // Mock empty address info lookup to ensure we're only testing target list
        Mockito.when(addressInfoRepository.findByZipCodeIn(zipCodes))
            .thenReturn(new ArrayList<>());
        
        // Mock target list repository
        MetricsTerritoryTargetListEntity targetEntity1 = new MetricsTerritoryTargetListEntity();
        targetEntity1.setBusinessName("Target Store 1");
        targetEntity1.setAddressName("Target Address 1");
        targetEntity1.setZipCode("12345");
        targetEntity1.setLatitude(40.0);
        targetEntity1.setLongitude(-74.0);
        targetEntity1.setLabel("High Value");
        
        MetricsTerritoryTargetListEntity targetEntity2 = new MetricsTerritoryTargetListEntity();
        targetEntity2.setBusinessName("Target Store 2");
        targetEntity2.setAddressName("Target Address 2");
        targetEntity2.setZipCode("12345");
        targetEntity2.setLatitude(41.0);
        targetEntity2.setLongitude(-75.0);
        targetEntity2.setLabel("New Customer");
        
        List<MetricsTerritoryTargetListEntity> targetEntities = List.of(targetEntity1, targetEntity2);
        
        Mockito.when(metricsTerritoryTargetListRepository.findByZipCodeIn(zipCodes))
            .thenReturn(targetEntities);
        
        // When
        MetricsTerritoryAlertAggregatedDto result = metricsTerritoryService.getAggregatedAlert(zipCodes);
        
        // Then
        Assertions.assertNotNull(result);
        
        // Check target alerts
        Assertions.assertEquals(2, result.getTargetAlerts().size());
        
        // Verify first target alert
        MetricsTerritoryAlertDto firstAlert = result.getTargetAlerts().get(0);
        Assertions.assertEquals("Target Store 1", firstAlert.getStoreName());
        Assertions.assertEquals("Target Address 1", firstAlert.getAddressName());
        Assertions.assertEquals("12345", firstAlert.getZipCode());
        Assertions.assertEquals(40.0, firstAlert.getLatitude());
        Assertions.assertEquals(-74.0, firstAlert.getLongitude());
        Assertions.assertEquals("High Value", firstAlert.getStoreLabel());
        
        // Verify second target alert
        MetricsTerritoryAlertDto secondAlert = result.getTargetAlerts().get(1);
        Assertions.assertEquals("Target Store 2", secondAlert.getStoreName());
        Assertions.assertEquals("Target Address 2", secondAlert.getAddressName());
        Assertions.assertEquals("12345", secondAlert.getZipCode());
        Assertions.assertEquals(41.0, secondAlert.getLatitude());
        Assertions.assertEquals(-75.0, secondAlert.getLongitude());
        Assertions.assertEquals("New Customer", secondAlert.getStoreLabel());
        
        // Check other alert lists are empty
        Assertions.assertTrue(result.getChurnAlerts().isEmpty());
        Assertions.assertTrue(result.getSaveAlerts().isEmpty());
        Assertions.assertTrue(result.getFrequencyAlerts().isEmpty());
        Assertions.assertTrue(result.getAovAlerts().isEmpty());

    }
}
