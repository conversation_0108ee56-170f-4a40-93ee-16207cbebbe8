package com.mercaso.data.master_catalog.service;

import static com.mercaso.data.master_catalog.utils.dto.MasterCatalogProductDtoUtils.buildMasterCatalogProductDto;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogImageUtils.buildMasterCatalogImages;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogProductAssociationUtils.buildMasterCatalogProductAssociation;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogProductUtils.buildMasterCatalogProduct;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.adaptor.impl.ExternalApiAdapterImpl;
import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.mapper.MasterCatalogProductMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogProductServiceImpl;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

class MasterCatalogProductServiceTest {

    private final MasterCatalogProductRepository masterCatalogProductRepository = mock(MasterCatalogProductRepository.class);
    private final MasterCatalogImageRepository masterCatalogImageRepository = mock(MasterCatalogImageRepository.class);
    private final MasterCatalogProductMapper masterCatalogProductMapper = mock(MasterCatalogProductMapper.class);
    private final ExternalApiAdapter externalApiAdapter = mock(ExternalApiAdapterImpl.class);
    private final MasterCatalogProductAssociationRepository masterCatalogProductAssociationRepository = mock(
        MasterCatalogProductAssociationRepository.class);
    private final S3OperationAdapter s3OperationAdapter = mock(S3OperationAdapter.class);

    private final MasterCatalogProductService masterCatalogProductService = new MasterCatalogProductServiceImpl(
        masterCatalogProductRepository,
        masterCatalogProductMapper,
        masterCatalogImageRepository,
        externalApiAdapter,
        masterCatalogProductAssociationRepository,
        s3OperationAdapter
    );

    private MasterCatalogProduct product;
    private MasterCatalogProductDto productDto;
    private List<MasterCatalogImage> images;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        UUID id = UUID.randomUUID();
        Instant now = Instant.now();

        product = buildMasterCatalogProduct(id);
        productDto = buildMasterCatalogProductDto(id, now);
        images = buildMasterCatalogImages(product.getMasterCatalogRawDataId());
        pageable = PageRequest.of(0, 10);
    }

    @Test
    void searchAssociatedProducts_withUpc_shouldReturnMatchingProducts() {

        MasterCatalogProductAssociation masterCatalogProductAssociation = buildMasterCatalogProductAssociation();
        when(masterCatalogProductAssociationRepository.findAllByUpcIn(anyList())).thenReturn(Collections.singletonList(
            masterCatalogProductAssociation));

        List<MasterCatalogProductAssociation> masterCatalogProductAssociations = Collections.singletonList(
            masterCatalogProductAssociation);
        when(masterCatalogProductAssociationRepository.findAll((Specification<MasterCatalogProductAssociation>) any())).thenReturn(
            masterCatalogProductAssociations);

        Page<MasterCatalogProduct> productPage = new PageImpl<>(Collections.singletonList(product), pageable, 1);
        when(masterCatalogProductRepository.findAll((Specification<MasterCatalogProduct>) any(), any(Pageable.class))).thenReturn(
            productPage);

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
            .thenReturn(images);
        when(masterCatalogProductMapper.toDto(any(MasterCatalogProduct.class)))
            .thenReturn(productDto);

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts("123456", null, pageable);

        assertThat(result.getTotalElements()).isEqualTo(1);
        assertThat(result.getContent().getFirst()).isEqualTo(productDto);
        assertThat(result.getContent().getFirst().getImages()).hasSize(2);
    }

    @Test
    void searchAssociatedProducts_withDescription_shouldReturnMatchingProducts() {
        when(externalApiAdapter.getUpcsFromExternalApi(anyString()))
            .thenReturn(Collections.singletonList("123456"));

        MasterCatalogProductAssociation masterCatalogProductAssociation = buildMasterCatalogProductAssociation();
        when(masterCatalogProductAssociationRepository.findAllByUpcIn(anyList())).thenReturn(Collections.singletonList(
            masterCatalogProductAssociation));

        List<MasterCatalogProductAssociation> masterCatalogProductAssociations = Collections.singletonList(
            masterCatalogProductAssociation);
        when(masterCatalogProductAssociationRepository.findAll((Specification<MasterCatalogProductAssociation>) any())).thenReturn(
            masterCatalogProductAssociations);

        Page<MasterCatalogProduct> productPage = new PageImpl<>(Collections.singletonList(product), pageable, 1);
        when(masterCatalogProductRepository.findAll((Specification<MasterCatalogProduct>) any(), any(Pageable.class))).thenReturn(
            productPage);
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
            .thenReturn(images);
        when(masterCatalogProductMapper.toDto(any(MasterCatalogProduct.class)))
            .thenReturn(productDto);

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts(null,
            "Test Description",
            pageable);

        assertThat(result.getTotalElements()).isEqualTo(1);
        assertThat(result.getContent().getFirst()).isEqualTo(productDto);
        assertThat(result.getContent().getFirst().getImages()).hasSize(2);
    }

    @Test
    void searchAssociatedProducts_withNoResults_shouldReturnEmptyPage() {

        MasterCatalogProductAssociation masterCatalogProductAssociation = buildMasterCatalogProductAssociation();
        when(masterCatalogProductAssociationRepository.findAllByUpcIn(anyList())).thenReturn(Collections.singletonList(
            masterCatalogProductAssociation));

        when(masterCatalogProductRepository.findAllByUpcIn(anyList())).thenReturn(Collections.emptyList());

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
            .thenReturn(images);
        when(masterCatalogProductMapper.toDto(any(MasterCatalogProduct.class)))
            .thenReturn(productDto);

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts("999999", null, pageable);

        assertThat(result.getTotalElements()).isZero();
        assertThat(result.getContent()).isEmpty();
    }

    @Test
    void searchAssociatedProducts_withNoUpcAndNoDescription_shouldReturnEmptyPage() {
        when(masterCatalogProductRepository.findAll(any(Pageable.class)))
            .thenReturn(Page.empty());

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts(null, null, pageable);

        assertThat(result.getTotalElements()).isZero();
    }

    @Test
    void searchAssociatedProducts_withNoUpcAndInvalidDescription_shouldReturnEmptyPage() {
        when(externalApiAdapter.getUpcsFromExternalApi(anyString()))
            .thenReturn(Collections.emptyList());

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts(null,
            "Invalid Description",
            pageable);

        assertThat(result.getTotalElements()).isZero();
        assertThat(result.getContent()).isEmpty();
    }

    @Test
    void searchAssociatedProducts_withInvalidUpc_shouldReturnEmptyPage() {
        when(masterCatalogProductAssociationRepository.findAllByUpcIn(any())).thenReturn(null);

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchAssociatedProducts("InvalidUpc", null, pageable);

        assertThat(result.getTotalElements()).isZero();
        assertThat(result.getContent()).isEmpty();
    }

    @Test
    void searchProducts_whenUpcProvided_thenFilterByUpc(){

        String upc = RandomStringUtils.randomNumeric(12);

        MasterCatalogProduct product = MasterCatalogProduct.builder()
            .upc(upc)
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProductDto productDto = MasterCatalogProductDto.builder()
            .upc(upc)
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList())).thenReturn(images);
        when(masterCatalogProductMapper.toDto(product)).thenReturn(productDto);
        when(masterCatalogProductRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(new PageImpl<>(Collections.singletonList(product)));

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchProducts(upc, null, PageRequest.of(1, 10));

        assert result.getContent().size() == 1;
        assert result.getContent().get(0).getUpc().equals(upc);
    }

    @Test
    void searchProducts_whenNameProvided_thenFilterByName(){

        String upc = RandomStringUtils.randomNumeric(12);
        String name = RandomStringUtils.randomAlphabetic(5);

        MasterCatalogProduct product = MasterCatalogProduct.builder()
            .upc(upc)
            .name(name)
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProductDto productDto = MasterCatalogProductDto.builder()
            .upc(upc)
            .name(name)
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList())).thenReturn(images);
        when(masterCatalogProductMapper.toDto(product)).thenReturn(productDto);
        when(masterCatalogProductRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(new PageImpl<>(Collections.singletonList(product)));

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchProducts(null,name, PageRequest.of(1, 10));

        assert result.getContent().size() == 1;
        assert result.getContent().get(0).getName().equals(name);
    }

    @Test
    void searchProducts_whenNotAnyProvided_thenReturnAll(){

        MasterCatalogProduct product1 = MasterCatalogProduct.builder()
            .upc(UUID.randomUUID().toString())
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProduct product2 = MasterCatalogProduct.builder()
            .upc(UUID.randomUUID().toString())
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProductDto productDto1 = MasterCatalogProductDto.builder()
            .upc(UUID.randomUUID().toString())
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProductDto productDto2 = MasterCatalogProductDto.builder()
            .upc(UUID.randomUUID().toString())
            .name("Test Name")
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList())).thenReturn(images);

        when(masterCatalogProductMapper.toDto(product1)).thenReturn(productDto1);
        when(masterCatalogProductMapper.toDto(product2)).thenReturn(productDto2);

        when(masterCatalogProductRepository.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(new PageImpl<>(List.of(product1, product2)));

        Page<MasterCatalogProductDto> result = masterCatalogProductService.searchProducts(null,null, PageRequest.of(1, 10));

        assert result.getContent().size() == 2;
    }

}
