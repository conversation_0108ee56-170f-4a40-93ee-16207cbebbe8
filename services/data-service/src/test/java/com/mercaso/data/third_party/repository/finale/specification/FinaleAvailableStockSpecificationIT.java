package com.mercaso.data.third_party.repository.finale.specification;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockFilter;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.enums.finale.FinaleLocationTypeEnums;
import com.mercaso.data.third_party.mock.finale.FinaleAvailableStockEntityMock;
import com.mercaso.data.third_party.repository.finale.FinaleAvailableStockRepository;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

class FinaleAvailableStockSpecificationIT extends AbstractIT {

    @Autowired
    private FinaleAvailableStockRepository finaleAvailableStockRepository;

    @Test
    void testSkusFilter() {

        Long sku = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);

        FinaleAvailableStockEntity finaleAvailableStockEntity = FinaleAvailableStockEntityMock.customerEntityMock();
        finaleAvailableStockEntity.setSku(sku.toString());
        finaleAvailableStockRepository.save(finaleAvailableStockEntity);

        FinaleAvailableStockFilter finaleAvailableStockFilter = new FinaleAvailableStockFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        finaleAvailableStockFilter.setPageable(customPageable);
        finaleAvailableStockFilter.setSkus(List.of(sku.toString()));
        Specification<FinaleAvailableStockEntity> finaleAvailableStockEntitySpecification = FinaleAvailableStockSpecifications.byStockSubLocationsAndSkus(
            finaleAvailableStockFilter.getSkus(), finaleAvailableStockFilter.getLocationType());

        Page<FinaleAvailableStockEntity> page = finaleAvailableStockRepository.findAll(finaleAvailableStockEntitySpecification,
            finaleAvailableStockFilter.getPageable());

        List<FinaleAvailableStockEntity> resultContent = page.getContent();
        FinaleAvailableStockEntity first = resultContent.getFirst();

        assertEquals(sku.toString(), first.getSku());
        assertEquals(1, resultContent.size());
        assertEquals(1, page.getTotalElements());
        assertEquals(finaleAvailableStockEntity.getName(), first.getName());
    }

    @Test
    void testLocationTypeFilter() {

        String stockSublocations = "Main";

        FinaleAvailableStockEntity finaleAvailableStockEntity = FinaleAvailableStockEntityMock.customerEntityMock();
        finaleAvailableStockEntity.setStockSublocations(stockSublocations);
        finaleAvailableStockRepository.save(finaleAvailableStockEntity);

        FinaleAvailableStockFilter finaleAvailableStockFilter = new FinaleAvailableStockFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        finaleAvailableStockFilter.setPageable(customPageable);
        finaleAvailableStockFilter.setLocationType(FinaleLocationTypeEnums.SHOPIFY);
        Specification<FinaleAvailableStockEntity> finaleAvailableStockEntitySpecification = FinaleAvailableStockSpecifications.byStockSubLocationsAndSkus(
            finaleAvailableStockFilter.getSkus(), finaleAvailableStockFilter.getLocationType());

        Page<FinaleAvailableStockEntity> page = finaleAvailableStockRepository.findAll(finaleAvailableStockEntitySpecification,
            finaleAvailableStockFilter.getPageable());

        List<FinaleAvailableStockEntity> resultContent = page.getContent();
        FinaleAvailableStockEntity first = resultContent.getFirst();

        assertEquals(1, resultContent.size());
        assertEquals(1, page.getTotalElements());
        assertEquals(finaleAvailableStockEntity.getName(), first.getName());
        assertEquals(stockSublocations, first.getStockSublocations());
    }

    @Test
    void testLocationTypeAndSkusFilter() {

        Long sku = ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE);
        String stockSublocations =  ThreadLocalRandom.current().nextLong(0, Long.MAX_VALUE) + ", Main";

        FinaleAvailableStockEntity finaleAvailableStockEntity = FinaleAvailableStockEntityMock.customerEntityMock();
        finaleAvailableStockEntity.setStockSublocations(stockSublocations);
        finaleAvailableStockEntity.setSku(sku.toString());
        finaleAvailableStockRepository.save(finaleAvailableStockEntity);

        FinaleAvailableStockFilter finaleAvailableStockFilter = new FinaleAvailableStockFilter();
        CustomPageable customPageable = new CustomPageable(0, 10);
        finaleAvailableStockFilter.setPageable(customPageable);
        finaleAvailableStockFilter.setLocationType(FinaleLocationTypeEnums.MFC);
        finaleAvailableStockFilter.setSkus(List.of(sku.toString()));
        Specification<FinaleAvailableStockEntity> finaleAvailableStockEntitySpecification = FinaleAvailableStockSpecifications.byStockSubLocationsAndSkus(
            finaleAvailableStockFilter.getSkus(), finaleAvailableStockFilter.getLocationType());

        Page<FinaleAvailableStockEntity> page = finaleAvailableStockRepository.findAll(finaleAvailableStockEntitySpecification,
            finaleAvailableStockFilter.getPageable());

        List<FinaleAvailableStockEntity> resultContent = page.getContent();
        FinaleAvailableStockEntity first = resultContent.getFirst();

        assertEquals(stockSublocations, first.getStockSublocations());
        assertEquals(sku.toString(), first.getSku());
        assertEquals(1, resultContent.size());
        assertEquals(1, page.getTotalElements());
        assertEquals(finaleAvailableStockEntity.getName(), first.getName());
    }
}
