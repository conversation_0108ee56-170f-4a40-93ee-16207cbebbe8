package com.mercaso.data.metrics.controller;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.adapter.MetricsS3OperationAdapter;
import com.mercaso.data.metrics.dto.DocumentResponseDto;
import com.mercaso.data.metrics.dto.UploadDocumentRequestDto;
import com.mercaso.data.metrics.utils.controller_utils.MetricsUploadRestApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


class MetricsUploadControllerIT extends AbstractIT {

    @Autowired
    private MetricsUploadRestApi metricsUploadRestApi;

    @MockBean
    private MetricsS3OperationAdapter metricsS3OperationAdapter;

    @Test
    void testUploadDocument() {
        DocumentResponseDto responseDto = new DocumentResponseDto();
        when(metricsS3OperationAdapter.upload(any(UploadDocumentRequestDto.class))).thenReturn(responseDto);

        metricsUploadRestApi.uploadDocument();
    }
}
