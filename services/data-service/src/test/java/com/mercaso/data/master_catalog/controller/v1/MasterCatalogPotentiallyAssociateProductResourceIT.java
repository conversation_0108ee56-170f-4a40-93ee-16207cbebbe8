package com.mercaso.data.master_catalog.controller.v1;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyAssociateProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyAssociateProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogPotentiallyAssociateProductResourceApiUtils;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.web.client.HttpClientErrorException;

class MasterCatalogPotentiallyAssociateProductResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogPotentiallyAssociateProductResourceApiUtils apiUtils;

    @Autowired
    private MasterCatalogPotentiallyAssociateProductRepository potentiallyAssociateProductRepository;

    @Autowired
    private MasterCatalogProductRepository masterCatalogProductRepository;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogImageRepository masterCatalogImageRepository;

    @Autowired
    private MasterCatalogBatchJobRepository batchJobRepository;

    @Autowired
    private MasterCatalogTaskRepository taskRepository;

    @Autowired
    private MasterCatalogProductAssociationRepository productAssociationRepository;

    @MockBean
    private S3OperationAdapter s3OperationAdapter;

    @Test
    void searchPotentiallyAssociateProducts() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        // Create raw data
        MasterCatalogRawData rawData1 = createRawData("Test Product 1", "123456789");
        MasterCatalogRawData rawData2 = createRawData("Test Product 2", "987654321");
        rawData1 = masterCatalogRawDataRepository.save(rawData1);
        rawData2 = masterCatalogRawDataRepository.save(rawData2);
        registerForCleanup(rawData1);
        registerForCleanup(rawData2);

        // Create products
        MasterCatalogProduct product1 = createProduct("Product 1", "123456789", rawData1.getId());
        MasterCatalogProduct product2 = createProduct("Product 2", "987654321", rawData2.getId());
        product1 = masterCatalogProductRepository.save(product1);
        product2 = masterCatalogProductRepository.save(product2);
        registerForCleanup(product1);
        registerForCleanup(product2);

        // Create images
        MasterCatalogImage image1 = createImage("path/to/image1.jpg", rawData1.getId());
        MasterCatalogImage image2 = createImage("path/to/image2.jpg", rawData2.getId());
        image1 = masterCatalogImageRepository.save(image1);
        image2 = masterCatalogImageRepository.save(image2);
        registerForCleanup(image1);
        registerForCleanup(image2);

        // Create potentially associate product
        MasterCatalogPotentiallyAssociateProduct associateProduct = createPotentiallyAssociateProduct(
                taskId, jobId, product1.getId(), product2.getId());
        associateProduct = potentiallyAssociateProductRepository.save(associateProduct);
        registerForCleanup(associateProduct);

        // When
        when(s3OperationAdapter.getSignedUrl(image1.getImagePath())).thenReturn("imagePath");
        CustomPage<MasterCatalogPotentiallyAssociateProductDto> result = 
                apiUtils.searchPotentiallyAssociateProducts(taskId, 1, 10);

        // Then
        assertThat(result).isNotNull();
        MasterCatalogPotentiallyAssociateProductDto dto = result.getData().get(0);
        assertThat(dto.getTaskId()).isEqualTo(taskId);
        assertThat(dto.getJobId()).isEqualTo(jobId);
        assertThat(dto.getProductId()).isEqualTo(product1.getId());
        assertThat(dto.getPotentiallyAssociateProductId()).isEqualTo(product2.getId());
        assertThat(dto.getStatus()).isEqualTo(PotentiallyAssociateProductStatus.PENDING_REVIEW);
        
        // Verify images are populated
        assertThat(dto.getProductImages()).isNotNull();
        assertThat(dto.getPotentiallyAssociateProductImages()).isNotNull();
    }

    @Test
    void update() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        // Create raw data
        MasterCatalogRawData rawData1 = createRawData("Test Product 1", "123456789");
        MasterCatalogRawData rawData2 = createRawData("Test Product 2", "987654321");
        rawData1 = masterCatalogRawDataRepository.save(rawData1);
        rawData2 = masterCatalogRawDataRepository.save(rawData2);
        registerForCleanup(rawData1);
        registerForCleanup(rawData2);

        // Create products
        MasterCatalogProduct product1 = createProduct("Product 1", "123456789", rawData1.getId());
        MasterCatalogProduct product2 = createProduct("Product 2", "987654321", rawData2.getId());
        product1 = masterCatalogProductRepository.save(product1);
        product2 = masterCatalogProductRepository.save(product2);
        registerForCleanup(product1);
        registerForCleanup(product2);

        // Create batch job
        MasterCatalogBatchJob batchJob = createBatchJob(jobId);
        batchJob = batchJobRepository.save(batchJob);
        registerForCleanup(batchJob);

        // Create task
        MasterCatalogTask task = createTask(taskId, jobId);
        task = taskRepository.save(task);
        registerForCleanup(task);

        // Create potentially associate product
        MasterCatalogPotentiallyAssociateProduct associateProduct = createPotentiallyAssociateProduct(
                taskId, jobId, product1.getId(), product2.getId());
        associateProduct = potentiallyAssociateProductRepository.save(associateProduct);
        registerForCleanup(associateProduct);

        // When
        apiUtils.updatePotentiallyAssociateProduct(associateProduct.getId(), true);

        // Verify the task status is updated
        MasterCatalogTask updatedTask = taskRepository.findById(taskId).orElse(null);
        assertThat(updatedTask).isNotNull();
        assertThat(updatedTask.getStatus()).isEqualTo(MasterCatalogTaskStatus.IN_PROGRESS);
    }

    @Test
    void submitPotentiallyAssociateProducts_Success() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        // Create raw data
        MasterCatalogRawData rawData1 = createRawData("Test Product 1", "123456789");
        MasterCatalogRawData rawData2 = createRawData("Test Product 2", "987654321");
        rawData1 = masterCatalogRawDataRepository.save(rawData1);
        rawData2 = masterCatalogRawDataRepository.save(rawData2);
        registerForCleanup(rawData1);
        registerForCleanup(rawData2);

        // Create products
        MasterCatalogProduct product1 = createProduct("Product 1", "123456789", rawData1.getId());
        MasterCatalogProduct product2 = createProduct("Product 2", "987654321", rawData2.getId());
        product1 = masterCatalogProductRepository.save(product1);
        product2 = masterCatalogProductRepository.save(product2);
        registerForCleanup(product1);
        registerForCleanup(product2);

        // Create batch job
        MasterCatalogBatchJob batchJob = createBatchJob(jobId);
        batchJob = batchJobRepository.save(batchJob);
        registerForCleanup(batchJob);

        // Create task
        MasterCatalogTask task = createTask(taskId, jobId);
        task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
        task = taskRepository.save(task);
        registerForCleanup(task);

        // Create potentially associate product with IN_STAGE status
        MasterCatalogPotentiallyAssociateProduct associateProduct1 = createPotentiallyAssociateProduct(
                taskId, jobId, product1.getId(), product2.getId());
        associateProduct1.setStatus(PotentiallyAssociateProductStatus.IN_STAGE);
        associateProduct1.setAssociated(true);
        associateProduct1 = potentiallyAssociateProductRepository.save(associateProduct1);
        registerForCleanup(associateProduct1);

        // Create another potentially associate product with IN_STAGE status
        MasterCatalogPotentiallyAssociateProduct associateProduct2 = createPotentiallyAssociateProduct(
                taskId, jobId, product2.getId(), product1.getId());
        associateProduct2.setStatus(PotentiallyAssociateProductStatus.IN_STAGE);
        associateProduct2.setAssociated(false);
        associateProduct2 = potentiallyAssociateProductRepository.save(associateProduct2);
        registerForCleanup(associateProduct2);

        List<UUID> idsToSubmit = Arrays.asList(associateProduct1.getId(), associateProduct2.getId());

        // When
        apiUtils.submitPotentiallyAssociateProducts(idsToSubmit);

        // Then
        // Verify the products are marked as COMPLETED
        MasterCatalogPotentiallyAssociateProduct updatedProduct1 = 
                potentiallyAssociateProductRepository.findById(associateProduct1.getId()).orElse(null);
        MasterCatalogPotentiallyAssociateProduct updatedProduct2 = 
                potentiallyAssociateProductRepository.findById(associateProduct2.getId()).orElse(null);
        
        assertThat(updatedProduct1).isNotNull();
        assertThat(updatedProduct1.getStatus()).isEqualTo(PotentiallyAssociateProductStatus.COMPLETED);
        assertThat(updatedProduct2).isNotNull();
        assertThat(updatedProduct2.getStatus()).isEqualTo(PotentiallyAssociateProductStatus.COMPLETED);

        // Verify the job status is updated
        MasterCatalogBatchJob updatedJob = batchJobRepository.findById(jobId).orElse(null);
        assertThat(updatedJob).isNotNull();
        assertThat(updatedJob.getStatus()).isEqualTo(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_COMPLETED);
        assertThat(updatedJob.getCompletedAt()).isNotNull();

        // Verify the task status is updated to COMPLETED
        MasterCatalogTask updatedTask = taskRepository.findById(taskId).orElse(null);
        assertThat(updatedTask).isNotNull();
        assertThat(updatedTask.getStatus()).isEqualTo(MasterCatalogTaskStatus.COMPLETED);

        // Verify product association is created for the associated product
        List<MasterCatalogProductAssociation> associations = 
                productAssociationRepository.findAllByUpcIn(Arrays.asList("123456789", "987654321"));
        assertThat(associations).hasSize(2);
        assertThat(associations.get(0).getAssociationGroup())
                .isEqualTo(associations.get(1).getAssociationGroup());
    }

    private MasterCatalogRawData createRawData(String name, String upc) {
        return MasterCatalogRawData.builder()
                .id(UUID.randomUUID())
                .name(name)
                .upc(upc)
                .storeId(UUID.randomUUID())
                .description("Test description")
                .brand("Test Brand")
                .createdAt(Instant.now())
                .updatedAt(Instant.now())
                .build();
    }

    private MasterCatalogProduct createProduct(String name, String upc, UUID rawDataId) {
        return MasterCatalogProduct.builder()
                .name(name)
                .upc(upc)
                .description("Test description")
                .brand("Test Brand")
                .masterCatalogRawDataId(rawDataId)
                .singleProduct(true)
                .build();
    }

    private MasterCatalogImage createImage(String imagePath, UUID rawDataId) {
        return MasterCatalogImage.builder()
                .id(UUID.randomUUID())
                .imagePath(imagePath)
                .masterCatalogRawDataId(rawDataId)
                .primaryImage(false)
                .build();
    }

    private MasterCatalogPotentiallyAssociateProduct createPotentiallyAssociateProduct(
            UUID taskId, UUID jobId, UUID productId, UUID potentiallyAssociateProductId) {
        return MasterCatalogPotentiallyAssociateProduct.builder()
                .taskId(taskId)
                .jobId(jobId)
                .productId(productId)
                .productName("Product Name")
                .productUpc("123456789")
                .potentiallyAssociateProductId(potentiallyAssociateProductId)
                .potentiallyAssociateProductName("Associate Product Name")
                .potentiallyAssociateProductUpc("987654321")
                .status(PotentiallyAssociateProductStatus.PENDING_REVIEW)
                .associated(false)
                .createdBy("test-user")
                .updatedBy("test-user")
                .build();
    }

    private MasterCatalogBatchJob createBatchJob(UUID jobId) {
        return MasterCatalogBatchJob.builder()
                .id(jobId)
                .jobNumber(UUID.randomUUID().toString())
                .status(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS)
                .createdAt(Instant.now())
                .updatedAt(Instant.now())
                .build();
    }

    private MasterCatalogTask createTask(UUID taskId, UUID jobId) {
        return MasterCatalogTask.builder()
                .id(taskId)
                .jobId(jobId)
                .taskNumber(UUID.randomUUID().toString())
                .status(MasterCatalogTaskStatus.ASSIGNED)
                .type(MasterCatalogTaskType.DUPLICATION_WITH_PRODUCT)
                .createdAt(Instant.now())
                .updatedAt(Instant.now())
                .build();
    }
}
