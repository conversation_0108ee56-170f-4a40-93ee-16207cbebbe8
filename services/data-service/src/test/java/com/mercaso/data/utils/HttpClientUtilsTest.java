package com.mercaso.data.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.apache.hc.core5.http.Method;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

class HttpClientUtilsTest {

    private MockWebServer mockWebServer;

    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
    }

    @Test
    void testExecutePostRequest_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer token");
        Map<String, Object> body = new HashMap<>();
        body.put("param", "value");

        Map<String, Object> response = HttpClientUtils.executePostRequest(url, body, headers, Map.class);

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecuteGetRequest_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();
        Map<String, String> headers = new HashMap<>();

        Map<String, Object> response = HttpClientUtils.executeGetRequest(url, headers, Map.class);

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecuteGetRequest_TypeReference_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();

        Map<String, String> response = HttpClientUtils.executeGetRequest(url, new TypeReference<Map<String, String>>() {
        });

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecuteRequest_InvalidMethod() {
        String url = "http://example.com";
        Map<String, String> headers = new HashMap<>();
        Object body = null;

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            HttpClientUtils.executeRequest(url, "PUT", body, headers, String.class);
        });

        assertEquals("Unsupported HTTP method: PUT", exception.getMessage());
    }

    @Test
    void testExecutePostRequest_UnsuccessfulResponse() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(500)
            .setBody("Internal Server Error"));

        String url = mockWebServer.url("/test").toString();
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> body = new HashMap<>();

        IOException exception = assertThrows(IOException.class, () -> {
            HttpClientUtils.executePostRequest(url, body, headers, String.class);
        });

        assertTrue(exception.getMessage().contains("Unexpected code"));
    }

    @Test
    void testExecuteGetRequest_TypeReference_UnsuccessfulResponse() {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(500)
            .setBody("Internal Server Error"));

        String url = mockWebServer.url("/test").toString();

        IOException exception = assertThrows(IOException.class, () -> {
            HttpClientUtils.executeGetRequest(url, new TypeReference<Map<String, String>>() {
            });
        });

        assertTrue(exception.getMessage().contains("Unexpected code"));
    }

    @Test
    void testExecuteRequest_TypeReference_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();

        Map<String, String> response = HttpClientUtils.executeRequest(url,
            Method.GET,
            null,
            new TypeReference<Map<String, String>>() {
            });

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecutePostRequest_WithMultipartFile_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"filename\":\"test.txt\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/upload").toString();
        Map<String, String> headers = new HashMap<>();
        MultipartFile file = new MockMultipartFile("test.txt", "test.txt", "text/plain", "test content".getBytes());

        Map<String, Object> response = HttpClientUtils.executePostRequest(url, file, headers, Map.class);

        assertNotNull(response);
        assertEquals("test.txt", response.get("filename"));
    }

    @Test
    void testExecutePostRequest_WithStringBody_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"result\":\"success\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();
        Map<String, String> headers = new HashMap<>();
        String body = "test string body";

        Map<String, Object> response = HttpClientUtils.executePostRequest(url, body, headers, Map.class);

        assertNotNull(response);
        assertEquals("success", response.get("result"));
    }

    @Test
    void testExecuteRequest_WithTypeReference_UnsuccessfulResponse() {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(500)
            .setBody("Internal Server Error"));

        String url = mockWebServer.url("/test").toString();

        IOException exception = assertThrows(IOException.class, () -> {
            HttpClientUtils.executeRequest(url, Method.GET, null, new TypeReference<Map<String, String>>() {
            });
        });

        assertTrue(exception.getMessage().contains("Unexpected code"));
    }

    @Test
    void testExecuteRequest_WithTypeReference_POST_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();
        Map<String, Object> body = new HashMap<>();
        body.put("param", "value");

        Map<String, String> response = HttpClientUtils.executeRequest(url,
            Method.POST,
            body,
            new TypeReference<Map<String, String>>() {
            });

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecuteRequest_WithTypeReference_GET_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();

        Map<String, String> response = HttpClientUtils.executeRequest(url,
            Method.GET,
            null,
            new TypeReference<Map<String, String>>() {
            });

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Test
    void testExecutePostRequest_WithNullBody_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"result\":\"success\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();
        Map<String, String> headers = new HashMap<>();

        Map<String, Object> response = HttpClientUtils.executePostRequest(url, null, headers, Map.class);

        assertNotNull(response);
        assertEquals("success", response.get("result"));
    }

    @Test
    void testExecuteGetRequest_NullHeaders_Success() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();

        Map<String, Object> response = HttpClientUtils.executeGetRequest(url, null, Map.class);

        assertNotNull(response);
        assertEquals("value", response.get("key"));
    }

    @Disabled("This test is to verify timeout behavior, but it's disabled to avoid slowing down the test suite")
    @Test
    void testExecuteRequest_Timeout() {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{\"key\":\"value\"}")
            .setHeadersDelay(70, TimeUnit.SECONDS));

        String url = mockWebServer.url("/test").toString();

        assertThrows(IOException.class, () -> {
            HttpClientUtils.executeGetRequest(url, null, Map.class);
        });
    }

    @Test
    void testExecuteRequest_EmptyJsonResponseBody() throws IOException {
        mockWebServer.enqueue(new MockResponse()
            .setResponseCode(200)
            .setBody("{}")
            .addHeader("Content-Type", "application/json"));

        String url = mockWebServer.url("/test").toString();

        Map<String, Object> response = HttpClientUtils.executeGetRequest(url, null, Map.class);

        assertNotNull(response);
        assertTrue(response.isEmpty());
    }
}
