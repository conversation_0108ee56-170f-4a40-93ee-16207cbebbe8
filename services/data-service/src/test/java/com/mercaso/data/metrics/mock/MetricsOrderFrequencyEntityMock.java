package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.MetricsOrderFrequencyEntity;
import java.time.LocalDateTime;

public class MetricsOrderFrequencyEntityMock {

    public static MetricsOrderFrequencyEntity metricsOrderFrequencyEntityMock(String addressId, String dateType) {
        MetricsOrderFrequencyEntity metricsOrderFrequencyEntity = new MetricsOrderFrequencyEntity();
        metricsOrderFrequencyEntity.setAddressId(addressId);
        metricsOrderFrequencyEntity.setDateType(dateType);
        metricsOrderFrequencyEntity.setDate(LocalDateTime.now());
        metricsOrderFrequencyEntity.setCreatedAt(LocalDateTime.now());
        metricsOrderFrequencyEntity.setFrequency(8);
        return metricsOrderFrequencyEntity;
    }
}
