package com.mercaso.data.master_catalog.controller.v2;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogImageUtils.buildMasterCatalogImage;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogV2Request;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogRawDataV2ResourceApiUtils;

import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

class MasterCatalogRawDataV2ResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogRawDataV2ResourceApiUtils masterCatalogRawDataV2ResourceApiUtils;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogImageRepository masterCatalogImageRepository;

    @MockBean
    private S3OperationAdapter s3OperationAdapter;

    @Test
    void searchMasterCatalogRawData() {
        // Create test data
        UUID testStoreId = UUID.randomUUID();
        
        MasterCatalogRawData rawData1 = buildMasterCatalogRawData(testStoreId);
        rawData1.setUpc("test-upc-1");
        rawData1.setDepartment("Electronics");
        rawData1.setCategory("Mobile");
        rawData1.setSubCategory("Smartphones");
        rawData1.setClazz("Premium");
        rawData1.setDescription("Latest smartphone with advanced features");
        
        MasterCatalogRawData rawData2 = buildMasterCatalogRawData(testStoreId);
        rawData2.setUpc("test-upc-2");
        rawData2.setDepartment("Electronics");
        rawData2.setCategory("Mobile");
        rawData2.setSubCategory("Smartphones");
        rawData2.setClazz("Basic");
        rawData2.setDescription("Budget smartphone for everyday use");
        
        List<MasterCatalogRawData> saved = masterCatalogRawDataRepository.saveAll(
                List.of(rawData1, rawData2));
        registerForCleanup(saved);

        List<MasterCatalogImage> images = saved.stream()
                .map(x -> buildMasterCatalogImage(x.getId()))
                .toList();
        masterCatalogImageRepository.saveAll(images);
        registerForCleanup(images);

        // Test search with multiple criteria
        SearchMasterCatalogV2Request searchRequest = new SearchMasterCatalogV2Request();
        searchRequest.setStoreId(testStoreId);
        searchRequest.setDepartment("Electronics");
        searchRequest.setCategory("Mobile");
        searchRequest.setDescription("smartphone");
        searchRequest.setPage(1);
        searchRequest.setPageSize(10);

        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://test.com");

        CustomPage<MasterCatalogRawDataDto> result = 
                masterCatalogRawDataV2ResourceApiUtils.searchMasterCatalogRawData(searchRequest);

        // Verify results
        assertEquals(2, result.getData().size());
        assertEquals(2, result.getTotalCount());
        assertTrue(result.getData().stream().allMatch(dto -> 
            dto.getStoreId().equals(testStoreId) &&
            "Electronics".equals(dto.getDepartment()) &&
            "Mobile".equals(dto.getCategory()) &&
            dto.getDescription().toLowerCase().contains("smartphone")));
    }
}