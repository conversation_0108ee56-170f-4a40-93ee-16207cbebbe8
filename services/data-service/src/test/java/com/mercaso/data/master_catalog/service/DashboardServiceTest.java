package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.dashboard.DashboardRequest;
import com.mercaso.data.master_catalog.dto.dashboard.InventoryAndReplenishmentTrendDto;
import com.mercaso.data.master_catalog.dto.dashboard.OrderTrendDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrder;
import com.mercaso.data.master_catalog.entity.MasterCatalogSuqareInventoryDailyMetric;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderLineItemRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareOrderRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSuqareInventoryDailyMetricRepository;
import com.mercaso.data.master_catalog.service.impl.DashboardServiceImpl;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DashboardServiceTest {

    private MasterCatalogSuqareInventoryDailyMetricRepository inventoryMetricRepository;
    private MasterCatalogSquareOrderRepository orderRepository;
    private MasterCatalogRawDataRepository rawDataRepository;
    private MasterCatalogSquareOrderLineItemRepository orderLineItemRepository;
    private DashboardService dashboardService;

    @BeforeEach
    void setUp() {
        inventoryMetricRepository = mock(MasterCatalogSuqareInventoryDailyMetricRepository.class);
        orderRepository = mock(MasterCatalogSquareOrderRepository.class);
        rawDataRepository = mock(MasterCatalogRawDataRepository.class);
        orderLineItemRepository = mock(MasterCatalogSquareOrderLineItemRepository.class);
        dashboardService = new DashboardServiceImpl(inventoryMetricRepository,
            orderRepository,
            rawDataRepository,
            orderLineItemRepository);
    }

    @Test
    void getInventoryReplenishmentTrend_shouldReturnEmptyList_whenRawDataIdNotFound() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);
        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(Collections.emptyList());

        List<InventoryAndReplenishmentTrendDto> result = dashboardService.getInventoryReplenishmentTrend(request);

        assertTrue(result.isEmpty());
    }

    @Test
    void getInventoryReplenishmentTrend_shouldReturnTrendData_whenDataFound() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);
        UUID rawDataId = UUID.randomUUID();
        Instant now = Instant.now().truncatedTo(ChronoUnit.DAYS);

        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(rawDataId));
        when(inventoryMetricRepository.findByDateRangeAndNotBothQuantitiesZero(any(), any(), any()))
            .thenReturn(List.of(
                createInventoryMetric(now, 10, 5),
                createInventoryMetric(now.minus(1, ChronoUnit.DAYS), 15, 8)
            ));

        List<InventoryAndReplenishmentTrendDto> result = dashboardService.getInventoryReplenishmentTrend(request);

        assertEquals(30, result.size());
    }

    @Test
    void getOrderTrend_shouldReturnEmptyList_whenNoOrdersFound() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);
        UUID rawDataId = UUID.randomUUID();

        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(rawDataId));
        when(orderLineItemRepository.findOrderIdsByMasterCatalogRawDataId(any())).thenReturn(Collections.emptyList());

        List<OrderTrendDto> result = dashboardService.getOrderTrend(request);

        assertTrue(result.isEmpty());
    }

    @Test
    void getOrderTrend_shouldReturnTrendData_whenOrdersFound() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);
        UUID rawDataId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();
        Instant now = Instant.now().truncatedTo(ChronoUnit.DAYS);

        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(rawDataId));
        when(orderLineItemRepository.findOrderIdsByMasterCatalogRawDataId(any())).thenReturn(List.of(orderId));
        when(orderRepository.findByIdInAndCreatedAtBetweenOrderByCreatedAtDesc(anyList(), any(), any()))
            .thenReturn(List.of(
                createOrder(now),
                createOrder(now.minus(1, ChronoUnit.DAYS))
            ));

        List<OrderTrendDto> result = dashboardService.getOrderTrend(request);

        assertEquals(30, result.size());
        assertEquals(1, result.get(0).getQuantity());
    }

    @Test
    void getInventoryReplenishmentTrend_shouldHandleNullDaysBefore() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", null);
        UUID rawDataId = UUID.randomUUID();

        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(rawDataId));
        when(inventoryMetricRepository.findByDateRangeAndNotBothQuantitiesZero(any(), any(), any()))
            .thenReturn(Collections.emptyList());

        List<InventoryAndReplenishmentTrendDto> result = dashboardService.getInventoryReplenishmentTrend(request);

        assertTrue(result.isEmpty());
    }

    @Test
    void getOrderTrend_shouldHandleNullDaysBefore() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", null);
        UUID rawDataId = UUID.randomUUID();
        UUID orderId = UUID.randomUUID();

        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(rawDataId));
        when(orderLineItemRepository.findOrderIdsByMasterCatalogRawDataId(any())).thenReturn(List.of(orderId));
        when(orderRepository.findByIdInAndCreatedAtBetweenOrderByCreatedAtDesc(anyList(), any(), any()))
            .thenReturn(Collections.emptyList());

        List<OrderTrendDto> result = dashboardService.getOrderTrend(request);

        assertEquals(30, result.size());
    }

    @Test
    void getOrderTrend_shouldReturnEmptyList_whenRawDataIdIsNull() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);

        // Mock rawDataRepository to return empty list, which will make rawDataId null
        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(Collections.emptyList());

        List<OrderTrendDto> result = dashboardService.getOrderTrend(request);

        assertTrue(result.isEmpty());
        verify(rawDataRepository).findIdsByStoreIdAndUpc(request.getStoreId(), request.getUpc());
    }

    @Test
    void getInventoryReplenishmentTrend_shouldUseFirstId_whenMultipleRawDataIdsFound() {
        DashboardRequest request = new DashboardRequest(UUID.randomUUID(), "123456", 30);
        UUID firstRawDataId = UUID.randomUUID();
        UUID secondRawDataId = UUID.randomUUID();

        // Mock returning multiple rawDataIds
        when(rawDataRepository.findIdsByStoreIdAndUpc(any(), any())).thenReturn(List.of(firstRawDataId, secondRawDataId));
        when(inventoryMetricRepository.findByDateRangeAndNotBothQuantitiesZero(any(), any(), any()))
            .thenReturn(Collections.emptyList());

        List<InventoryAndReplenishmentTrendDto> result = dashboardService.getInventoryReplenishmentTrend(request);

        assertTrue(result.isEmpty());
        verify(rawDataRepository).findIdsByStoreIdAndUpc(request.getStoreId(), request.getUpc());
        verify(inventoryMetricRepository).findByDateRangeAndNotBothQuantitiesZero(eq(firstRawDataId),
            any(),
            any());
    }

    private MasterCatalogSuqareInventoryDailyMetric createInventoryMetric(Instant date, int quantity, int inStockQuantity) {
        MasterCatalogSuqareInventoryDailyMetric metric = new MasterCatalogSuqareInventoryDailyMetric();
        metric.setDate(date);
        metric.setQuantity(quantity);
        metric.setInStockQuantity(inStockQuantity);
        return metric;
    }

    private MasterCatalogSquareOrder createOrder(Instant createdAt) {
        MasterCatalogSquareOrder order = new MasterCatalogSquareOrder();
        order.setCreatedAt(createdAt);
        order.setOrderCreatedAt(createdAt);
        return order;
    }
}
