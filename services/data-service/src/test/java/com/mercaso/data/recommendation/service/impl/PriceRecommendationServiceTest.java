package com.mercaso.data.recommendation.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.entity.PriceRecommendation;
import com.mercaso.data.recommendation.mapper.PriceRecommendationMapper;
import com.mercaso.data.recommendation.repository.PriceRecommendationRepository;
import com.mercaso.data.recommendation.service.PriceRecommendationService;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

public class PriceRecommendationServiceTest {

  private final PriceRecommendationRepository repository = mock(PriceRecommendationRepository.class);
  private final PriceRecommendationMapper mapper = mock(PriceRecommendationMapper.class);
  private final PriceRecommendationService service = new PriceRecommendationServiceImpl(repository,
      mapper);

  @Test
  void searchAll_shouldReturnPagedData() {
    String storeId = UUID.randomUUID().toString();
    List<PriceRecommendation> entityList = new ArrayList<>();
    List<PriceRecommendationDto> dtoList = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      PriceRecommendation entity = PriceRecommendation.builder().id(UUID.randomUUID()).name("Product" + i).build();
      PriceRecommendationDto dto = PriceRecommendationDto.builder().name(entity.getName()).build();
      entityList.add(entity);
      dtoList.add(dto);
      when(mapper.toDto(entity)).thenReturn(dto);
    }
    when(repository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(new PageImpl<>(entityList));
    Pageable pageable = PageRequest.of(0, 3);

    PageableResponse<PriceRecommendationDto> result = service.searchAll(storeId, null, pageable);

    assertEquals(5, result.getData().size());
    assertEquals(1, result.getTotalPages());
  }

  @Test
  void searchAll_shouldFilterBySearchText() {
    PriceRecommendation entity = PriceRecommendation.builder().id(UUID.randomUUID()).name("TestName").build();
    PriceRecommendationDto dto = PriceRecommendationDto.builder().name(entity.getName()).build();
    when(mapper.toDto(entity)).thenReturn(dto);
    when(repository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(new PageImpl<>(List.of(entity)));
    Pageable pageable = PageRequest.of(0, 10);

    PageableResponse<PriceRecommendationDto> result = service.searchAll("storeId", "TestName", pageable);

    assertTrue(result.getData().stream().allMatch(d -> d.getName().contains("TestName")));
  }

  @Test
  void searchAll_shouldReturnEmptyIfNoMatch() {
    when(repository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(new PageImpl<>(List.of()));
    Pageable pageable = PageRequest.of(0, 10);

    PageableResponse<PriceRecommendationDto> result = service.searchAll("storeId", "NotExist", pageable);

    assertEquals(0, result.getData().size());
  }

  @Test
  void searchEverRecommendations_shouldReturnPagedData() {
    String departmentId = "dept1";
    List<PriceRecommendation> entityList = new ArrayList<>();
    List<PriceRecommendationDto> dtoList = new ArrayList<>();
    for (int i = 0; i < 3; i++) {
      PriceRecommendation entity = PriceRecommendation.builder().id(UUID.randomUUID()).departmentId(departmentId).build();
      PriceRecommendationDto dto = PriceRecommendationDto.builder().departmentId(departmentId).build();
      entityList.add(entity);
      dtoList.add(dto);
      when(mapper.toDto(entity)).thenReturn(dto);
    }
    PageRequest pageable = PageRequest.of(0, 10);
    when(repository.findAll(any(Specification.class), eq(pageable))).thenReturn(new PageImpl<>(entityList, pageable, 3));

    PageableResponse<PriceRecommendationDto> result = service.searchEvergreenRecommendations(departmentId, pageable);

    assertEquals(3, result.getData().size());
    assertEquals(1, result.getTotalPages());
  }

  @Test
  void searchEverRecommendations_shouldReturnEmptyIfNoMatch() {
    PageRequest pageable = PageRequest.of(0, 10);
    when(repository.findAll(any(Specification.class), eq(pageable))).thenReturn(new PageImpl<>(List.of(), pageable, 0));

    PageableResponse<PriceRecommendationDto> result = service.searchEvergreenRecommendations("notExistDept", pageable);

    assertEquals(0, result.getData().size());
  }

  @Test
  void getEverGreenDepartments_shouldReturnDistinctDepartments() {
    PriceRecommendation rec1 = PriceRecommendation.builder()
        .departmentId("dept1").department("Electronics").everGreen(true).storeId("b350491e-e238-4caf-b328-cf2438e057d8").build();
    PriceRecommendation rec2 = PriceRecommendation.builder()
        .departmentId("dept2").department("Home").everGreen(true).storeId("b350491e-e238-4caf-b328-cf2438e057d8").build();
    PriceRecommendation rec3 = PriceRecommendation.builder()
        .departmentId("dept1").department("Electronics").everGreen(true).storeId("b350491e-e238-4caf-b328-cf2438e057d8").build();

    when(repository.findByStoreIdAndEverGreen("b350491e-e238-4caf-b328-cf2438e057d8", true))
        .thenReturn(List.of(rec1, rec2, rec3));

    List<DepartmentDto> result = service.getEverGreenDepartments();

    assertTrue(result.stream().anyMatch(d -> "dept1".equals(d.id()) && "Electronics".equals(d.name())));
    assertTrue(result.stream().anyMatch(d -> "dept2".equals(d.id()) && "Home".equals(d.name())));
  }
}
