package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.MasterCatalogLocationDto;
import com.mercaso.data.utils.SerializationUtils;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
public class MasterCatalogLocationDtoUtils {

    public static MasterCatalogLocationDto buildMasterCatalogLocationDto(UUID storeId) {
        return MasterCatalogLocationDto.builder()
            .storeId(storeId)
            .city("Xi'an")
            .country("US")
            .addressLine1("Beijing Hutong 008")
            .metadata(SerializationUtils.readTree("{\"locationId\": \"K4TVGHXK843DZ\", \"merchantId\": \"R3918N57RGZ35\"}"))
            .longitude(116.397228D)
            .latitude(39.918043D)
            .build();
    }

}
