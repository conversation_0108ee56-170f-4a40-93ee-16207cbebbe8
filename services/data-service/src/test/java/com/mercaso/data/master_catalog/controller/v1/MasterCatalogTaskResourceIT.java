package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.config.SecurityContextUtilStaticFilter;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogTaskResourceApiUtils;
import com.mercaso.data.utils.BusinessNumberGenerator;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

public class MasterCatalogTaskResourceIT extends AbstractIT {

  @Autowired
  private MasterCatalogTaskResourceApiUtils masterCatalogTaskResourceApiUtils;
  @Autowired
  private MasterCatalogTaskRepository masterCatalogTaskRepository;
  @Autowired
  private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

  @Test
  void assign() {
    UUID jobId = UUID.randomUUID();
    String taskId = UUID.randomUUID().toString();
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(UUID.fromString(taskId))
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    MasterCatalogBatchJob job = MasterCatalogBatchJob.builder().id(jobId)
        .jobNumber(UUID.randomUUID().toString())
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS).build();

    masterCatalogTaskRepository.save(task);
    masterCatalogBatchJobRepository.save(job);

    masterCatalogTaskResourceApiUtils.assign(taskId, "zhangsan");

    MasterCatalogTask masterCatalogTask = masterCatalogTaskRepository.findById(UUID.fromString(taskId)).orElseThrow();

    assert masterCatalogTask.getId().equals(UUID.fromString(taskId));
    assert masterCatalogTask.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert masterCatalogTask.getAssignedTo().equals("zhangsan");
  }

  @Test
  void search() {
    UUID jobId = UUID.randomUUID();
    String taskNumber = UUID.randomUUID().toString();
    MasterCatalogTask pendingTask = MasterCatalogTask.builder()
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(taskNumber)
        .status(MasterCatalogTaskStatus.PENDING)
        .build();
    
    MasterCatalogTask assignedTask = MasterCatalogTask.builder()
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.ASSIGNED)
        .assignedTo("testUser")
        .build();

    MasterCatalogBatchJob job1 = MasterCatalogBatchJob.builder()
        .id(jobId)
        .jobNumber("234")
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
        .build();
    MasterCatalogBatchJob job2 = MasterCatalogBatchJob.builder()
        .id(UUID.randomUUID())
        .jobNumber("23456")
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
        .build();

    masterCatalogTaskRepository.save(pendingTask);
    masterCatalogTaskRepository.save(assignedTask);

    masterCatalogBatchJobRepository.save(job1);
    masterCatalogBatchJobRepository.save(job2);

    var result = masterCatalogTaskResourceApiUtils.search(null,"123", null, null,  null, MasterCatalogTaskStatus.PENDING, null, 1, 10);

    assert result.getData().stream().allMatch(task -> task.getStatus() == MasterCatalogTaskStatus.PENDING);
    assert result.getData().stream().allMatch(task -> task.getTaskNumber().equals(taskNumber));
  }

  @Test
  void getDetailById() {
    UUID jobId = UUID.randomUUID();
    UUID taskId = UUID.randomUUID();

    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(taskId)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    masterCatalogTaskRepository.save(task);

    var result = masterCatalogTaskResourceApiUtils.getById(taskId);

    assert result.getId().equals(taskId);
    assert result.getStatus() == MasterCatalogTaskStatus.PENDING;
    assert result.getTotalRecordCount() == 0;
  }

  @Test
  void batchAssign() {
    // Given
    UUID jobId = UUID.randomUUID();
    UUID taskId1 = UUID.randomUUID();
    UUID taskId2 = UUID.randomUUID();
    UUID taskId3 = UUID.randomUUID();
    String assignTo = "testUser";

    MasterCatalogTask task1 = MasterCatalogTask.builder()
        .id(taskId1)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    MasterCatalogTask task2 = MasterCatalogTask.builder()
        .id(taskId2)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    MasterCatalogTask task3 = MasterCatalogTask.builder()
        .id(taskId3)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.ASSIGNED)
        .assignedTo("previousUser")
        .build();

    MasterCatalogBatchJob job = MasterCatalogBatchJob.builder()
        .id(jobId)
        .jobNumber(UUID.randomUUID().toString())
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
        .build();

    // Save test data
    masterCatalogTaskRepository.save(task1);
    masterCatalogTaskRepository.save(task2);
    masterCatalogTaskRepository.save(task3);
    masterCatalogBatchJobRepository.save(job);

    List<UUID> taskIds = Arrays.asList(taskId1, taskId2, taskId3);

    // When
    masterCatalogTaskResourceApiUtils.batchAssign(assignTo, taskIds);

    // Then - Verify all tasks are assigned correctly
    MasterCatalogTask updatedTask1 = masterCatalogTaskRepository.findById(taskId1).orElseThrow();
    MasterCatalogTask updatedTask2 = masterCatalogTaskRepository.findById(taskId2).orElseThrow();
    MasterCatalogTask updatedTask3 = masterCatalogTaskRepository.findById(taskId3).orElseThrow();

    // Task 1: was PENDING, should now be ASSIGNED
    assert updatedTask1.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert updatedTask1.getAssignedTo().equals(assignTo);
    assert updatedTask1.getAssignedBy() != null;

    // Task 2: was PENDING, should now be ASSIGNED
    assert updatedTask2.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert updatedTask2.getAssignedTo().equals(assignTo);
    assert updatedTask2.getAssignedBy() != null;

    // Task 3: was already ASSIGNED, status should remain ASSIGNED but assignTo should be updated
    assert updatedTask3.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert updatedTask3.getAssignedTo().equals(assignTo);
    assert updatedTask3.getAssignedBy() != null;
  }
}
