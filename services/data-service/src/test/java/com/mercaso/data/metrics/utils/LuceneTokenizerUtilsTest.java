package com.mercaso.data.metrics.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class LuceneTokenizerUtilsTest {

    @Test
    void testConvertKeywordToTsQuery() {
        String keyword = "test";
        String result = LuceneTokenizer.convertKeywordToTsQuery(keyword);
        Assertions.assertEquals("test:*", result);
    }

    @Test
    void testConvertKeywordToTsQueryTwoKeyWord() {
        String keyword = "test test2";
        String result = LuceneTokenizer.convertKeywordToTsQuery(keyword);
        Assertions.assertEquals("test:* & test2:*", result);
    }

    @Test
    void testConvertKeywordToTsQueryTwoKeyWordBy() {
        String keyword = "test,test2";
        String result = LuceneTokenizer.convertKeywordToTsQuery(keyword);
        Assertions.assertEquals("test,test2:*", result);
    }

    @Test
    void testTokenizeWithSpecialPattern() {
        String text = "123-456 & test";
        String result = LuceneTokenizer.convertKeywordToTsQuery(text);
        Assertions.assertEquals("123-456:* & test:*", result);
    }
}