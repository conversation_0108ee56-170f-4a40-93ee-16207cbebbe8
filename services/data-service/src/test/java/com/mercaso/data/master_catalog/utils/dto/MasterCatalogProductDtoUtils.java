package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;

public class MasterCatalogProductDtoUtils {

    public static MasterCatalogProductDto buildMasterCatalogProductDto(UUID id, Instant now) {
        return MasterCatalogProductDto.builder()
            .id(id)
            .createdAt(now)
            .updatedAt(now)
            .upc("123456")
            .name("Test Product")
            .description("Test Description")
            .brand("Test Brand")
            .skuNumber("SKU123")
            .department("Test Department")
            .category("Test Category")
            .subCategory("Test Subcategory")
            .clazz("Test Class")
            .primaryVendor("Test Vendor")
            .masterCatalogRawDataId(id)
            .singleProduct(true)
            .images(Arrays.asList("image1.jpg", "image2.jpg"))
            .build();
    }
}
