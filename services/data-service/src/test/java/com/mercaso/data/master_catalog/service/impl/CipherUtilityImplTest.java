package com.mercaso.data.master_catalog.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import com.mercaso.data.master_catalog.config.CipherUtilityConfig;

public class CipherUtilityImplTest {

    private CipherUtilityImpl encryptionService;
    private final CipherUtilityConfig cipherUtilityConfig = new CipherUtilityConfig();

    @BeforeEach
    void setUp() {
        cipherUtilityConfig.setKey("LHuWR+c3fJAy7PibZR7kY4PLgbCHJ3rc");
        encryptionService = new CipherUtilityImpl(cipherUtilityConfig);
    }

    @Test
    @DisplayName("Test key is not correct when decrypting")
    void shouldThrowExceptionWhenDecryptingWithWrongKey() {
        String data = "data122334";
        String encrypted = encryptionService.encrypt(data);

        CipherUtilityConfig newCipherUtilityConfig = new CipherUtilityConfig();
        newCipherUtilityConfig.setKey("LHuWR+c3fJAy7PibZR7kY4PLgbCHJ3rd");
        CipherUtilityImpl newEncryptionService = new CipherUtilityImpl(newCipherUtilityConfig);
        assertThrows(RuntimeException.class, () -> {
            newEncryptionService.decrypt(encrypted);
        }, "Decrypting with wrong key should throw an exception");
    }

    @Test
    @DisplayName("Test basic encryption and decryption functionality")
    void shouldEncryptAndDecryptSuccessfully() {
        String data = "data122334";
        String encrypted = encryptionService.encrypt(data);
        String decrypted = encryptionService.decrypt(encrypted);
        
        assertNotEquals(data, encrypted, "Encrypted data should not be equal to original data");
        assertEquals(data, decrypted, "Decrypted data should match original data");
    }

    @Test
    @DisplayName("Test encryption and decryption with special characters")
    void shouldHandleSpecialCharacters() {
        String data = "Special chars !@#$%^&*()_+{}[]|\\;:'\",.<>?/";
        String encrypted = encryptionService.encrypt(data);
        String decrypted = encryptionService.decrypt(encrypted);
        
        assertEquals(data, decrypted, "Special characters should be encrypted and decrypted correctly");
    }

    @Test
    @DisplayName("Test encryption empty string")
    void shouldThrowExceptionWhenEncryptingEmptyString() {
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.encrypt("");
        }, "Data to encrypt cannot be empty or null");
    }

    @Test
    @DisplayName("Test decryption empty string")
    void shouldThrowExceptionWhendecryptingEmptyString() {
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.decrypt("");
        }, "Data to decrypt cannot be empty or null");
    }

    @Test
    @DisplayName("Test encryption and decryption of long string")
    void shouldHandleLongString() {
        String data = "a".repeat(1000);
        String encrypted = encryptionService.encrypt(data);
        String decrypted = encryptionService.decrypt(encrypted);
        
        assertEquals(data, decrypted, "Long string should be encrypted and decrypted correctly");
    }

    @Test
    @DisplayName("Test encryption of null value")
    void shouldThrowExceptionWhenEncryptingNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.encrypt(null);
        }, "Data to encrypt cannot be empty or null");
    }

    @Test
    @DisplayName("Test decryption of null value")
    void shouldThrowExceptionWhenDecryptingNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.decrypt(null);
        }, "Data to decrypt cannot be empty or null");
    }

    @Test
    @DisplayName("Test decryption of invalid encrypted data")
    void shouldThrowExceptionWhenDecryptingInvalidData() {
        assertThrows(IllegalArgumentException.class, () -> {
            encryptionService.decrypt("invalid-encrypted-data");
        }, "Decrypting invalid data should throw an exception");
    }
}
