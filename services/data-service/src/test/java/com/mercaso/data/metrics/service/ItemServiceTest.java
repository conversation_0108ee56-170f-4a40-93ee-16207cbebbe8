package com.mercaso.data.metrics.service;

import static com.mercaso.data.metrics.mock.MetricsItemReplenishmentForecastDtoMock.metricsItemReplenishmentForecastDtoMock;
import static com.mercaso.data.metrics.mock.MetricsItemReplenishmentForecastEntityMock.metricsItemReplenishmentForecastEntityMock;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.data.metrics.dto.MetricsItemReplenishmentForecastDto;
import com.mercaso.data.metrics.entity.MetricsItemReplenishmentForecastEntity;
import com.mercaso.data.metrics.mapper.MetricsItemReplenishmentForecastEntityMapper;
import com.mercaso.data.metrics.repository.MetricsItemReplenishmentForecastRepository;
import com.mercaso.data.metrics.service.impl.ItemServiceImpl;
import java.util.List;
import org.junit.jupiter.api.Test;

class ItemServiceTest {

    private final MetricsItemReplenishmentForecastRepository metricsItemReplenishmentForecastRepository = mock(
        MetricsItemReplenishmentForecastRepository.class);
    private final MetricsItemReplenishmentForecastEntityMapper metricsItemReplenishmentForecastEntityMapper = mock(
        MetricsItemReplenishmentForecastEntityMapper.class);
    private final ItemService itemService = new ItemServiceImpl(metricsItemReplenishmentForecastRepository,
        metricsItemReplenishmentForecastEntityMapper);

    @Test
    void getItemReplenishmentForecast() {
        MetricsItemReplenishmentForecastEntity metricsItemReplenishmentForecastEntity = metricsItemReplenishmentForecastEntityMock();
        when(metricsItemReplenishmentForecastRepository.findAllByAddressIdAndFilterDepartmentAndNextOrderTimeBefore(any(),
            any(),
            any())).thenReturn(List.of(metricsItemReplenishmentForecastEntity));

        MetricsItemReplenishmentForecastDto metricsItemReplenishmentForecastDto = metricsItemReplenishmentForecastDtoMock();
        when(metricsItemReplenishmentForecastEntityMapper.toDto(any()))
            .thenReturn(metricsItemReplenishmentForecastDto);

        List<MetricsItemReplenishmentForecastDto> result = itemService.getItemReplenishmentForecast("123456",
            "test");
        assert result != null;
        assertEquals(1, result.size());
        assertEquals(metricsItemReplenishmentForecastDto, result.getFirst());

    }
}
