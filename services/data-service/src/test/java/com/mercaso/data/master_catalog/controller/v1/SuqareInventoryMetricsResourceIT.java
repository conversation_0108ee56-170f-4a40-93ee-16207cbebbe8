package com.mercaso.data.master_catalog.controller.v1;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.service.SuqareInventoryMetricsService;
import com.mercaso.data.master_catalog.utils.resource_utils.SuqareInventoryMetricsResourceApiUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

class SuqareInventoryMetricsResourceIT extends AbstractIT {

    @Autowired
    private SuqareInventoryMetricsResourceApiUtils suqareInventoryMetricsResourceApiUtils;

    @MockBean
    private SuqareInventoryMetricsService squareInventoryMetricsService;

    @Test
    void initDailyMetrics() {
        Integer daysBefore = 7;

        ResponseEntity<Void> response =
            suqareInventoryMetricsResourceApiUtils.initDailyMetrics(daysBefore);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        verify(squareInventoryMetricsService, times(1)).initDailyMetrics(daysBefore);
    }

    @Test
    void calculateYesterdayMetrics() {
        suqareInventoryMetricsResourceApiUtils.calculateYesterdayMetrics();

        verify(squareInventoryMetricsService, times(1)).calculateYesterdayMetrics();
    }
}
