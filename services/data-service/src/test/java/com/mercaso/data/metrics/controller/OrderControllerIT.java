package com.mercaso.data.metrics.controller;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.dto.MetricsOrderAmountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderDepartmentDistributionDto;
import com.mercaso.data.metrics.dto.MetricsOrderDiscountSummaryDto;
import com.mercaso.data.metrics.dto.MetricsOrderFrequencyDto;
import com.mercaso.data.metrics.dto.MetricsOrderItemRecommendationDto;
import com.mercaso.data.metrics.entity.MetricsOrderAmountEntity;
import com.mercaso.data.metrics.entity.MetricsOrderDepartmentDistributionEntity;
import com.mercaso.data.metrics.entity.MetricsOrderDiscountEntity;
import com.mercaso.data.metrics.entity.MetricsOrderFrequencyEntity;
import com.mercaso.data.metrics.entity.MetricsOrderItemRecommendationEntity;
import com.mercaso.data.metrics.mock.MetricsOrderAmountEntityMock;
import com.mercaso.data.metrics.mock.MetricsOrderDepartmentDistributionEntityMock;
import com.mercaso.data.metrics.mock.MetricsOrderDiscountEntityMock;
import com.mercaso.data.metrics.mock.MetricsOrderFrequencyEntityMock;
import com.mercaso.data.metrics.mock.MetricsOrderItemRecommendationEntityMock;
import com.mercaso.data.metrics.repository.MetricsOrderAmountRepository;
import com.mercaso.data.metrics.repository.MetricsOrderDepartmentDistributionRepository;
import com.mercaso.data.metrics.repository.MetricsOrderDiscountRepository;
import com.mercaso.data.metrics.repository.MetricsOrderFrequencyRepository;
import com.mercaso.data.metrics.repository.MetricsOrderItemRecommendationRepository;
import com.mercaso.data.metrics.repository.OrderSkuQuantitySummaryRepository;
import com.mercaso.data.metrics.utils.controller_utils.OrderRestApi;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class OrderControllerIT extends AbstractIT {

    @Autowired
    private OrderRestApi orderRestApi;

    @Autowired
    private OrderSkuQuantitySummaryRepository orderSkuQuantitySummaryRepository;

    @Autowired
    private MetricsOrderItemRecommendationRepository metricsOrderItemRecommendationRepository;

    @Autowired
    private MetricsOrderAmountRepository metricsOrderAmountRepository;

    @Autowired
    private MetricsOrderFrequencyRepository metricsOrderFrequencyRepository;

    @Autowired
    private MetricsOrderDepartmentDistributionRepository metricsOrderDepartmentDistributionRepository;

    @Autowired
    private MetricsOrderDiscountRepository metricsOrderDiscountRepository;


    @Test
    void testGetUnpurchasedItemsRecommendation() {

        String addressId = UUID.randomUUID().toString();

        MetricsOrderItemRecommendationEntity entity = MetricsOrderItemRecommendationEntityMock.metricsOrderItemRecommendationEntityMock(
            "UNPURCHASED", "All", "Beverages", addressId);
        metricsOrderItemRecommendationRepository.save(entity);

        List<MetricsOrderItemRecommendationDto> resultList = orderRestApi.getUnpurchasedItemsRecommendation(addressId, "All");

        assertEquals(1, resultList.size());
    }

    @Test
    void testGetPurchasedItemsRecommendation() {

        String addressId = UUID.randomUUID().toString();

        MetricsOrderItemRecommendationEntity entity = MetricsOrderItemRecommendationEntityMock.metricsOrderItemRecommendationEntityMock(
            "PURCHASED",
            "Beverages",
            "Beverages",
            addressId);
        entity.setLastOrderedDate(null);
        metricsOrderItemRecommendationRepository.save(entity);

        List<MetricsOrderItemRecommendationDto> resultList = orderRestApi.getPurchasedItemsRecommendation(addressId, "Beverages");

        assertEquals(1, resultList.size());
    }

    @Test
    void testGetOrderAmountSummary() {

        String departmentName = "All";
        String addressId = UUID.randomUUID().toString();
        String timeAggType = "DAILY";
        Integer timeLength = 1;

        MetricsOrderAmountEntity entity = MetricsOrderAmountEntityMock.metricsOrderAmountEntityMock("Beverages",
            "DAILY",
            addressId,
            "All");

        MetricsOrderAmountEntity entity2 = MetricsOrderAmountEntityMock.metricsOrderAmountEntityMock("Child",
            "DAILY",
            addressId,
            "All");

        metricsOrderAmountRepository.saveAll(List.of(entity, entity2));

        List<MetricsOrderAmountSummaryDto> orderAmountSummary = orderRestApi.getOrderAmountSummary(
            departmentName, addressId, timeAggType, timeLength);

        assertEquals(1, orderAmountSummary.size());
        assertEquals(entity2.getDepartment(), orderAmountSummary.getFirst().getDepartment());
        assertEquals(entity2.getDateType(), orderAmountSummary.getFirst().getDateType());
    }

    @Test
    void testGetOrderFrequencyV2() {
        String addressId = UUID.randomUUID().toString();
        String timeAggType = "DAILY";
        Integer timeLength = 1;

        MetricsOrderFrequencyEntity entity = MetricsOrderFrequencyEntityMock.metricsOrderFrequencyEntityMock(
            addressId,
            timeAggType);

        metricsOrderFrequencyRepository.save(entity);

        List<MetricsOrderFrequencyDto> frequencies = orderRestApi.getOrderFrequencyV2(addressId, timeAggType, timeLength);

        assertEquals(1, frequencies.size());
        assertEquals(entity.getFrequency(),frequencies.getFirst().getFrequency());
    }

    @Test
    void testGetOrderDepartmentDistribution() {
        String addressId = UUID.randomUUID().toString();
        String timeAggType = "DAILY";
        Integer timeLength = 15;

        MetricsOrderDepartmentDistributionEntity entity = MetricsOrderDepartmentDistributionEntityMock.metricsOrderDepartmentDistributionEntityMock(
            addressId,
            timeAggType);

        metricsOrderDepartmentDistributionRepository.save(entity);

        List<MetricsOrderDepartmentDistributionDto> frequencies = orderRestApi.getOrderDepartmentDistribution(addressId,
            timeAggType.toString(),
            timeLength);

        assertEquals(1, frequencies.size());
        assertEquals(entity.getDateType().toString(), frequencies.getFirst().getDateType());
    }


    @Test
    void testGetOrderDiscountSummary() {
        String addressId = UUID.randomUUID().toString();
        String timeAggType = "DAILY";
        Integer timeLength = 1;

        MetricsOrderDiscountEntity entity = MetricsOrderDiscountEntityMock.metricsOrderDiscountEntityMock(
            addressId,
            timeAggType,timeLength);

        metricsOrderDiscountRepository.save(entity);

        MetricsOrderDiscountSummaryDto result = orderRestApi.getOrderDiscountSummary(addressId,
            timeAggType.toString(),
            timeLength);

        assertNotNull(result);
        assertEquals(entity.getDiscountOrderCount(),result.getDiscountOrderCount());
        assertEquals(entity.getTotalOrderAmount().intValue(),result.getTotalOrderAmount().intValue());
    }

    @Test
    void testGetOrderDiscountSummaryNotFound() {
        String addressId = UUID.randomUUID().toString();
        String timeAggType = "DAILY";
        Integer timeLength = 1;

        MetricsOrderDiscountEntity entity = MetricsOrderDiscountEntityMock.metricsOrderDiscountEntityMock(
            addressId,
            timeAggType,timeLength);

        metricsOrderDiscountRepository.save(entity);

        MetricsOrderDiscountSummaryDto result = orderRestApi.getOrderDiscountSummary(addressId,
            "MONTHLY",
            timeLength);

        assertNull(result);
    }
}
