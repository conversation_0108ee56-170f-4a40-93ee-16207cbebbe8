package com.mercaso.data.master_catalog.utils.entity;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogLocationUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    public static MasterCatalogLocation buildMasterCatalogLocation(UUID storeId, String locationId) {
        ObjectNode metadata = objectMapper.createObjectNode();
        metadata.put("locationId", locationId);
        metadata.put("name", "Test Location");
        metadata.put("status", "ACTIVE");
        metadata.put("type", "PHYSICAL");

        return MasterCatalogLocation.builder()
            .id(UUID.randomUUID())
            .storeId(storeId)
            .metadata(metadata)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }
} 