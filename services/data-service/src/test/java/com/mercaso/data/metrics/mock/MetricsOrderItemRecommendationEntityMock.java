package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.MetricsOrderItemRecommendationEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MetricsOrderItemRecommendationEntityMock {

    public static MetricsOrderItemRecommendationEntity metricsOrderItemRecommendationEntityMock(String type,
        String filterDepartment, String department, String addressId) {
        MetricsOrderItemRecommendationEntity entity = new MetricsOrderItemRecommendationEntity();
        entity.setAddressId(addressId);
        entity.setRank(1);
        entity.setType(type);
        entity.setAmount(BigDecimal.ONE);
        entity.setSku("sku_" + Math.random() * 1000);
        entity.setName("name_" + Math.random() * 1000);
        entity.setDepartment(department);
        entity.setLastOrderedDate(LocalDateTime.now());
        entity.setFilterDepartment(filterDepartment);
        entity.setCreatedAt(LocalDateTime.now());
        return entity;
    }

}
