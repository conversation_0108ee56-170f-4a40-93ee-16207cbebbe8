package com.mercaso.data.recommendation.controller;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.dto.PriceRecommendationDto;
import com.mercaso.data.recommendation.entity.PriceRecommendation;
import com.mercaso.data.recommendation.repository.PriceRecommendationRepository;
import com.mercaso.data.recommendation.utils.resource_utils.PriceRecommendationResourceApiUtils;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class PriceRecommendationsResourceIT extends AbstractIT {

  @Autowired
  private PriceRecommendationRepository priceRecommendationRepository;

  @Autowired
  private PriceRecommendationResourceApiUtils priceRecommendationResourceApiUtils;

  @Test
  void getDepartments() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId1 = UUID.randomUUID().toString();
    String departmentId2 = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId1);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId2);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId2);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    List<DepartmentDto> departmentDtos = priceRecommendationResourceApiUtils.searchDepartments(
        storeId);

    assertNotNull(departmentDtos);
    assertEquals(2, departmentDtos.size());
  }

  @Test
  void searchAllPriceRecommendations() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    String defaultStoreId = "b350491e-e238-4caf-b328-cf2438e057d8";
    PriceRecommendation defaultPrice1= mockData(defaultStoreId, departmentId);
    PriceRecommendation defaultPrice2 = mockData(defaultStoreId, departmentId);

    PriceRecommendation pr1 = mockData(storeId, departmentId);
    PriceRecommendation pr2 = mockData(storeId, departmentId);
    pr1.setLastPurchasePrice("50.00");
    pr2.setLastPurchasePrice("55.00");
    pr1.setUpc(defaultPrice1.getUpc());
    pr2.setUpc(defaultPrice2.getUpc());

    priceRecommendationRepository.saveAllAndFlush(List.of(pr1, pr2, defaultPrice1, defaultPrice2));

    PageableResponse<PriceRecommendationDto> response = priceRecommendationResourceApiUtils
        .searchAll(storeId, null);

    assertNotNull(response);
    assert response.getData().stream().filter(price -> price.getLastPurchasePrice() != null).map(price -> price.getLastPurchasePrice().toString()).toList().containsAll(List.of("50.00", "55.00"));
  }

  @Test
  void searchPriceRecommendations() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, null, false, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(3, data.size());

  }

  @Test
  void searchWithNotExistName() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation = mockData(storeId, departmentId);

    priceRecommendationRepository.save(priceRecommendation);

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, "notExistName", false, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(0, data.size());

  }

  @Test
  void searchLatestOrderPriceRecommendation() throws Exception {
    String storeId = UUID.randomUUID().toString();
    String departmentId = UUID.randomUUID().toString();
    PriceRecommendation priceRecommendation1 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation2 = mockData(storeId, departmentId);
    PriceRecommendation priceRecommendation3 = mockData(storeId, departmentId);

    priceRecommendation1.setLatestOrder(true);
    priceRecommendation2.setLatestOrder(false);
    priceRecommendation3.setLatestOrder(true);

    priceRecommendationRepository.saveAll(List.of(priceRecommendation1, priceRecommendation2,
        priceRecommendation3));

    PageableResponse<PriceRecommendationDto> priceRecommendationDtoPageableResponse = priceRecommendationResourceApiUtils
        .searchPriceRecommendations(storeId, departmentId, null, true, 0, 10);

    assertNotNull(priceRecommendationDtoPageableResponse);
    List<PriceRecommendationDto> data = priceRecommendationDtoPageableResponse.getData();
    assertNotNull(data);
    assertEquals(2, data.size());
  }

  @Test
  void searchEverGreenRecommendations() throws Exception {
    String departmentId = UUID.randomUUID().toString();
    String storeId = UUID.randomUUID().toString();
    String defaultStoreId = "b350491e-e238-4caf-b328-cf2438e057d8";
    PriceRecommendation pr1 = mockData(defaultStoreId, departmentId);
    PriceRecommendation pr2 = mockData(defaultStoreId, departmentId);
    PriceRecommendation pr3 = mockData(storeId, departmentId);
    pr1.setEverGreen(true);
    priceRecommendationRepository.saveAll(List.of(pr1, pr2, pr3));

    PageableResponse<PriceRecommendationDto> response = priceRecommendationResourceApiUtils
        .searchEverGreenRecommendations(departmentId);

    assertNotNull(response);
    assertNotNull(response.getData());
    assertEquals(1, response.getData().size());
    assertEquals(1, response.getTotalPages());
  }

  @Test
  void getEverGreenDepartments() throws Exception {
    PriceRecommendation rec1 = PriceRecommendation.builder()
        .departmentId("dept1").department("Electronics")
        .storeId("b350491e-e238-4caf-b328-cf2438e057d8")
        .everGreen(true).build();
    PriceRecommendation rec2 = PriceRecommendation.builder()
        .departmentId("dept2").department("Home")
        .storeId("b350491e-e238-4caf-b328-cf2438e057d8")
        .everGreen(true).build();

    PriceRecommendation rec3 = PriceRecommendation.builder()
        .departmentId("dept1").department("Electronics")
        .storeId("b350491e-e238-4caf-b328-cf2438e057d8")
        .everGreen(true).build();

    priceRecommendationRepository.save(rec1);
    priceRecommendationRepository.save(rec2);
    priceRecommendationRepository.save(rec3);


    List<DepartmentDto> departments = priceRecommendationResourceApiUtils
        .searchEverGreenDepartments();

    assertTrue(departments.stream().anyMatch(d -> "dept1".equals(d.id())));
    assertTrue(departments.stream().anyMatch(d -> "dept2".equals(d.id())));
  }

  private PriceRecommendation mockData(String storeId, String departmentId) {
    return PriceRecommendation.builder()
        .storeId(storeId)
        .productId(RandomStringUtils.randomNumeric(13))
        .sku("DW" + RandomStringUtils.randomNumeric(6))
        .name(RandomStringUtils.randomAlphabetic(10))
        .highPrice("44.88")
        .lowPrice("40.99")
        .avgPrice("42.99")
        .lastPurchasePrice("38.99")
        .departmentId(departmentId)
        .department(departmentId)
        .upc(RandomStringUtils.randomNumeric(14))
        .build();
  }

}