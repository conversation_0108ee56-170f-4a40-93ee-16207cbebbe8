package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SquareDataSyncResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V1_SQUARE_DATA_SYNC_ALL = "/master-catalog/v1/square-data-sync/all";

    public SquareDataSyncResourceApiUtils(Environment environment) {
        super(environment);
    }

    public void syncSquareData() {
        postEntity(V1_SQUARE_DATA_SYNC_ALL,
            SquareDataSyncRequest.builder().storeId(UUID.randomUUID()).build(),
            Void.class);
    }
}
