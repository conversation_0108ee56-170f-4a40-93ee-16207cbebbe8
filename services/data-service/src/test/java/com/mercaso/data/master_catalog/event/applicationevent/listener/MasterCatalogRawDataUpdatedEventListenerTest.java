package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.event.model.domain.MasterCatalogRawDataUpdatedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.MasterCatalogRawDataUpdatedPayload;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

public class MasterCatalogRawDataUpdatedEventListenerTest {

    private MasterCatalogRawDataDuplicationRepository duplicationRepository;
    private MasterCatalogProductRepository productRepository;
    private MasterCatalogProductAssociationRepository productAssociationRepository;
    private MasterCatalogRawDataService rawDataService;
    private TransactionTemplate transactionTemplate;
    private MasterCatalogRawDataUpdatedEventListener listener;

    @BeforeEach
    void setUp() {
        duplicationRepository = mock(MasterCatalogRawDataDuplicationRepository.class);
        productRepository = mock(MasterCatalogProductRepository.class);
        productAssociationRepository = mock(MasterCatalogProductAssociationRepository.class);
        rawDataService = mock(MasterCatalogRawDataService.class);
        transactionTemplate = mock(TransactionTemplate.class);

        // Mock transactionTemplate to execute the callback immediately
        when(transactionTemplate.execute(any(TransactionCallback.class))).thenAnswer(invocation -> {
            TransactionCallback<?> callback = invocation.getArgument(0);
            return callback.doInTransaction(null);
        });

        listener = new MasterCatalogRawDataUpdatedEventListener(
            duplicationRepository,
            productRepository,
            productAssociationRepository,
            rawDataService,
            transactionTemplate
        );
    }

    @Test
    void testHandleEvent_WhenDuplicationGroupExists_ShouldDeleteGroupAndProducts() {
        // Given
        UUID rawDataId = UUID.randomUUID();
        String upc = "123456789";
        UUID groupId = UUID.randomUUID();
        
        MasterCatalogRawData rawData = createRawData(rawDataId, upc);
        MasterCatalogRawDataDuplication duplication1 = createDuplication("123456789", groupId);
        MasterCatalogRawDataDuplication duplication2 = createDuplication("987654321", groupId);
        List<MasterCatalogRawDataDuplication> duplications = Arrays.asList(duplication1);
        List<MasterCatalogRawDataDuplication> allDuplicationsInGroup = Arrays.asList(duplication1, duplication2);
        
        MasterCatalogProduct product1 = createProduct("123456789", false);
        MasterCatalogProduct product2 = createProduct("987654321", true);
        List<MasterCatalogProduct> products = Arrays.asList(product1, product2);

        MasterCatalogRawDataUpdatedPayload payload = createPayload(rawDataId, rawData);
        MasterCatalogRawDataUpdatedEvent event = new MasterCatalogRawDataUpdatedEvent(this, payload);

        when(duplicationRepository.findByUpc(upc)).thenReturn(duplications);
        when(duplicationRepository.findAllByDuplicationGroup(groupId)).thenReturn(allDuplicationsInGroup);
        when(productRepository.findAllByUpcIn(Arrays.asList("123456789", "987654321"))).thenReturn(products);

        // Mock association cleanup for associated product
        UUID associationGroupId = UUID.randomUUID();
        MasterCatalogProductAssociation association = createAssociation("987654321", associationGroupId);
        when(productAssociationRepository.findAllByUpcIn(List.of("987654321"))).thenReturn(List.of(association));
        when(productAssociationRepository.findByAssociationGroup(associationGroupId)).thenReturn(Collections.emptyList());

        // When
        listener.handleEvent(event);

        // Then
        verify(duplicationRepository).deleteAll(allDuplicationsInGroup);
        verify(productRepository).deleteAll(products);
        verify(productAssociationRepository).delete(association);
        verify(rawDataService).syncMasterCatalogRawData(rawData);
    }

    @Test
    void testHandleEvent_WhenNoDuplicationGroup_ShouldDeleteProductsByRawDataId() {
        // Given
        UUID rawDataId = UUID.randomUUID();
        String upc = "123456789";
        
        MasterCatalogRawData rawData = createRawData(rawDataId, upc);
        MasterCatalogProduct product = createProduct(upc, false);

        MasterCatalogRawDataUpdatedPayload payload = createPayload(rawDataId, rawData);
        MasterCatalogRawDataUpdatedEvent event = new MasterCatalogRawDataUpdatedEvent(this, payload);

        when(duplicationRepository.findByUpc(upc)).thenReturn(Collections.emptyList());
        when(productRepository.findByMasterCatalogRawDataId(rawDataId)).thenReturn(Optional.of(product));

        // When
        listener.handleEvent(event);

        // Then
        verify(productRepository).delete(product);
        verify(productAssociationRepository, never()).findAllByUpcIn(anyList());
        verify(rawDataService).syncMasterCatalogRawData(rawData);
    }

    @Test
    void testHandleEvent_WhenProductIsAssociated_ShouldCleanupAssociations() {
        // Given
        UUID rawDataId = UUID.randomUUID();
        String upc = "123456789";
        UUID associationGroupId = UUID.randomUUID();
        
        MasterCatalogRawData rawData = createRawData(rawDataId, upc);
        MasterCatalogProduct associatedProduct = createProduct(upc, true);

        MasterCatalogProductAssociation association = createAssociation(upc, associationGroupId);
        MasterCatalogProductAssociation remainingAssociation = createAssociation("999999999", associationGroupId);

        MasterCatalogRawDataUpdatedPayload payload = createPayload(rawDataId, rawData);
        MasterCatalogRawDataUpdatedEvent event = new MasterCatalogRawDataUpdatedEvent(this, payload);

        when(duplicationRepository.findByUpc(upc)).thenReturn(Collections.emptyList());
        when(productRepository.findByMasterCatalogRawDataId(rawDataId)).thenReturn(Optional.of(associatedProduct));
        when(productAssociationRepository.findAllByUpcIn(List.of(upc))).thenReturn(List.of(association));
        when(productAssociationRepository.findByAssociationGroup(associationGroupId)).thenReturn(List.of(remainingAssociation));

        // When
        listener.handleEvent(event);

        // Then
        verify(productRepository).delete(associatedProduct);
        verify(productAssociationRepository).delete(association);
        verify(productAssociationRepository).deleteAll(List.of(remainingAssociation));
        verify(rawDataService).syncMasterCatalogRawData(rawData);
    }

    private MasterCatalogRawDataUpdatedPayload createPayload(UUID rawDataId, MasterCatalogRawData rawData) {
        return MasterCatalogRawDataUpdatedPayload.builder()
            .rawDataId(rawDataId)
            .currentData(rawData)
            .previousData(null)
            .build();
    }

    private MasterCatalogRawData createRawData(UUID id, String upc) {
        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(id);
        rawData.setUpc(upc);
        return rawData;
    }

    private MasterCatalogRawDataDuplication createDuplication(String upc, UUID groupId) {
        return MasterCatalogRawDataDuplication.builder()
            .id(UUID.randomUUID())
            .upc(upc)
            .duplicationGroup(groupId)
            .build();
    }

    private MasterCatalogProduct createProduct(String upc, boolean associated) {
        return MasterCatalogProduct.builder()
            .id(UUID.randomUUID())
            .upc(upc)
            .name("Test Product")
            .description("Test Description")
            .associated(associated)
            .build();
    }

    private MasterCatalogProductAssociation createAssociation(String upc, UUID associationGroup) {
        return MasterCatalogProductAssociation.builder()
            .id(UUID.randomUUID())
            .upc(upc)
            .associationGroup(associationGroup)
            .build();
    }
}
