package com.mercaso.data.third_party.controller.shopify;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.third_party.dto.shopify.ShopifyOrderFilter;
import com.mercaso.data.third_party.dto.shopify.ShopifyProductFilter;
import com.mercaso.data.third_party.utils.controller_utils.ShopifyRestApi;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;


class ShopifyControllerIT extends AbstractIT {

    @Autowired
    protected ShopifyRestApi shopifyRestApi;

    @Test
    void testGetOrders() {

        ShopifyOrderFilter shopifyOrderFilter = new ShopifyOrderFilter();
        shopifyOrderFilter.setPageable(new CustomPageable(1, 10));
        ResponseEntity<CustomPage> orders = shopifyRestApi.getOrders(shopifyOrderFilter);
        assertNotNull(orders);
    }

    @Test
    void testGetProducts() {

        ShopifyProductFilter shopifyProductFilter = new ShopifyProductFilter();
        shopifyProductFilter.setPageable(new CustomPageable(1, 10));
        ResponseEntity<CustomPage> products = shopifyRestApi.getProducts(shopifyProductFilter);
        assertNotNull(products);
    }
}
