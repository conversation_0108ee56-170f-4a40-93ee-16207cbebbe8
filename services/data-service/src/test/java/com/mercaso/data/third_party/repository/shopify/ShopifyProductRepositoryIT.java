package com.mercaso.data.third_party.repository.shopify;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductEntity;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductVariantEntity;
import com.mercaso.data.third_party.mock.shopify.ShopifyProductEntityMock;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

class ShopifyProductRepositoryIT extends AbstractIT {

    @Autowired
    private ShopifyProductRepository productRepository;


    @Test
    void testFindAll() {
        ShopifyProductEntity shopifyProductEntity = ShopifyProductEntityMock.productEntityMock();
        productRepository.save(shopifyProductEntity);

        List<ShopifyProductEntity> resultList = productRepository.findAll();
        Assertions.assertNotEquals(0, resultList.size());
    }

    @Test
    void testFindBySku() {

        ShopifyProductEntity shopifyProductEntity = ShopifyProductEntityMock.productEntityMock();
        ShopifyProductVariantEntity first = shopifyProductEntity.getVariants().getFirst();
        first.setSku("M-65955");
        productRepository.save(shopifyProductEntity);

        PageRequest pageRequest = PageRequest.of(0, 10, Sort.unsorted());
        Page<ShopifyProductEntity> result = productRepository.findByVariantSkuInJsonb(List.of("M-65955"), pageRequest);
        Assertions.assertNotNull(result);
    }
}