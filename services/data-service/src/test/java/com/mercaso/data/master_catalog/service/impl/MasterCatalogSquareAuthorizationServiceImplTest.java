package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.mapper.MasterCatalogSquareAuthorizationMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogSquareAuthorizationMapperImpl;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class MasterCatalogSquareAuthorizationServiceImplTest {

    private final MasterCatalogSquareAuthorizationRepository repository = mock(MasterCatalogSquareAuthorizationRepository.class);

    private final CipherUtility cipherUtility = mock(CipherUtility.class);

    private final SquareApiAdapter squareApiAdapter = mock(SquareApiAdapter.class);

    private final MasterCatalogSquareAuthorizationMapper mapper = new MasterCatalogSquareAuthorizationMapperImpl();

    private final MasterCatalogSquareAuthorizationServiceImpl service = new MasterCatalogSquareAuthorizationServiceImpl(repository,
      cipherUtility, mapper, squareApiAdapter);

    private MasterCatalogSquareAuthorizationRequestDto requestDto;
    private MasterCatalogSquareAuthorization entity;
    private MasterCatalogSquareAuthorizationDto responseDto;
    private UUID storeId;
    private UUID state;
    private List<String> permissions;

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        state = UUID.randomUUID();
        permissions = Arrays.asList("READ", "WRITE");

        requestDto = new MasterCatalogSquareAuthorizationRequestDto();
        requestDto.setStoreId(storeId);
        requestDto.setState(state);
        requestDto.setApplicationId("app123");
        requestDto.setApplicationSecret("secret123");
        requestDto.setPermissions(permissions);

        entity = new MasterCatalogSquareAuthorization();
        entity.setStoreId(storeId);
        entity.setState(state);
        entity.setEncryptedApplicationId("encrypted_app123");
        entity.setEncryptedApplicationSecret("encrypted_secret123");
        entity.setPermissions(String.join(",", permissions));

        responseDto = new MasterCatalogSquareAuthorizationDto();
        responseDto.setStoreId(storeId);
        responseDto.setState(state);
        responseDto.setPermissions(String.join(",", permissions));
    }

    @Test
    @DisplayName("Should successfully create square authorization")
    void shouldCreateSquareAuthorization() {
        // Given
        when(cipherUtility.encrypt("app123")).thenReturn("encrypted_app123");
        when(cipherUtility.encrypt("secret123")).thenReturn("encrypted_secret123");
        when(repository.save(any(MasterCatalogSquareAuthorization.class))).thenReturn(entity);

        // When
        MasterCatalogSquareAuthorizationDto result = service.createSquareAuthorization(requestDto);

        // Then
        assertNotNull(result);
        assertEquals(storeId, result.getStoreId());
        assertEquals(state, result.getState());
        assertEquals(String.join(",", permissions), result.getPermissions());

        verify(cipherUtility).encrypt("app123");
        verify(cipherUtility).encrypt("secret123");
        verify(repository).save(any(MasterCatalogSquareAuthorization.class));
    }

    @Test
    @DisplayName("Should create authorization with token info provided")
    void shouldCreateAuthorizationWithTokenInfoProvided() {
        requestDto.setAccessToken("accessToken123");
        requestDto.setRefreshToken("refreshToken123");
        requestDto.setAccessTokenExpiresAt(Instant.now().plus(30, ChronoUnit.DAYS));

        // Given
        when(cipherUtility.encrypt("app123")).thenReturn("encrypted_app123");
        when(cipherUtility.encrypt("secret123")).thenReturn("encrypted_secret123");
        when(cipherUtility.encrypt("accessToken123")).thenReturn("encrypted_accessToken123");
        when(cipherUtility.encrypt("refreshToken123")).thenReturn("encrypted_refreshToken123");
        when(repository.save(any(MasterCatalogSquareAuthorization.class))).thenReturn(entity);

        // When
        MasterCatalogSquareAuthorizationDto result = service.createSquareAuthorization(requestDto);

        // Then
        assertNotNull(result);
        assertEquals(storeId, result.getStoreId());
        assertEquals(state, result.getState());
        assertEquals(String.join(",", permissions), result.getPermissions());

        verify(cipherUtility).encrypt("app123");
        verify(cipherUtility).encrypt("secret123");
        verify(cipherUtility).encrypt("accessToken123");
        verify(cipherUtility).encrypt("refreshToken123");
        verify(repository).save(any(MasterCatalogSquareAuthorization.class));
    }

    @Test
    @DisplayName("Should encrypt application credentials when creating authorization")
    void shouldEncryptCredentialsWhenCreatingAuthorizationTest() {
        // Given
        when(cipherUtility.encrypt("app123")).thenReturn("encrypted_app123");
        when(cipherUtility.encrypt("secret123")).thenReturn("encrypted_secret123");
        when(repository.save(any(MasterCatalogSquareAuthorization.class))).thenReturn(entity);

        // When
        service.createSquareAuthorization(requestDto);

        // Then
        verify(cipherUtility).encrypt("app123");
        verify(cipherUtility).encrypt("secret123");
    }

    @Test
    @DisplayName("Should preserve all request fields in created entity")
    void shouldPreserveAllRequestFieldsTest() {
        // Given
        when(cipherUtility.encrypt(anyString())).thenReturn("encrypted");
        when(repository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        service.createSquareAuthorization(requestDto);

        // Then
        verify(repository).save(argThat(savedEntity -> {
            assertEquals(requestDto.getStoreId(), savedEntity.getStoreId());
            assertEquals(requestDto.getState(), savedEntity.getState());
            assertEquals(String.join(",", requestDto.getPermissions()), savedEntity.getPermissions());
            return true;
        }));
    }

    @Test
    @DisplayName("Test refresh square authorization when access token is about to expire in 24 hours")
    void testRefreshSquareAuthorization_accessTokenAboutExpireIn24Hours() {
        // Arrange
        MasterCatalogSquareAuthorization authorization = new MasterCatalogSquareAuthorization();
        authorization.setStoreId(UUID.randomUUID());
        authorization.setAccessTokenExpiresAt(Instant.now().plusSeconds(3600)); // Token expires in 1 hour
        authorization.setEncryptedRefreshToken("encryptedRefreshToken");
        authorization.setEncryptedApplicationId("encryptedApplicationId");
        authorization.setEncryptedApplicationSecret("encryptedApplicationSecret");

        when(repository.findAll()).thenReturn(Collections.singletonList(authorization));
        when(cipherUtility.decrypt(any())).thenReturn("decryptedValue");
        when(squareApiAdapter.obtainTokenByRefreshToken(any(), any(), any()))
          .thenReturn(SquareObtainTokenResponse.builder()
            .accessToken("accessToken")
            .expiresAt(Instant.now().plusSeconds(7200).toString())
            .refreshToken("refreshToken")
            .build());

        // Act
        service.refreshSquareAuthorization();

        // Assert
        verify(repository, times(1)).findAll();
        verify(repository, times(1)).save(any(MasterCatalogSquareAuthorization.class));
        verify(squareApiAdapter, times(1)).obtainTokenByRefreshToken(any(), any(), any());
    }

    @Test
    @DisplayName("Test refresh square authorization when access token is not about to expire in 24 hours")
    void testRefreshSquareAuthorization_accessTokenNotAboutExpireIn24Hours() {
        // Arrange
        MasterCatalogSquareAuthorization authorization = new MasterCatalogSquareAuthorization();
        authorization.setStoreId(UUID.randomUUID());
        authorization.setEncryptedAccessToken("encryptedAccessToken");
        authorization.setAccessTokenExpiresAt(Instant.now().plusSeconds(87400)); // Token expires in 24 hours
        authorization.setEncryptedRefreshToken("encryptedRefreshToken");
        authorization.setEncryptedApplicationId("encryptedApplicationId");
        authorization.setEncryptedApplicationSecret("encryptedApplicationSecret");

        when(repository.findAll()).thenReturn(Collections.singletonList(authorization));
        when(cipherUtility.decrypt(any())).thenReturn("decryptedValue");

        // Act
        service.refreshSquareAuthorization();

        // Assert
        verify(repository, times(1)).findAll();
        verify(repository, never()).save(any(MasterCatalogSquareAuthorization.class));
        verify(squareApiAdapter, never()).obtainTokenByRefreshToken(any(), any(), any());
    }
} 