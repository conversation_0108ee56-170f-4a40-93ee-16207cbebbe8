package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogSquareOrderLineItem;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogSquareOrderLineItemUtils {

    public static MasterCatalogSquareOrderLineItem buildMasterCatalogSquareOrderLineItem(UUID masterCatalogOrderId,
        UUID masterCatalogRawDataId) {
        MasterCatalogSquareOrderLineItem masterCatalogOrderLineItem = new MasterCatalogSquareOrderLineItem();
        masterCatalogOrderLineItem.setQuantity(1);
        masterCatalogOrderLineItem.setMasterCatalogRawDataId(masterCatalogRawDataId);
        masterCatalogOrderLineItem.setMasterCatalogOrderId(masterCatalogOrderId);
        return masterCatalogOrderLineItem;

    }
}
