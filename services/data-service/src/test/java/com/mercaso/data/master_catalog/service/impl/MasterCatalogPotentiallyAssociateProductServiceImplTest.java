package com.mercaso.data.master_catalog.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyAssociateProductDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyAssociateProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.PotentiallyAssociateProductStatus;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyAssociateProductMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyAssociateProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

class MasterCatalogPotentiallyAssociateProductServiceImplTest {

    private MasterCatalogPotentiallyAssociateProductRepository potentiallyAssociateProductRepository;
    private MasterCatalogProductRepository masterCatalogProductRepository;
    private MasterCatalogImageRepository masterCatalogImageRepository;
    private S3OperationAdapter s3OperationAdapter;
    private MasterCatalogPotentiallyAssociateProductMapper mapper;
    private MasterCatalogBatchJobRepository batchJobRepository;
    private MasterCatalogTaskRepository masterCatalogTaskRepository;
    private MasterCatalogTaskService masterCatalogTaskService;
    private MasterCatalogProductAssociationRepository productAssociationRepository;
    private MasterCatalogPotentiallyAssociateProductServiceImpl service;

    @BeforeEach
    void setUp() {
        potentiallyAssociateProductRepository = mock(MasterCatalogPotentiallyAssociateProductRepository.class);
        masterCatalogProductRepository = mock(MasterCatalogProductRepository.class);
        masterCatalogImageRepository = mock(MasterCatalogImageRepository.class);
        s3OperationAdapter = mock(S3OperationAdapter.class);
        mapper = mock(MasterCatalogPotentiallyAssociateProductMapper.class);
        batchJobRepository = mock(MasterCatalogBatchJobRepository.class);
        masterCatalogTaskRepository = mock(MasterCatalogTaskRepository.class);
        masterCatalogTaskService = mock(MasterCatalogTaskService.class);
        productAssociationRepository = mock(MasterCatalogProductAssociationRepository.class);

        service = new MasterCatalogPotentiallyAssociateProductServiceImpl(
                s3OperationAdapter,
                masterCatalogTaskService,
                batchJobRepository,
                masterCatalogTaskRepository,
                masterCatalogImageRepository,
                masterCatalogProductRepository,
                productAssociationRepository,
                potentiallyAssociateProductRepository,
                mapper
        );
        service.setTASK_RECORD_COUNT(20);
    }

    @Test
    void search_withValidTaskIdAndNoStatus_shouldReturnResults() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("createdAt")));

        // Create test entities
        MasterCatalogPotentiallyAssociateProduct entity = createPotentiallyAssociateProduct(
                taskId, jobId, productId1, productId2);
        Page<MasterCatalogPotentiallyAssociateProduct> entityPage = new PageImpl<>(
                Collections.singletonList(entity), pageRequest, 1);

        MasterCatalogProduct product1 = createProduct(productId1, rawDataId1);
        MasterCatalogProduct product2 = createProduct(productId2, rawDataId2);
        List<MasterCatalogProduct> products = Arrays.asList(product1, product2);

        MasterCatalogImage image1 = createImage(rawDataId1, "path/to/image1.jpg");
        MasterCatalogImage image2 = createImage(rawDataId2, "path/to/image2.jpg");
        List<MasterCatalogImage> images = Arrays.asList(image1, image2);

        MasterCatalogPotentiallyAssociateProductDto dto = createDto(entity);

        // Mock repository calls
        when(potentiallyAssociateProductRepository.findAll(any(Specification.class), eq(pageRequest)))
                .thenReturn(entityPage);
        when(masterCatalogProductRepository.findAllById(anyList()))
                .thenReturn(products);
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
                .thenReturn(images);
        when(mapper.toDto(entity)).thenReturn(dto);
        when(s3OperationAdapter.getSignedUrl("path/to/image1.jpg")).thenReturn("signed-url-1");
        when(s3OperationAdapter.getSignedUrl("path/to/image2.jpg")).thenReturn("signed-url-2");

        // When
        Page<MasterCatalogPotentiallyAssociateProductDto> result = service.search(taskId, null, pageRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        
        MasterCatalogPotentiallyAssociateProductDto resultDto = result.getContent().get(0);
        assertThat(resultDto.getTaskId()).isEqualTo(taskId);
        assertThat(resultDto.getJobId()).isEqualTo(jobId);
        assertThat(resultDto.getProductId()).isEqualTo(productId1);
        assertThat(resultDto.getPotentiallyAssociateProductId()).isEqualTo(productId2);

        // Verify repository interactions
        verify(potentiallyAssociateProductRepository).findAll(any(Specification.class), eq(pageRequest));
        verify(masterCatalogProductRepository).findAllById(anyList());
        verify(masterCatalogImageRepository).findAllByMasterCatalogRawDataIdIn(anyList());
        verify(mapper).toDto(entity);
    }

    @Test
    void search_withValidTaskIdAndStatus_shouldFilterByStatus() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        PotentiallyAssociateProductStatus status = PotentiallyAssociateProductStatus.COMPLETED;
        
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("createdAt")));

        // Create test entities
        MasterCatalogPotentiallyAssociateProduct entity = createPotentiallyAssociateProduct(
                taskId, jobId, productId1, productId2);
        entity.setStatus(status);
        Page<MasterCatalogPotentiallyAssociateProduct> entityPage = new PageImpl<>(
                Collections.singletonList(entity), pageRequest, 1);

        MasterCatalogProduct product1 = createProduct(productId1, rawDataId1);
        MasterCatalogProduct product2 = createProduct(productId2, rawDataId2);
        List<MasterCatalogProduct> products = Arrays.asList(product1, product2);

        MasterCatalogImage image1 = createImage(rawDataId1, "path/to/image1.jpg");
        List<MasterCatalogImage> images = Collections.singletonList(image1);

        MasterCatalogPotentiallyAssociateProductDto dto = createDto(entity);

        // Mock repository calls
        when(potentiallyAssociateProductRepository.findAll(any(Specification.class), eq(pageRequest)))
                .thenReturn(entityPage);
        when(masterCatalogProductRepository.findAllById(anyList()))
                .thenReturn(products);
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
                .thenReturn(images);
        when(mapper.toDto(entity)).thenReturn(dto);
        when(s3OperationAdapter.getSignedUrl("path/to/image1.jpg")).thenReturn("signed-url-1");

        // When
        Page<MasterCatalogPotentiallyAssociateProductDto> result = service.search(taskId, status, pageRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        
        MasterCatalogPotentiallyAssociateProductDto resultDto = result.getContent().get(0);
        assertThat(resultDto.getStatus()).isEqualTo(status);

        // Verify repository interactions
        verify(potentiallyAssociateProductRepository).findAll(any(Specification.class), eq(pageRequest));
        verify(masterCatalogProductRepository).findAllById(anyList());
        verify(masterCatalogImageRepository).findAllByMasterCatalogRawDataIdIn(anyList());
        verify(mapper).toDto(entity);
    }

    @Test
    void search_withEmptyResults_shouldReturnEmptyPage() {
        // Given
        UUID taskId = UUID.randomUUID();
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("createdAt")));
        Page<MasterCatalogPotentiallyAssociateProduct> emptyPage = Page.empty();

        // Mock repository calls
        when(potentiallyAssociateProductRepository.findAll(any(Specification.class), eq(pageRequest)))
                .thenReturn(emptyPage);

        // When
        Page<MasterCatalogPotentiallyAssociateProductDto> result = service.search(taskId, null, pageRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isEmpty()).isTrue();
        assertThat(result.getContent()).isEmpty();

        // Verify repository interactions
        verify(potentiallyAssociateProductRepository).findAll(any(Specification.class), eq(pageRequest));
    }

    @Test
    void search_withNoProductsFound_shouldReturnEmptyPage() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("createdAt")));

        // Create test entities
        MasterCatalogPotentiallyAssociateProduct entity = createPotentiallyAssociateProduct(
                taskId, jobId, productId1, productId2);
        Page<MasterCatalogPotentiallyAssociateProduct> entityPage = new PageImpl<>(
                Collections.singletonList(entity), pageRequest, 1);

        // Mock repository calls - no products found
        when(potentiallyAssociateProductRepository.findAll(any(Specification.class), eq(pageRequest)))
                .thenReturn(entityPage);
        when(masterCatalogProductRepository.findAllById(anyList()))
                .thenReturn(Collections.emptyList());

        // When
        Page<MasterCatalogPotentiallyAssociateProductDto> result = service.search(taskId, null, pageRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isEmpty()).isTrue();

        // Verify repository interactions
        verify(potentiallyAssociateProductRepository).findAll(any(Specification.class), eq(pageRequest));
        verify(masterCatalogProductRepository).findAllById(anyList());
    }

    @Test
    void search_withImagesPresent_shouldPopulateImagesCorrectly() {
        // Given
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        
        PageRequest pageRequest = PageRequest.of(0, 10, Sort.by(Sort.Order.desc("createdAt")));

        // Create test entities
        MasterCatalogPotentiallyAssociateProduct entity = createPotentiallyAssociateProduct(
                taskId, jobId, productId1, productId2);
        Page<MasterCatalogPotentiallyAssociateProduct> entityPage = new PageImpl<>(
                Collections.singletonList(entity), pageRequest, 1);

        MasterCatalogProduct product1 = createProduct(productId1, rawDataId1);
        MasterCatalogProduct product2 = createProduct(productId2, rawDataId2);
        List<MasterCatalogProduct> products = Arrays.asList(product1, product2);

        // Create multiple images for each rawData
        MasterCatalogImage image1a = createImage(rawDataId1, "path/to/image1a.jpg");
        MasterCatalogImage image1b = createImage(rawDataId1, "path/to/image1b.jpg");
        MasterCatalogImage image2a = createImage(rawDataId2, "path/to/image2a.jpg");
        List<MasterCatalogImage> images = Arrays.asList(image1a, image1b, image2a);

        MasterCatalogPotentiallyAssociateProductDto dto = createDto(entity);

        // Mock repository calls
        when(potentiallyAssociateProductRepository.findAll(any(Specification.class), eq(pageRequest)))
                .thenReturn(entityPage);
        when(masterCatalogProductRepository.findAllById(anyList()))
                .thenReturn(products);
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList()))
                .thenReturn(images);
        when(mapper.toDto(entity)).thenReturn(dto);
        when(s3OperationAdapter.getSignedUrl("path/to/image1a.jpg")).thenReturn("signed-url-1a");
        when(s3OperationAdapter.getSignedUrl("path/to/image1b.jpg")).thenReturn("signed-url-1b");
        when(s3OperationAdapter.getSignedUrl("path/to/image2a.jpg")).thenReturn("signed-url-2a");

        // When
        Page<MasterCatalogPotentiallyAssociateProductDto> result = service.search(taskId, null, pageRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        
        MasterCatalogPotentiallyAssociateProductDto resultDto = result.getContent().get(0);
        
        // Verify images are populated correctly
        assertThat(resultDto.getProductImages()).isNotNull();
        assertThat(resultDto.getPotentiallyAssociateProductImages()).isNotNull();

        // Verify S3 adapter was called for each image
        verify(s3OperationAdapter).getSignedUrl("path/to/image1a.jpg");
        verify(s3OperationAdapter).getSignedUrl("path/to/image1b.jpg");
        verify(s3OperationAdapter).getSignedUrl("path/to/image2a.jpg");
    }

    private MasterCatalogPotentiallyAssociateProduct createPotentiallyAssociateProduct(
            UUID taskId, UUID jobId, UUID productId, UUID potentiallyAssociateProductId) {
        return MasterCatalogPotentiallyAssociateProduct.builder()
                .taskId(taskId)
                .jobId(jobId)
                .productId(productId)
                .productName("Product Name")
                .productUpc("123456789")
                .potentiallyAssociateProductId(potentiallyAssociateProductId)
                .potentiallyAssociateProductName("Associate Product Name")
                .potentiallyAssociateProductUpc("987654321")
                .status(PotentiallyAssociateProductStatus.PENDING_REVIEW)
                .associated(false)
                .createdBy("test-user")
                .updatedBy("test-user")
                .build();
    }

    private MasterCatalogProduct createProduct(UUID productId, UUID rawDataId) {
        MasterCatalogProduct masterCatalogProduct = MasterCatalogProduct.builder()
            .name("Product Name")
            .upc("123456789")
            .description("Test description")
            .brand("Test Brand")
            .masterCatalogRawDataId(rawDataId)
            .singleProduct(true)
            .build();
        masterCatalogProduct.setId(productId);
        return masterCatalogProduct;
    }

    private MasterCatalogImage createImage(UUID rawDataId, String imagePath) {
        return MasterCatalogImage.builder()
                .id(UUID.randomUUID())
                .imagePath(imagePath)
                .masterCatalogRawDataId(rawDataId)
                .primaryImage(false)
                .build();
    }

    private MasterCatalogPotentiallyAssociateProductDto createDto(MasterCatalogPotentiallyAssociateProduct entity) {
        return MasterCatalogPotentiallyAssociateProductDto.builder()
                .id(entity.getId())
                .taskId(entity.getTaskId())
                .jobId(entity.getJobId())
                .productId(entity.getProductId())
                .productName(entity.getProductName())
                .productUpc(entity.getProductUpc())
                .potentiallyAssociateProductId(entity.getPotentiallyAssociateProductId())
                .potentiallyAssociateProductName(entity.getPotentiallyAssociateProductName())
                .potentiallyAssociateProductUpc(entity.getPotentiallyAssociateProductUpc())
                .status(entity.getStatus())
                .associated(entity.getAssociated())
                .createdBy(entity.getCreatedBy())
                .updatedBy(entity.getUpdatedBy())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    @Test
    void update_withValidData_shouldUpdateSuccessfully() {
        // Given
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        boolean associated = true;

        MasterCatalogPotentiallyAssociateProduct existingProduct = MasterCatalogPotentiallyAssociateProduct.builder()
                .taskId(taskId)
                .jobId(jobId)
                .status(PotentiallyAssociateProductStatus.PENDING_REVIEW)
                .associated(false)
                .build();
        existingProduct.setId(id);

        MasterCatalogBatchJob batchJob = MasterCatalogBatchJob.builder()
                .id(jobId)
                .status(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS)
                .build();

        MasterCatalogTask task = MasterCatalogTask.builder()
                .id(taskId)
                .jobId(jobId)
                .status(MasterCatalogTaskStatus.ASSIGNED)
                .build();

        MasterCatalogPotentiallyAssociateProduct updatedProduct = MasterCatalogPotentiallyAssociateProduct.builder()
                .taskId(taskId)
                .jobId(jobId)
                .status(PotentiallyAssociateProductStatus.IN_STAGE)
                .associated(true)
                .build();
        updatedProduct.setId(id);

        MasterCatalogPotentiallyAssociateProductDto expectedDto = MasterCatalogPotentiallyAssociateProductDto.builder()
                .id(id)
                .taskId(taskId)
                .jobId(jobId)
                .status(PotentiallyAssociateProductStatus.IN_STAGE)
                .associated(true)
                .build();

        // Mock repository calls
        when(masterCatalogTaskService.validateUserPermission(taskId,"review")).thenReturn(task);
        when(potentiallyAssociateProductRepository.findById(id))
                .thenReturn(java.util.Optional.of(existingProduct));
        when(batchJobRepository.findById(jobId))
                .thenReturn(java.util.Optional.of(batchJob));
        when(masterCatalogTaskRepository.findById(taskId))
                .thenReturn(java.util.Optional.of(task));
        when(potentiallyAssociateProductRepository.save(any(MasterCatalogPotentiallyAssociateProduct.class)))
                .thenReturn(updatedProduct);
        when(mapper.toDto(updatedProduct)).thenReturn(expectedDto);

        // When
        MasterCatalogPotentiallyAssociateProductDto result = service.update(id, associated);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
        assertThat(result.getAssociated()).isTrue();
        assertThat(result.getStatus()).isEqualTo(PotentiallyAssociateProductStatus.IN_STAGE);

        // Verify interactions
        verify(potentiallyAssociateProductRepository).findById(id);
        verify(batchJobRepository).findById(jobId);
        verify(masterCatalogTaskRepository).save(task);
        verify(potentiallyAssociateProductRepository).save(any(MasterCatalogPotentiallyAssociateProduct.class));
        verify(mapper).toDto(updatedProduct);

        // Verify task status was updated
        assertThat(task.getStatus()).isEqualTo(MasterCatalogTaskStatus.IN_PROGRESS);
    }

    @Test
    void update_withNonExistentId_shouldThrowException() {
        // Given
        UUID id = UUID.randomUUID();
        boolean associated = true;

        when(potentiallyAssociateProductRepository.findById(id))
                .thenReturn(java.util.Optional.empty());

        // When & Then
        try {
            service.update(id, associated);
        } catch (MasterCatalogBusinessException e) {
            assertThat(e.getCode()).isEqualTo("001004");
        }

        verify(potentiallyAssociateProductRepository).findById(id);
    }

    @Test
    void update_withCompletedStatus_shouldThrowException() {
        // Given
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        boolean associated = true;

        MasterCatalogPotentiallyAssociateProduct existingProduct = MasterCatalogPotentiallyAssociateProduct.builder()
                .taskId(taskId)
                .jobId(jobId)
                .status(PotentiallyAssociateProductStatus.COMPLETED)
                .associated(false)
                .build();
        existingProduct.setId(id);

        MasterCatalogTask task = MasterCatalogTask.builder().type(MasterCatalogTaskType.CREATE_ASSOCIATION)
            .jobNumber(UUID.randomUUID().toString())
            .status(MasterCatalogTaskStatus.IN_PROGRESS)
            .id(taskId)
            .assignedTo("reviewed-123")
            .build();

        when(masterCatalogTaskService.validateUserPermission(taskId, "review")).thenReturn(task);
        when(potentiallyAssociateProductRepository.findById(id))
                .thenReturn(java.util.Optional.of(existingProduct));

        // When & Then
        try {
            service.update(id, associated);
            assertThat(false).as("Expected MasterCatalogBusinessException to be thrown").isTrue();
        } catch (MasterCatalogBusinessException e) {
            assertThat(e.getArgs()[0].toString()).contains("The data is already reviewed, cannot update");
        }

        verify(potentiallyAssociateProductRepository).findById(id);
    }

    @Test
    void processPotentiallyAssociateProduct_withValidInput_shouldCreateAssociatedProducts() {
        // Given
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        List<List<UUID>> productIds = Arrays.asList(
            Arrays.asList(productId1, productId2)
        );

        MasterCatalogProduct product1 = createProduct(productId1, UUID.randomUUID());
        MasterCatalogProduct product2 = createProduct(productId2, UUID.randomUUID());

        MasterCatalogTaskDto taskDto = MasterCatalogTaskDto.builder()
            .id(taskId)
            .build();

        MasterCatalogPotentiallyAssociateProduct expectedEntity = createPotentiallyAssociateProduct(
            taskId, jobId, productId1, productId2);

        MasterCatalogPotentiallyAssociateProductDto expectedDto = createDto(expectedEntity);

        // Mock service dependencies
        when(masterCatalogTaskService.createTasks(eq(jobId), eq(MasterCatalogTaskType.CREATE_ASSOCIATION), eq(1)))
            .thenReturn(Arrays.asList(taskDto));
        when(masterCatalogProductRepository.findById(productId1))
            .thenReturn(java.util.Optional.of(product1));
        when(masterCatalogProductRepository.findById(productId2))
            .thenReturn(java.util.Optional.of(product2));
        when(mapper.mapProductToPotentialAssociateProduct(any(UUID.class), eq(jobId), eq(taskId),
            eq(product1), eq(product2), eq(PotentiallyAssociateProductStatus.PENDING_REVIEW)))
            .thenReturn(expectedEntity);
        when(mapper.toDto(expectedEntity)).thenReturn(expectedDto);

        // When
        List<MasterCatalogPotentiallyAssociateProductDto> result =
            service.processPotentiallyAssociateProduct(jobId, productIds);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.get(0)).isEqualTo(expectedDto);

        // Verify interactions
        verify(masterCatalogTaskService).createTasks(jobId, MasterCatalogTaskType.CREATE_ASSOCIATION, 1);
        verify(masterCatalogProductRepository).findById(productId1);
        verify(masterCatalogProductRepository).findById(productId2);
        verify(potentiallyAssociateProductRepository).saveAll(anyList());
        verify(mapper).mapProductToPotentialAssociateProduct(any(UUID.class), eq(jobId), eq(taskId),
            eq(product1), eq(product2), eq(PotentiallyAssociateProductStatus.PENDING_REVIEW));
    }

    @Test
    void processPotentiallyAssociateProduct_withInvalidProductIds_shouldSkipInvalidEntries() {
        // Given
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID nonExistentId = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();

        List<List<UUID>> productIds = Arrays.asList(
            Arrays.asList(productId1, productId2),  // Valid pair
            Arrays.asList(nonExistentId, productId2) // Invalid pair - first product doesn't exist
        );

        MasterCatalogProduct product1 = createProduct(productId1, UUID.randomUUID());
        MasterCatalogProduct product2 = createProduct(productId2, UUID.randomUUID());

        MasterCatalogTaskDto taskDto = MasterCatalogTaskDto.builder()
            .id(taskId)
            .build();

        MasterCatalogPotentiallyAssociateProduct validEntity = createPotentiallyAssociateProduct(
            taskId, jobId, productId1, productId2);

        MasterCatalogPotentiallyAssociateProductDto validDto = createDto(validEntity);

        // Mock service dependencies
        when(masterCatalogTaskService.createTasks(eq(jobId), eq(MasterCatalogTaskType.CREATE_ASSOCIATION), eq(1)))
            .thenReturn(Arrays.asList(taskDto));
        when(masterCatalogProductRepository.findById(productId1))
            .thenReturn(java.util.Optional.of(product1));
        when(masterCatalogProductRepository.findById(productId2))
            .thenReturn(java.util.Optional.of(product2));
        when(masterCatalogProductRepository.findById(nonExistentId))
            .thenReturn(java.util.Optional.empty()); // Non-existent product
        when(mapper.mapProductToPotentialAssociateProduct(any(UUID.class), eq(jobId), eq(taskId),
            eq(product1), eq(product2), eq(PotentiallyAssociateProductStatus.PENDING_REVIEW)))
            .thenReturn(validEntity);
        when(mapper.toDto(validEntity)).thenReturn(validDto);

        // When
        List<MasterCatalogPotentiallyAssociateProductDto> result =
            service.processPotentiallyAssociateProduct(jobId, productIds);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.get(0)).isEqualTo(validDto);

        // Verify interactions
        verify(masterCatalogTaskService).createTasks(jobId, MasterCatalogTaskType.CREATE_ASSOCIATION, 1);
        verify(masterCatalogProductRepository, atLeastOnce()).findById(productId1);
        verify(masterCatalogProductRepository, atLeastOnce()).findById(productId2);
        verify(masterCatalogProductRepository, atLeastOnce()).findById(nonExistentId);
        verify(potentiallyAssociateProductRepository).saveAll(anyList());
    }

    @Test
    void processPotentiallyAssociateProduct_withEmptyProductIds_shouldReturnEmptyList() {
        // Given
        UUID jobId = UUID.randomUUID();
        List<List<UUID>> emptyProductIds = Collections.emptyList();

        // Mock service dependencies
        when(masterCatalogTaskService.createTasks(eq(jobId), eq(MasterCatalogTaskType.CREATE_ASSOCIATION), eq(0)))
            .thenReturn(Collections.emptyList());

        // When
        List<MasterCatalogPotentiallyAssociateProductDto> result =
            service.processPotentiallyAssociateProduct(jobId, emptyProductIds);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();

        // Verify interactions
        verify(masterCatalogTaskService).createTasks(jobId, MasterCatalogTaskType.CREATE_ASSOCIATION, 0);
        verify(potentiallyAssociateProductRepository).saveAll(Collections.emptyList());
    }

    @Test
    void processPotentiallyAssociateProduct_withSingleElementLists_shouldFilterThemOut() {
        // Given
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();
        UUID singleProductId = UUID.randomUUID();

        List<List<UUID>> productIds = Arrays.asList(
            Arrays.asList(productId1, productId2),  // Valid pair
            Arrays.asList(singleProductId),         // Single element - should be filtered out
            Arrays.asList(productId1, productId2, UUID.randomUUID()) // Three elements - should be filtered out
        );

        UUID taskId = UUID.randomUUID();
        MasterCatalogProduct product1 = createProduct(productId1, UUID.randomUUID());
        MasterCatalogProduct product2 = createProduct(productId2, UUID.randomUUID());

        MasterCatalogTaskDto taskDto = MasterCatalogTaskDto.builder()
            .id(taskId)
            .build();

        MasterCatalogPotentiallyAssociateProduct validEntity = createPotentiallyAssociateProduct(
            taskId, jobId, productId1, productId2);

        MasterCatalogPotentiallyAssociateProductDto validDto = createDto(validEntity);

        // Mock service dependencies
        when(masterCatalogTaskService.createTasks(eq(jobId), eq(MasterCatalogTaskType.CREATE_ASSOCIATION), eq(1)))
            .thenReturn(Arrays.asList(taskDto));
        when(masterCatalogProductRepository.findById(productId1))
            .thenReturn(java.util.Optional.of(product1));
        when(masterCatalogProductRepository.findById(productId2))
            .thenReturn(java.util.Optional.of(product2));
        when(mapper.mapProductToPotentialAssociateProduct(any(UUID.class), eq(jobId), eq(taskId),
            eq(product1), eq(product2), eq(PotentiallyAssociateProductStatus.PENDING_REVIEW)))
            .thenReturn(validEntity);
        when(mapper.toDto(validEntity)).thenReturn(validDto);

        // When
        List<MasterCatalogPotentiallyAssociateProductDto> result =
            service.processPotentiallyAssociateProduct(jobId, productIds);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.get(0)).isEqualTo(validDto);

        // Verify interactions
        verify(masterCatalogTaskService).createTasks(jobId, MasterCatalogTaskType.CREATE_ASSOCIATION, 1);
        verify(masterCatalogProductRepository, atLeastOnce()).findById(productId1);
        verify(masterCatalogProductRepository, atLeastOnce()).findById(productId2);
                  verify(potentiallyAssociateProductRepository).saveAll(anyList());
      }

    @Test
    void submit_EmptyList_ShouldReturn() {
        // Given
        List<UUID> emptyList = Collections.emptyList();
        when(potentiallyAssociateProductRepository.findAllById(emptyList))
            .thenReturn(Collections.emptyList());

        // When
        service.submit(emptyList);

        // Then
        verify(potentiallyAssociateProductRepository).findAllById(emptyList);
        verifyNoInteractions(batchJobRepository);
        verifyNoInteractions(masterCatalogTaskRepository);
        verifyNoInteractions(productAssociationRepository);
    }

    @Test
    void submit_NoValidData_ShouldReturn() {
        // Given
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        List<UUID> ids = Arrays.asList(id);
        
        MasterCatalogPotentiallyAssociateProduct product = createPotentiallyAssociateProduct(
            id, jobId, taskId, PotentiallyAssociateProductStatus.COMPLETED);

        MasterCatalogTask task = MasterCatalogTask.builder().type(MasterCatalogTaskType.CREATE_ASSOCIATION)
            .jobNumber(UUID.randomUUID().toString())
            .status(MasterCatalogTaskStatus.IN_PROGRESS)
            .id(taskId)
            .assignedTo("reviewed-123")
            .build();

        when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.ofNullable(task));
        when(potentiallyAssociateProductRepository.findAllById(ids))
            .thenReturn(Arrays.asList(product));

        // When
        service.submit(ids);

        // Then
        verify(potentiallyAssociateProductRepository).findAllById(ids);
        verifyNoInteractions(batchJobRepository);
        verifyNoInteractions(productAssociationRepository);
    }

    @Test
    void submit_Success_ShouldProcessSubmission() {
        // Given
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        List<UUID> ids = Arrays.asList(id1, id2);
        
        MasterCatalogPotentiallyAssociateProduct product1 = createPotentiallyAssociateProduct(
            id1, jobId, taskId, PotentiallyAssociateProductStatus.IN_STAGE);
        product1.setAssociated(true);
        product1.setProductUpc("123456789");
        product1.setPotentiallyAssociateProductUpc("987654321");
        
        MasterCatalogPotentiallyAssociateProduct product2 = createPotentiallyAssociateProduct(
            id2, jobId, taskId, PotentiallyAssociateProductStatus.IN_STAGE);
        product2.setAssociated(false);
        product2.setProductUpc("111111111");
        product2.setPotentiallyAssociateProductUpc("222222222");
        
        MasterCatalogBatchJob job = createBatchJob(jobId);
        MasterCatalogTask task = createTask(taskId, jobId);

        when(masterCatalogTaskService.validateUserPermission(taskId,"submit")).thenReturn(task);
        when(potentiallyAssociateProductRepository.findAllById(ids))
            .thenReturn(Arrays.asList(product1, product2));
        when(batchJobRepository.findById(jobId))
            .thenReturn(Optional.of(job));
        when(masterCatalogTaskRepository.findById(taskId))
            .thenReturn(Optional.of(task));
        when(potentiallyAssociateProductRepository.findByTaskId(taskId))
            .thenReturn(Arrays.asList(product1, product2));
        when(masterCatalogTaskRepository.findByJobId(jobId))
            .thenReturn(Arrays.asList(task));
        when(productAssociationRepository.findAllByUpcIn(any(ArrayList.class)))
            .thenReturn(Collections.emptyList());

        // When
        service.submit(ids);

        // Then
        // Verify products are updated to COMPLETED status
        ArgumentCaptor<List<MasterCatalogPotentiallyAssociateProduct>> saveCaptor = 
            ArgumentCaptor.forClass(List.class);
        verify(potentiallyAssociateProductRepository).saveAll(saveCaptor.capture());
        
        List<MasterCatalogPotentiallyAssociateProduct> savedProducts = saveCaptor.getValue();
        assertEquals(PotentiallyAssociateProductStatus.COMPLETED, savedProducts.get(0).getStatus());
        assertEquals(PotentiallyAssociateProductStatus.COMPLETED, savedProducts.get(1).getStatus());

        // Verify task status is checked and updated
        ArgumentCaptor<MasterCatalogTask> taskCaptor = ArgumentCaptor.forClass(MasterCatalogTask.class);
        verify(masterCatalogTaskRepository).save(taskCaptor.capture());
        MasterCatalogTask savedTask = taskCaptor.getValue();
        assertEquals(MasterCatalogTaskStatus.COMPLETED, savedTask.getStatus());

        // Verify job status is checked and updated
        verify(masterCatalogTaskRepository).findByJobId(jobId);
        ArgumentCaptor<MasterCatalogBatchJob> jobCaptor = ArgumentCaptor.forClass(MasterCatalogBatchJob.class);
        verify(batchJobRepository).save(jobCaptor.capture());
        MasterCatalogBatchJob savedJob = jobCaptor.getValue();
        assertEquals(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_COMPLETED, savedJob.getStatus());

        // Verify product associations are created (only for associated=true products)
        verify(productAssociationRepository).findAllByUpcIn(any(ArrayList.class));
        ArgumentCaptor<List<MasterCatalogProductAssociation>> associationCaptor = 
            ArgumentCaptor.forClass(List.class);
        verify(productAssociationRepository).saveAll(associationCaptor.capture());
        List<MasterCatalogProductAssociation> savedAssociations = associationCaptor.getValue();
        // Should create 2 associations for the 2 UPCs of product1 (since only product1 has associated=true)
        assertEquals(2, savedAssociations.size());

        // Verify products are marked as associated
        ArgumentCaptor<Set<UUID>> productIdsCaptor = ArgumentCaptor.forClass(Set.class);
        verify(masterCatalogProductRepository).markAsAssociated(productIdsCaptor.capture());
        Set<UUID> markedProductIds = productIdsCaptor.getValue();
        // Should contain only product IDs from associated=true products (only product1)
        assertEquals(2, markedProductIds.size()); // Only product1 has associated=true, so 1 product * 2 IDs = 2 total IDs
        assertThat(markedProductIds).contains(product1.getProductId());
        assertThat(markedProductIds).contains(product1.getPotentiallyAssociateProductId());
        // product2 has associated=false, so its IDs should not be included
        assertThat(markedProductIds).doesNotContain(product2.getProductId());
        assertThat(markedProductIds).doesNotContain(product2.getPotentiallyAssociateProductId());
    }

    @Test
    void submit_WithEmptyValidData_ShouldNotCallMarkProductAsAssociated() {
        // Given
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        
        List<UUID> ids = Arrays.asList(id);
        
        // Create product with COMPLETED status (not IN_STAGE, so won't be processed)
        MasterCatalogPotentiallyAssociateProduct product = createPotentiallyAssociateProduct(
            id, jobId, taskId, PotentiallyAssociateProductStatus.COMPLETED);

        MasterCatalogTask task = MasterCatalogTask.builder().type(MasterCatalogTaskType.CREATE_ASSOCIATION)
            .jobNumber(UUID.randomUUID().toString())
            .status(MasterCatalogTaskStatus.IN_PROGRESS)
            .id(taskId)
            .assignedTo("reviewed-123")
            .build();

        when(masterCatalogTaskService.validateUserPermission(taskId,"submit")).thenReturn(task);
        when(potentiallyAssociateProductRepository.findAllById(ids))
            .thenReturn(Arrays.asList(product));

        // When
        service.submit(ids);

        // Then
        verify(potentiallyAssociateProductRepository).findAllById(ids);
        verifyNoInteractions(masterCatalogProductRepository); // Should not call markAsAssociated
        verifyNoInteractions(batchJobRepository);
        verifyNoInteractions(productAssociationRepository);
    }

    @Test
    void submit_WithSingleValidProduct_ShouldCallMarkProductAsAssociated() {
        // Given
        UUID id = UUID.randomUUID();
        UUID taskId = UUID.randomUUID();
        UUID jobId = UUID.randomUUID();
        UUID productId = UUID.randomUUID();
        UUID potentiallyAssociateProductId = UUID.randomUUID();
        
        List<UUID> ids = Arrays.asList(id);
        
        MasterCatalogPotentiallyAssociateProduct product = createPotentiallyAssociateProduct(
            id, jobId, taskId, PotentiallyAssociateProductStatus.IN_STAGE);
        product.setAssociated(true);
        product.setProductId(productId);
        product.setPotentiallyAssociateProductId(potentiallyAssociateProductId);
        
        MasterCatalogBatchJob job = createBatchJob(jobId);
        MasterCatalogTask task = createTask(taskId, jobId);

        when(masterCatalogTaskService.validateUserPermission(taskId,"submit")).thenReturn(task);
        when(potentiallyAssociateProductRepository.findAllById(ids))
            .thenReturn(Arrays.asList(product));
        when(batchJobRepository.findById(jobId))
            .thenReturn(Optional.of(job));
        when(masterCatalogTaskRepository.findById(taskId))
            .thenReturn(Optional.of(task));
        when(potentiallyAssociateProductRepository.findByTaskId(taskId))
            .thenReturn(Arrays.asList(product));
        when(masterCatalogTaskRepository.findByJobId(jobId))
            .thenReturn(Arrays.asList(task));
        when(productAssociationRepository.findAllByUpcIn(any(ArrayList.class)))
            .thenReturn(Collections.emptyList());

        // When
        service.submit(ids);

        // Then
        // Verify markProductAsAssociated is called with correct product IDs
        ArgumentCaptor<Set<UUID>> productIdsCaptor = ArgumentCaptor.forClass(Set.class);
        verify(masterCatalogProductRepository).markAsAssociated(productIdsCaptor.capture());
        Set<UUID> markedProductIds = productIdsCaptor.getValue();
        
        assertEquals(2, markedProductIds.size());
        assertThat(markedProductIds).contains(productId);
        assertThat(markedProductIds).contains(potentiallyAssociateProductId);
    }

    private MasterCatalogPotentiallyAssociateProduct createPotentiallyAssociateProduct(
            UUID id, UUID jobId, UUID taskId, PotentiallyAssociateProductStatus status) {
        return MasterCatalogPotentiallyAssociateProduct.builder()
            .id(id)
            .jobId(jobId)
            .taskId(taskId)
            .productId(UUID.randomUUID())
            .potentiallyAssociateProductId(UUID.randomUUID())
            .productName("Test Product")
            .productUpc("123456789")
            .potentiallyAssociateProductName("Test Associate Product")
            .potentiallyAssociateProductUpc("987654321")
            .status(status)
            .associated(false)
            .createdBy("test-user")
            .updatedBy("test-user")
            .build();
    }

    private MasterCatalogBatchJob createBatchJob(UUID jobId) {
        return MasterCatalogBatchJob.builder()
            .id(jobId)
            .jobNumber("JOB-" + jobId.toString())
            .status(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }

    private MasterCatalogTask createTask(UUID taskId, UUID jobId) {
        return MasterCatalogTask.builder()
            .id(taskId)
            .jobId(jobId)
            .assignedTo("reviewed-123")
            .taskNumber("TASK-" + taskId.toString())
            .status(MasterCatalogTaskStatus.IN_PROGRESS)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }
  }
