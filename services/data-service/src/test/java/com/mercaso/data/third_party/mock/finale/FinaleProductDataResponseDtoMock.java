package com.mercaso.data.third_party.mock.finale;

import com.mercaso.data.third_party.dto.finale.FinaleProductDataResponseDto;
import java.util.List;

public class FinaleProductDataResponseDtoMock {

    public static FinaleProductDataResponseDto mockFinaleProductDataResponseDto() {
        FinaleProductDataResponseDto responseDto = new FinaleProductDataResponseDto();
        FinaleProductDataResponseDto.ProductData data = new FinaleProductDataResponseDto.ProductData();
        FinaleProductDataResponseDto.ProductViewConnection productViewConnection = new FinaleProductDataResponseDto.ProductViewConnection();
        FinaleProductDataResponseDto.Summary summary = new FinaleProductDataResponseDto.Summary();
        FinaleProductDataResponseDto.Metrics metrics = new FinaleProductDataResponseDto.Metrics();

        metrics.setCount(List.of(1));
        summary.setMetrics(metrics);
        productViewConnection.setSummary(summary);
        productViewConnection.setEdges(List.of(new FinaleProductDataResponseDto.Edge()));
        data.setProductViewConnection(productViewConnection);
        responseDto.setData(data);

        return responseDto;
    }

}
