package com.mercaso.data.recommendation.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.recommendation.dto.DepartmentDto;
import com.mercaso.data.recommendation.dto.ItemRecommendationDto;
import com.mercaso.data.recommendation.dto.PageableResponse;
import com.mercaso.data.recommendation.entity.ItemRecommendation;
import com.mercaso.data.recommendation.repository.ItemRecommendationRepository;
import com.mercaso.data.recommendation.utils.resource_utils.ItemRecommendationResourceApiUtils;

/**
 * Integration test for ItemRecommendationResource.
 */
class ItemRecommendationResourceIT extends AbstractIT {

  @Autowired
  private ItemRecommendationResourceApiUtils itemRecommendationResourceApiUtils;

  @Autowired
  private ItemRecommendationRepository itemRecommendationRepository;

  private String storeId;
  private List<ItemRecommendation> testRecommendations;

  @BeforeEach
  void setUp() {
    // Initialize test data
    storeId = "store-" + UUID.randomUUID();
    testRecommendations = new ArrayList<>();

    // Create first recommendation with high reasonValue for sorting test
    ItemRecommendation recommendation1 = new ItemRecommendation();
    recommendation1.setStoreId(storeId);
    recommendation1.setSkuNumber("SKU123");
    recommendation1.setSkuName("Test Product 1");
    recommendation1.setProductId("PROD123");
    recommendation1.setVersion("V1");
    recommendation1.setDepartment("Electronics");
    recommendation1.setDepartmentId("DEPT001");
    recommendation1.setReason("PURCHASE_RATE");
    recommendation1.setReasonValue("85.5"); // This will be rounded to 86
    recommendation1.setCreatedAt(Instant.now());
    recommendation1.setUpdatedAt(Instant.now());

    // Create second recommendation with lower reasonValue
    ItemRecommendation recommendation2 = new ItemRecommendation();
    recommendation2.setStoreId(storeId);
    recommendation2.setSkuNumber("SKU456");
    recommendation2.setSkuName("Test Product 2");
    recommendation2.setProductId("PROD456");
    recommendation2.setDepartment("Home");
    recommendation2.setDepartmentId("DEPT002");
    recommendation2.setVersion("V1");
    recommendation2.setReason("NRS_PURCHASE_RATE");
    recommendation2.setReasonValue("42.7"); // This will be rounded to 43
    recommendation2.setCreatedAt(Instant.now());
    recommendation2.setUpdatedAt(Instant.now());

    // Create third recommendation with different store ID (should not be returned in the test)
    ItemRecommendation recommendation3 = new ItemRecommendation();
    recommendation3.setStoreId("different-store");
    recommendation3.setSkuNumber("SKU789");
    recommendation3.setSkuName("Test Product 3");
    recommendation3.setProductId("PROD789");
    recommendation3.setDepartment("Clothing");
    recommendation3.setDepartmentId("DEPT003");
    recommendation3.setVersion("V1");
    recommendation3.setReason("POPULARITY");
    recommendation3.setReasonValue("99.9");
    recommendation3.setCreatedAt(Instant.now());
    recommendation3.setUpdatedAt(Instant.now());

    // Save all test entities to the database
    testRecommendations.add(itemRecommendationRepository.save(recommendation1));
    testRecommendations.add(itemRecommendationRepository.save(recommendation2));
    testRecommendations.add(itemRecommendationRepository.save(recommendation3));
  }

  @AfterEach
  void tearDown() {
    // Clean up test data
    itemRecommendationRepository.deleteAll(testRecommendations);
  }

  @Test
  @DisplayName("Search item recommendations")
  void searchItemRecommendations() throws Exception {
    // When: Perform the request with actual API call
    PageableResponse<ItemRecommendationDto> result = itemRecommendationResourceApiUtils
        .searchItemRecommendations(
            storeId, 0, 10);

    // Verify response structure
    assertNotNull(result);
    assertEquals(0, result.getPageNumber());
    assertEquals(10, result.getPageSize());
    assertEquals(1, result.getTotalPages());
    assertEquals(2, result.getTotalElements());

    // Verify data - expect items to be sorted by reasonValue DESC
    List<ItemRecommendationDto> resultData = result.getData();
    assertEquals(2, resultData.size());

    // Verify first item (should be recommendation1 with higher reasonValue)
    ItemRecommendationDto firstItem = resultData.get(0);
    assertEquals("SKU123", firstItem.getSku());
    assertEquals("PROD123", firstItem.getProductId());
    assertEquals("PURCHASE_RATE", firstItem.getReason().getType());
    assertEquals(86, firstItem.getReason().getValue()); // Rounded from 85.5

    // Verify second item
    ItemRecommendationDto secondItem = resultData.get(1);
    assertEquals("SKU456", secondItem.getSku());
    assertEquals("PROD456", secondItem.getProductId());
    assertEquals("NRS_PURCHASE_RATE", secondItem.getReason().getType());
    assertEquals(43, secondItem.getReason().getValue()); // Rounded from 42.7

    // Test with multiple reasons filter
    List<String> multipleReasons = List.of("PURCHASE_RATE", "NRS_PURCHASE_RATE");
    PageableResponse<ItemRecommendationDto> multiReasonResult = itemRecommendationResourceApiUtils
        .searchItemRecommendationsWithReasons(storeId, multipleReasons, 0, 10);

    // Verify both items are returned (same as no filter)
    assertNotNull(multiReasonResult);
    assertEquals(2, multiReasonResult.getTotalElements());
    assertEquals(2, multiReasonResult.getData().size());
  }

  @Test
  @DisplayName("Get distinct departments for store and version")
  void getDepartments() throws Exception {
    // When: Call the departments API
    List<DepartmentDto> departments = itemRecommendationResourceApiUtils.getDepartments(storeId,
        "V1");

    // Then: Verify the response
    assertNotNull(departments);
    assertEquals(2, departments.size());

    // Verify departments are sorted alphabetically by name and contain expected values
    DepartmentDto firstDept = departments.get(0);
    assertEquals("DEPT001", firstDept.id());
    assertEquals("Electronics", firstDept.name());

    DepartmentDto secondDept = departments.get(1);
    assertEquals("DEPT002", secondDept.id());
    assertEquals("Home", secondDept.name());
  }

}