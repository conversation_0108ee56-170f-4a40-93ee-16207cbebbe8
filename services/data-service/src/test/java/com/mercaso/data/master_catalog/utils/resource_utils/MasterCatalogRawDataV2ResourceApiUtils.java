package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogV2Request;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogRawDataV2ResourceApiUtils extends IntegrationTestRestUtil {

    public static final String V2_RAW_DATA_SEARCH = "/master-catalog/v2/raw-data/search";

    public MasterCatalogRawDataV2ResourceApiUtils(Environment environment) {
        super(environment);
    }

    /**
     * Search master catalog raw data using V2 API with SearchMasterCatalogV2Request
     */
    public CustomPage<MasterCatalogRawDataDto> searchMasterCatalogRawData(SearchMasterCatalogV2Request searchRequest) {

        Map<String, String> params = new java.util.HashMap<>(Map.of());
        params.put("page", "1");
        params.put("pageSize", "2");
        if(ObjectUtils.isNotEmpty(searchRequest.getStoreId()) ){
            params.put("storeId", searchRequest.getStoreId().toString());
        }
        if(StringUtils.isNotEmpty(searchRequest.getUpc())){
            params.put("upc", searchRequest.getUpc());
        }
        if(StringUtils.isNotEmpty(searchRequest.getDescription())){
            params.put("description", searchRequest.getDescription());
        }
        if(StringUtils.isNotEmpty(searchRequest.getDepartment())){
            params.put("department", searchRequest.getDepartment());
        }
        if(StringUtils.isNotEmpty(searchRequest.getCategory())){
            params.put("category", searchRequest.getCategory());
        }
        if(StringUtils.isNotEmpty(searchRequest.getSubCategory())){
            params.put("subCategory", searchRequest.getSubCategory());
        }
        if(StringUtils.isNotEmpty(searchRequest.getClazz())){
            params.put("clazz", searchRequest.getClazz());
        }


        ParameterizedTypeReference<CustomPage<MasterCatalogRawDataDto>> responseType = new ParameterizedTypeReference<>() {
        };

        return getEntityByMap(V2_RAW_DATA_SEARCH, responseType, params).getBody();

    }
}
