package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import java.time.Instant;
import java.util.UUID;

public class MasterCatalogPotentiallyDuplicateRawDataUtils {

    public static MasterCatalogPotentiallyDuplicateRawData buildMasterCatalogPotentiallyDuplicateRawData(
        UUID rawDataId,
        UUID potentiallyDuplicateRawDataId,
        UUID jobId,
        UUID taskId
    ) {
        return MasterCatalogPotentiallyDuplicateRawData.builder()
            .rawDataId(rawDataId)
            .upc("123456789")
            .name("Test Description")
            .primaryVendor("Test Vendor")
            .potentiallyDuplicateRawDataId(potentiallyDuplicateRawDataId)
            .potentiallyDuplicateUpc("987654321")
            .potentiallyDuplicateName("Test Duplicate Description")
            .potentiallyDuplicateVendor("Test Duplicate Vendor")
            .status(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW)
            .jobId(jobId)
            .taskId(taskId)
            .duplicated(false)
            .createdAt(Instant.now())
            .createdBy("test-user")
            .updatedAt(Instant.now())
            .updatedBy("test-user")
            .build();
    }
} 