package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import java.time.Instant;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogProductAssociationUtils {

    public static MasterCatalogProductAssociation buildMasterCatalogProductAssociation() {
        return MasterCatalogProductAssociation.builder()
            .upc(String.valueOf(Instant.now().getEpochSecond()))
            .associationGroup(UUID.randomUUID())
            .build();
    }
}
