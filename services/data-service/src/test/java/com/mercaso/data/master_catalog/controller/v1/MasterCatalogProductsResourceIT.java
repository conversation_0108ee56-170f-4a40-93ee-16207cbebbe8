package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogProductAssociationUtils.buildMasterCatalogProductAssociation;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogProductUtils.buildMasterCatalogProduct;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogProductDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogProduct;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductAssociation;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductAssociationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogProductsResourceApiUtils;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

class MasterCatalogProductsResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogProductsResourceApiUtils masterCatalogProductsResourceApiUtils;
    @Autowired
    private MasterCatalogProductRepository masterCatalogProductRepository;
    @Autowired
    private MasterCatalogImageRepository masterCatalogImageRepository;
    @Autowired
    private MasterCatalogProductAssociationRepository masterCatalogProductAssociationRepository;
    @MockBean
    private ExternalApiAdapter externalApiAdapter;

    @Test
    void searchAssociatedProducts() {

        String upc1 = RandomStringUtils.randomNumeric(12);
        MasterCatalogProduct product1 = buildMasterCatalogProduct(UUID.randomUUID());
        product1.setUpc(upc1);
        MasterCatalogProduct savedProduct1 = masterCatalogProductRepository.save(product1);

        String upc2 = RandomStringUtils.randomNumeric(12);
        MasterCatalogProduct product2 = buildMasterCatalogProduct(UUID.randomUUID());
        product2.setUpc(upc2);
        MasterCatalogProduct savedProduct2 = masterCatalogProductRepository.save(product2);

        String upc3 = RandomStringUtils.randomNumeric(12);
        MasterCatalogProduct product3 = buildMasterCatalogProduct(UUID.randomUUID());
        product3.setUpc(upc3);
        MasterCatalogProduct savedProduct3 = masterCatalogProductRepository.save(product3);

        UUID associationGroup = UUID.randomUUID();
        MasterCatalogProductAssociation masterCatalogProductAssociation1 = buildMasterCatalogProductAssociation();
        masterCatalogProductAssociation1.setUpc(upc1);
        masterCatalogProductAssociation1.setAssociationGroup(associationGroup);

        MasterCatalogProductAssociation masterCatalogProductAssociation2 = buildMasterCatalogProductAssociation();
        masterCatalogProductAssociation2.setUpc(upc2);
        masterCatalogProductAssociation2.setAssociationGroup(associationGroup);

        MasterCatalogProductAssociation masterCatalogProductAssociation3 = buildMasterCatalogProductAssociation();
        masterCatalogProductAssociation3.setUpc(upc3);

        MasterCatalogProductAssociation savedMasterCatalogProductAssociation1 = masterCatalogProductAssociationRepository.save(
            masterCatalogProductAssociation1);

        MasterCatalogProductAssociation savedMasterCatalogProductAssociation2 = masterCatalogProductAssociationRepository.save(
            masterCatalogProductAssociation2);

        MasterCatalogProductAssociation savedMasterCatalogProductAssociation3 = masterCatalogProductAssociationRepository.save(
            masterCatalogProductAssociation3);

        CustomPage<MasterCatalogProductDto> result = masterCatalogProductsResourceApiUtils.searchAssociatedProducts(upc1);
        assert !result.getData().isEmpty();
        assert result.getData().stream().noneMatch(dto -> dto.getUpc().equals(upc1));
        assert result.getData().stream().anyMatch(dto -> dto.getUpc().equals(upc2));
        assert result.getData().stream().noneMatch(dto -> dto.getUpc().equals(upc3));

    }

    @Test
    void searchProducts() {
        String upc1 = RandomStringUtils.randomNumeric(12);
        String upc2 = RandomStringUtils.randomNumeric(12);
        String name = RandomStringUtils.randomAlphabetic(5);

        MasterCatalogProduct product1 = MasterCatalogProduct.builder()
            .upc(upc1)
            .name(name)
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        MasterCatalogProduct product2 = MasterCatalogProduct.builder()
            .upc(upc2)
            .name(name)
            .description("Test Description")
            .masterCatalogRawDataId(UUID.randomUUID())
            .build();

        masterCatalogProductRepository.save(product1);
        masterCatalogProductRepository.save(product2);
        CustomPage<MasterCatalogProductDto> productPage = masterCatalogProductsResourceApiUtils.searchProducts(
            upc1, name);

        assert productPage.getData().size() == 1;
        assert productPage.getData().stream().allMatch(dto -> dto.getUpc().equals(upc1));
        assert productPage.getData().stream().allMatch(dto -> dto.getName().contains(name));
    }
}
