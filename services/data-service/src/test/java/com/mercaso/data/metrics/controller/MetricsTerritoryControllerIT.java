package com.mercaso.data.metrics.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.metrics.dto.MetricsTerritoryAlertDto;
import com.mercaso.data.metrics.dto.MetricsTerritorySalespersonDto;
import com.mercaso.data.metrics.enums.AlertType;
import com.mercaso.data.metrics.service.MetricsTerritoryService;
import com.mercaso.data.metrics.utils.controller_utils.MetricsTerritoryRestApi;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Page;

class MetricsTerritoryControllerIT extends AbstractIT {

    @Autowired
    private MetricsTerritoryRestApi metricsTerritoryRestApi;

    @MockBean
    private MetricsTerritoryService metricsTerritoryService;


    @BeforeEach
    void setUp() {

        // Given
        MetricsTerritorySalespersonDto dto = new MetricsTerritorySalespersonDto("John Doe", List.of("12345", "67890", "13579"));
        Page<MetricsTerritorySalespersonDto> page = new PageImpl<>(
            List.of(dto),
            PageRequest.of(0, 10),
            1
        );

        when(metricsTerritoryService.getSalespersons()).thenReturn(page);

        // Given
        String zipCode = "90001";
        MetricsTerritoryAlertDto metricsTerritoryAlertDto = new MetricsTerritoryAlertDto();
        metricsTerritoryAlertDto.setZipCode(zipCode);

        Page<MetricsTerritoryAlertDto> alertPage = new PageImpl<>(
            List.of(metricsTerritoryAlertDto),
            PageRequest.of(0, 10),
            1
        );
        
        when(metricsTerritoryService.getAlert(eq(AlertType.DECLINING_AOV), any())).thenReturn(alertPage);
    }

    @Test
    void testGetSalesperson() {

        // When
        CustomPage<MetricsTerritorySalespersonDto> result = metricsTerritoryRestApi.getSalesperson();

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertFalse(result.getData().isEmpty());
        assertEquals(1, result.getTotalCount());
        assertEquals("John Doe", result.getData().getFirst().getSalespersonName());
    }

    @Test
    void testGetAlert() {
        String zipCode = "90001";

        // When
        CustomPage<MetricsTerritoryAlertDto> result = metricsTerritoryRestApi.getAlert(
            AlertType.DECLINING_AOV,
            List.of(zipCode)
        );

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertFalse(result.getData().isEmpty());
        assertEquals(1, result.getTotalCount());
        assertEquals(zipCode, result.getData().getFirst().getZipCode());
    }
}
