package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;

public class MetricsStoreProfilingStoreInfoEntityMock {

    public static StoreProfilingStoreInfoEntity metricsStoreProfilingStoreInfoEntityMock() {
        StoreProfilingStoreInfoEntity entity = new StoreProfilingStoreInfoEntity();
        entity.setStoreId("1");
        entity.setStoreName("test");
        entity.setAddressName("test");
        entity.setLatitude(34.16424);
        entity.setLongitude(-118.52207);
        entity.setZipCode("90001");
        entity.setIsNrsStore(false);
        entity.setIsSquareStore(false);
        return entity;
    }


}
