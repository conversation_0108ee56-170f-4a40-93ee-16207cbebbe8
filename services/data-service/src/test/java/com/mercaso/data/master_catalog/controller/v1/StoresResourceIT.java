package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.StoreEntityUtils.buildStore;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.dto.StoreDto;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.StoresResourceApiUtils;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class StoresResourceIT extends AbstractIT {

    @Autowired
    private StoresResourceApiUtils storesResourceApiUtils;

    @Autowired
    private StoreRepository storeRepository;

    @Test
    void getStores() throws Exception {

        Store store1 = buildStore("MerCaso");
        Store store2 = buildStore("Rosie’s Laundromat");

        storeRepository.saveAll(List.of(store1, store2));

        List<StoreDto> results = storesResourceApiUtils.getStores();
        assert results.stream().anyMatch(store -> store.getName().equals("MerCaso"));
        assert results.stream().anyMatch(store -> store.getName().equals("Rosie’s Laundromat"));
    }
}
