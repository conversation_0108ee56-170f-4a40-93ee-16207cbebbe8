package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.model.domain.CreateAssociationInProcessEvent;
import com.mercaso.data.master_catalog.event.payload.domain.CreateAssociationInProcessPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyAssociateProductService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CreateAssociationInProcessEventListenerTest {

    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
    private MasterCatalogPotentiallyAssociateProductService potentiallyAssociateProductService;
    private CreateAssociationInProcessEventListener listener;

    @BeforeEach
    void setUp() {
        masterCatalogBatchJobMapper = mock(MasterCatalogBatchJobMapper.class);
        masterCatalogBatchJobRepository = mock(MasterCatalogBatchJobRepository.class);
        potentiallyAssociateProductService = mock(MasterCatalogPotentiallyAssociateProductService.class);

        listener = new CreateAssociationInProcessEventListener(
            masterCatalogBatchJobMapper,
            masterCatalogBatchJobRepository,
            potentiallyAssociateProductService
        );
    }

    @Test
    void testHandleEvent() {
        // Given
        UUID jobId = UUID.randomUUID();
        UUID productId1 = UUID.randomUUID();
        UUID productId2 = UUID.randomUUID();

        MasterCatalogBatchJobDto jobDto = MasterCatalogBatchJobDto.builder()
            .id(jobId)
            .build();
        
        MasterCatalogBatchJob jobEntity = new MasterCatalogBatchJob();
        jobEntity.setId(jobId);

        List<List<UUID>> productIds = Arrays.asList(
            Arrays.asList(productId1, productId2)
        );

        CreateAssociationInProcessPayload payload = CreateAssociationInProcessPayload.builder()
            .batchJobId(jobId)
            .productIds(productIds)
            .build();
        payload.setData(jobDto);

        CreateAssociationInProcessEvent event = new CreateAssociationInProcessEvent(jobDto, payload);

        when(masterCatalogBatchJobMapper.toEntity(jobDto)).thenReturn(jobEntity);

        // When
        listener.handleEvent(event);

        // Then
        verify(potentiallyAssociateProductService).processPotentiallyAssociateProduct(
            eq(jobId), eq(productIds));
        verify(masterCatalogBatchJobRepository).save(any(MasterCatalogBatchJob.class));

        assertEquals(MasterCatalogBatchJobStatus.CREATE_ASSOCIATION_IN_PROGRESS, jobEntity.getStatus());
    }

    @Test
    void testHandleEvent_failWhenAssociateServiceThrowsException() {
        // Given
        UUID jobId = UUID.randomUUID();
        MasterCatalogBatchJobDto jobDto = new MasterCatalogBatchJobDto();
        jobDto.setId(jobId);

        List<List<UUID>> productIds = new ArrayList<>();
        productIds.add(Arrays.asList(UUID.randomUUID(), UUID.randomUUID()));

        CreateAssociationInProcessPayload payload = new CreateAssociationInProcessPayload();
        payload.setData(jobDto);
        payload.setProductIds(productIds);

        CreateAssociationInProcessEvent event = new CreateAssociationInProcessEvent(jobDto, payload);

        MasterCatalogBatchJob job = new MasterCatalogBatchJob();
        job.setId(jobId);
        when(masterCatalogBatchJobMapper.toEntity(jobDto)).thenReturn(job);

        // Simulate a failure in associate product service
        doThrow(new RuntimeException("Associate service failure"))
            .when(potentiallyAssociateProductService)
            .processPotentiallyAssociateProduct(jobId, productIds);

        // When & Then
        RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
            listener.handleEvent(event);
        });
        assertEquals("Associate service failure", thrown.getMessage());

        // Verify that the repository save is not called because the process failed
        verify(masterCatalogBatchJobRepository, never()).save(any(MasterCatalogBatchJob.class));
    }
}

