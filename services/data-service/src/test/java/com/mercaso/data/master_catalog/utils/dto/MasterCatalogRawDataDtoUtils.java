package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogRawDataDtoUtils {

    public static MasterCatalogRawDataDto buildMasterCatalogRawDataDto() {
        MasterCatalogRawDataDto masterCatalogRawDataDto = new MasterCatalogRawDataDto();
        masterCatalogRawDataDto.setStoreId(UUID.randomUUID());
        masterCatalogRawDataDto.setUpc("upc");
        masterCatalogRawDataDto.setDescription("description");
        return masterCatalogRawDataDto;
    }

}
