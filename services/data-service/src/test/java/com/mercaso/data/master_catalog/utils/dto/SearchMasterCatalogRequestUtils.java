package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SearchMasterCatalogRequestUtils {

    public static SearchMasterCatalogRequest buildSearchMasterCatalogRequest() {
        SearchMasterCatalogRequest searchMasterCatalogRequest = new SearchMasterCatalogRequest();
        searchMasterCatalogRequest.setPage(1);
        searchMasterCatalogRequest.setPageSize(20);
        return searchMasterCatalogRequest;
    }
}
