package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import java.time.Instant;
import net.bytebuddy.utility.RandomString;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Component;

@Component
public class SquareObtainTokenResponseUtils {

    public SquareObtainTokenResponse createSquareObtainTokenResponse() {
        return SquareObtainTokenResponse.builder()
          .accessToken(RandomStringUtils.random(12))
          .expiresAt(Instant.now().plusSeconds(10000).toString())
          .refreshToken(RandomStringUtils.random(24))
          .build();
    }

    public SquareObtainTokenResponse createSquareObtainTokenResponse(String accessToken, Instant expiresAt) {
        return SquareObtainTokenResponse.builder()
          .accessToken(accessToken)
          .expiresAt(expiresAt.toString())
          .build();
    }
}
