package com.mercaso.data.third_party.mock.shopify;

import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductEntity;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductOptionEntity;
import com.mercaso.data.third_party.entity.shopify.product.ShopifyProductVariantEntity;
import java.time.ZonedDateTime;
import java.util.Collections;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.UniformRandomGenerator;

public class ShopifyProductEntityMock {

    public static ShopifyProductEntity productEntityMock(){

        ShopifyProductEntity productEntity = new ShopifyProductEntity();
        productEntity.setId(RandomUtils.nextLong());
        productEntity.setBodyHtml("<strong>Good snowboard!</strong>");
        productEntity.setCreatedAt(ZonedDateTime.parse("2024-07-02T12:02:43-04:00"));
        productEntity.setHandle("burton-custom-freestyle-151");
        productEntity.setProductType("Snowboard");
        productEntity.setPublishedAt(null);
        productEntity.setPublishedScope("web");
        productEntity.setStatus("draft");
        productEntity.setTags("");
        productEntity.setTemplateSuffix(null);
        productEntity.setTitle("Burton Custom Freestyle 151");
        productEntity.setUpdatedAt(ZonedDateTime.parse("2024-07-02T12:02:43-04:00"));
        productEntity.setVendor("Burton");
        productEntity.setImages(Collections.emptyList());


        ShopifyProductOptionEntity option = new ShopifyProductOptionEntity();
        option.setId(1064576507L);
        option.setProductId(1072481055L);
        option.setName("Title");
        option.setPosition(1);
        option.setValues(Collections.singletonList("Default Title"));
        productEntity.setOptions(Collections.singletonList(option));

        ShopifyProductVariantEntity variant = getShopifyProductVariantEntity();
        productEntity.setVariants(Collections.singletonList(variant));

        return productEntity;
    }

    private static ShopifyProductVariantEntity getShopifyProductVariantEntity() {
        ShopifyProductVariantEntity variant = new ShopifyProductVariantEntity();
        variant.setId(1070325039L);
        variant.setProductId(1072481055L);
        variant.setTitle("Default Title");
        variant.setPrice(0.00);
        variant.setSku("M-65955");
        variant.setPosition(1);
        variant.setInventoryPolicy("deny");
        variant.setCompareAtPrice(null);
        variant.setFulfillmentService("manual");
        variant.setInventoryManagement(null);
        variant.setOption1("Default Title");
        variant.setOption2(null);
        variant.setOption3(null);
        variant.setCreatedAt(ZonedDateTime.parse("2024-07-02T12:02:43-04:00"));
        variant.setUpdatedAt(ZonedDateTime.parse("2024-07-02T12:02:43-04:00"));
        variant.setTaxable(true);
        variant.setBarcode(null);
        variant.setGrams(0);
        variant.setWeight(0);
        variant.setWeightUnit("lb");
        variant.setInventoryItemId(1070325039L);
        variant.setInventoryQuantity(0);
        variant.setRequiresShipping(true);
        variant.setImageId(null);
        return variant;
    }
}