package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.AddressInfoEntity;

public class AddressInfoEntityMock {

    public static AddressInfoEntity addressInfoEntityMock() {
        AddressInfoEntity entity = new AddressInfoEntity();
        entity.setAddressId("1");
        entity.setAddressName("test");
        entity.setSalespeopleEmail("<EMAIL>");
        entity.setLatitude(34.16424);
        entity.setLongitude(-118.52207);
        entity.setZipCode("90001");
        return entity;
    }

}
