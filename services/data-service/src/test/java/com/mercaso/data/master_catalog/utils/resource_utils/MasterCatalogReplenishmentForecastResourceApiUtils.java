package com.mercaso.data.master_catalog.utils.resource_utils;

import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MasterCatalogReplenishmentForecastResourceApiUtils {

    private final TestRestTemplate restTemplate;
    private static final String BASE_URL = "/master-catalog/v1/replenishment-forecast";

    public ResponseEntity<byte[]> exportReplenishmentForecast(UUID storeId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        return restTemplate.exchange(
            BASE_URL + "/export?storeId=" + storeId,
            HttpMethod.GET,
            new HttpEntity<>(headers),
            byte[].class
        );
    }
} 