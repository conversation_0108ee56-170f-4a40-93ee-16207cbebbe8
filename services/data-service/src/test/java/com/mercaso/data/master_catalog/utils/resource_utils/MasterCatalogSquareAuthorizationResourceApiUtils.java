package com.mercaso.data.master_catalog.utils.resource_utils;

import static org.junit.Assert.assertTrue;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogSquareAuthorizationResourceApiUtils extends IntegrationTestRestUtil {

    private static final String MASTER_CATALOG_V1_SQUARE_AUTHORIZATION = "/master-catalog/v1/square-authorization";
    private static final String MASTER_CATALOG_V1_SQUARE_AUTHORIZATION_REFRESH = "/master-catalog/v1/square-authorization/refresh";

    public MasterCatalogSquareAuthorizationResourceApiUtils(Environment environment) {
        super(environment);
    }

    public void createSquareAuthorization(
      MasterCatalogSquareAuthorizationRequestDto masterCatalogSquareAuthorizationRequestDto) throws Exception {

        ResponseEntity<String> stringResponseEntity = performRequest(MASTER_CATALOG_V1_SQUARE_AUTHORIZATION,
          null,
          masterCatalogSquareAuthorizationRequestDto,
          HttpMethod.POST);

        assertTrue(stringResponseEntity.getStatusCode().is2xxSuccessful());
    }

    public void refreshSquareAuthorization() throws Exception {
        ResponseEntity<String> stringResponseEntity = performRequest(MASTER_CATALOG_V1_SQUARE_AUTHORIZATION_REFRESH,
          null,
          null,
          HttpMethod.POST);

        assertTrue(stringResponseEntity.getStatusCode().is2xxSuccessful());
    }
}
