package com.mercaso.data.master_catalog.utils.dto;

import com.mercaso.data.master_catalog.dto.MasterCatalogSquareAuthorizationRequestDto;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.UUID;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogSquareAuthorizationRequestDtoUtils {

    public MasterCatalogSquareAuthorizationRequestDto createMasterCatalogSquareAuthorizationRequestDto(UUID storeId, UUID state,
      String applicationId, String applicationSecret) {
        return MasterCatalogSquareAuthorizationRequestDto.builder()
          .storeId(storeId)
          .state(state)
          .applicationId(applicationId)
          .applicationSecret(applicationSecret)
          .permissions(new ArrayList<>(Arrays.asList("READ_ORDERS", "WRITE_ORDERS")))
          .build();
    }
}
