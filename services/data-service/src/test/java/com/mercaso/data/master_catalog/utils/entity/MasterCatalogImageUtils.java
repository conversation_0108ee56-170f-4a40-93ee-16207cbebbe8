package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MasterCatalogImageUtils {

    public static MasterCatalogImage buildMasterCatalogImage(UUID masterCatalogRawDataId) {
        return MasterCatalogImage.builder()
            .id(UUID.randomUUID())
            .imagePath("test/image/path")
            .masterCatalogRawDataId(masterCatalogRawDataId)
            .primaryImage(true)
            .build();
    }

    public static List<MasterCatalogImage> buildMasterCatalogImages(UUID masterCatalogRawDataId) {
        return Arrays.asList(
            MasterCatalogImage.builder().imagePath("image1.jpg").masterCatalogRawDataId(masterCatalogRawDataId).build(),
            MasterCatalogImage.builder().imagePath("image2.jpg").masterCatalogRawDataId(masterCatalogRawDataId).build()
        );
    }

}
