package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.master_catalog.dto.SquareDataSyncRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogLocation;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.MasterCatalogLocationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.service.impl.SquareDataSyncServiceImpl;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class SquareDataSyncServiceTest {

    private final StoreRepository storeRepository = mock(StoreRepository.class);
    private final SquareInventorySyncService squareInventorySyncService = mock(SquareInventorySyncService.class);
    private final SquareItemSyncService squareItemSyncService = mock(SquareItemSyncService.class);
    private final SquareOrderSyncService squareOrderSyncService = mock(
        SquareOrderSyncService.class);
    private final MasterCatalogLocationRepository masterCatalogLocationRepository = mock(
        MasterCatalogLocationRepository.class);
    private final MasterCatalogSquareAuthorizationRepository masterCatalogSquareAuthorizationRepository = mock(
        MasterCatalogSquareAuthorizationRepository.class);

    private final SquareDataSyncServiceImpl squareDataSyncService = new SquareDataSyncServiceImpl(
        storeRepository,
        squareInventorySyncService,
        squareItemSyncService,
        squareOrderSyncService,
        masterCatalogLocationRepository,
        masterCatalogSquareAuthorizationRepository);


    private UUID storeId;
    private Store store;
    private ObjectMapper objectMapper;
    private SquareDataSyncRequest request = new SquareDataSyncRequest();

    @BeforeEach
    void setUp() {
        storeId = UUID.randomUUID();
        store = new Store();
        store.setId(storeId);
        store.setIntegrationType("SQUARE");
        objectMapper = new ObjectMapper();
        request.setStoreId(storeId);
    }

    @Test
    void testSyncSquareData_WithValidStoreId_ItemsPresent() throws Exception {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(storeId);

        // Create metadata as string to match the implementation
        String metadata1 = "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LCRZX1K3K66X1\"}";
        String metadata2 = "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LQ6C3T6VVG02X\"}";
        String metadata3 = "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"L3T6VVG02XQ6C\"}";

        List<MasterCatalogLocation> locations = Arrays.asList(
            MasterCatalogLocation.builder()
                .id(UUID.randomUUID())
                .storeId(storeId)
                .metadata(objectMapper.readTree(metadata1))
                .build(),
            MasterCatalogLocation.builder()
                .id(UUID.randomUUID())
                .storeId(storeId)
                .metadata(objectMapper.readTree(metadata2))
                .build(),
            MasterCatalogLocation.builder()
                .id(UUID.randomUUID())
                .storeId(storeId)
                .metadata(objectMapper.readTree(metadata3))
                .build()
        );

        when(masterCatalogLocationRepository.findAllByStoreId(storeId)).thenReturn(locations);
        when(squareItemSyncService.syncItems(request)).thenReturn(Collections.emptyList());
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act
        squareDataSyncService.syncSquareData(request);

        // Assert
        verify(squareItemSyncService).syncItems(request);

        ArgumentCaptor<List<String>> locationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(squareInventorySyncService).syncInventory(eq(request), locationIdsCaptor.capture());
        verify(squareOrderSyncService).syncOrder(eq(request), locationIdsCaptor.capture());

        List<List<String>> allLocationIds = locationIdsCaptor.getAllValues();
        assertEquals(2, allLocationIds.size());
        assertTrue(allLocationIds.stream().allMatch(locationIds -> locationIds.size() == 3));
        assertTrue(allLocationIds.stream()
            .allMatch(locationIds -> locationIds.contains("LCRZX1K3K66X1") && locationIds.contains("LQ6C3T6VVG02X")
                && locationIds.contains("L3T6VVG02XQ6C")));
    }

    @Test
    void testSyncSquareData_WithBlankStoreId_SyncAllStores() throws Exception {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(null);

        Store anotherStore = new Store();
        anotherStore.setId(UUID.randomUUID());
        anotherStore.setIntegrationType("SQUARE");

        List<Store> squareStores = Arrays.asList(store, anotherStore);
        when(storeRepository.findAllByIntegrationTypeIs("SQUARE")).thenReturn(squareStores);

        JsonNode metadata1 = objectMapper.readTree("{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LOC1\"}");
        JsonNode metadata2 = objectMapper.readTree("{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LOC2\"}");

        List<MasterCatalogLocation> storeLocations = Arrays.asList(
            MasterCatalogLocation.builder().id(UUID.randomUUID()).storeId(storeId).metadata(metadata1).build(),
            MasterCatalogLocation.builder().id(UUID.randomUUID()).storeId(storeId).metadata(metadata2).build()
        );

        when(masterCatalogLocationRepository.findAllByStoreId(any(UUID.class))).thenReturn(storeLocations);
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(any())).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act
        squareDataSyncService.syncSquareData(request);

        // Assert
        verify(storeRepository, times(1)).findAllByIntegrationTypeIs("SQUARE");

        ArgumentCaptor<List<String>> locationIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(squareInventorySyncService, times(2)).syncInventory(any(), locationIdsCaptor.capture());
        verify(squareOrderSyncService, times(2)).syncOrder(any(), locationIdsCaptor.capture());
    }

    @Test
    void testSyncSquareData_WithBlankStoreId_NoStoresFound() {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(null); // No storeId provided

        when(storeRepository.findAllByIntegrationTypeIs("SQUARE")).thenReturn(Collections.emptyList());

        // Act
        squareDataSyncService.syncSquareData(request);

        // Assert
        verify(storeRepository, times(1)).findAllByIntegrationTypeIs("SQUARE");
        verify(squareItemSyncService, never()).syncItems(any());
        verify(squareInventorySyncService, never()).syncInventory(any(), any());
    }

    @Test
    void testSyncSquareData_NullRequest_SyncAllStores() throws Exception {
        // Arrange
        when(storeRepository.findAllByIntegrationTypeIs("SQUARE"))
            .thenReturn(Collections.singletonList(store));

        JsonNode metadata = objectMapper.readTree("{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LOC1\"}");
        MasterCatalogLocation location = MasterCatalogLocation.builder()
            .id(UUID.randomUUID())
            .storeId(storeId)
            .metadata(metadata)
            .build();

        when(masterCatalogLocationRepository.findAllByStoreId(storeId)).thenReturn(Collections.singletonList(location));
        when(squareItemSyncService.syncItems(any())).thenReturn(Collections.emptyList());
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act
        squareDataSyncService.syncSquareData(null);

        // Assert
        verify(storeRepository).findAllByIntegrationTypeIs("SQUARE");
    }

    @Test
    void testSyncSquareData_WithStoreId_StoreNotFound() throws Exception {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(storeId);

        JsonNode metadata = objectMapper.readTree(
            "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"" + UUID.randomUUID() + "\"}");
        MasterCatalogLocation location = MasterCatalogLocation.builder()
            .id(UUID.randomUUID())
            .storeId(storeId)
            .metadata(metadata)
            .build();

        when(masterCatalogLocationRepository.findAllByStoreId(storeId))
            .thenReturn(Collections.singletonList(location));
        when(squareItemSyncService.syncItems(request))
            .thenReturn(Collections.emptyList());
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act
        squareDataSyncService.syncSquareData(request);

        // Assert
        verify(squareItemSyncService, times(1)).syncItems(request);
        ArgumentCaptor<List<String>> inventoryCaptor = ArgumentCaptor.forClass(List.class);
        verify(squareInventorySyncService, times(1)).syncInventory(eq(request), inventoryCaptor.capture());

        List<String> capturedLocationIds = inventoryCaptor.getValue();
        assertEquals(1, capturedLocationIds.size());
    }

    @Test
    void testSyncSquareData_ItemsWithNoPresentAtLocationIds() throws Exception {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(storeId);

        // Mock empty locations
        when(masterCatalogLocationRepository.findAllByStoreId(storeId))
            .thenReturn(Collections.emptyList());
        when(squareItemSyncService.syncItems(request))
            .thenReturn(Collections.emptyList());
        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act
        squareDataSyncService.syncSquareData(request);

        // Assert
        verify(squareItemSyncService).syncItems(request);
        verify(squareInventorySyncService, never()).syncInventory(any(), any());
        verify(squareOrderSyncService, never()).syncOrder(any(), any());
    }

    @Test
    void testSyncSquareData_ExceptionDuringSynchronization() {
        // Arrange
        SquareDataSyncRequest request = new SquareDataSyncRequest();
        request.setStoreId(storeId);

        when(squareItemSyncService.syncItems(request)).thenThrow(new RuntimeException("Sync error"));

        when(masterCatalogSquareAuthorizationRepository.findByStoreId(storeId)).thenReturn(MasterCatalogSquareAuthorization.builder()
            .storeId(storeId)
            .build());

        // Act & Assert
        try {
            squareDataSyncService.syncSquareData(request);
        } catch (RuntimeException e) {
            assertEquals("Sync error", e.getMessage());
        }

        verify(squareItemSyncService, times(1)).syncItems(request);
        verify(squareInventorySyncService, never()).syncInventory(any(), any());
    }
}
