package com.mercaso.data.third_party.repository.shopify;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.third_party.entity.shopify.customer.ShopifyCustomerEntity;
import com.mercaso.data.third_party.mock.shopify.ShopifyCustomerEntityMock;
import jakarta.annotation.PostConstruct;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShopifyCustomerRepositoryIT extends AbstractIT {

    @Autowired
    private ShopifyCustomerRepository customerRepository;

    @PostConstruct
    void init() {
        //init data
        ShopifyCustomerEntity shopifyCustomerEntity = ShopifyCustomerEntityMock.customerEntityMock();
        customerRepository.save(shopifyCustomerEntity);
    }

    @Test
    void testFindAll() {
        List<ShopifyCustomerEntity> resultList = customerRepository.findAll();
        Assertions.assertNotEquals(0, resultList.size());
    }

}