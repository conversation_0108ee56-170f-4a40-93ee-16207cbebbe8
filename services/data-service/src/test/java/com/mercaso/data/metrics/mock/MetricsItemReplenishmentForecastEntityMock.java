package com.mercaso.data.metrics.mock;

import com.mercaso.data.metrics.entity.MetricsItemReplenishmentForecastEntity;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MetricsItemReplenishmentForecastEntityMock {

    public static MetricsItemReplenishmentForecastEntity metricsItemReplenishmentForecastEntityMock() {
        return MetricsItemReplenishmentForecastEntity.builder()
            .id(UUID.randomUUID())
            .addressId(UUID.randomUUID().toString())
            .sku("303123124")
            .name("Test Product")
            .orderAvgDays(new BigDecimal(7))
            .avgDailyDemand(new BigDecimal(10))
            .nextOrderTime(Instant.now().plus(6, ChronoUnit.DAYS))
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();

    }
}
