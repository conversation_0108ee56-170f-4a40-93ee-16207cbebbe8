package com.mercaso.data.image_management.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import com.mercaso.data.image_management.dto.ImageManagementItemImageDto;
import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.mapper.ImageManagementItemImageMapper;
import com.mercaso.data.image_management.repository.ImageManagementImageRepository;
import com.mercaso.data.image_management.repository.ImageManagementItemImageRepository;
import com.mercaso.data.image_management.service.impl.ImageManagementImageServiceImpl;
import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.document.operations.operations.DocumentOperations;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

public class ImageManagementImageSearchServiceTest {

    private ImsClientAdaptor imsClientAdaptor;
    private DocumentOperations documentOperations;
    private ImageManagementImageRepository imageManagementRepository;
    private ImageManagementItemImageRepository imageManagementItemImageRepository;
    private ImageManagementItemImageMapper imageManagementItemImageMapper;
    private ImageManagementImageService service;
    @BeforeEach
    void setUp() {
        imsClientAdaptor = mock(ImsClientAdaptor.class);
        documentOperations = mock(DocumentOperations.class);
        imageManagementRepository = mock(ImageManagementImageRepository.class);
        imageManagementItemImageRepository = mock(ImageManagementItemImageRepository.class);
        imageManagementItemImageMapper = mock(ImageManagementItemImageMapper.class);
        service = new ImageManagementImageServiceImpl(
            imsClientAdaptor, 
            documentOperations, 
            imageManagementRepository, 
            imageManagementItemImageRepository,
            imageManagementItemImageMapper
        );
        when(imageManagementItemImageMapper.toDto(any(ImageManagementItemImage.class)))
            .thenAnswer(invocation -> {
                ImageManagementItemImage entity = invocation.getArgument(0);
                return ImageManagementItemImageDto.builder()
                    .sku(entity.getSku())
                    .upc(entity.getUpc())
                    .imageType(entity.getImageType())
                    .isPrimary(entity.getIsPrimary())
                    .build();
            });
    }

    @Test
    public void searchImages_WithSkuFilter_ShouldReturnFilteredResults() {
        String sku = "TEST-SKU-123";
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .sku(sku)
            .page(1) // 1-based page indexing
            .pageSize(10)
            .build();

        List<ImageManagementItemImage> mockImages = Arrays.asList(
            createMockImage("TEST-SKU-123", "123456789", "Front_2", ImageTypeEnum.RAW.name()),
            createMockImage("TEST-SKU-123", "123456789", "Top_1", ImageTypeEnum.RAW.name())
        );

        // PageRequest.of(0, 10) because page 1 becomes 0 internally
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(mockImages, PageRequest.of(0, 10), 2);

        when(imageManagementItemImageRepository.searchImages(
            eq(sku), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)
        )).thenReturn(mockPage);

        Page<ImageManagementItemImageDto> response = service.searchImages(request);

        assertNotNull(response);
        assertEquals(2L, response.getTotalElements());
        assertEquals(0, response.getNumber()); // Spring Data returns 0-based page number
        assertEquals(10, response.getSize());
        assertEquals(1, response.getTotalPages());
        assertEquals(2, response.getContent().size());
        assertEquals(sku, response.getContent().get(0).getSku());
    }

    @Test
    public void searchImages_WithUpcFilter_ShouldReturnFilteredResults() {
        String upc = "123456789";
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .upc(upc)
            .page(1) // 1-based page indexing
            .pageSize(5)
            .build();

        List<ImageManagementItemImage> mockImages = Arrays.asList(
            createMockImage("SKU-1", upc, "Front_2", ImageTypeEnum.RAW.name()),
            createMockImage("SKU-2", upc, "Top_1", ImageTypeEnum.EDIT.name())
        );

        // PageRequest.of(0, 5) because page 1 becomes 0 internally
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(mockImages, PageRequest.of(0, 5), 2);

        when(imageManagementItemImageRepository.searchImages(
            isNull(), eq(upc), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)
        )).thenReturn(mockPage);

        Page<ImageManagementItemImageDto> response = service.searchImages(request);

        assertNotNull(response);
        assertEquals(2L, response.getTotalElements());
        assertEquals(upc, response.getContent().get(0).getUpc());
        assertEquals(upc, response.getContent().get(1).getUpc());
    }

    @Test
    public void searchImages_WithImageTypeFilter_ShouldReturnFilteredResults() {
        ImageTypeEnum imageType = ImageTypeEnum.RAW;
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .imageType(imageType)
            .page(1) // 1-based page indexing
            .pageSize(20)
            .build();

        List<ImageManagementItemImage> mockImages = Arrays.asList(
            createMockImage("SKU-1", "123456789", "Front_2", imageType.name()),
            createMockImage("SKU-2", "987654321", "Top_1", imageType.name())
        );

        // PageRequest.of(0, 20) because page 1 becomes 0 internally
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(mockImages, PageRequest.of(0, 20), 2);

        when(imageManagementItemImageRepository.searchImages(
            isNull(), isNull(), eq(imageType.name()), isNull(), isNull(), isNull(), any(Pageable.class)
        )).thenReturn(mockPage);

        Page<ImageManagementItemImageDto> response = service.searchImages(request);

        assertNotNull(response);
        assertEquals(2L, response.getTotalElements());
        assertEquals(imageType.name(), response.getContent().get(0).getImageType());
        assertEquals(imageType.name(), response.getContent().get(1).getImageType());
    }

    @Test
    public void searchImages_WithPrimaryImageFilter_ShouldReturnFilteredResults() {
        Boolean isPrimary = true;
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .isPrimary(isPrimary)
            .page(1) // 1-based page indexing
            .pageSize(10)
            .build();

        List<ImageManagementItemImage> mockImages = Arrays.asList(
            createMockImage("SKU-1", "123456789", "Front_2", ImageTypeEnum.RAW.name(), true),
            createMockImage("SKU-2", "987654321", "Front_2", ImageTypeEnum.EDIT.name(), true)
        );

        // PageRequest.of(0, 10) because page 1 becomes 0 internally
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(mockImages, PageRequest.of(0, 10), 2);

        when(imageManagementItemImageRepository.searchImages(
            isNull(), isNull(), isNull(), isNull(), eq(isPrimary), isNull(), any(Pageable.class)
        )).thenReturn(mockPage);

        Page<ImageManagementItemImageDto> response = service.searchImages(request);

        assertNotNull(response);
        assertEquals(2L, response.getTotalElements());
        assertTrue(response.getContent().get(0).getIsPrimary());
        assertTrue(response.getContent().get(1).getIsPrimary());
    }

    @Test
    public void searchImages_WithDefaultPagination_ShouldUseDefaultValues() {
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .sku("TEST-SKU")
            .build();

        List<ImageManagementItemImage> mockImages = Arrays.asList(
            createMockImage("TEST-SKU", "123456789", "Front_2", ImageTypeEnum.RAW.name())
        );

        // PageRequest.of(0, 20) because default page is 0 and default pageSize is 20
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(mockImages, PageRequest.of(0, 20), 1);

        when(imageManagementItemImageRepository.searchImages(
            eq("TEST-SKU"),  isNull(), isNull(),  isNull(),  isNull(),  isNull(), any(Pageable.class)
        )).thenReturn(mockPage);

        Page<ImageManagementItemImageDto> response = service.searchImages(request);

        assertNotNull(response);
        assertEquals(0, response.getNumber()); // Default page (0-based)
        assertEquals(20, response.getSize()); // Default page size
    }

    @Test
    public void searchImages_ShouldSetImageUrlInDto() {
        String sku = "TEST-SKU-URL";
        String filePath = "test/path/to/image.jpg";
        UUID imageId = UUID.randomUUID();
        ImageManagementItemImage itemImage = createMockImage(sku, "123456789", "Front_2", ImageTypeEnum.RAW.name());
        itemImage.setImageId(imageId);
        Page<ImageManagementItemImage> mockPage = new PageImpl<>(List.of(itemImage), PageRequest.of(0, 10), 1);
        when(imageManagementItemImageRepository.searchImages(
            eq(sku), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)
        )).thenReturn(mockPage);
        // Mock the image entity lookup
        com.mercaso.data.image_management.entity.ImageManagementImage imageEntity = com.mercaso.data.image_management.entity.ImageManagementImage.builder()
            .id(imageId)
            .filePath(filePath)
            .fileName("image.jpg")
            .fileSize(100L)
            .mimeType("image/jpeg")
            .shotAt(Instant.now())
            .createdBy("admin")
            .build();
        when(imageManagementRepository.findById(imageId)).thenReturn(java.util.Optional.of(imageEntity));
        // Let the mapper return a DTO with the imageId
        when(imageManagementItemImageMapper.toDto(any(ImageManagementItemImage.class)))
            .thenAnswer(invocation -> {
                ImageManagementItemImage entity = invocation.getArgument(0);
                return ImageManagementItemImageDto.builder()
                    .sku(entity.getSku())
                    .upc(entity.getUpc())
                    .imageType(entity.getImageType())
                    .isPrimary(entity.getIsPrimary())
                    .imageId(entity.getImageId() != null ? entity.getImageId().toString() : null)
                    .imageUrl(filePath)
                    .build();
            });
        ImageSearchRequestDto request = ImageSearchRequestDto.builder()
            .sku(sku)
            .page(1) // 1-based page indexing
            .pageSize(10)
            .build();
        Page<ImageManagementItemImageDto> response = service.searchImages(request);
        assertNotNull(response);
        assertEquals(1, response.getContent().size());
        ImageManagementItemImageDto dto = response.getContent().get(0);
        assertEquals(filePath, dto.getImageUrl());
    }

    private ImageManagementItemImage createMockImage(String sku, String upc, String imageAngle, String imageType) {
        return createMockImage(sku, upc, imageAngle, imageType, false);
    }

    private ImageManagementItemImage createMockImage(String sku, String upc, String imageAngle, String imageType, Boolean isPrimary) {
        return ImageManagementItemImage.builder()
            .id(UUID.randomUUID())
            .imageId(UUID.randomUUID())
            .sku(sku)
            .upc(upc)
            .imageAngel(imageAngle)
            .imageType(imageType)
            .isPrimary(isPrimary)
            .eachFlag(false)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .build();
    }
} 