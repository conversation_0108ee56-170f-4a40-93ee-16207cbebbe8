package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogImageUtils.buildMasterCatalogImage;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataDuplicationUtils.buildMasterCatalogRawDataDuplication;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.UpdateMasterCatalogRequest;
import com.mercaso.data.master_catalog.dto.external.DocumentResponseDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogRawDataResourceApiUtils;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import com.mercaso.data.recommendation.dto.PageableResponse;

import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
class MasterCatalogRawDataResourceIT extends AbstractIT {

    @Autowired
    private MasterCatalogRawDataResourceApiUtils masterCatalogRawDataResourceApiUtils;

    @Autowired
    private MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @Autowired
    private MasterCatalogImageRepository masterCatalogImageRepository;

    @MockBean
    private ExternalApiAdapter externalApiAdapter;

    @MockBean
    private S3OperationAdapter s3OperationAdapter;

    @Test
    void searchMasterCatalogRawData() {

        MasterCatalogRawData masterCatalogRawData1 = buildMasterCatalogRawData(UUID.randomUUID());
        MasterCatalogRawData masterCatalogRawData2 = buildMasterCatalogRawData(UUID.randomUUID());
        MasterCatalogRawData masterCatalogRawData3 = buildMasterCatalogRawData(UUID.randomUUID());
        List<MasterCatalogRawData> saved = masterCatalogRawDataRepository.saveAll(
                List.of(masterCatalogRawData1, masterCatalogRawData2, masterCatalogRawData3));
        registerForCleanup(saved);

        List<MasterCatalogImage> masterCatalogImages = saved.stream().map(x -> buildMasterCatalogImage(x.getId())).toList();
        masterCatalogImageRepository.saveAll(masterCatalogImages);
        registerForCleanup(masterCatalogImages);

        CustomPage<MasterCatalogRawDataDto> result =
                masterCatalogRawDataResourceApiUtils.getMasterCatalogRawData();
        assert result.getData().size() >= 2;
        assert result.getTotalCount() >= 3;
    }

    @Test
    void searchMasterCatalogRawDataByImage() throws JsonProcessingException {
        String upc = String.valueOf(Instant.now().getEpochSecond());
        MasterCatalogRawData masterCatalogRawData = buildMasterCatalogRawData(UUID.randomUUID());
        masterCatalogRawData.setUpc(upc);
        MasterCatalogRawData saved =
                masterCatalogRawDataRepository.saveAndFlush(masterCatalogRawData);
        registerForCleanup(saved);
        log.info("searchMasterCatalogRawDataByImage Saved: {}", saved);
        MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(saved.getId());
        MasterCatalogImage save = masterCatalogImageRepository.saveAndFlush(masterCatalogImage);
        registerForCleanup(save);
        log.info("searchMasterCatalogRawDataByImage Saved: {}", save);
        when(externalApiAdapter.getUpcsFromExternalApiByImage(any())).thenReturn(List.of(upc));
        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://test.com");

        CustomPage<MasterCatalogRawDataDto> result =
                masterCatalogRawDataResourceApiUtils.searchMasterCatalogRawDataByImage();
        log.info("searchMasterCatalogRawDataByImage Result: {}", result);
        assert result.getData().stream().anyMatch(x -> x.getUpc().equalsIgnoreCase(upc));
        assert result.getTotalCount() >= 1;
    }

    @Test
    void getMasterCatalogRawDataById() {
        // Create test data
        MasterCatalogRawData masterCatalogRawData = buildMasterCatalogRawData(UUID.randomUUID());
        masterCatalogRawData.setUpc(String.valueOf(Instant.now().getEpochSecond()));

        // Save the raw data and ensure it's persisted
        MasterCatalogRawData saved =
                masterCatalogRawDataRepository.saveAndFlush(masterCatalogRawData);
        registerForCleanup(saved);
        log.info("getMasterCatalogRawDataById Saved raw data with ID: {} and UPC: {}",
                saved.getId(), saved.getUpc());

        // Verify the data was actually saved
        if (saved.getId() == null) {
            throw new IllegalStateException("Failed to save MasterCatalogRawData - ID is null");
        }

        // Create and save associated image
        MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(saved.getId());
        MasterCatalogImage savedImage =
                masterCatalogImageRepository.saveAndFlush(masterCatalogImage);
        registerForCleanup(savedImage);
        log.info("getMasterCatalogRawDataById Saved image with ID: {} for raw data ID: {}",
                savedImage.getId(), saved.getId());

        // Verify data exists in database before API call
        boolean exists = masterCatalogRawDataRepository.existsById(saved.getId());
        log.info("getMasterCatalogRawDataById Data exists check: {} for ID: {}", exists,
                saved.getId());

        if (!exists) {
            throw new IllegalStateException(
                    "Test data not found in database after save - ID: " + saved.getId());
        }

        // Make the API call
        try {
            MasterCatalogRawDataDto result =
                    masterCatalogRawDataResourceApiUtils.getMasterCatalogRawDataById(saved.getId());
            log.info("getMasterCatalogRawDataById API call successful, returned UPC: {}",
                    result.getUpc());

            // Validate the result
            assert result != null : "Result should not be null";
            assert result.getUpc() != null : "UPC should not be null";
            assert result.getUpc().equalsIgnoreCase(saved.getUpc()) : String
                    .format("UPC mismatch: expected %s, got %s", saved.getUpc(), result.getUpc());

        } catch (Exception e) {
            log.error("getMasterCatalogRawDataById API call failed for ID: {}, error: {}",
                    saved.getId(), e.getMessage(), e);

            // Log additional debugging info
            log.error("Debugging info:");
            log.error("- Raw data ID: {}", saved.getId());
            log.error("- Raw data UPC: {}", saved.getUpc());
            log.error("- Database record exists: {}",
                    masterCatalogRawDataRepository.existsById(saved.getId()));
            log.error("- Total raw data count: {}", masterCatalogRawDataRepository.count());
            log.error("- Total image count: {}", masterCatalogImageRepository.count());

            throw e; // Re-throw to fail the test
        }
    }

    @Test
    @Ignore
    void searchMasterCatalogDuplicationRawData() {
        String upc1 = "847693759673294729475729574248";
        String upc2 = "767389462389346438492675967284";
        String upc3 = "867748294756867238473765682904";
        String upc4 = "563416380786824186036496759026";
        UUID duplicationGroup = UUID.randomUUID();

        log.info("upcs : {}", List.of(upc1, upc2, upc3, upc4));

        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication1 =
                buildMasterCatalogRawDataDuplication(upc1, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication2 =
                buildMasterCatalogRawDataDuplication(upc2, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication3 =
                buildMasterCatalogRawDataDuplication(upc3, duplicationGroup);
        MasterCatalogRawDataDuplication masterCatalogRawDataDuplication4 =
                buildMasterCatalogRawDataDuplication(upc4, duplicationGroup);

        MasterCatalogRawData masterCatalogRawData1 = buildMasterCatalogRawData(upc2);
        MasterCatalogRawData masterCatalogRawData2 = buildMasterCatalogRawData(upc3);
        MasterCatalogRawData masterCatalogRawData3 = buildMasterCatalogRawData(upc4);
        List<MasterCatalogRawDataDuplication> masterCatalogRawDataDuplications = masterCatalogRawDataDuplicationRepository.saveAllAndFlush(
                List.of(masterCatalogRawDataDuplication1, masterCatalogRawDataDuplication2,
                        masterCatalogRawDataDuplication3, masterCatalogRawDataDuplication4));
        List<MasterCatalogRawData> masterCatalogRawDataList =
                masterCatalogRawDataRepository.saveAllAndFlush(List.of(masterCatalogRawData1,
                        masterCatalogRawData2, masterCatalogRawData3));
        registerForCleanup(masterCatalogRawDataList);
        registerForCleanup(masterCatalogRawDataDuplications);

        List<UUID> ids =
                masterCatalogRawDataList.stream().map(MasterCatalogRawData::getId).toList();
        ids.forEach(id -> {
            MasterCatalogImage masterCatalogImage = buildMasterCatalogImage(id);
            masterCatalogImageRepository.saveAndFlush(masterCatalogImage);
            registerForCleanup(masterCatalogImage);
        });

        CustomPage<MasterCatalogRawDataDto> rawData =
                masterCatalogRawDataResourceApiUtils.getMasterCatalogDuplicationRawData(upc1);

        List<String> upcList =
                rawData.getData().stream().map(MasterCatalogRawDataDto::getUpc).toList();

        log.info("getMasterCatalogDuplicationRawData Result: {}", rawData);

        assert upcList.containsAll(List.of(upc2, upc3, upc4));
    }


  @Test
  void uploadImageSuccess() {
    // Mock S3 upload response
    DocumentResponseDto mockUploadResponse = DocumentResponseDto.builder()
        .name("test_image_20241201123456789_abc12345.jpg")
        .signedUrl("https://test-bucket.s3.amazonaws.com/test_image_20241201123456789_abc12345.jpg")
        .build();

    when(s3OperationAdapter.upload(any())).thenReturn(mockUploadResponse);

    // Prepare test image file
    byte[] testImageContent = "fake-image-content-for-testing".getBytes();
    String testFilename = "test_image.jpg";

    // Upload image using V1 API
    String uploadedFilename = masterCatalogRawDataResourceApiUtils
        .uploadImage(testImageContent, testFilename).getName();

    // Verify response - should return the uploaded filename
    assertNotNull(uploadedFilename);
    assertEquals(mockUploadResponse.getName(), uploadedFilename);
  }

  @Test
  void updateMasterCatalogRawData() {
    // Create test raw data
    MasterCatalogRawData rawData = buildMasterCatalogRawData(UUID.randomUUID());
    rawData.setName("Original Name");
    rawData.setDescription("Original Description");
    rawData.setBrand("Original Brand");
    rawData.setPackageSize(10);
    rawData.setDepartment("Original Department");
    rawData.setCategory("Original Category");
    rawData.setSubCategory("Original Subcategory");
    rawData.setClazz("Original Class");
    
    MasterCatalogRawData savedRawData = masterCatalogRawDataRepository.saveAndFlush(rawData);
    registerForCleanup(savedRawData);
    
    // Create initial images
    List<MasterCatalogImage> initialImages = List.of(
        buildMasterCatalogImage(savedRawData.getId()),
        buildMasterCatalogImage(savedRawData.getId())
    );
    masterCatalogImageRepository.saveAll(initialImages);
    registerForCleanup(initialImages);
    
    // Prepare update request
    List<UpdateMasterCatalogRequest.ImageRequest> newImages = List.of(
        UpdateMasterCatalogRequest.ImageRequest.builder()
            .imagePath("path/to/new/image1.jpg")
            .primaryImage(true)
            .build(),
        UpdateMasterCatalogRequest.ImageRequest.builder()
            .imagePath("path/to/new/image2.jpg")
            .primaryImage(false)
            .build(),
        UpdateMasterCatalogRequest.ImageRequest.builder()
            .imagePath("path/to/new/image3.jpg")
            .primaryImage(false)
            .build()
    );
    
    UpdateMasterCatalogRequest updateRequest = UpdateMasterCatalogRequest.builder()
        .name("Updated Name")
        .description("Updated Description")
        .brand("Updated Brand")
        .packageSize(20)
        .department("Updated Department")
        .category("Updated Category")
        .subcategory("Updated Subcategory")
        .clazz("Updated Class")
        .images(newImages)
        .build();

    when(s3OperationAdapter.getSignedUrl(any())).thenReturn("test");
    
    // Call update API
    masterCatalogRawDataResourceApiUtils
        .updateMasterCatalogRawData(savedRawData.getId(), updateRequest);
    
    // Verify database state
    MasterCatalogRawData updatedRawData = masterCatalogRawDataRepository.findById(savedRawData.getId()).orElse(null);
    assertNotNull(updatedRawData);
    assertEquals("Updated Name", updatedRawData.getName());
    assertEquals("Updated Description", updatedRawData.getDescription());
    assertEquals("Updated Brand", updatedRawData.getBrand());
    assertEquals(Integer.valueOf(20), updatedRawData.getPackageSize());
    assertEquals("Updated Department", updatedRawData.getDepartment());
    assertEquals("Updated Category", updatedRawData.getCategory());
    assertEquals("Updated Subcategory", updatedRawData.getSubCategory());
    assertEquals("Updated Class", updatedRawData.getClazz());
    
    // Verify old images were deleted and new ones created
    List<MasterCatalogImage> finalImages = masterCatalogImageRepository
        .findAllByMasterCatalogRawDataIdIn(List.of(savedRawData.getId()));
    assertEquals(3, finalImages.size());
    
    // Clean up new images
    registerForCleanup(finalImages);

  }

}
