package com.mercaso.data.master_catalog.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.adaptor.SquareApiAdapter;
import com.mercaso.data.master_catalog.dto.square.SquareObtainTokenResponse;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareAuthorization;
import com.mercaso.data.master_catalog.repository.MasterCatalogSquareAuthorizationRepository;
import com.mercaso.data.master_catalog.service.CipherUtility;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SquareAuthorizationServiceImplTest {

    @Mock
    private MasterCatalogSquareAuthorizationRepository authorizationRepository;
    
    @Mock
    private SquareApiAdapter squareApiAdapter;
    
    @Mock
    private CipherUtility cipherUtility;
    
    @InjectMocks
    private SquareAuthorizationServiceImpl squareAuthorizationService;
    
    private static final String TEST_CODE = "test_code";
    private static final String TEST_STATE = UUID.randomUUID().toString();
    private static final String TEST_APP_ID = "test_app_id";
    private static final String TEST_APP_SECRET = "test_app_secret";
    private static final String TEST_ACCESS_TOKEN = "test_access_token";
    private static final String TEST_REFRESH_TOKEN = "test_refresh_token";
    private static final String TEST_EXPIRES_AT = "2024-12-31T23:59:59Z";
    
    private MasterCatalogSquareAuthorization authorization;
    private SquareObtainTokenResponse tokenResponse;

    @BeforeEach
    void setUp() {
        authorization = new MasterCatalogSquareAuthorization();
        authorization.setEncryptedApplicationId("encrypted_app_id");
        authorization.setEncryptedApplicationSecret("encrypted_app_secret");
        
        tokenResponse = SquareObtainTokenResponse.builder()
            .accessToken(TEST_ACCESS_TOKEN)
            .refreshToken(TEST_REFRESH_TOKEN)
            .expiresAt(TEST_EXPIRES_AT)
            .build();
    }

    @Test
    void obtainTokenByAuthorizationCode_Success() {
        // Given
        when(authorizationRepository.findByState(UUID.fromString(TEST_STATE))).thenReturn(authorization);
        when(cipherUtility.decrypt(any())).thenReturn(TEST_APP_ID, TEST_APP_SECRET);
        when(squareApiAdapter.obtainTokenByAuthorizationCode(TEST_CODE, TEST_APP_ID, TEST_APP_SECRET))
            .thenReturn(tokenResponse);
        when(cipherUtility.encrypt(any())).thenReturn("encrypted_token");

        // When
        squareAuthorizationService.obtainTokenByAuthorizationCode(TEST_CODE, TEST_STATE);

        // Then
        verify(authorizationRepository).save(authorization);
        verify(cipherUtility).encrypt(TEST_ACCESS_TOKEN);
        verify(cipherUtility).encrypt(TEST_REFRESH_TOKEN);
    }

    @Test
    void obtainTokenByAuthorizationCode_NoAuthorizationFound() {
        // Given
        when(authorizationRepository.findByState(UUID.fromString(TEST_STATE))).thenReturn(null);

        // When
        squareAuthorizationService.obtainTokenByAuthorizationCode(TEST_CODE, TEST_STATE);

        // Then
        verify(authorizationRepository, never()).save(any());
    }

    @Test
    void obtainTokenByAuthorizationCode_EmptyCode() {
        // When
        squareAuthorizationService.obtainTokenByAuthorizationCode("", TEST_STATE);

        // Then
        verify(authorizationRepository, never()).findByState(any());
        verify(authorizationRepository, never()).save(any());
    }

    @Test
    void obtainTokenByAuthorizationCode_EmptyState() {
        // When
        squareAuthorizationService.obtainTokenByAuthorizationCode(TEST_CODE, "");

        // Then
        verify(authorizationRepository, never()).findByState(any());
        verify(authorizationRepository, never()).save(any());
    }

    @Test
    void obtainTokenByAuthorizationCode_ApiError() {
        // Given
        when(authorizationRepository.findByState(UUID.fromString(TEST_STATE))).thenReturn(authorization);
        when(cipherUtility.decrypt(any())).thenReturn(TEST_APP_ID, TEST_APP_SECRET);
        when(squareApiAdapter.obtainTokenByAuthorizationCode(any(), any(), any()))
            .thenThrow(new RuntimeException("API Error"));

        // When
        squareAuthorizationService.obtainTokenByAuthorizationCode(TEST_CODE, TEST_STATE);

        // Then
        verify(authorizationRepository, never()).save(any());
    }
} 