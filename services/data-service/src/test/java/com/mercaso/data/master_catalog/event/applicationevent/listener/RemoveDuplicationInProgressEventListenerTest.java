package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationInProgressEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationInProgressPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.mapper.MasterCatalogPotentiallyDuplicateRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogPotentiallyDuplicateRawDataService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class RemoveDuplicationInProgressEventListenerTest {

    private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;
    private MasterCatalogBatchJobMapper masterCatalogBatchJobMapper;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private RemoveDuplicationInProgressEventListener listener;
    private MasterCatalogPotentiallyDuplicateRawDataService masterCatalogPotentiallyDuplicateRawDataService;
    private MasterCatalogPotentiallyDuplicateRawDataMapper masterCatalogPotentiallyDuplicateRawDataMapper;
    private ApplicationEventPublisherProvider applicationEventPublisherProvider;

    @BeforeEach
    void setUp() {
        masterCatalogBatchJobMapper = mock(
            MasterCatalogBatchJobMapper.class);
        masterCatalogBatchJobRepository = mock(
            MasterCatalogBatchJobRepository.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        masterCatalogPotentiallyDuplicateRawDataService = mock(
                MasterCatalogPotentiallyDuplicateRawDataService.class);
        masterCatalogPotentiallyDuplicateRawDataMapper = mock(
            MasterCatalogPotentiallyDuplicateRawDataMapper.class);
        applicationEventPublisherProvider = mock(ApplicationEventPublisherProvider.class);

        listener = new RemoveDuplicationInProgressEventListener(
            masterCatalogBatchJobRepository,
            masterCatalogBatchJobMapper,
            masterCatalogPotentiallyDuplicateRawDataService,
            applicationEventPublisherProvider,
            masterCatalogPotentiallyDuplicateRawDataMapper);
    }

    @Test
    void testHandleEvent() {

        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();

        MasterCatalogBatchJobDto taskDto = MasterCatalogBatchJobDto.builder()
            .id(taskId).build();
        MasterCatalogBatchJob taskEntity = new MasterCatalogBatchJob();
        taskEntity.setId(taskId);

        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(rawDataId1);
        rawData1.setName("Product A");
        rawData1.setUpc("**********");
        rawData1.setPrimaryVendor("Vendor XYZ");
        rawData1.setCreatedAt(Instant.now());
        rawData1.setUpdatedAt(Instant.now());
        MasterCatalogRawData rawData2 = new MasterCatalogRawData();
        rawData2.setId(rawDataId2);
        rawData2.setName("Product B");
        rawData2.setUpc("**********");
        rawData2.setPrimaryVendor("Vendor XYZ");
        rawData2.setCreatedAt(Instant.now());
        rawData2.setUpdatedAt(Instant.now());

        List<List<UUID>> rawDataIds = Arrays.asList(
            Collections.singletonList(rawDataId1), Arrays.asList(rawDataId1, rawDataId2));
        RemoveDuplicationInProgressPayload payload = RemoveDuplicationInProgressPayload.builder()
            .batchJobId(taskId)
            .rawDataIds(rawDataIds)
            .build();
        payload.setData(taskDto);
        RemoveDuplicationInProgressEvent event = new RemoveDuplicationInProgressEvent(taskDto,
            payload);

        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(taskEntity);
        when(masterCatalogRawDataRepository.findAllById((List.of(rawDataId1)))).thenReturn(
            List.of(rawData1));
        when(masterCatalogRawDataRepository.findAllById(
            (Arrays.asList(rawDataId1, rawDataId2)))).thenReturn(
            Arrays.asList(rawData1, rawData2));

        listener.handleEvent(event);
        verify(masterCatalogPotentiallyDuplicateRawDataService).processPotentiallyDuplicateItem(
            eq(rawDataIds), eq(taskId),
            eq(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED), eq(MasterCatalogTaskType.DUPLICATION_IN_BATCH));

        verify(masterCatalogBatchJobRepository).save(
            any(MasterCatalogBatchJob.class));

        assert taskEntity.getStatus()
            .equals(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS);
    }

    @Test
    public void testHandleEvent_failWhenDuplicateServiceThrowsException() {
        // Arrange: create dummy task dto and payload
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        List<List<UUID>> rawDataIds = new ArrayList<>();
        rawDataIds.add(Arrays.asList(UUID.randomUUID(), UUID.randomUUID()));

        RemoveDuplicationInProgressPayload payload = new RemoveDuplicationInProgressPayload();
        payload.setData(taskDto);
        payload.setRawDataIds(rawDataIds);

        RemoveDuplicationInProgressEvent event = new RemoveDuplicationInProgressEvent(taskDto,
            payload);

        // Create a dummy task entity and configure mapper to return it
        MasterCatalogBatchJob task = new MasterCatalogBatchJob();
        task.setId(taskId);
        when(masterCatalogBatchJobMapper.toEntity(taskDto)).thenReturn(task);

        // Arrange: simulate a failure in duplicate processing service
        doThrow(new RuntimeException("Duplicate service failure"))
            .when(masterCatalogPotentiallyDuplicateRawDataService)
            .processPotentiallyDuplicateItem(rawDataIds, taskId,
                PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED, MasterCatalogTaskType.DUPLICATION_IN_BATCH);

        // Act & Assert: verify that the exception is thrown when handling the event
        RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
            listener.handleEvent(event);
        });
        assertEquals("Duplicate service failure", thrown.getMessage());

        // Verify that the repository save is not called because the process failed
        verify(masterCatalogBatchJobRepository, never()).save(
            any(MasterCatalogBatchJob.class));
    }

}
