package com.mercaso.data.metrics.controller;

import static com.mercaso.data.metrics.mock.MetricsItemReplenishmentForecastEntityMock.metricsItemReplenishmentForecastEntityMock;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.dto.MetricsItemReplenishmentForecastDto;
import com.mercaso.data.metrics.entity.MetricsItemReplenishmentForecastEntity;
import com.mercaso.data.metrics.repository.MetricsItemReplenishmentForecastRepository;
import com.mercaso.data.metrics.utils.controller_utils.SaleStatisticsRestApi;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SaleStatisticsControllerIT extends AbstractIT {

    @Autowired
    private SaleStatisticsRestApi saleStatisticsRestApi;

    @Autowired
    private MetricsItemReplenishmentForecastRepository metricsItemReplenishmentForecastRepository;


    @Test
    void testGetItemReplenishmentForecast() throws Exception {
        String addressId = UUID.randomUUID().toString();
        String departmentName = "Candy";

        MetricsItemReplenishmentForecastEntity metricsItemReplenishmentForecastEntity1 = metricsItemReplenishmentForecastEntityMock();
        metricsItemReplenishmentForecastEntity1.setAddressId(addressId);
        metricsItemReplenishmentForecastRepository.save(metricsItemReplenishmentForecastEntity1);

        MetricsItemReplenishmentForecastEntity metricsItemReplenishmentForecastEntity2 = metricsItemReplenishmentForecastEntityMock();
        metricsItemReplenishmentForecastEntity2.setAddressId(addressId);
        metricsItemReplenishmentForecastEntity2.setDepartment(departmentName);
        metricsItemReplenishmentForecastEntity2.setFilterDepartment(departmentName);
        metricsItemReplenishmentForecastEntity2.setNextOrderTime(Instant.now().plus(13, ChronoUnit.DAYS));
        MetricsItemReplenishmentForecastEntity save2 = metricsItemReplenishmentForecastRepository.save(
            metricsItemReplenishmentForecastEntity2);

        MetricsItemReplenishmentForecastEntity metricsItemReplenishmentForecastEntity3 = metricsItemReplenishmentForecastEntityMock();
        metricsItemReplenishmentForecastEntity3.setAddressId(addressId);
        metricsItemReplenishmentForecastEntity3.setDepartment(departmentName);
        metricsItemReplenishmentForecastEntity3.setFilterDepartment(departmentName);
        metricsItemReplenishmentForecastEntity3.setNextOrderTime(Instant.now().plus(15, ChronoUnit.DAYS));
        metricsItemReplenishmentForecastRepository.save(metricsItemReplenishmentForecastEntity3);

        List<MetricsItemReplenishmentForecastDto> result = saleStatisticsRestApi.getItemReplenishmentForecast(
            addressId,
            departmentName);
        assertNotNull(result);
        assert result.getFirst().getAddressId().equals(save2.getAddressId());
        assert result.getFirst().getDepartment().equals(save2.getDepartment());
    }
}
