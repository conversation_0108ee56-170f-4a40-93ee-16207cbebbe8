package com.mercaso.data.image_management.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.image_management.dto.ImageSearchRequestDto;
import com.mercaso.data.image_management.dto.Result;
import com.mercaso.data.image_management.entity.ImageManagementImage;
import com.mercaso.data.image_management.entity.ImageManagementItemImage;
import com.mercaso.data.image_management.enums.ImageTypeEnum;
import com.mercaso.data.image_management.repository.ImageManagementImageRepository;
import com.mercaso.data.image_management.repository.ImageManagementItemImageRepository;
import com.mercaso.data.image_management.utils.resource_utils.ImageManagementImageResourceApiUtils;
import com.mercaso.data.master_catalog.adaptor.ImsClientAdaptor;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.document.operations.operations.DocumentOperations;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
public class ImageManagementImageResourceIT extends AbstractIT {

  @Autowired
  private ImageManagementImageResourceApiUtils imageManagementImageResourceApiUtils;

  @Autowired
  private ImageManagementItemImageRepository imageManagementItemImageRepository;

  @Autowired
  private ImageManagementImageRepository imageManagementImageRepository;

  @MockBean
  private DocumentOperations documentOperations;

  @MockBean
  private ImsClientAdaptor imsClientAdaptor;

  @Test
  public void uploadImage() throws IOException {
    String shotAt = "2025-03-31";
    String fileName = "17506195178130_Front_2.png";
    ImageTypeEnum imageTypeEnum = ImageTypeEnum.RAW;

    String filePath = getFilePath("image/17506195178130_Front_2.jpeg");
    File file = new File(filePath);

    when(imsClientAdaptor.searchItemDetailByUpc(any())).thenReturn(List.of());

    when(documentOperations.uploadDocument(any())).thenReturn(null);
    Result<?> result = imageManagementImageResourceApiUtils.upload(shotAt, fileName,
        imageTypeEnum, file);

    assert ErrorCodeEnums.COMMON_CODE.getCode().equals(result.getCode());
  }

  @Test
  public void searchImages() throws IOException {
    String shotAt = "2025-03-31T15:45:00Z";
    String sku = "Test-Sku-31";
    String upc = "Test-Upc-31";
    String fileName = "17506195178130_Front_2.png";
    ImageTypeEnum imageTypeEnum = ImageTypeEnum.RAW;
    UUID imageId = UUID.randomUUID();
    String filePath = getFilePath("image/17506195178130_Front_2.jpeg");
    File file = new File(filePath);

    ImageManagementItemImage imageManagementItemImage = new ImageManagementItemImage();
    imageManagementItemImage.setImageId(imageId);
    imageManagementItemImage.setImageAngel("Front_2");
    imageManagementItemImage.setEachFlag(true);
    imageManagementItemImage.setIsPrimary(true);
    imageManagementItemImage.setSku(sku);
    imageManagementItemImage.setUpc(upc);
    imageManagementItemImage.setImageType(ImageTypeEnum.RAW.toString());

    imageManagementItemImageRepository.save(imageManagementItemImage);

    ImageManagementImage imageManagementImage = new ImageManagementImage();
    imageManagementImage.setId(imageId);
    imageManagementImage.setFileName(fileName);
    imageManagementImage.setFileSize(file.length());
    imageManagementImage.setFilePath(filePath);
    imageManagementImage.setMimeType("image/jpeg");
    imageManagementImage.setShotAt(Instant.parse(shotAt));
    imageManagementImage.setCreatedBy("testUser");
    imageManagementImageRepository.save(imageManagementImage);

    var request = ImageSearchRequestDto.builder()
        .sku(null)
        .upc(null)
        .imageType(imageTypeEnum)
        .imageAngle(null)
        .isPrimary(null)
        .isEach(null)
        .page(1)
        .pageSize(10)
        .sortBy(null)
        .sortDirection(null)
        .build();
    var page = imageManagementImageResourceApiUtils.searchImages(request);
    assert page != null;
    assert page.getTotalCount() > 0;
    assert page.getData().stream().anyMatch(
        img -> (img.getImageUrl() != null && img.getImageUrl().contains(fileName)) || (
            img.getImageType() != null && img.getImageType().equals(imageTypeEnum.name())));
  }

  private String getFilePath(String fileName) {
    return getClass().getClassLoader().getResource(fileName).getPath();
  }
}
