package com.mercaso.data.master_catalog.event.applicationevent.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.GenerateProductsSubmitEvent;
import com.mercaso.data.master_catalog.event.payload.domain.GenerateProductsSubmitPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePotentialDuplicationService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataService;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class GenerateProductsSubmitEventListenerTest {

    private MasterCatalogPotentiallyDuplicateRawDataRepository potentiallyDuplicateRawDataRepository;
    private MasterCatalogRawDataDuplicationRepository rawDataDuplicationRepository;
    private MasterCatalogRawDataDuplicationService rawDataDuplicationService;
    private MasterCatalogBatchJobRepository productGenerationTaskRepository;
    private MasterCatalogBatchJobMapper productGenerationTaskMapper;
    private ApplicationEventPublisherProvider eventPublisherProvider;
    private MasterCatalogRawDataService masterCatalogRawDataService;
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;
    private MasterCatalogProductRepository masterCatalogProductRepository;
    private MasterCatalogTaskRepository masterCatalogTaskRepository;
    private MasterCatalogGeneratePotentialDuplicationService masterCatalogGeneratePotentialDuplicationService;

    private GenerateProductsSubmitEventListener listener;

    @BeforeEach
    void setUp() {
        potentiallyDuplicateRawDataRepository = mock(MasterCatalogPotentiallyDuplicateRawDataRepository.class);
        rawDataDuplicationRepository = mock(MasterCatalogRawDataDuplicationRepository.class);
        rawDataDuplicationService = mock(MasterCatalogRawDataDuplicationService.class);
        productGenerationTaskRepository = mock(MasterCatalogBatchJobRepository.class);
        productGenerationTaskMapper = mock(MasterCatalogBatchJobMapper.class);
        eventPublisherProvider = mock(ApplicationEventPublisherProvider.class);
        masterCatalogRawDataService = mock(MasterCatalogRawDataService.class);
        masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
        masterCatalogProductRepository = mock(MasterCatalogProductRepository.class);
        masterCatalogTaskRepository = mock(MasterCatalogTaskRepository.class);
        masterCatalogGeneratePotentialDuplicationService = mock(
            MasterCatalogGeneratePotentialDuplicationService.class);
        listener = new GenerateProductsSubmitEventListener(potentiallyDuplicateRawDataRepository,
            rawDataDuplicationRepository,
            rawDataDuplicationService,
            productGenerationTaskRepository,
            productGenerationTaskMapper,
            eventPublisherProvider,
            masterCatalogRawDataService,
            masterCatalogRawDataRepository,
            masterCatalogProductRepository,
            masterCatalogTaskRepository,
            masterCatalogGeneratePotentialDuplicationService);
    }

    @Test
    void handleGenerateProductsSubmitEvent_shouldSkipWhenUnreviewedDataExists() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        GenerateProductsSubmitPayload payload = new GenerateProductsSubmitPayload();
        payload.setData(taskDto);

        GenerateProductsSubmitEvent event = new GenerateProductsSubmitEvent(this, payload);

        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();
        MasterCatalogPotentiallyDuplicateRawData unreviewedData = new MasterCatalogPotentiallyDuplicateRawData();
        unreviewedData.setStatus(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
        dataList.add(unreviewedData);

        when(potentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);
        when(productGenerationTaskMapper.toEntity(taskDto)).thenReturn(new MasterCatalogBatchJob());

        // Act
        listener.handleEvent(event);

        // Assert
        verify(rawDataDuplicationRepository, never()).saveAll(any());
    }

    @Test
    void handleGenerateProductsSubmitEvent_shouldProcessWhenAllDataReviewed() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        UUID rawDataId1 = UUID.randomUUID();
        UUID rawDataId2 = UUID.randomUUID();
        UUID groupId = UUID.randomUUID();

        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        GenerateProductsSubmitPayload payload = new GenerateProductsSubmitPayload();
        payload.setData(taskDto);

        GenerateProductsSubmitEvent event = new GenerateProductsSubmitEvent(this, payload);

        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();
        MasterCatalogPotentiallyDuplicateRawData reviewedData = new MasterCatalogPotentiallyDuplicateRawData();
        reviewedData.setStatus(PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED);
        reviewedData.setDuplicated(true);
        reviewedData.setRawDataId(rawDataId1);
        reviewedData.setName("Test Name");
        reviewedData.setPotentiallyDuplicateName("Test Name123");
        reviewedData.setPotentiallyDuplicateRawDataId(rawDataId2);
        dataList.add(reviewedData);

        MasterCatalogBatchJob taskEntity = MasterCatalogBatchJob.builder()
            .id(taskId)
            .status(MasterCatalogBatchJobStatus.GENERATE_PRODUCTS_IN_PROGRESS)
            .build();

        when(potentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);
        when(productGenerationTaskMapper.toEntity(taskDto)).thenReturn(taskEntity);

        // Act
        listener.handleEvent(event);

        // Assert
        verify(rawDataDuplicationRepository).saveAll(any());
        verify(productGenerationTaskRepository).save(any());
    }

    @Test
    void handleGenerateProductsSubmitEvent_shouldHandleExceptionGracefully() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        GenerateProductsSubmitPayload payload = new GenerateProductsSubmitPayload();
        payload.setData(taskDto);

        GenerateProductsSubmitEvent event = new GenerateProductsSubmitEvent(this, payload);

        when(potentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenThrow(new RuntimeException("Test exception"));

        // Act & Assert
        try {
            listener.handleEvent(event);
        } catch (Exception e) {
            assert e.getMessage().equals("Test exception");
        }
    }

    @Test
    void handleGenerateProductsSubmitEvent_shouldProcessMultipleDuplicates() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        GenerateProductsSubmitPayload payload = new GenerateProductsSubmitPayload();
        payload.setData(taskDto);

        GenerateProductsSubmitEvent event = new GenerateProductsSubmitEvent(this, payload);

        List<MasterCatalogPotentiallyDuplicateRawData> dataList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            MasterCatalogPotentiallyDuplicateRawData data = new MasterCatalogPotentiallyDuplicateRawData();
            data.setStatus(PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED);
            data.setDuplicated(true);
            data.setRawDataId(UUID.randomUUID());
            data.setName("Test Name" + i);
            data.setPotentiallyDuplicateRawDataId(UUID.randomUUID());
            data.setPotentiallyDuplicateName("Test Name123" + i);
            dataList.add(data);
        }

        when(potentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(dataList);
        when(productGenerationTaskMapper.toEntity(taskDto)).thenReturn(new MasterCatalogBatchJob());

        // Act
        listener.handleEvent(event);

        // Assert
        verify(rawDataDuplicationRepository).saveAll(any());
        verify(productGenerationTaskRepository).save(any());
    }

    @Test
    void handleGenerateProductsSubmitEvent_shouldHandleEmptyDuplicationList() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogBatchJobDto taskDto = new MasterCatalogBatchJobDto();
        taskDto.setId(taskId);

        GenerateProductsSubmitPayload payload = new GenerateProductsSubmitPayload();
        payload.setData(taskDto);

        GenerateProductsSubmitEvent event = new GenerateProductsSubmitEvent(this, payload);

        when(potentiallyDuplicateRawDataRepository.findAllByJobId(taskId)).thenReturn(List.of());
        when(productGenerationTaskMapper.toEntity(taskDto)).thenReturn(new MasterCatalogBatchJob());

        // Act
        listener.handleEvent(event);

        // Assert
        verify(rawDataDuplicationRepository, never()).saveAll(any());
        verify(productGenerationTaskRepository).save(any());
    }
}
