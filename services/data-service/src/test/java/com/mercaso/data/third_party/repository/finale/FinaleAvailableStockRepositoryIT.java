package com.mercaso.data.third_party.repository.finale;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.mock.finale.FinaleAvailableStockEntityMock;
import jakarta.annotation.PostConstruct;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class FinaleAvailableStockRepositoryIT extends AbstractIT {

    @Autowired
    private FinaleAvailableStockRepository availableStockRepository;

    @PostConstruct
    void init() {
        //init data
        FinaleAvailableStockEntity entity = FinaleAvailableStockEntityMock.customerEntityMock();
        availableStockRepository.save(entity);
    }

    @Test
    void testFindAll() {
        List<FinaleAvailableStockEntity> resultList = availableStockRepository.findAll();
        Assertions.assertNotEquals(0, resultList.size());
    }


}
