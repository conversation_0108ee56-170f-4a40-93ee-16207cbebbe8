package com.mercaso.data.metrics.mock;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.metrics.entity.MetricsOrderAmountEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MetricsOrderAmountEntityMock {

    public static MetricsOrderAmountEntity metricsOrderAmountEntityMock(String department, String dataType, String addressId,
        String filterDepartment) {
        MetricsOrderAmountEntity metricsOrderAmountEntity = new MetricsOrderAmountEntity();


        metricsOrderAmountEntity.setAddressId(addressId);
        metricsOrderAmountEntity.setFilterDepartment(filterDepartment);
        metricsOrderAmountEntity.setDateType(dataType);
        metricsOrderAmountEntity.setDate(LocalDateTime.now());
        metricsOrderAmountEntity.setTotalAmount(BigDecimal.valueOf(100.00));
        metricsOrderAmountEntity.setTotalQuantity(10);
        metricsOrderAmountEntity.setUniqueItems(10);

        List<Map<String, String>> itemsList = new ArrayList<>();
        itemsList.add(new HashMap<>() {{
            put("rank", "1");
            put("quantity", "10");
        }});
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.valueToTree(itemsList);

        metricsOrderAmountEntity.setItems(jsonNode);
        metricsOrderAmountEntity.setCreatedAt(LocalDateTime.now());
        metricsOrderAmountEntity.setDepartment(department);
        
        return metricsOrderAmountEntity;
    }
}
