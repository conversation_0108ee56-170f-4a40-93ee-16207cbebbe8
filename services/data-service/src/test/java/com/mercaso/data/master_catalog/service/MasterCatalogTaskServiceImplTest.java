package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.mapper.MasterCatalogTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogTaskServiceImpl;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

@ExtendWith(MockitoExtension.class)
class MasterCatalogTaskServiceImplTest {

  @Mock
  private MasterCatalogTaskMapper masterCatalogTaskMapper;

  @Mock
  private MasterCatalogTaskRepository masterCatalogTaskRepository;

  @Mock
  private MasterCatalogPotentiallyDuplicateRawDataRepository duplicateRawDataRepository;

  @InjectMocks
  private MasterCatalogTaskServiceImpl masterCatalogTaskService;

  private MockedStatic<SecurityContextUtil> securityContextUtil;

  private UUID taskId;
  private UUID jobId;
  private MasterCatalogTask task;
  private MasterCatalogTaskDto taskDto;

  @BeforeEach
  void setUp() {
    taskId = UUID.randomUUID();
    jobId = UUID.randomUUID();
    securityContextUtil = Mockito.mockStatic(SecurityContextUtil.class);

    task = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    taskDto = MasterCatalogTaskDto.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();
  }

  @AfterEach
  void tearDown() {
    securityContextUtil.close();
  }

  @Test
  void createTasks_ShouldCreateTasksSuccessfully() {

    Integer taskCount = 3;
    List<MasterCatalogTask> savedTasks = List.of(task, task, task);

    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(savedTasks);
    when(masterCatalogTaskMapper.toDto(any(MasterCatalogTask.class))).thenReturn(taskDto);

    List<MasterCatalogTaskDto> result = masterCatalogTaskService.createTasks(jobId,
        MasterCatalogTaskType.DUPLICATION_IN_BATCH, taskCount);

    // Then
    assertEquals(3, result.size());
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> tasks) ->
        tasks.size() == 3 &&
        tasks.stream().allMatch(t ->
            t.getJobId().equals(jobId) &&
            t.getStatus() == MasterCatalogTaskStatus.PENDING &&
            t.getType() == MasterCatalogTaskType.DUPLICATION_IN_BATCH
        )
    ));
    verify(masterCatalogTaskMapper, times(3)).toDto(any(MasterCatalogTask.class));
  }

  @Test
  void assign_WhenTaskExistsAndPending_ShouldAssignSuccessfully() {
    // Given
    String assignedTo = UUID.randomUUID().toString();
    String currentUserId = UUID.randomUUID().toString();

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
    when(masterCatalogTaskRepository.save(any(MasterCatalogTask.class))).thenReturn(task);

    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

    masterCatalogTaskService.assign(taskId, assignedTo);

    verify(masterCatalogTaskRepository).save(argThat(savedTask ->
        assignedTo.equals(savedTask.getAssignedTo()) &&
        currentUserId.equals(savedTask.getAssignedBy()) &&
        savedTask.getStatus() == MasterCatalogTaskStatus.ASSIGNED
    ));
  }

  @Test
  void assign_WhenTaskNotFound_ShouldThrowException() {

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.empty());

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "task not fount with id");
  }

  @Test
  void assign_WhenTaskNotPending_ShouldThrowException() {

    task.setStatus(MasterCatalogTaskStatus.COMPLETED);
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "the task is not a available task");
  }

  @Test
  void search_WithNullParameters_ShouldReturnAllResults() {
    // Given
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);

    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog"));
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null,null, null, null, null, null, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void search_WithEmptyStringAssignTo_ShouldIgnoreParameter() {
    // Given
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog"));
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);
    
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null, null, null, "", MasterCatalogTaskStatus.PENDING, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void search_WithoutPermission_ShouldReturnEmptyPage() {
    // Given
    PageRequest pageRequest = PageRequest.of(0, 10);
    
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("other_role"));
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null,  null, null,  null, null, null, pageRequest);
    
    // Then
    assertTrue(result.isEmpty());
    verify(masterCatalogTaskRepository, never()).findAll(any(Specification.class), any(PageRequest.class));
  }

  @Test
  void search_WithReviewerRole_ShouldFilterByCurrentUser() {
    // Given
    String currentUserId = "reviewer123";
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);
    
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog_reviewer"));
    securityContextUtil.when(SecurityContextUtil::getLoginUserId)
        .thenReturn(currentUserId);
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null,null, null, null, null, null, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void getDetailsById_WithValidId_ShouldReturnTaskWithDetails() {

    UUID taskId = UUID.randomUUID();
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles).thenReturn(List.of("admin_master_catalog"));
    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn("test-user");
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
    when(masterCatalogTaskMapper.toDto(task)).thenReturn(taskDto);
    when(duplicateRawDataRepository.findByTaskId(taskId)).thenReturn(Collections.emptyList());

    MasterCatalogTaskDto result = masterCatalogTaskService.getDetailsById(taskId);

    assertNotNull(result);
    assertEquals(0, result.getTotalRecordCount());
    verify(masterCatalogTaskRepository).findById(taskId);
    verify(duplicateRawDataRepository).findByTaskId(taskId);
  }

  @Test
  void getDetailsById_WithNonExistentId_ShouldThrowException() {
    // Given
    UUID taskId = UUID.randomUUID();
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.empty());

    // When & Then
    assertThrows(Exception.class, () -> masterCatalogTaskService.getDetailsById(taskId));
  }

  @Test
  void batchAssign_WithValidTasks_ShouldAssignAllSuccessfully() {
    // Given
    UUID taskId1 = UUID.randomUUID();
    UUID taskId2 = UUID.randomUUID();
    UUID taskId3 = UUID.randomUUID();
    List<UUID> taskIds = List.of(taskId1, taskId2, taskId3);
    String assignTo = "testUser";
    String currentUserId = "currentUser";

    MasterCatalogTask task1 = MasterCatalogTask.builder()
        .id(taskId1)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    MasterCatalogTask task2 = MasterCatalogTask.builder()
        .id(taskId2)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    MasterCatalogTask task3 = MasterCatalogTask.builder()
        .id(taskId3)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.ASSIGNED)
        .assignedTo("previousUser")
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    List<MasterCatalogTask> tasks = List.of(task1, task2, task3);

    when(masterCatalogTaskRepository.findAllById(taskIds)).thenReturn(tasks);
    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(tasks);
    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

    // When
    masterCatalogTaskService.batchAssign(taskIds, assignTo);

    // Then
    verify(masterCatalogTaskRepository).findAllById(taskIds);
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> savedTasks) ->
        savedTasks.size() == 3 &&
        savedTasks.stream().allMatch(t ->
            assignTo.equals(t.getAssignedTo()) &&
            currentUserId.equals(t.getAssignedBy()) &&
            (t.getId().equals(taskId1) ? t.getStatus() == MasterCatalogTaskStatus.ASSIGNED : true) &&
            (t.getId().equals(taskId2) ? t.getStatus() == MasterCatalogTaskStatus.ASSIGNED : true) &&
            (t.getId().equals(taskId3) ? t.getStatus() == MasterCatalogTaskStatus.ASSIGNED : true)
        )
    ));
  }

  @Test
  void batchAssign_WithEmptyList_ShouldHandleGracefully() {
    // Given
    List<UUID> emptyTaskIds = List.of();
    String assignTo = "testUser";

    when(masterCatalogTaskRepository.findAllById(emptyTaskIds)).thenReturn(List.of());

    // When
    masterCatalogTaskService.batchAssign(emptyTaskIds, assignTo);

    // Then
    verify(masterCatalogTaskRepository).findAllById(emptyTaskIds);
    verify(masterCatalogTaskRepository).saveAll(List.of());
  }

  @Test
  void batchAssign_WithCompletedTask_ShouldThrowException() {
    // Given
    UUID taskId = UUID.randomUUID();
    List<UUID> taskIds = List.of(taskId);
    String assignTo = "testUser";

    MasterCatalogTask completedTask = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.COMPLETED)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    when(masterCatalogTaskRepository.findAllById(taskIds)).thenReturn(List.of(completedTask));

    // When & Then
    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.batchAssign(taskIds, assignTo),
        "the task is not a available task");

    verify(masterCatalogTaskRepository).findAllById(taskIds);
    verify(masterCatalogTaskRepository, never()).saveAll(anyList());
  }

  @Test
  void batchAssign_WithMixedStatusTasks_ShouldUpdateOnlyValidTasks() {
    // Given
    UUID taskId1 = UUID.randomUUID();
    UUID taskId2 = UUID.randomUUID();
    List<UUID> taskIds = List.of(taskId1, taskId2);
    String assignTo = "testUser";
    String currentUserId = "currentUser";

    MasterCatalogTask pendingTask = MasterCatalogTask.builder()
        .id(taskId1)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    MasterCatalogTask assignedTask = MasterCatalogTask.builder()
        .id(taskId2)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.ASSIGNED)
        .assignedTo("previousUser")
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    List<MasterCatalogTask> tasks = List.of(pendingTask, assignedTask);

    when(masterCatalogTaskRepository.findAllById(taskIds)).thenReturn(tasks);
    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(tasks);
    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

    // When
    masterCatalogTaskService.batchAssign(taskIds, assignTo);

    // Then
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> savedTasks) ->
        savedTasks.size() == 2 &&
        savedTasks.stream().allMatch(t ->
            assignTo.equals(t.getAssignedTo()) &&
            currentUserId.equals(t.getAssignedBy())
        ) &&
        // Pending task should become ASSIGNED
        savedTasks.stream().anyMatch(t -> 
            t.getId().equals(taskId1) && t.getStatus() == MasterCatalogTaskStatus.ASSIGNED
        ) &&
        // Already assigned task should remain ASSIGNED
        savedTasks.stream().anyMatch(t -> 
            t.getId().equals(taskId2) && t.getStatus() == MasterCatalogTaskStatus.ASSIGNED
        )
    ));
  }

  @Test
  void batchAssign_WithSingleTask_ShouldAssignSuccessfully() {
    // Given
    UUID singleTaskId = UUID.randomUUID();
    List<UUID> taskIds = List.of(singleTaskId);
    String assignTo = "singleUser";
    String currentUserId = "currentUser";

    MasterCatalogTask singleTask = MasterCatalogTask.builder()
        .id(singleTaskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    when(masterCatalogTaskRepository.findAllById(taskIds)).thenReturn(List.of(singleTask));
    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(List.of(singleTask));
    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

    // When
    masterCatalogTaskService.batchAssign(taskIds, assignTo);

    // Then
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> savedTasks) ->
        savedTasks.size() == 1 &&
        savedTasks.get(0).getAssignedTo().equals(assignTo) &&
        savedTasks.get(0).getAssignedBy().equals(currentUserId) &&
        savedTasks.get(0).getStatus() == MasterCatalogTaskStatus.ASSIGNED
    ));
  }
}
