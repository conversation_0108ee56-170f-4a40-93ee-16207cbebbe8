package com.mercaso.data.third_party.mock.shopify;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.data.third_party.entity.shopify.order.ShopifyOrdersLineItemEntity;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.RandomUtils;

public class ShopifyOrderLineItemEntityMock {

    public static ShopifyOrdersLineItemEntity ordersLineItemMock(){
        ObjectMapper objectMapper = new ObjectMapper();

        ShopifyOrdersLineItemEntity lineItemEntity = new ShopifyOrdersLineItemEntity();
        lineItemEntity.setId(RandomUtils.nextLong());
        lineItemEntity.setAdminGraphqlApiId("gid://shopify/LineItem/466157049");
        lineItemEntity.setAttributedStaffs(objectMapper.valueToTree(Collections.emptyList()));
        lineItemEntity.setCurrentQuantity(0);
        lineItemEntity.setFulfillableQuantity(0);
        lineItemEntity.setFulfillmentService("manual");
        lineItemEntity.setFulfillmentStatus(null);
        lineItemEntity.setGiftCard(false);
        lineItemEntity.setGrams(200);
        lineItemEntity.setName("IPod Nano - 8gb - green");
        lineItemEntity.setPrice(new BigDecimal("199.00"));
        lineItemEntity.setPriceSet(objectMapper.valueToTree(Map.of(
            "shop_money", Map.of("amount", "199.00", "currency_code", "USD"),
            "presentment_money", Map.of("amount", "199.00", "currency_code", "USD")
        )));
        lineItemEntity.setProductExists(true);
        lineItemEntity.setProductId(632910392L);
        lineItemEntity.setProperties(objectMapper.valueToTree(List.of(
            Map.of("name", "Custom Engraving Front", "value", "Happy Birthday"),
            Map.of("name", "Custom Engraving Back", "value", "Merry Christmas")
        )));
        lineItemEntity.setQuantity(1);
        lineItemEntity.setRequiresShipping(true);
        lineItemEntity.setSku("IPOD2008GREEN");
        lineItemEntity.setTaxable(true);
        lineItemEntity.setTitle("IPod Nano - 8gb");
        lineItemEntity.setTotalDiscount(new BigDecimal("0.00"));
        lineItemEntity.setTotalDiscountSet(objectMapper.valueToTree(Map.of(
            "shop_money", Map.of("amount", "0.00", "currency_code", "USD"),
            "presentment_money", Map.of("amount", "0.00", "currency_code", "USD")
        )));
        lineItemEntity.setVariantId(39072856L);
        lineItemEntity.setVariantInventoryManagement("shopify");
        lineItemEntity.setVariantTitle("green");
        lineItemEntity.setVendor(null);
        lineItemEntity.setTaxLines(objectMapper.valueToTree(List.of(
            Map.of(
                "channel_liable", "",
                "price", "3.98",
                "price_set", Map.of(
                    "shop_money", Map.of("amount", "3.98", "currency_code", "USD"),
                    "presentment_money", Map.of("amount", "3.98", "currency_code", "USD")
                ),
                "rate", 0.06,
                "title", "State Tax"
            )
        )));
        lineItemEntity.setDuties(objectMapper.valueToTree(Collections.emptyList()));
        lineItemEntity.setDiscountAllocations(objectMapper.valueToTree(List.of(
            Map.of(
                "amount", "3.34",
                "amount_set", Map.of(
                    "shop_money", Map.of("amount", "3.34", "currency_code", "USD"),
                    "presentment_money", Map.of("amount", "3.34", "currency_code", "USD")
                ),
                "discount_application_index", 0
            )
        )));

        lineItemEntity.setRefunded(true);

        return lineItemEntity;
    }
}
