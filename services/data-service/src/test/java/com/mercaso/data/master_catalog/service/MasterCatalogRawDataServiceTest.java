package com.mercaso.data.master_catalog.service;

import static com.mercaso.data.master_catalog.utils.dto.MasterCatalogRawDataDtoUtils.buildMasterCatalogRawDataDto;
import static com.mercaso.data.master_catalog.utils.dto.SearchMasterCatalogRequestUtils.buildSearchMasterCatalogRequest;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.adaptor.S3OperationAdapter;
import com.mercaso.data.master_catalog.dto.MasterCatalogRawDataDto;
import com.mercaso.data.master_catalog.dto.SearchMasterCatalogRequest;
import com.mercaso.data.master_catalog.dto.external.UpcByDescriptionDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogImage;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.mapper.MasterCatalogRawDataMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogImageRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogRawDataServiceImpl;
import com.mercaso.data.utils.NormalizeUtils;
import com.mercaso.data.utils.SerializationUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

class MasterCatalogRawDataServiceTest {

    private final OkHttpClient mockClient = mock(OkHttpClient.class);
    private final Call mockCall = mock(Call.class);
    private final Response mockResponse = mock(Response.class);

    private final MasterCatalogRawDataRepository masterCatalogRawDataRepository = mock(MasterCatalogRawDataRepository.class);
    private final MasterCatalogRawDataMapper masterCatalogRawDataMapper = mock(MasterCatalogRawDataMapper.class);
    private final MasterCatalogImageRepository masterCatalogImageRepository = mock(MasterCatalogImageRepository.class);
    private final ExternalApiAdapter externalApiAdapter = mock(ExternalApiAdapter.class);
    private final S3OperationAdapter s3OperationAdapter = mock(S3OperationAdapter.class);
    private final MasterCatalogBatchJobRepository masterCatalogBatchJobRepository =
        mock(MasterCatalogBatchJobRepository.class);
    private final MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository =
            mock(MasterCatalogRawDataDuplicationRepository.class);
  private final ApplicationEventPublisherProvider applicationEventPublisherProvider = mock(
      ApplicationEventPublisherProvider.class);

    private final MasterCatalogRawDataService masterCatalogRawDataService = new MasterCatalogRawDataServiceImpl(
        masterCatalogRawDataDuplicationRepository,
        masterCatalogRawDataRepository,
        masterCatalogRawDataMapper,
        masterCatalogImageRepository,
        externalApiAdapter,
        s3OperationAdapter,
        applicationEventPublisherProvider);

    @Test
    void searchMasterCatalogRawDataByUpsIs() {
        Page<MasterCatalogRawData> masterCatalogRawDataPage = new PageImpl<>(List.of(buildMasterCatalogRawData(UUID.randomUUID())),
            PageRequest.of(1, 2),
            1);
        when(masterCatalogRawDataRepository.findByUpcIs(any(), any())).thenReturn(masterCatalogRawDataPage);

        MasterCatalogRawDataDto masterCatalogRawDataDto = buildMasterCatalogRawDataDto();
        when(masterCatalogRawDataMapper.toDto(any())).thenReturn(masterCatalogRawDataDto);

        SearchMasterCatalogRequest searchMasterCatalogRequest = buildSearchMasterCatalogRequest();
        searchMasterCatalogRequest.setUpc("10000111");
        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(searchMasterCatalogRequest);
        assert result.getContent().contains(masterCatalogRawDataDto);
    }

    @Test
    void searchMasterCatalogRawDataByStoreIdIs() {
        Page<MasterCatalogRawData> masterCatalogRawDataPage = new PageImpl<>(List.of(buildMasterCatalogRawData(UUID.randomUUID())),
            PageRequest.of(1, 2),
            1);
        when(masterCatalogRawDataRepository.findByStoreIdIs(any(), any())).thenReturn(masterCatalogRawDataPage);

        MasterCatalogRawDataDto masterCatalogRawDataDto = buildMasterCatalogRawDataDto();
        when(masterCatalogRawDataMapper.toDto(any())).thenReturn(masterCatalogRawDataDto);

        SearchMasterCatalogRequest searchMasterCatalogRequest = buildSearchMasterCatalogRequest();
        searchMasterCatalogRequest.setStoreId(UUID.randomUUID().toString());
        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(searchMasterCatalogRequest);
        assert result.getContent().contains(masterCatalogRawDataDto);
    }

    @Test
    void searchMasterCatalogRawDataWithEmptyRequest() {
        Page<MasterCatalogRawData> masterCatalogRawDataPage = new PageImpl<>(List.of(buildMasterCatalogRawData(UUID.randomUUID())),
            PageRequest.of(1, 2),
            1);
        when(masterCatalogRawDataRepository.findAll((Pageable) any())).thenReturn(masterCatalogRawDataPage);

        MasterCatalogRawDataDto masterCatalogRawDataDto = buildMasterCatalogRawDataDto();
        when(masterCatalogRawDataMapper.toDto(any())).thenReturn(masterCatalogRawDataDto);

        SearchMasterCatalogRequest searchMasterCatalogRequest = buildSearchMasterCatalogRequest();
        searchMasterCatalogRequest.setUpc(null);
        searchMasterCatalogRequest.setStoreId(null);
        searchMasterCatalogRequest.setDescription(null);
        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(searchMasterCatalogRequest);
        assert result.getContent().contains(masterCatalogRawDataDto);
    }

    @Test
    void searchMasterCatalogRawDataWithInvalidStoreId() {
        SearchMasterCatalogRequest searchMasterCatalogRequest = buildSearchMasterCatalogRequest();
        searchMasterCatalogRequest.setStoreId("invalid-uuid");
        assertThrows(IllegalArgumentException.class,
            () -> masterCatalogRawDataService.searchMasterCatalogRawData(searchMasterCatalogRequest));
    }

    @Test
    void searchMasterCatalogRawDataReturnsImages() {
        MasterCatalogRawData rawData = buildMasterCatalogRawData(UUID.randomUUID());
        UUID rawDataId = rawData.getId();
        Page<MasterCatalogRawData> masterCatalogRawDataPage = new PageImpl<>(List.of(rawData), PageRequest.of(0, 10), 1);
        when(masterCatalogRawDataRepository.findAll(any(PageRequest.class))).thenReturn(masterCatalogRawDataPage);

        MasterCatalogImage image = new MasterCatalogImage();
        image.setMasterCatalogRawDataId(rawDataId);
        image.setImagePath("https://drive.google.com/file/d/1Uof-u5HBfRHsSqL7ACPRepdzYaWWMSYn/view?usp=drive_link");
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(any())).thenReturn(List.of(image));
        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://1Uof-u5HBfRHsSqL7ACPRepdzYaWWMSYn.jpg");

        MasterCatalogRawDataDto masterCatalogRawDataDto = buildMasterCatalogRawDataDto();
        when(masterCatalogRawDataMapper.toDto(any())).thenReturn(masterCatalogRawDataDto);

        SearchMasterCatalogRequest searchMasterCatalogRequest = buildSearchMasterCatalogRequest();
        searchMasterCatalogRequest.setUpc(null);
        searchMasterCatalogRequest.setStoreId(null);
        searchMasterCatalogRequest.setDescription(null);
        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(searchMasterCatalogRequest);
        assert result.getContent().getFirst().getImages().getFirst().contains("1Uof-u5HBfRHsSqL7ACPRepdzYaWWMSYn");
    }

    @Test
    void searchMasterCatalogRawData_withDescription_shouldReturnMatchingData() throws IOException {
        // Prepare test data
        String description = "test description";
        SearchMasterCatalogRequest searchRequest = new SearchMasterCatalogRequest();
        searchRequest.setDescription(description);
        searchRequest.setPage(1);
        searchRequest.setPageSize(10);

        List<UpcByDescriptionDto> apiResponse = List.of(
            new UpcByDescriptionDto("Test Product 1", "123456"),
            new UpcByDescriptionDto("Test Product 2", "789012")
        );

        List<MasterCatalogRawData> rawDataList = List.of(
            buildMasterCatalogRawData(UUID.randomUUID()),
            buildMasterCatalogRawData(UUID.randomUUID())
        );

        Page<MasterCatalogRawData> rawDataPage = new PageImpl<>(rawDataList, PageRequest.of(0, 10), 2);

        when(mockClient.newCall(any(Request.class))).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(mockResponse);
        when(mockResponse.isSuccessful()).thenReturn(true);
        when(mockResponse.body()).thenReturn(ResponseBody.create(SerializationUtils.serialize(apiResponse),
            MediaType.get("application/json")));

        // Mock database query
        when(masterCatalogRawDataRepository.findByUpcIn(anyList(), any(PageRequest.class))).thenReturn(rawDataPage);

        // Mock DTO conversion
        when(masterCatalogRawDataMapper.toDto(any(MasterCatalogRawData.class)))
            .thenAnswer(invocation -> {
                MasterCatalogRawData entity = invocation.getArgument(0);
                MasterCatalogRawDataDto dto = new MasterCatalogRawDataDto();
                dto.setUpc(entity.getUpc());
                dto.setName(entity.getName());
                return dto;
            });

        // Execute test
        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(searchRequest);

        // Verify results
        assertEquals(2, result.getTotalElements());
        assertEquals(2, result.getContent().size());
        assertEquals(rawDataList.get(0).getUpc(), result.getContent().get(0).getUpc());
        assertEquals(rawDataList.get(1).getUpc(), result.getContent().get(1).getUpc());
    }

    @Test
    void searchMasterCatalogRawData_UPC_EmptyResult() {
        SearchMasterCatalogRequest request = new SearchMasterCatalogRequest();
        request.setUpc("123456");
        request.setPage(1);
        request.setPageSize(20);

        PageRequest pageRequest = PageRequest.of(0, 20);
        when(masterCatalogRawDataRepository.findByUpcIs("123456", pageRequest))
            .thenReturn(Page.empty());

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(request);

        assertThat(result).isEmpty();
    }

    @Test
    void searchMasterCatalogRawData_UPC_WithResult() {
        SearchMasterCatalogRequest request = new SearchMasterCatalogRequest();
        request.setUpc("123456");
        request.setPage(1);
        request.setPageSize(20);

        PageRequest pageRequest = PageRequest.of(0, 20);
        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(UUID.randomUUID());
        Page<MasterCatalogRawData> rawDataPage = new PageImpl<>(List.of(rawData));

        when(masterCatalogRawDataRepository.findByUpcIs("123456", pageRequest))
            .thenReturn(rawDataPage);

        MasterCatalogImage image = new MasterCatalogImage();
        image.setMasterCatalogRawDataId(rawData.getId());
        image.setImagePath("path/to/image.jpg");

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(List.of(rawData.getId())))
            .thenReturn(List.of(image));
        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://image.jpg");

        MasterCatalogRawDataDto dto = new MasterCatalogRawDataDto();
        when(masterCatalogRawDataMapper.toDto(rawData)).thenReturn(dto);

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(request);

        assertThat(result).hasSize(1);
        assertThat(result.getContent().getFirst().getImages()).containsExactly("http://image.jpg");
    }

    @Test
    void searchMasterCatalogRawData_STORE_ID() {
        SearchMasterCatalogRequest request = new SearchMasterCatalogRequest();
        request.setStoreId(UUID.randomUUID().toString());
        request.setPage(1);
        request.setPageSize(20);

        PageRequest pageRequest = PageRequest.of(0, 20);
        when(masterCatalogRawDataRepository.findByStoreIdIs(any(UUID.class), eq(pageRequest)))
            .thenReturn(Page.empty());

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(request);

        assertTrue(result.isEmpty());
    }

    @Test
    void searchMasterCatalogRawData_ALL() {
        SearchMasterCatalogRequest request = new SearchMasterCatalogRequest();
        request.setPage(1);
        request.setPageSize(20);

        PageRequest pageRequest = PageRequest.of(0, 20);
        when(masterCatalogRawDataRepository.findAll(pageRequest))
            .thenReturn(Page.empty());

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawData(request);

        assertTrue(result.isEmpty());
    }

    @Test
    void searchMasterCatalogRawData_Pagination() {
        SearchMasterCatalogRequest request = new SearchMasterCatalogRequest();
        request.setPage(2);
        request.setPageSize(10);

        PageRequest pageRequest = PageRequest.of(1, 10);
        when(masterCatalogRawDataRepository.findAll(pageRequest))
            .thenReturn(Page.empty());

        masterCatalogRawDataService.searchMasterCatalogRawData(request);

        verify(masterCatalogRawDataRepository).findAll(pageRequest);
    }

    @Test
    void searchMasterCatalogRawDataByImage_shouldReturnMatchingData() {
        MultipartFile file = mock(MultipartFile.class);
        Pageable pageable = PageRequest.of(0, 10);

        List<String> upcs = List.of("123456", "789012");
        when(externalApiAdapter.getUpcsFromExternalApiByImage(file)).thenReturn(upcs);

        MasterCatalogRawData rawData1 = buildMasterCatalogRawData(UUID.randomUUID());
        MasterCatalogRawData rawData2 = buildMasterCatalogRawData(UUID.randomUUID());
        Page<MasterCatalogRawData> rawDataPage = new PageImpl<>(List.of(rawData1, rawData2), pageable, 2);

        when(masterCatalogRawDataRepository.findByUpcIn(upcs, pageable)).thenReturn(rawDataPage);
        when(s3OperationAdapter.getSignedUrl(any())).thenReturn("http://image1.jpg").thenReturn("http://image2.jpg");

        MasterCatalogImage image1 = new MasterCatalogImage();
        image1.setMasterCatalogRawDataId(rawData1.getId());
        image1.setImagePath("path/to/image1.jpg");

        MasterCatalogImage image2 = new MasterCatalogImage();
        image2.setMasterCatalogRawDataId(rawData2.getId());
        image2.setImagePath("path/to/image2.jpg");

        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(List.of(rawData1.getId(), rawData2.getId())))
            .thenReturn(List.of(image1, image2));

        MasterCatalogRawDataDto dto1 = new MasterCatalogRawDataDto();
        dto1.setImages(List.of("path/to/image1.jpg"));
        when(masterCatalogRawDataMapper.toDto(rawData1)).thenReturn(dto1);

        MasterCatalogRawDataDto dto2 = new MasterCatalogRawDataDto();
        dto2.setImages(List.of("path/to/image2.jpg"));
        when(masterCatalogRawDataMapper.toDto(rawData2)).thenReturn(dto2);

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawDataByImage(file, pageable);

        assertThat(result).hasSize(2);
        assertThat(result.getContent().get(0).getImages()).containsExactly("http://image1.jpg");
        assertThat(result.getContent().get(1).getImages()).containsExactly("http://image2.jpg");
    }

    @Test
    void searchMasterCatalogRawDataByImage_shouldReturnEmptyPageWhenNoDataFound() {
        MultipartFile file = mock(MultipartFile.class);
        Pageable pageable = PageRequest.of(0, 10);

        List<String> upcs = List.of("123456", "789012");
        when(externalApiAdapter.getUpcsFromExternalApiByImage(file)).thenReturn(upcs);

        when(masterCatalogRawDataRepository.findByUpcIn(upcs, pageable)).thenReturn(Page.empty());

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchMasterCatalogRawDataByImage(file, pageable);

        assertThat(result).isEmpty();
    }

    @Test
    void normalizeUpc_shouldPadWithLeadingZeros() {
        // Test cases for UPC normalization
        assertEquals("00000000123456", NormalizeUtils.normalizeUpc("123456"));
        assertEquals("00000000123456", NormalizeUtils.normalizeUpc("0123456"));
        assertEquals("12345678901234", NormalizeUtils.normalizeUpc("12345678901234"));
        assertEquals("00000012345678", NormalizeUtils.normalizeUpc("12345678"));
        assertEquals(null, NormalizeUtils.normalizeUpc(null));
        assertEquals("", NormalizeUtils.normalizeUpc(""));
    }

    @Test
    void normalizeUpc_shouldRemoveNonDigitCharacters() {
        assertEquals("00000000123456", NormalizeUtils.normalizeUpc("123-456"));
        assertEquals("00000000123456", NormalizeUtils.normalizeUpc("123 456"));
        assertEquals("00000000123456", NormalizeUtils.normalizeUpc("ABC123456"));
    }

    @Test
    void markAsCompleted_shouldUpdateStatusAndCompletedBy() {
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        Set<UUID> ids = Set.of(id1, id2);
        String completedBy = "Test User";

        MasterCatalogRawData rawData1 = new MasterCatalogRawData();
        rawData1.setId(id1);

        MasterCatalogRawData rawData2 = new MasterCatalogRawData();
        rawData2.setId(id2);

        List<MasterCatalogRawData> rawDataList = List.of(rawData1, rawData2);

        when(masterCatalogRawDataRepository.findAllById(ids)).thenReturn(rawDataList);

        masterCatalogRawDataService.markAsCompleted(ids);

        ArgumentCaptor<List<MasterCatalogRawData>> captor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogRawDataRepository).saveAll(captor.capture());

        List<MasterCatalogRawData> savedData = captor.getValue();
        assertEquals(2, savedData.size());
    }

    @Test
    void markAsCompleted_withEmptyCompletedBy_shouldUseDefaultValue() {
        UUID id = UUID.randomUUID();
        Set<UUID> ids = Set.of(id);

        MasterCatalogRawData rawData = new MasterCatalogRawData();
        rawData.setId(id);

        when(masterCatalogRawDataRepository.findAllById(ids)).thenReturn(List.of(rawData));

        masterCatalogRawDataService.markAsCompleted(ids);

        ArgumentCaptor<List<MasterCatalogRawData>> captor = ArgumentCaptor.forClass(List.class);
        verify(masterCatalogRawDataRepository).saveAll(captor.capture());

        MasterCatalogRawData savedData = captor.getValue().get(0);
    }

    @Test
    void searchDuplicationRawData_ShouldBeSuccessful() {
        String upc1 = "175097856274";
        String upc2 = "175097856275";
        UUID duplicatedUpc = UUID.randomUUID();
        MasterCatalogRawDataDuplication duplication1 = MasterCatalogRawDataDuplication
            .builder()
            .id(UUID.randomUUID())
            .upc(upc1)
            .duplicationGroup(duplicatedUpc)
            .build();

        MasterCatalogRawData rawData1 = buildMasterCatalogRawData(upc1);
        MasterCatalogRawData rawData2 = buildMasterCatalogRawData(upc2);

        MasterCatalogRawDataDto rawDataDto1 = MasterCatalogRawDataDto.builder().upc(upc1).build();
        MasterCatalogRawDataDto rawDataDto2 = MasterCatalogRawDataDto.builder().upc(upc2).build();

        when(masterCatalogRawDataDuplicationRepository.findByUpc(upc1)).thenReturn(List.of(duplication1));
        when(masterCatalogImageRepository.findAllByMasterCatalogRawDataIdIn(anyList())).thenReturn(List.of());
        when(masterCatalogRawDataMapper.toDto(rawData1)).thenReturn(rawDataDto1);
        when(masterCatalogRawDataMapper.toDto(rawData2)).thenReturn(rawDataDto2);
        when(masterCatalogRawDataDuplicationRepository.findUpcsByDuplicationGroupIn(List.of(duplicatedUpc))).thenReturn(List.of(upc1, upc2));
        when(masterCatalogRawDataRepository.findByUpcIn(any(), any())).thenReturn(new PageImpl<>(List.of(rawData1, rawData2)));

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchDuplicationRawData(1, 10, upc1);

        assert result.getContent().size() == 2;
        assert result.getContent().stream().map(MasterCatalogRawDataDto::getUpc).toList().containsAll(List.of(upc1, upc2));
    }

    @Test
    void searchDuplicationRawData_NoDuplications_ReturnsEmptyPage() {
        when(masterCatalogRawDataDuplicationRepository.findByUpc("123456")).thenReturn(Collections.emptyList());

        Page<MasterCatalogRawDataDto> result = masterCatalogRawDataService.searchDuplicationRawData(1, 10, "123456");

        assert result.isEmpty();
    }
}