package com.mercaso.data.third_party.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.data.third_party.exception.SerializationException;
import com.mercaso.data.utils.SerializationUtils;
import java.time.LocalDateTime;
import java.util.Map;
import org.junit.jupiter.api.Test;

class SerializationUtilsTest {

    @Test
    void testDeserialize() {
        String json = "{\"name\":\"John\",\"age\":30}";
        TestPerson person = SerializationUtils.deserialize(json, TestPerson.class);
        assertNotNull(person);
        assertEquals("<PERSON>", person.getName());
        assertEquals(30, person.getAge());
    }

    @Test
    void testDeserializeToMap() {
        String json = "{\"name\":\"John\",\"age\":30}";
        Map<String, Object> map = SerializationUtils.deserializeToMap(json);
        assertNotNull(map);
        assertEquals("John", map.get("name"));
        assertEquals(30, map.get("age"));
    }

    @Test
    void testSerialize() {
        TestPerson person = new TestPerson("John", 30);
        String json = SerializationUtils.serialize(person);
        assertTrue(json.contains("\"name\":\"John\""));
        assertTrue(json.contains("\"age\":30"));
    }

    @Test
    void testReadTree() {
        TestPerson person = new TestPerson("John", 30);
        JsonNode jsonNode = SerializationUtils.toTree(person);
        assertNotNull(jsonNode);
        assertEquals("John", jsonNode.get("name").asText());
        assertEquals(30, jsonNode.get("age").asInt());
    }

    @Test
    void testReadValue() {
        String json = "{\"name\":\"John\",\"age\":30}";
        TestPerson person = SerializationUtils.readValue(json, new TypeReference<TestPerson>() {
        });
        assertNotNull(person);
        assertEquals("John", person.getName());
        assertEquals(30, person.getAge());
    }

    @Test
    void testTreeToValue() {
        TestPerson person = new TestPerson("John", 30);
        JsonNode jsonNode = SerializationUtils.toTree(person);
        TestPerson convertedPerson = SerializationUtils.treeToValue(jsonNode, TestPerson.class);
        assertNotNull(convertedPerson);
        assertEquals("John", convertedPerson.getName());
        assertEquals(30, convertedPerson.getAge());
    }

    @Test
    void testCustomLocalDateTimeDeserializer() {
        String json = "{\"dateTime\":\"Aug 31 2024 2:30:00 PM\"}";
        TestDateObject dateObject = SerializationUtils.deserialize(json, TestDateObject.class);
        assertNotNull(dateObject);
        assertEquals(LocalDateTime.of(2024, 8, 31, 14, 30, 0), dateObject.getDateTime());
    }

    @Test
    void testDeserializationException() {
        String invalidJson = "{invalid_json}";
        assertThrows(SerializationException.class, () -> SerializationUtils.deserialize(invalidJson, TestPerson.class));
    }

    @Test
    void testReadTreeWithJsonString_ValidJson_ShouldReturnJsonNode() {
        // Given
        String jsonString = "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LCRZX1K3K66X1\"}";

        // When
        JsonNode result = SerializationUtils.readTree(jsonString);

        // Then
        assertNotNull(result);
        assertEquals("MLP68E8YSGYKM", result.get("merchantId").asText());
        assertEquals("LCRZX1K3K66X1", result.get("locationId").asText());
    }

    @Test
    void testReadTreeWithJsonString_EmptyJson_ShouldReturnEmptyNode() {
        // Given
        String jsonString = "{}";

        // When
        JsonNode result = SerializationUtils.readTree(jsonString);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testReadTreeWithJsonString_InvalidJson_ShouldThrowException() {
        // Given
        String invalidJson = "{invalid json}";

        // When & Then
        SerializationException exception = assertThrows(SerializationException.class,
            () -> SerializationUtils.readTree(invalidJson));
        assertEquals("readTree exception", exception.getMessage());
    }

    @Test
    void testReadTreeWithJsonString_ComplexJson_ShouldParseCorrectly() {
        // Given
        String complexJson = "{" +
            "\"merchantId\":\"MLP68E8YSGYKM\"," +
            "\"locationId\":\"LCRZX1K3K66X1\"," +
            "\"nested\":{" +
            "\"field1\":\"value1\"," +
            "\"field2\":123" +
            "}," +
            "\"array\":[1,2,3]" +
            "}";

        // When
        JsonNode result = SerializationUtils.readTree(complexJson);

        // Then
        assertNotNull(result);
        assertEquals("MLP68E8YSGYKM", result.get("merchantId").asText());
        assertEquals("LCRZX1K3K66X1", result.get("locationId").asText());
        assertTrue(result.has("nested"));
        assertEquals("value1", result.get("nested").get("field1").asText());
        assertEquals(123, result.get("nested").get("field2").asInt());
        assertTrue(result.get("array").isArray());
        assertEquals(3, result.get("array").size());
    }

    @Test
    void testReadTreeWithJsonString_SquareLocationInfo_ShouldParseCorrectly() {
        // Given
        String locationJson = "{\"merchantId\":\"MLP68E8YSGYKM\", \"locationId\":\"LQ6C3T6VVG02X\"}";

        // When
        JsonNode result = SerializationUtils.readTree(locationJson);

        // Then
        assertNotNull(result);
        assertEquals("MLP68E8YSGYKM", result.get("merchantId").asText());
        assertEquals("LQ6C3T6VVG02X", result.get("locationId").asText());
    }

    // Helper classes for testing
    private static class TestPerson {

        private String name;
        private int age;

        public TestPerson() {
        }

        public TestPerson(String name, int age) {
            this.name = name;
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }

    private static class TestDateObject {

        private LocalDateTime dateTime;

        public LocalDateTime getDateTime() {
            return dateTime;
        }

        public void setDateTime(LocalDateTime dateTime) {
            this.dateTime = dateTime;
        }
    }
}