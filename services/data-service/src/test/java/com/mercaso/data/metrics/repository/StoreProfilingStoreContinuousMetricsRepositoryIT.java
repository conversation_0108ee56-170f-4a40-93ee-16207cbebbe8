package com.mercaso.data.metrics.repository;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Transactional
class StoreProfilingStoreContinuousMetricsRepositoryIT extends AbstractIT {

    @Autowired
    private StoreProfilingStoreContinuousMetricsRepository repository;
    
    @Test
    void findByStoreIdAndMetricName_ShouldReturnEntities() {
        // Given
        String storeId = "test-store-2";
        String metricName = "test-metric";
        StoreProfilingStoreContinuousMetricsEntity entity = createTestEntity(storeId, metricName);
        repository.save(entity);
        repository.flush();

        // When
        List<StoreProfilingStoreContinuousMetricsEntity> result = repository.findByStoreIdAndMetricName(storeId, metricName);

        // Then
        assertThat(result).isNotEmpty();
        assertThat(result.get(0).getStoreId()).isEqualTo(storeId);
        assertThat(result.get(0).getMetricName()).isEqualTo(metricName);
    }

    private StoreProfilingStoreContinuousMetricsEntity createTestEntity(String storeId, String metricName) {
        StoreProfilingStoreContinuousMetricsEntity entity = new StoreProfilingStoreContinuousMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory("test-category");
        entity.setMetricName(metricName);
        entity.setMetricDesc("test description");
        entity.setMetricValue(new BigDecimal("100.50"));
        entity.setMetricDate(LocalDateTime.now());
        entity.setMetricDateType("daily");
        return entity;
    }
} 