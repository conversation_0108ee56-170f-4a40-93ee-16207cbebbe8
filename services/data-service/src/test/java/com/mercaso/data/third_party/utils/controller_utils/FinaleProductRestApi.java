package com.mercaso.data.third_party.utils.controller_utils;


import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.third_party.dto.finale.FinaleAvailableStockFilter;
import com.mercaso.data.third_party.dto.finale.FinaleFacilityFilter;
import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class FinaleProductRestApi extends IntegrationTestRestUtil {

    private static final String CREATE_FINALE_AVAILABLE_STOCK_URL = "/finale/availableStock";

    private static final String CREATE_FINALE_FACILITY_URL = "/finale/facilities";


    public FinaleProductRestApi(Environment environment) {
        super(environment);
    }

    public ResponseEntity<CustomPage> getProducts(FinaleAvailableStockFilter filter) {
        return postEntity(CREATE_FINALE_AVAILABLE_STOCK_URL, filter, CustomPage.class);
    }

    public ResponseEntity<CustomPage> getFacilities(FinaleFacilityFilter filter) {
        return postEntity(CREATE_FINALE_FACILITY_URL, filter, CustomPage.class);
    }
}
