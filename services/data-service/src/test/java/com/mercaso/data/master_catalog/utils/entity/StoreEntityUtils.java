package com.mercaso.data.master_catalog.utils.entity;

import com.mercaso.data.master_catalog.entity.Store;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StoreEntityUtils {

    public static Store buildStore(String name) {
        return Store.builder()
            .id(UUID.randomUUID())
            .name(name)
            .integrationType("SQUARE")
            .build();
    }
}
