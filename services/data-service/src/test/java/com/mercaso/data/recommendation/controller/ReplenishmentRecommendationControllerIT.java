package com.mercaso.data.recommendation.controller;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.recommendation.entity.ReplenishmentRecommendation;
import com.mercaso.data.master_catalog.entity.Store;
import com.mercaso.data.master_catalog.repository.ReplenishmentRecommendationRepository;
import com.mercaso.data.master_catalog.repository.StoreRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogReplenishmentForecastResourceApiUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

class ReplenishmentRecommendationControllerIT extends AbstractIT {

    @Autowired
    private MasterCatalogReplenishmentForecastResourceApiUtils masterCatalogReplenishmentForecastResourceApiUtils;
    @Autowired
    private StoreRepository storeRepository;
    @Autowired
    private ReplenishmentRecommendationRepository replenishmentRecommendationRepository;

    @Test
    @DisplayName("Export Replenishment Forecast")
    void exportReplenishmentForecast() {
        // Arrange
        UUID storeId = UUID.fromString("ecb65679-b851-498a-beee-e31c8926f123");
        Store store = new Store();
        store.setId(storeId);
        store.setName("Store Name");
        store.setIntegrationType("Integration Type");

        storeRepository.save(store);

        ReplenishmentRecommendation replenishmentRecommendation = new ReplenishmentRecommendation();
        replenishmentRecommendation.setId(UUID.randomUUID());
        replenishmentRecommendation.setStoreId(storeId);
        replenishmentRecommendation.setSku("SKU");
        replenishmentRecommendation.setRecommendedQuantity(BigDecimal.valueOf(10));
        replenishmentRecommendation.setBatchNumber(DateTimeFormatter.ofPattern("yyyyMMdd")
            .withZone(ZoneId.systemDefault())
            .format(Instant.now()));

        replenishmentRecommendationRepository.save(replenishmentRecommendation);

        // Act
        ResponseEntity<byte[]> response = masterCatalogReplenishmentForecastResourceApiUtils
            .exportReplenishmentForecast(storeId);

        // Assert
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertNotNull(response.getBody());
        Assertions.assertEquals(MediaType.APPLICATION_OCTET_STREAM_VALUE, response.getHeaders().getContentType().toString());
        Assertions.assertEquals("%s_%s.csv".formatted(store.getName(), replenishmentRecommendation.getBatchNumber()),
            response.getHeaders().getContentDisposition().getFilename());
    }
} 