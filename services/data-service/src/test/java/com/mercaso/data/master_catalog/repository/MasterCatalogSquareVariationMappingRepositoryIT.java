package com.mercaso.data.master_catalog.repository;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogSquareVariationMapping;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

class MasterCatalogSquareVariationMappingRepositoryIT extends AbstractIT {

    @Autowired
    private MasterCatalogSquareVariationMappingRepository variationMappingRepository;

    @Autowired
    private MasterCatalogRawDataRepository rawDataRepository;

    /**
     * Test the findAllByVariationIdInAndStoreIdIs method.
     */
    @Test
    @Transactional
    void testFindAllByVariationIdInAndStoreIdIs() {
        // 1. Prepare test data
        UUID storeId = UUID.randomUUID();

        // Create and save MasterCatalogRawData entities
        MasterCatalogRawData rawData1 = buildMasterCatalogRawData(storeId);
        rawData1.setUpdatedAt(Instant.now().minusSeconds(60));
        MasterCatalogRawData savedRawData1 = rawDataRepository.save(rawData1);

        MasterCatalogRawData rawData2 = buildMasterCatalogRawData(storeId);
        rawData2.setUpdatedAt(Instant.now().minusSeconds(30));
        MasterCatalogRawData savedRawData2 = rawDataRepository.save(rawData2);

        String variationId001 = UUID.randomUUID().toString();
        // Create and save MasterCatalogSquareVariationMapping entities
        MasterCatalogSquareVariationMapping mapping1 = MasterCatalogSquareVariationMapping.builder()
            .masterCatalogRawDataId(savedRawData1.getId())
            .variationId(variationId001)
            .build();
        variationMappingRepository.save(mapping1);

        MasterCatalogSquareVariationMapping mapping2 = MasterCatalogSquareVariationMapping.builder()
            .masterCatalogRawDataId(savedRawData2.getId())
            .variationId(UUID.randomUUID().toString())
            .build();
        variationMappingRepository.save(mapping2);

        MasterCatalogSquareVariationMapping mapping3 = MasterCatalogSquareVariationMapping.builder()
            .masterCatalogRawDataId(savedRawData1.getId())
            .variationId(UUID.randomUUID().toString())
            .build();
        variationMappingRepository.save(mapping3);

        // 2. Define query parameters
        List<String> variationIdList = List.of(variationId001);
        UUID queryStoreId = storeId;

        // 3. Call repository method
        List<MasterCatalogSquareVariationMapping> result = variationMappingRepository
            .findAllByVariationIdInAndStoreIdIs(variationIdList, queryStoreId);

        // 4. Assert results
        assertEquals(1, result.size(), "Should return two matching VariationMappings");

        // Verify that the returned VariationMappings contain the expected variationIds
        List<String> returnedVariationIds = Collections.singletonList(
            result.getFirst().getVariationId()
        );
        assertTrue(returnedVariationIds.contains(variationId001), "Result should contain variationId001");
    }
}
