# Finale Suppliers API Implementation

## Overview

This implementation provides functionality to call the Finale API to retrieve all supplier information and convert it to a `List<FinaleVendorInfoDto>`. The API endpoint called is:

```
https://app.finaleinventory.com/mercaso/api/partygroup
```

## Implementation Details

### 1. Response DTO (`FinaleAllSuppliersResponseDto`)

**Location**: `services/item-management-service/src/main/java/com/mercaso/ims/infrastructure/external/finale/dto/FinaleAllSuppliersResponseDto.java`

This DTO handles the array-based response structure from the Finale API:

```json
{
    "partyId": ["100001", "100002", ...],
    "partyUrl": ["/mercaso/api/partygroup/100001", "/mercaso/api/partygroup/100002", ...],
    "statusId": ["PARTY_ENABLED", "PARTY_ENABLED", ...],
    "lastUpdatedDate": ["2022-09-20T22:08:08", "2022-09-20T22:08:25", ...],
    "createdDate": ["2022-09-20T22:08:08", "2022-09-20T22:08:25", ...],
    "roleTypeIdList": [["USER"], ["USER"], ...],
    "glAccountList": [[], [], ...],
    "contentList": [[], [], ...],
    "userFieldDataList": [[], [], ...],
    "connectionRelationUrlList": [[], [], ...],
    "productStoreUrlList": [null, null, ...]
}
```

**Key Method**: `toFinaleVendorInfoDtoList()` - Converts the array-based response to a `List<FinaleVendorInfoDto>`

### 2. API Client Method (`FinaleExternalApiClient`)

**Location**: `services/item-management-service/src/main/java/com/mercaso/ims/infrastructure/external/finale/FinaleExternalApiClient.java`

**Method**: `getAllSuppliers()`
- Makes HTTP GET request to the Finale API
- Includes proper authentication headers
- Handles error responses
- Uses rate limiting and retry mechanisms
- Returns `List<FinaleVendorInfoDto>`

### 3. Adaptor Layer (`FinaleExternalApiAdaptor`)

**Location**: `services/item-management-service/src/main/java/com/mercaso/ims/infrastructure/external/finale/FinaleExternalApiAdaptor.java`

**Method**: `getAllSuppliers()` - Wraps the client method for use by application services

### 4. Application Service (`FinaleApplicationService`)

**Interface**: `services/item-management-service/src/main/java/com/mercaso/ims/application/service/FinaleApplicationService.java`
**Implementation**: `services/item-management-service/src/main/java/com/mercaso/ims/application/service/impl/FinaleApplicationServiceImpl.java`

**Method**: `getAllSuppliers()` - Public interface for getting all suppliers

### 5. Configuration

**Location**: `services/item-management-service/src/main/resources/config/application.yml`

Added new configuration property:
```yaml
finale:
  get_all_suppliers_url: https://app.finaleinventory.com/${finale.accountPathComponent}/api/partygroup
```

### 6. REST Controller (`FinaleSupplierController`)

**Location**: `services/item-management-service/src/main/java/com/mercaso/ims/infrastructure/rest/FinaleSupplierController.java`

Provides REST endpoints:
- `GET /v1/finale/suppliers` - Get all suppliers
- `GET /v1/finale/suppliers/by-status?status=PARTY_ENABLED` - Get suppliers by status
- `GET /v1/finale/suppliers/{partyId}` - Get specific supplier by party ID
- `GET /v1/finale/suppliers/count/enabled` - Get count of enabled suppliers

### 7. Example Usage (`FinaleSupplierExample`)

**Location**: `services/item-management-service/src/main/java/com/mercaso/ims/application/example/FinaleSupplierExample.java`

Demonstrates how to use the new functionality with various filtering and processing examples.

## Usage Examples

### Basic Usage

```java
@Autowired
private FinaleApplicationService finaleApplicationService;

public void getAllSuppliers() {
    List<FinaleVendorInfoDto> suppliers = finaleApplicationService.getAllSuppliers();
    
    for (FinaleVendorInfoDto supplier : suppliers) {
        System.out.println("Supplier ID: " + supplier.getPartyId());
        System.out.println("Status: " + supplier.getStatusId());
        System.out.println("Created: " + supplier.getCreatedDate());
    }
}
```

### Filter by Status

```java
public List<FinaleVendorInfoDto> getEnabledSuppliers() {
    List<FinaleVendorInfoDto> allSuppliers = finaleApplicationService.getAllSuppliers();
    
    return allSuppliers.stream()
        .filter(supplier -> "PARTY_ENABLED".equals(supplier.getStatusId()))
        .toList();
}
```

### REST API Usage

```bash
# Get all suppliers
curl -X GET "http://localhost:8080/v1/finale/suppliers" \
  -H "Authorization: Bearer <token>"

# Get suppliers by status
curl -X GET "http://localhost:8080/v1/finale/suppliers/by-status?status=PARTY_ENABLED" \
  -H "Authorization: Bearer <token>"

# Get specific supplier
curl -X GET "http://localhost:8080/v1/finale/suppliers/100001" \
  -H "Authorization: Bearer <token>"

# Get count of enabled suppliers
curl -X GET "http://localhost:8080/v1/finale/suppliers/count/enabled" \
  -H "Authorization: Bearer <token>"
```

## Testing

### Unit Tests

**Location**: `services/item-management-service/src/test/java/com/mercaso/ims/application/service/impl/FinaleApplicationServiceImplTest.java`

Added test method: `testGetAllSuppliers()` - Tests the application service method

**Location**: `services/item-management-service/src/test/java/com/mercaso/ims/infrastructure/external/finale/FinaleExternalApiClientTest.java`

Added test methods:
- `getAllSuppliers_Success_ReturnsSuppliersList()` - Tests successful API call
- `getAllSuppliers_UnsuccessfulResponse_ThrowsImsBusinessException()` - Tests error handling
- `getAllSuppliers_IOException_ThrowsImsBusinessException()` - Tests network error handling

## Error Handling

The implementation includes comprehensive error handling:
- Network errors are caught and wrapped in `ImsBusinessException`
- HTTP error responses are properly handled
- Retry mechanism is in place for transient failures
- Rate limiting is applied to prevent API abuse

## Security

- All REST endpoints require proper authorization
- Uses existing authentication mechanisms
- Follows the same security patterns as other Finale API integrations

## Performance Considerations

- Uses rate limiting to comply with Finale API limits
- Implements retry mechanism for reliability
- Caches are not implemented at this level (can be added if needed)
- Efficient array-to-list conversion with minimal object creation

## Future Enhancements

1. **Caching**: Add caching layer to reduce API calls
2. **Pagination**: If Finale API supports pagination, implement it
3. **Filtering**: Add server-side filtering capabilities
4. **Monitoring**: Add metrics and monitoring for API calls
5. **Async Processing**: Consider async processing for large datasets
